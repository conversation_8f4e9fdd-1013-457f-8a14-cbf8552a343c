package com.anytech.anytxn.parameter.common.service.account;

import com.alibaba.fastjson.JSON;
import com.anytech.anytxn.business.base.audit.ParmModificationRecord;
import com.anytech.anytxn.common.core.base.ParameterCompare;
import com.anytech.anytxn.common.core.base.PageResultDTO;
import com.anytech.anytxn.common.core.utils.BeanMapping;
import com.anytech.anytxn.common.core.utils.OrgNumberUtils;
import com.anytech.anytxn.common.core.utils.TenantUtils;
import com.anytech.anytxn.common.sequence.utils.Number16IdGen;
import com.anytech.anytxn.parameter.account.mapper.InterestSettlementMapper;
import com.anytech.anytxn.parameter.account.service.InterestSettlementServiceImpl;
import com.anytech.anytxn.parameter.base.account.domain.dto.InterestSettlementDTO;
import com.anytech.anytxn.parameter.base.account.domain.model.InterestSettlement;
import com.anytech.anytxn.parameter.base.common.enums.AnyTxnParameterRespCodeEnum;
import com.anytech.anytxn.parameter.base.common.exception.AnyTxnParameterException;

import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockedStatic;
import org.mockito.junit.jupiter.MockitoExtension;
import org.mockito.junit.jupiter.MockitoSettings;
import org.mockito.quality.Strictness;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.Arrays;
import java.util.Collections;
import java.util.List;

import static org.assertj.core.api.Assertions.assertThat;
import static org.assertj.core.api.Assertions.assertThatThrownBy;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.*;

/**
 * InterestSettlementServiceImpl 单元测试类
 * 
 * <AUTHOR>
 */
@ExtendWith(MockitoExtension.class)
@MockitoSettings(strictness = Strictness.LENIENT)
class InterestSettlementServiceTest {

    @Mock
    private InterestSettlementMapper interestSettlementMapper;

    @Mock
    private Number16IdGen numberIdGenerator;

    @InjectMocks
    private InterestSettlementServiceImpl interestSettlementService;

    private InterestSettlementDTO testInterestSettlementDTO;
    private InterestSettlement testInterestSettlement;
    private final String TEST_ID = "1234567890123456";
    private final String TEST_TABLE_ID = "INT_SETTLE_001";
    private final String TEST_ORG_NUMBER = "0001";
    private final String TEST_DESCRIPTION = "测试结息参数";

    @BeforeEach
    void setUp() {
        try (MockedStatic<OrgNumberUtils> orgUtils = mockStatic(OrgNumberUtils.class)) {
            orgUtils.when(() -> OrgNumberUtils.getOrg(anyString())).thenReturn(TEST_ORG_NUMBER);
            
            // 设置测试数据
            testInterestSettlementDTO = createTestInterestSettlementDTO();
            testInterestSettlement = createTestInterestSettlement();
        }
    }

    private InterestSettlementDTO createTestInterestSettlementDTO() {
        InterestSettlementDTO dto = new InterestSettlementDTO();
        dto.setId(TEST_ID);
        dto.setOrganizationNumber(TEST_ORG_NUMBER);
        dto.setTableId(TEST_TABLE_ID);
        dto.setDescription(TEST_DESCRIPTION);
        dto.setGraceOption("Y");
        dto.setWavieOption("1");
        dto.setInterestBillingTxnCode("INT001");
        dto.setIntTaxPostTxnCde("TAX001");
        dto.setInterestRate(new BigDecimal("0.05"));
        dto.setCreditIntSettlementType("1");
        dto.setInterestSettlementThreshold(new BigDecimal("10.00"));
        dto.setInterestSettlementFrequency("1");
        dto.setQ1IntBillingDate("0331");
        dto.setQ2IntBillingDate("0630");
        dto.setQ3IntBillingDate("0930");
        dto.setQ4IntBillingDate("1231");
        dto.setLmtUnitCodeFollowIndicator("0");
        dto.setStatus("A");
        dto.setCreateTime(LocalDateTime.now());
        dto.setUpdateTime(LocalDateTime.now());
        return dto;
    }

    private InterestSettlement createTestInterestSettlement() {
        InterestSettlement entity = new InterestSettlement();
        entity.setId(TEST_ID);
        entity.setOrganizationNumber(TEST_ORG_NUMBER);
        entity.setTableId(TEST_TABLE_ID);
        entity.setDescription(TEST_DESCRIPTION);
        entity.setGraceOption("Y");
        entity.setWavieOption("1");
        entity.setInterestBillingTxnCode("INT001");
        entity.setIntTaxPostTxnCde("TAX001");
        entity.setInterestRate(new BigDecimal("0.05"));
        entity.setCreditIntSettlementType("1");
        entity.setInterestSettlementThreshold(new BigDecimal("10.00"));
        entity.setInterestSettlementFrequency("1");
        entity.setQ1IntBillingDate("0331");
        entity.setQ2IntBillingDate("0630");
        entity.setQ3IntBillingDate("0930");
        entity.setQ4IntBillingDate("1231");
        entity.setLmtUnitCodeFollowIndicator("0");
        entity.setStatus("A");
        entity.setCreateTime(LocalDateTime.now());
        entity.setUpdateTime(LocalDateTime.now());
        entity.setVersionNumber(1L);
        return entity;
    }

    @Test
    void testAdd_Success() {
        try (MockedStatic<OrgNumberUtils> orgUtils = mockStatic(OrgNumberUtils.class);
             MockedStatic<BeanMapping> beanMapping = mockStatic(BeanMapping.class);
             MockedStatic<TenantUtils> tenantUtils = mockStatic(TenantUtils.class)) {
            
            // Arrange
            orgUtils.when(() -> OrgNumberUtils.getOrg(TEST_ORG_NUMBER)).thenReturn(TEST_ORG_NUMBER);
            tenantUtils.when(TenantUtils::getTenantId).thenReturn("tenant1");
            beanMapping.when(() -> BeanMapping.copy(testInterestSettlementDTO, InterestSettlement.class))
                      .thenReturn(testInterestSettlement);
            
            when(interestSettlementMapper.selectByTableIdAndOrg(TEST_TABLE_ID, TEST_ORG_NUMBER)).thenReturn(null);
            when(numberIdGenerator.generateId("tenant1")).thenReturn(Long.parseLong(TEST_ID));

            // Act
            ParameterCompare result = interestSettlementService.add(testInterestSettlementDTO);

            // Assert
            assertThat(result).isNotNull();
            verify(interestSettlementMapper).selectByTableIdAndOrg(TEST_TABLE_ID, TEST_ORG_NUMBER);
            verify(numberIdGenerator).generateId("tenant1");
        }
    }

    @Test
    void testAdd_EmptyDTO_ThrowsException() {
        // Act & Assert
        assertThatThrownBy(() -> interestSettlementService.add(null))
                .isInstanceOf(AnyTxnParameterException.class)
                .hasFieldOrPropertyWithValue("errCode", AnyTxnParameterRespCodeEnum.P_PARAMETER_EMPTY_FAULT.getCode());
    }

    @Test
    void testAdd_EmptyTableId_ThrowsException() {
        // Arrange
        testInterestSettlementDTO.setTableId(null);

        // Act & Assert
        assertThatThrownBy(() -> interestSettlementService.add(testInterestSettlementDTO))
                .isInstanceOf(AnyTxnParameterException.class)
                .hasFieldOrPropertyWithValue("errCode", AnyTxnParameterRespCodeEnum.P_PARAMETER_TABLE_ID_EMPTY_FAULT.getCode());
    }

    @Test
    void testAdd_AlreadyExists_ThrowsException() {
        try (MockedStatic<OrgNumberUtils> orgUtils = mockStatic(OrgNumberUtils.class)) {
            // Arrange
            orgUtils.when(() -> OrgNumberUtils.getOrg(TEST_ORG_NUMBER)).thenReturn(TEST_ORG_NUMBER);
            when(interestSettlementMapper.selectByTableIdAndOrg(TEST_TABLE_ID, TEST_ORG_NUMBER))
                    .thenReturn(testInterestSettlement);

            // Act & Assert
            assertThatThrownBy(() -> interestSettlementService.add(testInterestSettlementDTO))
                    .isInstanceOf(AnyTxnParameterException.class)
                    .hasFieldOrPropertyWithValue("errCode", AnyTxnParameterRespCodeEnum.D_EXIST_PARM_PAYMENT_ALLOCATED_BY_ID_FAULT.getCode());
        }
    }

    @Test
    void testModify_Success() {
        try (MockedStatic<BeanMapping> beanMapping = mockStatic(BeanMapping.class)) {
            // Arrange
            beanMapping.when(() -> BeanMapping.copy(testInterestSettlementDTO, InterestSettlement.class))
                      .thenReturn(testInterestSettlement);
            when(interestSettlementMapper.selectByPrimaryKey(TEST_ID)).thenReturn(testInterestSettlement);

            // Act
            ParameterCompare result = interestSettlementService.modify(testInterestSettlementDTO);

            // Assert
            assertThat(result).isNotNull();
            verify(interestSettlementMapper).selectByPrimaryKey(TEST_ID);
        }
    }

    @Test
    void testModify_EmptyDTO_ThrowsException() {
        // Act & Assert
        assertThatThrownBy(() -> interestSettlementService.modify(null))
                .isInstanceOf(AnyTxnParameterException.class)
                .hasFieldOrPropertyWithValue("errCode", AnyTxnParameterRespCodeEnum.P_PARAMETER_EMPTY_FAULT.getCode());
    }

    @Test
    void testModify_DataNotExist_ThrowsException() {
        // Arrange
        when(interestSettlementMapper.selectByPrimaryKey(TEST_ID)).thenReturn(null);

        // Act & Assert
        assertThatThrownBy(() -> interestSettlementService.modify(testInterestSettlementDTO))
                .isInstanceOf(AnyTxnParameterException.class)
                .hasFieldOrPropertyWithValue("errCode", AnyTxnParameterRespCodeEnum.P_PARAMETER_DATA_NOT_EXIST.getCode());
    }

    @Test
    void testRemove_Success() {
        // Arrange
        when(interestSettlementMapper.selectByPrimaryKey(TEST_ID)).thenReturn(testInterestSettlement);

        // Act
        ParameterCompare result = interestSettlementService.remove(TEST_ID);

        // Assert
        assertThat(result).isNotNull();
        verify(interestSettlementMapper).selectByPrimaryKey(TEST_ID);
    }

    @Test
    void testRemove_EmptyId_ThrowsException() {
        // Act & Assert
        assertThatThrownBy(() -> interestSettlementService.remove(null))
                .isInstanceOf(AnyTxnParameterException.class)
                .hasFieldOrPropertyWithValue("errCode", AnyTxnParameterRespCodeEnum.P_PARAMETER_EMPTY_FAULT.getCode());
    }

    @Test
    void testRemove_DataNotExist_ThrowsException() {
        // Arrange
        when(interestSettlementMapper.selectByPrimaryKey(TEST_ID)).thenReturn(null);

        // Act & Assert
        assertThatThrownBy(() -> interestSettlementService.remove(TEST_ID))
                .isInstanceOf(AnyTxnParameterException.class)
                .hasFieldOrPropertyWithValue("errCode", AnyTxnParameterRespCodeEnum.P_PARAMETER_DATA_NOT_EXIST.getCode());
    }

    @Test
    void testFindById_Success() {
        try (MockedStatic<BeanMapping> beanMapping = mockStatic(BeanMapping.class)) {
            // Arrange
            beanMapping.when(() -> BeanMapping.copy(testInterestSettlement, InterestSettlementDTO.class))
                      .thenReturn(testInterestSettlementDTO);
            when(interestSettlementMapper.selectByPrimaryKey(TEST_ID)).thenReturn(testInterestSettlement);

            // Act
            InterestSettlementDTO result = interestSettlementService.findById(TEST_ID);

            // Assert
            assertThat(result).isNotNull();
            assertThat(result.getId()).isEqualTo(TEST_ID);
            verify(interestSettlementMapper).selectByPrimaryKey(TEST_ID);
        }
    }

    @Test
    void testFindById_EmptyId_ThrowsException() {
        // Act & Assert
        assertThatThrownBy(() -> interestSettlementService.findById(null))
                .isInstanceOf(AnyTxnParameterException.class)
                .hasFieldOrPropertyWithValue("errCode", AnyTxnParameterRespCodeEnum.P_PARAMETER_EMPTY_FAULT.getCode());
    }

    @Test
    void testFindById_NotFound() {
        try (MockedStatic<BeanMapping> beanMapping = mockStatic(BeanMapping.class)) {
            // Arrange
            beanMapping.when(() -> BeanMapping.copy(null, InterestSettlementDTO.class)).thenReturn(null);
            when(interestSettlementMapper.selectByPrimaryKey(TEST_ID)).thenReturn(null);

            // Act
            InterestSettlementDTO result = interestSettlementService.findById(TEST_ID);

            // Assert
            assertThat(result).isNull();
            verify(interestSettlementMapper).selectByPrimaryKey(TEST_ID);
        }
    }

    @Test
    void testFindPage_Success() {
        try (MockedStatic<BeanMapping> beanMapping = mockStatic(BeanMapping.class);
             MockedStatic<OrgNumberUtils> orgUtils = mockStatic(OrgNumberUtils.class)) {
            
            // Arrange
            List<InterestSettlement> entityList = Arrays.asList(testInterestSettlement);
            List<InterestSettlementDTO> dtoList = Arrays.asList(testInterestSettlementDTO);
            
            orgUtils.when(() -> OrgNumberUtils.getOrg(TEST_ORG_NUMBER)).thenReturn(TEST_ORG_NUMBER);
            beanMapping.when(() -> BeanMapping.copyList(entityList, InterestSettlementDTO.class))
                      .thenReturn(dtoList);
            
            when(interestSettlementMapper.selectByConditionAndPage(TEST_TABLE_ID, TEST_DESCRIPTION, TEST_ORG_NUMBER))
                    .thenReturn(entityList);

            // Act
            PageResultDTO<InterestSettlementDTO> result = interestSettlementService.findPage(1, 10, TEST_ORG_NUMBER, TEST_TABLE_ID, TEST_DESCRIPTION);

            // Assert
            assertThat(result).isNotNull();
            assertThat(result.getData()).hasSize(1);
            assertThat(result.getPage()).isEqualTo(1);
            assertThat(result.getRows()).isEqualTo(10);
            verify(interestSettlementMapper).selectByConditionAndPage(TEST_TABLE_ID, TEST_DESCRIPTION, TEST_ORG_NUMBER);
        }
    }

    @Test
    void testFindPage_EmptyResult() {
        try (MockedStatic<BeanMapping> beanMapping = mockStatic(BeanMapping.class);
             MockedStatic<OrgNumberUtils> orgUtils = mockStatic(OrgNumberUtils.class)) {
            
            // Arrange
            List<InterestSettlement> entityList = Collections.emptyList();
            List<InterestSettlementDTO> dtoList = Collections.emptyList();
            
            orgUtils.when(() -> OrgNumberUtils.getOrg(TEST_ORG_NUMBER)).thenReturn(TEST_ORG_NUMBER);
            beanMapping.when(() -> BeanMapping.copyList(entityList, InterestSettlementDTO.class))
                      .thenReturn(dtoList);
            
            when(interestSettlementMapper.selectByConditionAndPage(TEST_TABLE_ID, TEST_DESCRIPTION, TEST_ORG_NUMBER))
                    .thenReturn(entityList);

            // Act
            PageResultDTO<InterestSettlementDTO> result = interestSettlementService.findPage(1, 10, TEST_ORG_NUMBER, TEST_TABLE_ID, TEST_DESCRIPTION);

            // Assert
            assertThat(result).isNotNull();
            assertThat(result.getData()).isEmpty();
            assertThat(result.getPage()).isEqualTo(1);
            assertThat(result.getRows()).isEqualTo(10);
        }
    }

    @Test
    void testFindByOrgAndTableId_Success() {
        try (MockedStatic<BeanMapping> beanMapping = mockStatic(BeanMapping.class)) {
            // Arrange
            beanMapping.when(() -> BeanMapping.copy(testInterestSettlement, InterestSettlementDTO.class))
                      .thenReturn(testInterestSettlementDTO);
            when(interestSettlementMapper.selectByTableIdAndOrg(TEST_TABLE_ID, TEST_ORG_NUMBER))
                    .thenReturn(testInterestSettlement);

            // Act
            InterestSettlementDTO result = interestSettlementService.findByOrgAndTableId(TEST_ORG_NUMBER, TEST_TABLE_ID);

            // Assert
            assertThat(result).isNotNull();
            assertThat(result.getTableId()).isEqualTo(TEST_TABLE_ID);
            verify(interestSettlementMapper).selectByTableIdAndOrg(TEST_TABLE_ID, TEST_ORG_NUMBER);
        }
    }

    @Test
    void testFindByOrgAndTableId_NotFound() {
        // Arrange
        when(interestSettlementMapper.selectByTableIdAndOrg(TEST_TABLE_ID, TEST_ORG_NUMBER)).thenReturn(null);

        // Act
        InterestSettlementDTO result = interestSettlementService.findByOrgAndTableId(TEST_ORG_NUMBER, TEST_TABLE_ID);

        // Assert
        assertThat(result).isNull();
        verify(interestSettlementMapper).selectByTableIdAndOrg(TEST_TABLE_ID, TEST_ORG_NUMBER);
    }


} 