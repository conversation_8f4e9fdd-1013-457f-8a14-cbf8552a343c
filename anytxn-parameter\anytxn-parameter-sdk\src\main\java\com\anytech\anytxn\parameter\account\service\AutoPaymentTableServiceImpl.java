package com.anytech.anytxn.parameter.account.service;

import com.alibaba.fastjson.JSON;
import com.anytech.anytxn.parameter.base.common.domain.model.system.ParmHolidayList;
import com.anytech.anytxn.parameter.base.common.domain.dto.system.HolidayListResDTO;
import com.github.pagehelper.Page;
import com.github.pagehelper.PageHelper;
import com.anytech.anytxn.common.core.annotation.db.DeleteParameterAnnotation;
import com.anytech.anytxn.common.core.annotation.db.InsertParameterAnnotation;
import com.anytech.anytxn.common.core.annotation.db.UpdateParameterAnnotation;
import com.anytech.anytxn.business.base.audit.AbstractParameterService;
import com.anytech.anytxn.common.core.base.ParameterCompare;
import com.anytech.anytxn.business.base.audit.ParmModificationRecord;
import com.anytech.anytxn.common.core.utils.OrgNumberUtils;
import com.anytech.anytxn.common.core.base.PageResultDTO;
import com.anytech.anytxn.common.core.utils.BeanMapping;
import com.anytech.anytxn.common.sequence.utils.Number16IdGen;
import com.anytech.anytxn.common.core.utils.TenantUtils;
import com.anytech.anytxn.parameter.base.account.domain.dto.AutoPaymentTableReqDTO;
import com.anytech.anytxn.parameter.base.account.domain.dto.AutoPaymentTableResDTO;
import com.anytech.anytxn.parameter.base.account.service.IAutoPaymentTableService;
import com.anytech.anytxn.parameter.base.common.enums.AnyTxnParameterRespCodeEnum;
import com.anytech.anytxn.parameter.base.common.enums.ParameterRepDetailEnum;
import com.anytech.anytxn.parameter.base.common.exception.AnyTxnParameterException;
import com.anytech.anytxn.parameter.account.mapper.ParmAutoPaymentTableMapper;
import com.anytech.anytxn.parameter.account.mapper.ParmAutoPaymentTableSelfMapper;
import com.anytech.anytxn.parameter.base.account.domain.model.ParmAutoPaymentTable;
//import org.apache.shardingsphere.transaction.annotation.ShardingSphereTransactionType;
//import org.apache.shardingsphere.transaction.core.TransactionType;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import java.time.LocalDate;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;


/**
 * Description   约定扣款实现类
 * Copyright:	Copyright (c) 2018
 * Company:		江融信
 * Author:		lichao
 * Version:		1.0
 * Create at:	2018/11/7 2:42 PM
 *
 * 修改历史:
 * 日期    作者    版本  修改描述
 * ------------------------------------------------------------------
 *
 **/
@Service(value = "parm_auto_payment_table_serviceImpl")
public class AutoPaymentTableServiceImpl extends AbstractParameterService implements IAutoPaymentTableService {

    private final Logger logger = LoggerFactory.getLogger(AutoPaymentTableServiceImpl.class);

    @Autowired
    private ParmAutoPaymentTableMapper parmAutoPaymentTableMapper;

    @Autowired
    private ParmAutoPaymentTableSelfMapper parmAutoPaymentTableSelfMapper;

    @Autowired
    private Number16IdGen numberIdGenerator;

    /**
     * @Description: 根据参数表的列表ID（tableId）机构号查询参数信息
     * @Param organizationNumber 机构号
     * @Param tableId 列表Id
     * @return: AutoPaymentTableRes
     * @Date: 2018/11/07
     **/
    @Override
    //@PreGetProcess(args = {"#0","#1"})
    public AutoPaymentTableResDTO findByAutoPaymentTableIdAndOrgNo(String autoPaymentTableId, String organizationNumber) {
        if (autoPaymentTableId == null || StringUtils.isEmpty(organizationNumber)) {
            throw new AnyTxnParameterException(AnyTxnParameterRespCodeEnum.P_PARAMETER_EMPTY_FAULT);
        }
        ParmAutoPaymentTable parmAutoPaymentTable = parmAutoPaymentTableSelfMapper.queryByAutoPaymentTableIdAndOrgNo(autoPaymentTableId,organizationNumber );
        if(parmAutoPaymentTable==null){
            throw new AnyTxnParameterException(AnyTxnParameterRespCodeEnum.D_QUERY_PARA_AUTO_PAYMENT_TABLE_FAULT);

        }
        return BeanMapping.copy(parmAutoPaymentTable, AutoPaymentTableResDTO.class);

    }

    /**
     * 添加约定扣款信息
     * @param autoPaymentTableReqDTO 约定扣款信息
     * @return AutoPaymentTableRes 约定扣款信息
     * @throws AnyTxnParameterException 异常
     */
    @Override
    @InsertParameterAnnotation(tableName = "parm_auto_payment_table", tableDesc = "Agreement Deductions")
    public ParameterCompare add(AutoPaymentTableReqDTO autoPaymentTableReqDTO)  {
        ParmAutoPaymentTable parmAutoPaymentTable = parmAutoPaymentTableSelfMapper.queryByAutoPaymentTableIdAndOrgNo(autoPaymentTableReqDTO.getTableId(), OrgNumberUtils.getOrg(autoPaymentTableReqDTO.getOrganizationNumber()));
        if (parmAutoPaymentTable!=null) {
            logger.warn("约定扣款信息已存在, TableId={} Organization ={}",
                    autoPaymentTableReqDTO.getTableId(), OrgNumberUtils.getOrg());
            throw new AnyTxnParameterException(AnyTxnParameterRespCodeEnum.D_EXIST_PARA_AUTO_PAYMENT_TABLE_FAULT);
        }

        //构建约定扣款信息
        ParmAutoPaymentTable autoPayment = BeanMapping.copy(autoPaymentTableReqDTO, ParmAutoPaymentTable.class);
        autoPayment.setId(String.valueOf(numberIdGenerator.generateId(TenantUtils.getTenantId())));


        return ParameterCompare.getBuilder().withAfter(autoPayment).build(ParmAutoPaymentTable.class);
    }

    /**
     * 修改约定扣款数据
     * @param autoPaymentTableReqDTO 约定扣款数据入参对象
     * @return AutoPaymentTableRes 约定扣款数据
     * @throws AnyTxnParameterException 异常
     */
    @Override
    @UpdateParameterAnnotation(tableName = "parm_auto_payment_table", tableDesc = "Agreement Deductions")
    public ParameterCompare modify(AutoPaymentTableReqDTO autoPaymentTableReqDTO) {
        if (autoPaymentTableReqDTO.getId() == null) {
            throw new AnyTxnParameterException(AnyTxnParameterRespCodeEnum.P_PARAMETER_EMPTY_PARA_AUTO_PAYMENT_TABLE_FAULT);
        }
        ParmAutoPaymentTable parmAutoPaymentTable = parmAutoPaymentTableMapper.selectByPrimaryKey(autoPaymentTableReqDTO.getId());

        if (parmAutoPaymentTable == null) {
            logger.error("修改约定扣款数据, 通过主键id({})未找到数据", autoPaymentTableReqDTO.getId());
            throw new AnyTxnParameterException(AnyTxnParameterRespCodeEnum.D_MODIFY_PARA_AUTO_PAYMENT_TABLE_BY_ID_FAULT);
        }
        // 拷贝修改的数据并更新
        ParmAutoPaymentTable autoPaymentTable = BeanMapping.copy(autoPaymentTableReqDTO, ParmAutoPaymentTable.class);
        autoPaymentTable.setVersionNumber(parmAutoPaymentTable.getVersionNumber());

        return ParameterCompare.getBuilder().withAfter(autoPaymentTable).withBefore(parmAutoPaymentTable).build(ParmAutoPaymentTable.class);
    }

    /**
     * 通过id删除条目
     * @param id 技术主键
     * @return true:删除成功|false:删除失败
     * @throws AnyTxnParameterException 异常
     */
    @Override
    @DeleteParameterAnnotation(tableName = "parm_auto_payment_table", tableDesc = "Agreement Deductions")
    public ParameterCompare remove(String id)  {
        if (id == null) {
            throw new AnyTxnParameterException(AnyTxnParameterRespCodeEnum.P_DELETE_EMPTY_PARA_AUTO_PAYMENT_TABLE_FAULT);
        }

        ParmAutoPaymentTable autoPaymentTable = parmAutoPaymentTableMapper.selectByPrimaryKey(id);
        if (autoPaymentTable == null) {
            logger.error("删除约定扣款数据, 通过主键id({})未找到数据", id);
            throw new AnyTxnParameterException(AnyTxnParameterRespCodeEnum.D_DELETE_PARA_AUTO_PAYMENT_TABLE_BY_ID_FAULT);
        }


        return ParameterCompare.getBuilder().withBefore(autoPaymentTable).build(ParmAutoPaymentTable.class);
    }

    /**
     * 通过id获取详情
     * @param id 主键id
     * @return 约定扣款详情
     * @throws AnyTxnParameterException 异常
     */
    @Override
    //@PreGetProcess(byPrimaryKey = true)
    public AutoPaymentTableResDTO find(String id)   {
        if (id == null) {
            throw new AnyTxnParameterException(AnyTxnParameterRespCodeEnum.P_PRIMARY_KEY_NULL_FAULT);
        }

        ParmAutoPaymentTable autoPaymentTable = parmAutoPaymentTableMapper.selectByPrimaryKey(id);

        if (autoPaymentTable == null) {
            logger.error("查询约定扣款信息, 通过主键id({})未找到数据", id);
            throw new AnyTxnParameterException(AnyTxnParameterRespCodeEnum.D_QUERY_PARA_AUTO_PAYMENT_TABLE_FAULT);
        }

        // 查询自扣假日表
        List<ParmHolidayList> ParmHolidayListByHolidayListId = parmAutoPaymentTableSelfMapper.selectByHolidayListId(autoPaymentTable.getHolidayListId(), autoPaymentTable.getOrganizationNumber());
        AutoPaymentTableResDTO autoPaymentTableResDTO = new AutoPaymentTableResDTO();
        BeanMapping.copy(autoPaymentTable, autoPaymentTableResDTO);

        List<LocalDate> holidayDayList = new ArrayList<>();
        autoPaymentTableResDTO.setHolidayDayList(holidayDayList);
        for (ParmHolidayList hl : ParmHolidayListByHolidayListId) {
            autoPaymentTableResDTO.getHolidayDayList().add(hl.getHolidayDay());
        }

        return autoPaymentTableResDTO;
    }

    /**
     * 查询约定扣款参数
     * @param pageSize 每页展示条数
     * @param pageNum 页数
     * @throws AnyTxnParameterException 异常
     * @return 约定扣款响应参数
     *
     */
    @Override
    public PageResultDTO<AutoPaymentTableResDTO> findAll(Integer pageNum, Integer pageSize,String tableId,String description, String organizationNumber)  {

        Map<String,Object> map = new HashMap<>(8);
        map.put("tableId",tableId);
        map.put("description",description);

        map.put("organizationNumber", StringUtils.isEmpty(organizationNumber) ? OrgNumberUtils.getOrg() : organizationNumber);
        Page<ParmAutoPaymentTable> page = PageHelper.startPage(pageNum, pageSize);
        List<ParmAutoPaymentTable> allocatedList = parmAutoPaymentTableSelfMapper.selectAll(map);
        List<AutoPaymentTableResDTO> res = BeanMapping.copyList(allocatedList, AutoPaymentTableResDTO.class);
        return new PageResultDTO<>(pageNum,pageSize,page.getTotal(),page.getPages(),res);
    }

    /**
     * 通过表状态查询约定扣款参数
     * @param status 表状态
     * @return 约定扣款响应参数
     * @throws AnyTxnParameterException 异常
     *
     */
    @Override
    public List<AutoPaymentTableResDTO> findByStatus(String status) {
        List<AutoPaymentTableResDTO> paymentAllocatedResList = null;
        if (!StringUtils.isEmpty(status)) {
            List<ParmAutoPaymentTable> autoPaymentList = parmAutoPaymentTableSelfMapper.selectByStatus(status, OrgNumberUtils.getOrg());
            paymentAllocatedResList = BeanMapping.copyList(autoPaymentList, AutoPaymentTableResDTO.class);
        }

        return paymentAllocatedResList;
    }

    @Override
    @Transactional(rollbackFor = Exception.class,propagation = Propagation.REQUIRED)
    //@ShardingSphereTransactionType(TransactionType.XA)
    protected boolean updateDb(ParmModificationRecord parmModificationRecord) {
        ParmAutoPaymentTable parmAutoPaymentTable = JSON.parseObject(parmModificationRecord.getParmBody(), ParmAutoPaymentTable.class);
        parmAutoPaymentTable.initUpdateDateTime();
        parmAutoPaymentTable.setUpdateBy(parmModificationRecord.getApplicationBy());
        int i = parmAutoPaymentTableMapper.updateByPrimaryKeySelective(parmAutoPaymentTable);

        // 修改日期  先删后加
        parmAutoPaymentTableMapper.deleteHolidayListByHolidayListId(parmAutoPaymentTable.getHolidayListId(),parmAutoPaymentTable.getOrganizationNumber());

        List<LocalDate> holidayList =  parmAutoPaymentTable.getHolidayDayList();
        if(CollectionUtils.isEmpty(holidayList)){
            logger.info("插入错误。listId为空！");
            throw new AnyTxnParameterException(AnyTxnParameterRespCodeEnum.P_PARAMETER_EMPTY_PARA_AUTO_PAYMENT_TABLE_FAULT);
        }
        for (LocalDate date :holidayList) {
            String id = String.valueOf(numberIdGenerator.generateId(TenantUtils.getTenantId()));
            parmAutoPaymentTableSelfMapper.addHolidays(id,date,parmAutoPaymentTable.getHolidayListId(),parmAutoPaymentTable);
            i++;
        }

        return i > 0;
    }

    @Override
    @Transactional(rollbackFor = Exception.class,propagation = Propagation.REQUIRED)
    //@ShardingSphereTransactionType(TransactionType.XA)
    protected boolean insertDb(ParmModificationRecord parmModificationRecord) {
        ParmAutoPaymentTable parmAutoPaymentTable = JSON.parseObject(parmModificationRecord.getParmBody(), ParmAutoPaymentTable.class);
        parmAutoPaymentTable.initCreateDateTime();
        parmAutoPaymentTable.initUpdateDateTime();
        parmAutoPaymentTable.setHolidayListId(numberIdGenerator.generateId(TenantUtils.getTenantId())+"");
        int i = parmAutoPaymentTableMapper.insertSelective(parmAutoPaymentTable);

        //将假日 信息存储到假日表
        String holidaysId = parmAutoPaymentTable.getHolidayListId();
        if(StringUtils.isEmpty(holidaysId)){
            logger.info("holidayId为空");
            throw new AnyTxnParameterException(AnyTxnParameterRespCodeEnum.P_PARAMETER_EMPTY_FAULT);
        }
        String checkHolidayId = parmAutoPaymentTableSelfMapper.getHoliaysId(holidaysId,parmAutoPaymentTable.getOrganizationNumber());
        if(!StringUtils.isEmpty(checkHolidayId)){
            logger.info("holidayId已经存在！");
            throw new AnyTxnParameterException(AnyTxnParameterRespCodeEnum.D_EXIST_PARA_AUTO_PAYMENT_TABLE_FAULT);
        }


        List<LocalDate> holidayList =  parmAutoPaymentTable.getHolidayDayList();
        if(CollectionUtils.isEmpty(holidayList)){
            logger.info("插入错误。listId为空！");
            throw new AnyTxnParameterException(AnyTxnParameterRespCodeEnum.P_PARAMETER_EMPTY_FAULT);
        }
        for (LocalDate date :holidayList) {
            String id = String.valueOf(numberIdGenerator.generateId(TenantUtils.getTenantId()));
            parmAutoPaymentTableSelfMapper.addHolidays(id,date,holidaysId, parmAutoPaymentTable);
            i++ ;
        }

        return i > 0;
    }

    @Override
    @Transactional(rollbackFor = Exception.class,propagation = Propagation.REQUIRED)
    //@ShardingSphereTransactionType(TransactionType.XA)
    protected boolean deleteDb(ParmModificationRecord parmModificationRecord) {
        ParmAutoPaymentTable parmAutoPaymentTable = JSON.parseObject(parmModificationRecord.getParmBody(), ParmAutoPaymentTable.class);
        int i = parmAutoPaymentTableMapper.deleteByPrimaryKey(parmAutoPaymentTable.getId());
        int y = parmAutoPaymentTableMapper.deleteHolidayListByHolidayListId(parmAutoPaymentTable.getHolidayListId(),parmAutoPaymentTable.getOrganizationNumber());
        return (i+y) > 1;
    }

    /**
     * @Description: 根据参数表的假日列表ID（holiday_list_id）读取假日列表表数据
     * @Param holidayListId 假日列表id
     * @return: HolidayListRes
     **/
    @Override
    public HolidayListResDTO findByHolidayListId(String holidayListId, String organizationNumber) {
        if (StringUtils.isEmpty(holidayListId)) {
            throw new AnyTxnParameterException(AnyTxnParameterRespCodeEnum.P_PARAMETER_EMPTY_FAULT);
        }

        organizationNumber = StringUtils.isEmpty(organizationNumber) ? OrgNumberUtils.getOrg() : organizationNumber;
        List<ParmHolidayList> ParmHolidayListByHolidayListId = parmAutoPaymentTableSelfMapper.selectByHolidayListId(holidayListId, organizationNumber);
        if (CollectionUtils.isEmpty(ParmHolidayListByHolidayListId)) {
            throw new AnyTxnParameterException(AnyTxnParameterRespCodeEnum.D_DATA_NOT_EXISTS_FAULT, ParameterRepDetailEnum.HOLIDAY_NOT_EXIST);
        }
        HolidayListResDTO res = new HolidayListResDTO();
        List<LocalDate> holidayDayList = new ArrayList<>();
        res.setHolidayDayList(holidayDayList);
        for (ParmHolidayList hl : ParmHolidayListByHolidayListId) {
            res.getHolidayDayList().add(hl.getHolidayDay());
        }

        res.setCreateTime(ParmHolidayListByHolidayListId.get(0).getCreateTime());
        res.setDescription(ParmHolidayListByHolidayListId.get(0).getDescription());
        res.setHolidayListId(holidayListId);
        res.setStatus(ParmHolidayListByHolidayListId.get(0).getStatus());
        res.setUpdateTime(ParmHolidayListByHolidayListId.get(0).getUpdateTime());
        res.setUpdateBy(ParmHolidayListByHolidayListId.get(0).getUpdateBy());
        res.setVersionNumber(ParmHolidayListByHolidayListId.get(0).getVersionNumber());
        res.setOrganizationNumber(ParmHolidayListByHolidayListId.get(0).getOrganizationNumber());
        return res;
    }


    @Override
    public String findAutoPaymentDateByTableId(String tableId,String orgNo) {
        return parmAutoPaymentTableSelfMapper.findAutoPaymentDateByTableId(tableId,orgNo) ;
    }

    @Override
    public LocalDate getDateByHolidayListIdAndToday(String holidayListId, Integer month,String orgNo) {
        return parmAutoPaymentTableSelfMapper.getDateByHolidayListIdAndToday(holidayListId, month,orgNo);
    }

}
