package com.anytech.anytxn.parameter.account.controller;

import io.swagger.v3.oas.annotations.tags.Tag;
import io.swagger.v3.oas.annotations.Operation;
import com.anytech.anytxn.common.core.base.ParameterCompare;
import com.anytech.anytxn.common.core.base.PageResultDTO;
import com.anytech.anytxn.common.core.base.AnyTxnHttpResponse;
import com.anytech.anytxn.common.core.base.BizBaseController;
import com.anytech.anytxn.parameter.base.account.domain.dto.AcctPmtBasicInfoReqDTO;
import com.anytech.anytxn.parameter.base.account.domain.dto.AcctPmtBasicInfoResDTO;
import com.anytech.anytxn.parameter.base.account.service.IParmAcctPmtAllocBasicDefService;
import com.anytech.anytxn.parameter.base.common.enums.ParameterRepDetailEnum;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import jakarta.validation.Valid;

/**
 * <pre>
 * Description:  账户间还款分配参数基本信息+控制
 * Company:		江融信
 * Author:		lichao
 * Version:		1.0
 * Create at:	2020/8/9 6:15 下午
 * <p>
 * 修改历史:
 * 日期    作者    版本  修改描述
 * ------------------------------------------------------------------
 *
 * </pre>
 */
@Tag(name = "账户间还款分配基本参数控制管理接口")
@RestController
public class AccountPmtBasicDefController extends BizBaseController {

    @Autowired
    private IParmAcctPmtAllocBasicDefService parmAcctPmtAllocBasicDefService;

    /**
     * 创建账户间还款参数定义信息
     *
     * @param acctPmtBasicInfoReqDto 对应参数定义请求信息+控制信息
     * @return 对应还款参数定义控制详情信息
     */
    @Operation(summary = "创建对应账户间还款定义+控制信息")
    @PostMapping(value = "/param/addAcctPmtBasicDefInfo")
    public AnyTxnHttpResponse<Object> create(
            @Valid @RequestBody AcctPmtBasicInfoReqDTO acctPmtBasicInfoReqDto) {
        ParameterCompare res = parmAcctPmtAllocBasicDefService.add(acctPmtBasicInfoReqDto);
        return AnyTxnHttpResponse.success(res, ParameterRepDetailEnum.CREATE.message());
    }

    /**
     * 更新对应账户间还款信息+控制信息
     *
     * @param acctPmtBasicInfoReqDto 对应账户间还款定义+控制请求数据
     * @return 对应账户间还款定义+控制详情
     */
    @Operation(summary = "更新对应账户间还款定义+控制信息")
    @PutMapping(value = "/param/updAcctPmtBasicDefInfo")
    public AnyTxnHttpResponse<Object> modify(
            @Valid @RequestBody AcctPmtBasicInfoReqDTO acctPmtBasicInfoReqDto) {
        ParameterCompare acctPmtBasicInfoResDto =
                parmAcctPmtAllocBasicDefService.modify(acctPmtBasicInfoReqDto);
        return AnyTxnHttpResponse.success(acctPmtBasicInfoResDto, ParameterRepDetailEnum.UPDATE.message());
    }

    /**
     * 分页查询，账户间还款定义+控制
     *
     * @param pageNum  页码
     * @param pageSize 每页大小
     * @return page
     */
    @Operation(summary = "分页查询，账户间还款定义+控制信息")
    @GetMapping(value = "/param/QueryAcctPmtBasicDefInfo/pageNum/{pageNum}/pageSize/{pageSize}")
    public AnyTxnHttpResponse<PageResultDTO<AcctPmtBasicInfoResDTO>> getPage(
            @PathVariable(value = "pageNum") Integer pageNum,
            @PathVariable(value = "pageSize") Integer pageSize,
            AcctPmtBasicInfoReqDTO acctPmtBasicInfoReqDto) {
        PageResultDTO<AcctPmtBasicInfoResDTO> page =
                parmAcctPmtAllocBasicDefService.findPage(pageNum, pageSize, acctPmtBasicInfoReqDto);
        return AnyTxnHttpResponse.success(page);
    }

    /**
     * 通过id获取账户间还款定义+控制表详情
     *
     * @param id 根据id获取对账户间还款定义+控制表信息
     * @return 账户间还款定义+控制表详情
     */
    @Operation(summary = "通过id获取账户间还款定义+控制详情")
    @GetMapping(value = "/param/queryAcctPmtBasicInfoById/id/{id}")
    public AnyTxnHttpResponse<AcctPmtBasicInfoResDTO> queryAcctPmtBasicInfoById(
            @PathVariable String id) {
        AcctPmtBasicInfoResDTO acctPmtBasicInfoResDto = parmAcctPmtAllocBasicDefService.findById(id);
        return AnyTxnHttpResponse.success(acctPmtBasicInfoResDto);
    }

    /**
     * 删除账户间还款定义+控制表详情，通过id
     *
     * @param id 账户间还款分配id
     * @return Boolean
     */
    @Operation(summary = "根据id删除账户间还款分配定义表信息，")
    @DeleteMapping(value = "/param/delAcctPmtBasicById/id/{id}")
    public AnyTxnHttpResponse<Object> remove(@PathVariable String id) {
        ParameterCompare deleted = parmAcctPmtAllocBasicDefService.remove(id);
        return AnyTxnHttpResponse.success(deleted, ParameterRepDetailEnum.DEL.message());
    }
}
