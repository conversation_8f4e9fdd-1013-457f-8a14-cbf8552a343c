package com.anytech.anytxn.parameter.common.controller.cache;

import com.anytech.anytxn.common.core.cache.api.RefreshHandle;
import com.anytech.anytxn.common.core.base.AnyTxnHttpResponse;
import com.anytech.anytxn.common.core.base.BizBaseController;
import com.anytech.anytxn.parameter.common.cache.ParamRefreshHandle;
import com.anytech.anytxn.parameter.base.common.enums.ParameterRepDetailEnum;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * @description: 缓存清空，查询接口
 * @author: zhangnan
 * @create: 2020-06-24
 **/
@RestController
public class ParamCacheController extends BizBaseController {

    @Qualifier("paramRefreshHandle")
    private RefreshHandle paramRefreshHandle;
    @GetMapping(value="/param/cache/clear")
    public AnyTxnHttpResponse clear(){
        ((ParamRefreshHandle)paramRefreshHandle).clearAllCache();
        return AnyTxnHttpResponse.success(ParameterRepDetailEnum.CLEAR.message());
    }

}
