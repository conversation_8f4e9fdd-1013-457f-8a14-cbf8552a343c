package com.anytech.anytxn.parameter.accounting.controller;

import io.swagger.v3.oas.annotations.tags.Tag;
import io.swagger.v3.oas.annotations.Operation;
import com.anytech.anytxn.common.core.base.PageResultDTO;
import com.anytech.anytxn.common.core.utils.OrgNumberUtils;
import com.anytech.anytxn.common.core.base.BizBaseController;
import com.anytech.anytxn.common.core.base.AnyTxnHttpResponse;
import com.anytech.anytxn.parameter.base.accounting.domain.dto.TPmsGlacgnDTO;
import com.anytech.anytxn.parameter.base.accounting.service.ITPmsGlacgnService;
import com.anytech.anytxn.parameter.base.common.enums.ParameterRepDetailEnum;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;

@Tag(name = "会计科目参数表")
@RestController
public class TPmsGlacgnController extends BizBaseController {
    private static final Logger logger = LoggerFactory.getLogger(TPmsGlacgnController.class);

    @Autowired
    private ITPmsGlacgnService itPmsGlacgnService;

    @Operation(summary = "分页查询会计科目参数表")
    @GetMapping(value = {"/param/cgn/{page}/{rows}"})
    public AnyTxnHttpResponse<PageResultDTO<TPmsGlacgnDTO>> page(@PathVariable(value = "page") Integer page,
                                                                 @PathVariable(value = "rows") Integer rows,
                                                                 @RequestParam(value = "organizationNumber",required = false) String organizationNumber,
                                                                 @RequestParam(value = "glAcct",required = false) String glAcct,
                                                                 @RequestParam(value = "glAcctName",required = false) String glAcctName,
                                                                 @RequestParam(value = "glClass",required = false) String glClass,
                                                                 @RequestParam(value = "currCode",required = false) String currCode) {
        PageResultDTO<TPmsGlacgnDTO> result = itPmsGlacgnService.page(page, rows,organizationNumber,glAcct,glAcctName,glClass,currCode);
        return AnyTxnHttpResponse.success(result);

    }
    @Operation(summary = "查询全部会计科目")
    @GetMapping(value = {"/param/cgn"})
    public AnyTxnHttpResponse<List<TPmsGlacgnDTO>> all(@RequestParam(required = false) String organizationNumber) {
        organizationNumber = OrgNumberUtils.getOrg(organizationNumber);
        List<TPmsGlacgnDTO> result = itPmsGlacgnService.findAll(organizationNumber);
        return AnyTxnHttpResponse.success(result);
    }

    @Operation(summary = "查询会计科目参数表明细")
    @GetMapping("/param/cgn/id/{id}")
    public AnyTxnHttpResponse<TPmsGlacgnDTO> detail(@PathVariable(value = "id") String id) {
        TPmsGlacgnDTO result = itPmsGlacgnService.detail(id);
        return AnyTxnHttpResponse.success(result);
    }

    @Operation(summary = "删除会会计科目参数")
    @DeleteMapping("/param/cgn/id/{id}")
    public AnyTxnHttpResponse<Object> remove(@PathVariable(value = "id") String id) {
        return AnyTxnHttpResponse.success(itPmsGlacgnService.remove(id), ParameterRepDetailEnum.DEL.message());
    }

    @Operation(summary = "新增会计科目参数表")
    @PostMapping("/param/cgn/add")
    public AnyTxnHttpResponse<Object> add(@RequestBody TPmsGlacgnDTO data) {
        return AnyTxnHttpResponse.success(itPmsGlacgnService.add(data),ParameterRepDetailEnum.CREATE.message());
    }

    @Operation(summary = "更新会计科目参数表")
    @PostMapping("/param/cgn/update")
    public AnyTxnHttpResponse<Object> update(@RequestBody TPmsGlacgnDTO data) {
        return AnyTxnHttpResponse.success(itPmsGlacgnService.update(data),ParameterRepDetailEnum.UPDATE.message());
    }
}
