package com.anytech.anytxn.parameter.common.service.deprecated;

import com.anytech.anytxn.parameter.base.common.constants.Constants;
import com.anytech.anytxn.common.core.utils.BeanMapping;
import com.anytech.anytxn.common.sequence.utils.Number16IdGen;
import com.anytech.anytxn.common.core.utils.TenantUtils;
import com.anytech.anytxn.parameter.base.common.domain.dto.system.SysCodeReqDTO;
import com.anytech.anytxn.parameter.base.common.domain.dto.system.SysCodeResDTO;
import com.anytech.anytxn.parameter.base.common.domain.dto.system.SysCodeSimpleResDTO;
import com.anytech.anytxn.parameter.base.common.service.system.ISysCodeService;
import com.anytech.anytxn.parameter.base.common.enums.AnyTxnParameterRespCodeEnum;
import com.anytech.anytxn.parameter.base.common.exception.AnyTxnParameterException;
import com.anytech.anytxn.parameter.common.mapper.deprecated.ParmSysCodeMapper;
import com.anytech.anytxn.parameter.common.mapper.deprecated.ParmSysCodeSelfMapper;
import com.anytech.anytxn.parameter.base.common.domain.model.system.ParmSysCode;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.util.HashMap;
import java.util.List;

/**
 * 系统字典 业务接口实现
 * <AUTHOR>
 * @date 2018-08-15
 **/
@Deprecated
@Service
public class SysCodeServiceImpl implements ISysCodeService {
    private Logger logger = LoggerFactory.getLogger(SysCodeServiceImpl.class);

    @Autowired
    private ParmSysCodeSelfMapper parmSysCodeSelfMapper;
    @Autowired
    private ParmSysCodeMapper parmSysCodeMapper;
    @Autowired
    private Number16IdGen numberIdGenerator;


    /**
     * 通过分类ID获取字典列表，按分类ID分组
     *
     * @param typeIds 字典分类id
     * @return
     */
    @Override
    public HashMap<String, List<SysCodeSimpleResDTO>> findMapByTypeIds(List<String> typeIds) {
        logger.debug("typeIds={}", typeIds);

        HashMap<String, List<SysCodeSimpleResDTO>> codeMap = new HashMap<>(16);
        for (String typeId : typeIds) {
            List<ParmSysCode> sysCodeList = parmSysCodeSelfMapper.selectListByTypeIdAndStatus(typeId, Constants.ENABLED);
            codeMap.put(typeId, BeanMapping.copyList(sysCodeList, SysCodeSimpleResDTO.class));
        }

        return codeMap;
    }

    /**
     * 根据父id获取字典表数据
     * @param pid
     * @return
     */
    @Override
    public List<SysCodeResDTO> getByPid(Long pid) {
        List<ParmSysCode> sysCodes = parmSysCodeSelfMapper.selectListByPid(pid);
        return BeanMapping.copyList(sysCodes,SysCodeResDTO.class);
    }

    /**
     *  通过分类ID获取字典列表
     * @param typeId 字典分类id
     * @return
     */
    @Override
    public List<SysCodeResDTO> getByTypeId(String typeId) {
        List<ParmSysCode> parmSysCodes = parmSysCodeSelfMapper.selectListByTypeIdAndStatus(typeId, Constants.ENABLED);
        return BeanMapping.copyList(parmSysCodes,SysCodeResDTO.class);
    }

    /**
     *  通过id获取字典表数据
     * @param id 字典表id
     * @return
     */
    @Override
    public SysCodeResDTO getByPrimaryId(Long id) {
        if (id == null) {
            throw new AnyTxnParameterException(AnyTxnParameterRespCodeEnum.P_PARAMETER_ID_EMPTY_FAULT);
        }
        ParmSysCode paraSysCode = parmSysCodeMapper.selectByPrimaryKey(id);
        return BeanMapping.copy(paraSysCode, SysCodeResDTO.class);
    }

    /**
     *  更新字典表数据
     * @param sysCodeReqDTO 字典表数据
     * @return
     */
    @Override
    public SysCodeResDTO modifySysCode(SysCodeReqDTO sysCodeReqDTO) {
        if (sysCodeReqDTO.getId() == null) {
            throw new AnyTxnParameterException(AnyTxnParameterRespCodeEnum.P_PARAMETER_ID_EMPTY_FAULT);
        }

        ParmSysCode paraSysCode = parmSysCodeMapper.selectByPrimaryKey(sysCodeReqDTO.getId());
        if (paraSysCode == null) {
            logger.error("更改系统字典表信息，通过id:{}未查到数据", sysCodeReqDTO.getId());
            throw new AnyTxnParameterException(AnyTxnParameterRespCodeEnum.D_QUERY_PARM_SYS_CODE_NULL_FAULT);
        }
        BeanMapping.copy(sysCodeReqDTO, paraSysCode);


        parmSysCodeMapper.updateByPrimaryKeySelective(paraSysCode);

        return BeanMapping.copy(paraSysCode, SysCodeResDTO.class);

    }

    @Override
    public SysCodeResDTO addSysCode(SysCodeReqDTO sysCodeReqDTO) {
        if (sysCodeReqDTO == null) {
            throw new AnyTxnParameterException(AnyTxnParameterRespCodeEnum.P_PARAMETER_EMPTY_FAULT);
        }
        ParmSysCode sysCode = BeanMapping.copy(sysCodeReqDTO, ParmSysCode.class);
        sysCode.setId(numberIdGenerator.generateId(TenantUtils.getTenantId()));
        sysCode.setCreateTime(LocalDateTime.now());
        parmSysCodeMapper.insert(sysCode);

        return BeanMapping.copy(sysCode, SysCodeResDTO.class);
    }

    @Override
    public int deleteSysCode(Long id) {
        if (id == null) {
            throw new AnyTxnParameterException(AnyTxnParameterRespCodeEnum.P_PARAMETER_ID_EMPTY_FAULT);
        }
        return parmSysCodeMapper.deleteByPrimaryKey(id);
    }

}
