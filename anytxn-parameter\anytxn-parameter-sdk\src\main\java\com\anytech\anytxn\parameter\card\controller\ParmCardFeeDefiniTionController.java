package com.anytech.anytxn.parameter.card.controller;

import io.swagger.v3.oas.annotations.tags.Tag;
import io.swagger.v3.oas.annotations.Operation;
import com.anytech.anytxn.common.core.base.ParameterCompare;
import com.anytech.anytxn.common.core.base.PageResultDTO;
import com.anytech.anytxn.common.core.utils.OrgNumberUtils;
import com.anytech.anytxn.common.core.base.AnyTxnHttpResponse;
import com.anytech.anytxn.common.core.base.BizBaseController;
import com.anytech.anytxn.parameter.base.card.domain.dto.CardFeeDefinitionReqDTO;
import com.anytech.anytxn.parameter.base.card.domain.dto.CardFeeDefinitionResDTO;
import com.anytech.anytxn.parameter.base.card.service.IParmCardFeeDefiniTionService;
import com.anytech.anytxn.parameter.base.common.enums.ParameterRepDetailEnum;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import jakarta.validation.Valid;
import java.util.List;

/**
 * <pre>
 * Description  卡片费用参数定义管理
 * Dept:		江融信
 * Author:		lichao
 * Version:		1.0
 * Create at:	2020-08-18 11:20
 *
 * 修改历史:
 * 日期    作者    版本  修改描述
 * ------------------------------------------------------------------
 **/
@Tag(name = "卡片费用参数管理接口")
@RestController
public class ParmCardFeeDefiniTionController extends BizBaseController {

    @Autowired
    private IParmCardFeeDefiniTionService parmCardFeeDefiniTionService;

    /**
     * 分页查询卡片费用参数
     * @param pageSize 页码
     * @param pageNum  每页数目
     * @return 卡片费用响应参数
     *
     */
    @Operation(summary = "分页查询，卡片费用参数信息")
    @GetMapping(value = "/param/queryCardFeeDefiniTion/pageNum/{pageNum}/pageSize/{pageSize}")
    public AnyTxnHttpResponse<PageResultDTO<CardFeeDefinitionResDTO>> getPage(@PathVariable(value = "pageNum") Integer pageNum,
                                                                              @PathVariable(value = "pageSize") Integer pageSize,
                                                                              @RequestParam(value = "feeCode",required = false) String feeCode,
                                                                              @RequestParam(value = "description",required = false) String description,
                                                                              @RequestParam(value = "feeTypeCode",required = false) String feeTypeCode,
                                                                              @RequestParam(required = false) String organizationNumber){
        PageResultDTO<CardFeeDefinitionResDTO> res = parmCardFeeDefiniTionService.findByPage(pageNum,pageSize,feeCode,description,feeTypeCode,organizationNumber);
        return AnyTxnHttpResponse.success(res);
    }

    /**
     *根据ID主键查询卡片费用参数
     * @param id 技术主键
     * @return 卡片费用响应参数
     */
    @Operation(summary = "通过id获取卡片费用参数详情")
    @GetMapping(value = "/param/queryCardFeeDefiniTionById/id/{id}")
    public AnyTxnHttpResponse<CardFeeDefinitionResDTO> findCardFeeDefinitionById(@PathVariable String id){
        CardFeeDefinitionResDTO res = parmCardFeeDefiniTionService.findById(id);
        return AnyTxnHttpResponse.success(res,ParameterRepDetailEnum.QUERY.message());
    }

    /**
     * 添加卡片费用参数
     * @param cardFeeDefinitionReqDTO 卡片费用请求参数
     * @return 卡片费用响应参数
     **/
    @Operation(summary = "新增卡片费用参数信息")
    @PostMapping(value = "/param/addCardFeeDefiniTion")
    public AnyTxnHttpResponse<Object> addCardFeeDefinition(@Valid @RequestBody CardFeeDefinitionReqDTO cardFeeDefinitionReqDTO){
        ParameterCompare res = parmCardFeeDefiniTionService.add(cardFeeDefinitionReqDTO);
        return AnyTxnHttpResponse.success(res, ParameterRepDetailEnum.CREATE.message());
    }

    /**
     * 修改卡片费用参数
     * @param cardFeeDefinitionReqDTO 卡片费用请求参数
     * @return 卡片费用响应参数
     **/
    @Operation(summary = "修改卡片费用参数信息")
    @PutMapping(value = "/param/updateCardFeeDefiniTion")
    public AnyTxnHttpResponse<Object> modifyCardFeeDefinition(@Valid @RequestBody CardFeeDefinitionReqDTO cardFeeDefinitionReqDTO){
        ParameterCompare res = parmCardFeeDefiniTionService.modify(cardFeeDefinitionReqDTO);
        return AnyTxnHttpResponse.success(res,ParameterRepDetailEnum.UPDATE.message());
    }

    /**
     * 根据ID删除卡片费用参数
     * @param id 技术主键
     * @return Boolearn
     **/
    @Operation(summary = "根据id删除卡片费用参数表信息")
    @DeleteMapping(value = "/param/delCardFeeDefiniTionById/id/{id}")
    public AnyTxnHttpResponse<Object> removeCardFeeDefiniTion(@PathVariable String id) {
        ParameterCompare res = parmCardFeeDefiniTionService.remove(id);
        return AnyTxnHttpResponse.success(res, ParameterRepDetailEnum.DEL.message());
    }

    /**
     * 通过机构号、费用子类,查询卡片费用参数详情
     * @param orgNumber 机构号
     * @param subFeeTypeCode 费用子类
     * @return AnyTxnHttpResponse< List<CardFeeDefinitionResDTO>>
     */
    @Operation(summary = "通过机构号、费用子类,查询卡片费用参数详情")
    @GetMapping(value = "/param/cardFeeDef/orgNumber/{orgNumber}/subFeeTypeCode/{subFeeTypeCode}")
    public AnyTxnHttpResponse< List<CardFeeDefinitionResDTO>> findCardFeeDefinitionById(@PathVariable String orgNumber,
                                                                                 @PathVariable String subFeeTypeCode){
        List<CardFeeDefinitionResDTO> cardFeeDefiList = parmCardFeeDefiniTionService.findByOrgAndSubFeeType(orgNumber, subFeeTypeCode);
        return AnyTxnHttpResponse.success(cardFeeDefiList,ParameterRepDetailEnum.QUERY.message());
    }

    /**
     * 通过机构号、费用类型,费用子类， 查询卡片费用参数详情
     * @param orgNumber 机构号
     * @param feeTypeCode 费用类型
     * @param subFeeTypeCode 费用子类
     * @return AnyTxnHttpResponse< List<CardFeeDefinitionResDTO>>
     */
    @Operation(summary = "通过机构号、费用类型、费用子类,查询卡片费用参数详情")
    @GetMapping(value = "/param/cardFeeDef/orgNumber/{orgNumber}/feeTypeCode/{feeTypeCode}/subFeeTypeCode/{subFeeTypeCode}")
    public AnyTxnHttpResponse< List<CardFeeDefinitionResDTO>> findCardFeeDefinitionById(@PathVariable String orgNumber,
                                                                                        @PathVariable String feeTypeCode,
                                                                                        @PathVariable String subFeeTypeCode){
        List<CardFeeDefinitionResDTO> cardFeeDefiList = parmCardFeeDefiniTionService.findByOrgFeeTypeAndSubFeeType(orgNumber, feeTypeCode,subFeeTypeCode);
        return AnyTxnHttpResponse.success(cardFeeDefiList,ParameterRepDetailEnum.QUERY.message());
    }

    /**
     * 通过机构号、费用类型,费用子类、费用代码，查询卡片费用参数详情
     * @param orgNumber 机构号
     * @param feeTypeCode 费用类型
     * @param subFeeTypeCode 费用子类
     * @param feeCode 费用代码
     * @return AnyTxnHttpResponse<CardFeeDefinitionResDTO>
     */
    @Operation(summary = "通过机构号、费用类型、费用子类、费用代码,查询卡片费用参数详情")
    @GetMapping(value = "/param/cardFeeDef/orgNumber/{orgNumber}/feeTypeCode/{feeTypeCode}/subFeeTypeCode/{subFeeTypeCode}/feeCode/{feeCode}")
    public AnyTxnHttpResponse<CardFeeDefinitionResDTO> findCardFeeDefinitionById(@PathVariable String orgNumber,
                                                                                        @PathVariable String feeTypeCode,
                                                                                        @PathVariable String subFeeTypeCode,
                                                                                        @PathVariable String feeCode){
        CardFeeDefinitionResDTO cardFeeDefi = parmCardFeeDefiniTionService.findByIndex(orgNumber, feeTypeCode,subFeeTypeCode,feeCode);
        return AnyTxnHttpResponse.success(cardFeeDefi,ParameterRepDetailEnum.QUERY.message());
    }

    /**
     * 根据机构号、费用代码查询卡片费用定义数据
     * @param orgNumber 机构号
     * @param feeCode 费用代码
     * @return CardFeeDefinitionResDTO
     */
    @Operation(summary = "通过机构号、费用代码查询卡片费用参数详情")
    @GetMapping(value = "/param/cardFeeDef/orgNumber/{orgNumber}/feeCode/{feeCode}")
    public AnyTxnHttpResponse<CardFeeDefinitionResDTO> findByOrgNumAndFeeCode(@PathVariable String orgNumber,
                                                                                 @PathVariable String feeCode){
        CardFeeDefinitionResDTO cardFeeDefi = parmCardFeeDefiniTionService.selectByOrgNumAndFeeCode(orgNumber, feeCode);
        return AnyTxnHttpResponse.success(cardFeeDefi,ParameterRepDetailEnum.QUERY.message());
    }


    /**
     * 通过机构号、卡产品， 查询卡片费用参数详情
     * @param orgNumber 机构号
     * @param productNumber 卡产品
     * @return AnyTxnHttpResponse< List<CardFeeDefinitionResDTO>>
     */
    @Operation(summary = "通过机构号、卡产品， 查询卡片费用参数详情")
    @GetMapping(value = "/param/cardFeeDef/orgNumber/{orgNumber}/productNumber/{productNumber}")
    public AnyTxnHttpResponse< List<CardFeeDefinitionResDTO>> findCardProductFeeDefine(@PathVariable String orgNumber,
                                                                                        @PathVariable String productNumber,
                                                                                        @RequestParam(required = false) String feeTypeCode
                                                                                        ){
        List<CardFeeDefinitionResDTO> cardFeeDefiList = parmCardFeeDefiniTionService.selectByOrgAndProductNumber(orgNumber, productNumber,feeTypeCode);
        return AnyTxnHttpResponse.success(cardFeeDefiList,ParameterRepDetailEnum.QUERY.message());
    }

    /**
     * 查询卡片费用参数所有数据
     * @return AnyTxnHttpResponse< List<CardFeeDefinitionResDTO>>
     */
    @Operation(summary = "查询卡片费用参数所有数据")
    @GetMapping(value = "/param/cardFeeDef/all")
    public AnyTxnHttpResponse< List<CardFeeDefinitionResDTO>> findAllCardProductFeeDefine(@RequestParam(required = false) String organizationNumber){
        List<CardFeeDefinitionResDTO> cardFeeDefiList = parmCardFeeDefiniTionService.selectAll(OrgNumberUtils.getOrg(organizationNumber));
        return AnyTxnHttpResponse.success(cardFeeDefiList,ParameterRepDetailEnum.QUERY.message());
    }
}
