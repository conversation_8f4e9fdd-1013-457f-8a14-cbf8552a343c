package com.anytech.anytxn.business.common.service;

import com.anytech.anytxn.common.core.utils.BeanMapping;
import com.anytech.anytxn.business.base.account.domain.dto.AccountManagementInfoDTO;
import com.anytech.anytxn.business.dao.account.mapper.AccountManagementInfoMapper;
import com.anytech.anytxn.business.dao.account.model.AccountManagementInfo;
import com.anytech.anytxn.business.base.card.domain.dto.CardAuthorizationDTO;
import com.anytech.anytxn.business.base.card.domain.dto.CardBasicDTO;
import com.anytech.anytxn.business.dao.card.mapper.CardAuthorizationInfoMapper;
import com.anytech.anytxn.business.dao.card.mapper.CardBasicInfoMapper;
import com.anytech.anytxn.business.dao.card.model.CardAuthorizationInfo;
import com.anytech.anytxn.business.dao.card.model.CardBasicInfo;
import com.anytech.anytxn.business.base.monetary.domain.bo.CustAccountBO;
import com.anytech.anytxn.business.base.customer.domain.dto.CustomerAuthorizationInfoDTO;
import com.anytech.anytxn.business.dao.customer.mapper.CustomerAuthorizationInfoSelfMapper;
import com.anytech.anytxn.business.dao.customer.model.CustomerAuthorizationInfo;
import com.anytech.anytxn.common.core.utils.OrgNumberUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * 通用数据表加载类（批次/联机共用时使用）
 * <AUTHOR>
 * @date 2020/7/21
 */
@Slf4j
@Service
public class SharedInfoFindServiceImpl {

    @Autowired
    private CardBasicInfoMapper cardBasicInfoMapper;
    @Autowired
    private CardAuthorizationInfoMapper cardAuthorizationInfoMapper;
    @Autowired
    private AccountManagementInfoMapper accountManagementInfoMapper;
    @Autowired
    private CustomerAuthorizationInfoSelfMapper customerAuthorizationInfoSelfMapper;


    /**
     * 基于卡号获取卡信息
     * @param cardNumber 卡号
     * @return
     */
    public CardBasicDTO getCardBasicByCardNumber(String cardNumber){
        CardBasicDTO result = null;
        // 批次先尝试从库获取
        if (CustAccountBO.isBatch()) {
            result = CustAccountBO.threadCustAccountBO.get().getCardBO().getCacheByCardNum(cardNumber);
        }

        // 批次获取失败/联机进行查询
        if (result == null) {
            CardBasicInfo cardBasicInfo = cardBasicInfoMapper.selectByPrimaryKey(cardNumber, OrgNumberUtils.getOrg());
            if(cardBasicInfo != null){
                result = BeanMapping.copy(cardBasicInfo, CardBasicDTO.class);
            }
        }

        return result;
    }

    /**
     * 基于卡号获取卡授权信息
     * @param cardNumber
     * @return
     */
    public CardAuthorizationDTO getCardAuthorizationByCardNumber(String cardNumber){
        CardAuthorizationDTO result = null;
        // 批次先尝试从库获取
        if (CustAccountBO.isBatch()) {
            result = CustAccountBO.threadCustAccountBO.get().getAuthBO().getCardAuthorizationByCarNum(cardNumber);
        }

        // 批次获取失败/联机进行查询
        if (result == null) {
            CardAuthorizationInfo cardAuthorizationInfo = cardAuthorizationInfoMapper.selectByPrimaryKey(cardNumber,OrgNumberUtils.getOrg());
            if(cardAuthorizationInfo != null){
                result = BeanMapping.copy(cardAuthorizationInfo, CardAuthorizationDTO.class);
            }
        }

        return result;
    }

    /**
     * 基于id获取管理账户
     * @param id
     * @return
     */
    public AccountManagementInfoDTO getManagementInfoById(String id){
        AccountManagementInfoDTO result = null;
        // 批次先尝试从库获取
        if (CustAccountBO.isBatch()) {
            result = CustAccountBO.threadCustAccountBO.get().getCustomerBO().getManagementById(id);
        }

        // 批次获取失败/联机进行查询(通常情况批次必定获取管理账户成功，因为批次开始时会加载所有管理账户)
        if (result == null) {
            AccountManagementInfo accountManagementInfo = accountManagementInfoMapper.selectByPrimaryKey(id);
            if(accountManagementInfo != null){
                result = BeanMapping.copy(accountManagementInfo, AccountManagementInfoDTO.class);
            }
        }

        return result;
    }

    /**
     * 基于客户号获取客户授权表
     * @param customerId
     * @return
     */
    public CustomerAuthorizationInfoDTO getCustomerAuthorizationByCustomerId(String customerId){
        CustomerAuthorizationInfoDTO result = null;
        // 批次先尝试从库获取
        if (CustAccountBO.isBatch()) {
            // 判断缓存客户授权是否是查询的客户授权
            CustomerAuthorizationInfoDTO cache = CustAccountBO.threadCustAccountBO.get().getCustomerAuthorizationInfo();
            if (cache.getCustomerId().equals(customerId)) {
                result = cache;
            }
        }

        // 批次获取失败/联机进行查询
        if (result == null) {
            CustomerAuthorizationInfo customerAuthorizationInfo = customerAuthorizationInfoSelfMapper.selectByCustomerId(OrgNumberUtils.getOrg(),customerId);
            if(customerAuthorizationInfo != null){
                result = BeanMapping.copy(customerAuthorizationInfo, CustomerAuthorizationInfoDTO.class);
            }
        }

        return result;
    }
}
