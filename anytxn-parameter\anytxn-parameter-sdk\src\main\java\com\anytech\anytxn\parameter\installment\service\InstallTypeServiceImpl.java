package com.anytech.anytxn.parameter.installment.service;

import com.alibaba.fastjson.JSON;
import com.anytech.anytxn.parameter.base.installment.domain.dto.*;
import com.anytech.anytxn.parameter.installment.mapper.*;
import com.anytech.anytxn.parameter.base.installment.domain.model.InstallTypeSupportMerchant;
import com.github.pagehelper.Page;
import com.github.pagehelper.PageHelper;
import com.anytech.anytxn.common.core.annotation.db.DeleteParameterAnnotation;
import com.anytech.anytxn.common.core.annotation.db.InsertParameterAnnotation;
import com.anytech.anytxn.common.core.annotation.db.UpdateParameterAnnotation;
import com.anytech.anytxn.business.base.audit.AbstractParameterService;
import com.anytech.anytxn.common.core.base.ParameterCompare;
import com.anytech.anytxn.business.base.audit.ParmModificationRecord;
import com.anytech.anytxn.common.core.utils.OrgNumberUtils;
import com.anytech.anytxn.parameter.base.common.constants.Constants;
import com.anytech.anytxn.common.core.base.PageResultDTO;
import com.anytech.anytxn.common.core.utils.BeanMapping;
import com.anytech.anytxn.common.sequence.utils.Number16IdGen;
import com.anytech.anytxn.common.core.utils.TenantUtils;
import com.anytech.anytxn.parameter.base.common.enums.ParameterRepDetailEnum;
import com.anytech.anytxn.parameter.base.common.domain.dto.ParmTransactionCodeResDTO;
import com.anytech.anytxn.parameter.base.common.enums.AnyTxnParameterRespCodeEnum;
import com.anytech.anytxn.parameter.base.common.exception.AnyTxnParameterException;
import com.anytech.anytxn.parameter.base.installment.service.IInstallTypeParmService;
import com.anytech.anytxn.parameter.base.common.domain.model.system.ParmTransactionCode;
import com.anytech.anytxn.parameter.base.installment.domain.model.InstallTypeParm;
import com.anytech.anytxn.parameter.base.installment.domain.model.InstallTypeSupportMcc;
import com.anytech.anytxn.parameter.base.installment.domain.model.InstallTypeSupportTxn;
import org.apache.commons.lang3.StringUtils;
//import org.apache.shardingsphere.transaction.annotation.ShardingSphereTransactionType;
//import org.apache.shardingsphere.transaction.core.TransactionType;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;

/***
 * 分期类型参数 业务接口实现
 * @author: huaxianchao
 * @create: 2019-05-14 10:27
 **/
@Service(value = "parm_install_type_serviceImpl")
public class InstallTypeServiceImpl   extends AbstractParameterService implements IInstallTypeParmService {

    private final Logger logger = LoggerFactory.getLogger(InstallTypeServiceImpl.class);

    @Autowired
    private InstallTypeParmMapper installTypeParmMapper;
    @Autowired
    private InstallTypeParmSelfMapper installTypeParmSelfMapper;
    @Autowired
    private InstallTypeSupportTxnSelfMapper installTypeSupportTxnSelfMapper;
    @Autowired
    private InstallTypeSupportMccSelfMapper installTypeSupportMccSelfMapper;
    @Autowired
    private InstallTypeSupportMerchantSelfMapper installTypeSupportMerchantSelfMapper;
    @Autowired
    private Number16IdGen numberIdGenerator;

    /**
     * 添加分期类型参数
     *
     * @param installTypeParmReqDTO
     * @return 分期类型响应参数
     * @throws AnyTxnParameterException
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    @InsertParameterAnnotation(tableName = "parm_install_type", tableDesc = "Installment Type", isJoinTable = true)
    public ParameterCompare addInstallTypeParm(InstallTypeParmReqDTO installTypeParmReqDTO) throws AnyTxnParameterException {
        //参数提取
        String organizationNumber = installTypeParmReqDTO.getOrganizationNumber();
        String type = installTypeParmReqDTO.getType();
        String status = installTypeParmReqDTO.getStatus();
        BigDecimal maxAuthAmount = installTypeParmReqDTO.getMaxAuthAmount();
        //增加最小授权金额
        BigDecimal minAuthAmount = installTypeParmReqDTO.getMinAuthAmount();
        Integer cdThreshold = installTypeParmReqDTO.getCdThreshold();
        Integer creatMonthCheck = installTypeParmReqDTO.getCreatMonthCheck();
        //参数校验
        if (StringUtils.isBlank(organizationNumber) || StringUtils.isEmpty(organizationNumber)
                || StringUtils.isBlank(type) || StringUtils.isEmpty(type)
                || StringUtils.isBlank(status) || StringUtils.isEmpty(status)
                || maxAuthAmount==null || cdThreshold==null
                || creatMonthCheck==null || minAuthAmount ==null) {
            logger.warn("添加分期类型参数 索引参数为空,organizationNumber={}, tableId={}",installTypeParmReqDTO.getOrganizationNumber(), installTypeParmReqDTO.getType());
            throw new AnyTxnParameterException(AnyTxnParameterRespCodeEnum.D_INSTALL_TYPE_NECESSITY_FAULT);
        }
        //参数校验
        if (!(Constants.ENABLED.equals(status) || Constants.DISABLED.equals(status))) {
            logger.warn("添加分期类型参数,status值不合理,status{}", status);
            throw new AnyTxnParameterException(AnyTxnParameterRespCodeEnum.D_INSTALL_TYPE_STATUS_FAULT);
        }

        //查询表中是否已经存在
        int exists = installTypeParmSelfMapper.isExists(installTypeParmReqDTO.getOrganizationNumber(), installTypeParmReqDTO.getType());
        if (exists > 0) {
            logger.warn("添加分期类型参数，数据已存在,organizationNumber={}, tableId={}", installTypeParmReqDTO.getOrganizationNumber(), installTypeParmReqDTO.getType());
            throw new AnyTxnParameterException(AnyTxnParameterRespCodeEnum.D_EXIST_INSTALL_TYPE_FAULT);
        }
        int existsByType = installTypeParmSelfMapper.isExistsByType(installTypeParmReqDTO.getAuthTransactionType(),
                installTypeParmReqDTO.getAuthTransactionTypeDetail(), OrgNumberUtils.getOrg());
        if (existsByType > 0)
        {
            logger.warn("添加分期类型参数，数据已存在,authTransactionType={}, authTransactionTypeDetail={}",installTypeParmReqDTO.getAuthTransactionType(),
                    installTypeParmReqDTO.getAuthTransactionTypeDetail());
            throw new AnyTxnParameterException(AnyTxnParameterRespCodeEnum.D_EXIST_INSTALL_TYPE_FAULT);
        }

        installTypeParmReqDTO.setId(numberIdGenerator.generateId(TenantUtils.getTenantId())+"");
        installTypeParmReqDTO.setVersionNumber(1L);



        return ParameterCompare.getBuilder().withMainParmId(installTypeParmReqDTO.getType()).withAfter(installTypeParmReqDTO).build(InstallTypeParmReqDTO.class);


    }

    /**
     * 通过id主键删除分期类型参数
     *
     * @param id 主键
     * @return Boolean
     * @throws AnyTxnParameterException
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    @DeleteParameterAnnotation(tableName = "parm_install_type", tableDesc = "Installment Type", isJoinTable = true)
    public ParameterCompare removeInstallTypeParm(String id) throws AnyTxnParameterException {
        //参数校验
        if (id == null || id.equals("0")) {
            logger.warn("根据id删除分期类型参数，参数为空");
            throw new AnyTxnParameterException(AnyTxnParameterRespCodeEnum.P_PARAMETER_EMPTY_FAULT);
        }
        InstallTypeParm installTypeParm = installTypeParmMapper.selectByPrimaryKey(id);
        if (installTypeParm == null) {
            logger.warn("根据id删除分期类型参数,要删除的数据不存在,id{}", id);
            throw new AnyTxnParameterException(AnyTxnParameterRespCodeEnum.D_INSERT_INSTALL_TYPE_SUPPORT_FAULT);
        }


        return ParameterCompare.getBuilder()
                .withMainParmId(installTypeParm.getType())
                .withBefore(installTypeParm).build(InstallTypeParm.class);
    }

    /**
     * 更新分期类型参数
     *
     * @param installTypeParmReqDTO
     * @return 分期类型响应参数
     * @throws AnyTxnParameterException
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    @UpdateParameterAnnotation(tableName = "parm_install_type", tableDesc = "Installment Type", isJoinTable = true)
    public ParameterCompare modifyInstallTypeParm(InstallTypeParmReqDTO installTypeParmReqDTO) throws AnyTxnParameterException {
        String id = installTypeParmReqDTO.getId();
        String status = installTypeParmReqDTO.getStatus();
        BigDecimal maxAuthAmount = installTypeParmReqDTO.getMaxAuthAmount();
        Integer cdThreshold = installTypeParmReqDTO.getCdThreshold();
        Integer creatMonthCheck = installTypeParmReqDTO.getCreatMonthCheck();
        //参数校验
        if (id == null || id.equals("0")) {
            logger.warn("根据id更新分期类型参数,id为空");
            throw new AnyTxnParameterException(AnyTxnParameterRespCodeEnum.P_PARAMETER_ID_EMPTY_FAULT);
        }
        if (StringUtils.isBlank(status) || StringUtils.isEmpty(status)) {
            logger.warn("修改分期类型参数表,有必填项参数为空:status{},maxAuthAmount{},cdThreshold{},creatMonthCheck{}", status, maxAuthAmount, cdThreshold, creatMonthCheck);
            throw new AnyTxnParameterException(AnyTxnParameterRespCodeEnum.D_INSTALL_TYPE_NECESSITY_FAULT);
        }
        if (!(Constants.ENABLED.equals(status) || Constants.DISABLED.equals(status))) {
            logger.warn("添加分期类型参数,status值不合理,status{}", status);
            throw new AnyTxnParameterException(AnyTxnParameterRespCodeEnum.D_INSTALL_TYPE_STATUS_FAULT);
        }

        InstallTypeParm installTypeParm = installTypeParmMapper.selectByPrimaryKey(id);

        InstallTypeParmResDTO byId = findById(id);
        InstallTypeParmReqDTO beforeValue = BeanMapping.copy(byId, InstallTypeParmReqDTO.class);
        if (installTypeParm == null) {
            logger.warn("更新分期类型参数,要更新的数据不存在,id{}", id);
            throw new AnyTxnParameterException(AnyTxnParameterRespCodeEnum.D_QUERY_INSTALL_TYPE_BY_ID_FAULT);
        }
        String orgNum = installTypeParm.getOrganizationNumber();
        String type = installTypeParm.getType();
        Long versionNumber = installTypeParm.getVersionNumber();
        BeanMapping.copy(installTypeParmReqDTO, installTypeParm);
        //设置默认值
        installTypeParm.initUpdateDateTime();
        installTypeParm.setUpdateBy(installTypeParmReqDTO.getUpdateBy());
        //查看修改后是否会与已存在的数据发生冲突
        InstallTypeParm installType = installTypeParmSelfMapper.selectByIndex(installTypeParmReqDTO.getOrganizationNumber(), installTypeParmReqDTO.getType());
        if (installType != null && !installType.getId().equals(id)) {
            logger.warn("更新分期类型参数表, 分期类型参数信息已存在,orgNum{},type{}",installType.getOrganizationNumber(), installType.getType());
            throw new AnyTxnParameterException(AnyTxnParameterRespCodeEnum.D_EXIST_INSTALL_TYPE_SUPPORT_FAULT);
        }
        InstallTypeParm existsByType = installTypeParmSelfMapper.selectByIndexType(installTypeParmReqDTO.getOrganizationNumber(),installTypeParmReqDTO.getAuthTransactionType(),
                installTypeParmReqDTO.getAuthTransactionTypeDetail());
        if (existsByType != null && !existsByType.getId().equals(id)) {
            logger.warn("更新分期类型参数表, 分期类型参数信息已存在,authTransactionType{},authTransactionTypeDetail{}" ,existsByType.getAuthTransactionType(),
                    existsByType.getAuthTransactionTypeDetail());
            throw new AnyTxnParameterException(AnyTxnParameterRespCodeEnum.D_EXIST_INSTALL_TYPE_SUPPORT_FAULT);
        }
        //设置唯一索引字段与原数据一致
        installTypeParm.setOrganizationNumber(orgNum);
        installTypeParm.setType(type);
        installTypeParm.setVersionNumber(versionNumber);
        InstallTypeParmReqDTO copy = BeanMapping.copy(installTypeParm, InstallTypeParmReqDTO.class);

        return ParameterCompare.getBuilder()
                .withMainParmId(installTypeParmReqDTO.getType())
                .withAfter(copy)
                .withBefore(beforeValue)
                .build(InstallTypeParmReqDTO.class);
    }

    /**
     * 通过ID查询分期类型参数信息
     *
     * @param id
     * @return InstallTypeParm
     * @throws AnyTxnParameterException
     */
    @Override
    public InstallTypeParmResDTO findById(String id) throws AnyTxnParameterException {
        //参数校验
        if (id == null) {
            logger.warn("根据id查询分期类型参数信息,id为空");
            throw new AnyTxnParameterException(AnyTxnParameterRespCodeEnum.P_PARAMETER_ID_EMPTY_FAULT);
        }
        InstallTypeParm installTypeParm = installTypeParmMapper.selectByPrimaryKey(id);
        //查询结果判断
        if (installTypeParm == null) {
            logger.warn("根据id查询分期类型信息,要查询的数据不存在,id{}", id);
            throw new AnyTxnParameterException(AnyTxnParameterRespCodeEnum.D_QUERY_INSTALL_TYPE_BY_ID_FAULT);
        }
        List<InstallTypeSupportTxn> typeSupportTxnList = installTypeSupportTxnSelfMapper.selectByTypeAndOrgNum(installTypeParm.getType(), installTypeParm.getOrganizationNumber());
        List<InstallTypeSupportMcc> typeSupportMccList = installTypeSupportMccSelfMapper.selectByTypeAndOrgNum(installTypeParm.getType(), installTypeParm.getOrganizationNumber());
        List<InstallTypeSupportMerchant> typeSupportMerchantList = installTypeSupportMerchantSelfMapper.selectByTypeAndOrgNum(installTypeParm.getType(), installTypeParm.getOrganizationNumber());

        InstallTypeParmResDTO installTypeParmResDTO = BeanMapping.copy(installTypeParm, InstallTypeParmResDTO.class);
        List<InstallTypeSupportTxnResDTO> installTypeSupportTxnResDtos = new ArrayList<>();
        if(!typeSupportTxnList.isEmpty()){
            installTypeSupportTxnResDtos = BeanMapping.copyList(typeSupportTxnList, InstallTypeSupportTxnResDTO.class);
        }
        List<InstallTypeMccResDTO> installTypeMccResDTOS = new ArrayList<>();
        if(!typeSupportMccList.isEmpty()){
            installTypeMccResDTOS = BeanMapping.copyList(typeSupportMccList, InstallTypeMccResDTO.class);
        }
        List<InstallTypeMerchantResDTO> installTypeMerchantResDTOS = new ArrayList<>();
        if(!typeSupportMerchantList.isEmpty()){
            installTypeMerchantResDTOS = BeanMapping.copyList(typeSupportMerchantList, InstallTypeMerchantResDTO.class);
        }
        installTypeParmResDTO.setInstallTypeSupportTxnResList(installTypeSupportTxnResDtos);
        installTypeParmResDTO.setInstallTypeMccResList(installTypeMccResDTOS);
        installTypeParmResDTO.setInstallTypeMerchantResList(installTypeMerchantResDTOS);
        return installTypeParmResDTO;

    }

    /**
     * 查询所有分期类型参数信息
     *
     * @param pageNum
     * @param pageSize
     * @return InstallTypeParm
     * @throws AnyTxnParameterException
     */
    @Override
    public PageResultDTO<InstallTypeParmResDTO> findAll(Integer pageNum, Integer pageSize, InstallTypeParmReqDTO installTypeParmReqDTO) {
        if(null == installTypeParmReqDTO){
            installTypeParmReqDTO = new InstallTypeParmReqDTO();
        }
        installTypeParmReqDTO.setOrganizationNumber(StringUtils.isEmpty(installTypeParmReqDTO.getOrganizationNumber()) ? OrgNumberUtils.getOrg() : installTypeParmReqDTO.getOrganizationNumber());
        Page<InstallTypeParm> page = PageHelper.startPage(pageNum, pageSize);
        List<InstallTypeParm> installTypeParms = installTypeParmSelfMapper.selectByCondition(installTypeParmReqDTO);
        List<InstallTypeParmResDTO> currencyRateRes = BeanMapping.copyList(installTypeParms, InstallTypeParmResDTO.class);
        return new PageResultDTO<>(pageNum, pageSize, page.getTotal(), page.getPages(), currencyRateRes);
    }

    /**
     * 通过机构号，type查询分期类型参数信息
     * @param organizationNumber
     * @param type
     * @return 分期类型响应参数
     * @throws AnyTxnParameterException
     */
    @Override
    public InstallTypeParmResDTO findByOrgNumAndType(String organizationNumber, String type) throws AnyTxnParameterException {
        //参数校验
        if (StringUtils.isBlank(organizationNumber) || StringUtils.isEmpty(organizationNumber) || StringUtils.isBlank(type) || StringUtils.isEmpty(type)) {
            logger.warn("根据orgNum,type查询分期类型信息,参数为空,orgNum{},type{}", organizationNumber, type);
            throw new AnyTxnParameterException(AnyTxnParameterRespCodeEnum.P_PARAMETER_EMPTY_FAULT);
        }
        InstallTypeParm installTypeParm = installTypeParmSelfMapper.selectByIndex(organizationNumber, type);
        //查询结果判断
        if (installTypeParm == null) {
            logger.warn("根据orgNum,type查询分期类型参数,查询结果为空,orgNum{},type{}", organizationNumber, type);
            throw new AnyTxnParameterException(AnyTxnParameterRespCodeEnum.D_QUERY_INSTALL_TYPE_NULL_FAULT);
        }
        return BeanMapping.copy(installTypeParm, InstallTypeParmResDTO.class);
    }

    /**
     * 根据type删除分期类型参数
     *
     * @param type
     * @return Boolean
     * @throws AnyTxnParameterException
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean removeByType(String type) throws AnyTxnParameterException {
        //参数校验
        if (StringUtils.isEmpty(type) || StringUtils.isBlank(type)) {
            logger.warn("根据type删除分期类型参数,type为空");
            throw new AnyTxnParameterException(AnyTxnParameterRespCodeEnum.P_PARAMETER_EMPTY_FAULT);
        }
        //查看该数据是否存在
        InstallTypeParm installTypeParm = installTypeParmSelfMapper.selectByIndex(OrgNumberUtils.getOrg(), type);
        if (installTypeParm == null) {
            logger.warn("根据type删除分期类型参数,要删除的数据不存在,type{}", type);
            throw new AnyTxnParameterException(AnyTxnParameterRespCodeEnum.D_QUERY_INSTALL_TYPE_NULL_FAULT);
        }
        try {
            return installTypeParmSelfMapper.deleteByType(type, OrgNumberUtils.getOrg()) > 0;
        } catch (Exception e) {
            logger.warn("根据type删除分期类型参数，删除数据库失败,type{}", type);
            throw new AnyTxnParameterException(AnyTxnParameterRespCodeEnum.D_DELETE_INSTALL_TYPE_SUPPORT_BY_TYPE_FAULT);
        }
    }
    /**
     * 通过机构号 大类  细类 查询分期类型参数信息
     * @param organizationNumber
     * @param authTransactionType authTransactionTypeDetail
     * @return 分期类型响应参数
     * @throws AnyTxnParameterException
     */
    @Override
    public InstallTypeParmResDTO findByOrgNumAndTypeAndDetail(String organizationNumber, String authTransactionType, String authTransactionTypeDetail) {
        if(null ==organizationNumber || "".equals(organizationNumber))
        {
            logger.warn("机构号不能为空,organizationNumber{}",organizationNumber);
            throw new AnyTxnParameterException(AnyTxnParameterRespCodeEnum.D_INSTALL_ACCOUNTING_TRANS_PARM_ORG_FAULT);
        }
        if ( null ==authTransactionType || "".equals(authTransactionType))
        {
            logger.warn("分期类型大类不能为空,authTransactionType{}",authTransactionType);
            throw new AnyTxnParameterException(AnyTxnParameterRespCodeEnum.D_INSTALL_TYPE_BIG_TYPE_FAULT);
        }
        if (null ==authTransactionTypeDetail || "".equals(authTransactionTypeDetail))
        {
            logger.warn("分期类型细类不能为空,authTransactionTypeDetail{}",authTransactionTypeDetail);
            throw new AnyTxnParameterException(AnyTxnParameterRespCodeEnum.D_INSTALL_TYPE_BIG_SMALL_FAULT);
        }
        InstallTypeParm installTypeParm = installTypeParmSelfMapper.selectByIndexType(organizationNumber,authTransactionType,authTransactionTypeDetail);
        //查询结果判断
        if (installTypeParm == null) {
            logger.warn("根据orgNum,大类，细类 查询分期类型参数,查询结果为空,organizationNumber{},authTransactionType{}，authTransactionTypeDetail{}",
                    organizationNumber, authTransactionType,authTransactionTypeDetail);
            throw new AnyTxnParameterException(AnyTxnParameterRespCodeEnum.D_QUERY_INSTALL_TYPE_NULL_FAULT);
        }
        return BeanMapping.copy(installTypeParm, InstallTypeParmResDTO.class);
    }

    /**
     * 根据消费、取现类交易属性 查询交易码表
     * @return CommonSelectResDTO
     */
    @Override
    public List<ParmTransactionCodeResDTO> findTransCodeAndDesc(String organizationNumber) {
        List<ParmTransactionCode> transactionCodeList = installTypeSupportTxnSelfMapper.selectSupportTransCodeAndDesc(organizationNumber);
        if (transactionCodeList.isEmpty()) {
            logger.error("根据消费、取现类交易属性查询交易码参数 未查询到信息");
            throw new AnyTxnParameterException(AnyTxnParameterRespCodeEnum.D_QUERY_INSTALL_TYPE_NULL_FAULT);
        }
        List<ParmTransactionCodeResDTO> parmTransactionCodeResDtos=null;
        try {
            parmTransactionCodeResDtos = BeanMapping.copyList(transactionCodeList, ParmTransactionCodeResDTO.class);
        }catch (Exception e){
            logger.error("根据消费、取现类交易属性查询交易码参数 部分数据有空值");
            throw new AnyTxnParameterException(AnyTxnParameterRespCodeEnum.D_INSTALL_TYPE_HAVE_NULL_FAULT);
        }
        return parmTransactionCodeResDtos;
    }

    @Override
    @Transactional(rollbackFor = Exception.class,propagation = Propagation.REQUIRED)
    //@ShardingSphereTransactionType(TransactionType.XA)
    protected boolean updateDb(ParmModificationRecord parmModificationRecord) {
        InstallTypeParmReqDTO installTypeParmReqDTO = JSON.parseObject(parmModificationRecord.getParmBody(), InstallTypeParmReqDTO.class);
        InstallTypeParm installTypeParm1 = installTypeParmMapper.selectByPrimaryKey(installTypeParmReqDTO.getId());
        installTypeParmReqDTO.setVersionNumber(installTypeParm1.getVersionNumber());
        InstallTypeParm installTypeParm = BeanMapping.copy(installTypeParmReqDTO, InstallTypeParm.class);
        try {
            installTypeParmMapper.updateByPrimaryKeySelective(installTypeParm);
            //更新分期类型交易码表
            List<InstallTypeSupportTxnResDTO> typeSupportTxnResList = installTypeParmReqDTO.getInstallTypeSupportTxnResList();
            List<InstallTypeSupportTxn> updateList = new ArrayList<>();
            if(!CollectionUtils.isEmpty(typeSupportTxnResList)){
                installTypeSupportTxnSelfMapper.deleteByTypeAndOrgNum(installTypeParmReqDTO.getType(),installTypeParmReqDTO.getOrganizationNumber());
                updateList = BeanMapping.copyList(typeSupportTxnResList, InstallTypeSupportTxn.class);
                for (InstallTypeSupportTxn in : updateList){
                    long uid = numberIdGenerator.generateId(TenantUtils.getTenantId());
                    in.setType(installTypeParm.getType());
                    in.setId(uid+"");
                    in.setStatus(installTypeParmReqDTO.getStatus());
                    in.initCreateDateTime();
                    in.initUpdateDateTime();
                    in.setOrganizationNumber(installTypeParm.getOrganizationNumber());
                }
                installTypeSupportTxnSelfMapper.insertList(updateList);
            }else {
                installTypeSupportTxnSelfMapper.deleteByTypeAndOrgNum(installTypeParmReqDTO.getType(),installTypeParmReqDTO.getOrganizationNumber());
            }

            //更新分期类型不支持的Mcc
            List<InstallTypeMccResDTO> installTypeMccResList = installTypeParmReqDTO.getInstallTypeMccResList();
            List<InstallTypeSupportMcc> updateMccList = new ArrayList<>();
            if(!CollectionUtils.isEmpty(installTypeMccResList)){
                installTypeSupportMccSelfMapper.deleteByTypeAndOrgNum(installTypeParmReqDTO.getType(),installTypeParmReqDTO.getOrganizationNumber());
                updateMccList = BeanMapping.copyList(installTypeMccResList, InstallTypeSupportMcc.class);
                for (InstallTypeSupportMcc in : updateMccList){
                    long uid = numberIdGenerator.generateId(TenantUtils.getTenantId());
                    in.setType(installTypeParm.getType());
                    in.setId(uid+"");
                    in.setStatus(installTypeParmReqDTO.getStatus());
                    in.initCreateDateTime();
                    in.initUpdateDateTime();
                    in.initApplyBy();installTypeParmReqDTO.getUpdateBy();
                    in.setOrganizationNumber(installTypeParm.getOrganizationNumber());
                }
                installTypeSupportMccSelfMapper.insertList(updateMccList);
            }else {
                installTypeSupportMccSelfMapper.deleteByTypeAndOrgNum(installTypeParmReqDTO.getType(),installTypeParmReqDTO.getOrganizationNumber());
            }

            //更新分期类型不支持的商户号
            List<InstallTypeMerchantResDTO> installTypeMerchantResList = installTypeParmReqDTO.getInstallTypeMerchantResList();
            List<InstallTypeSupportMerchant> updateMerchantList = new ArrayList<>();
            if(!CollectionUtils.isEmpty(installTypeMerchantResList)){
                installTypeSupportMerchantSelfMapper.deleteByTypeAndOrgNum(installTypeParmReqDTO.getType(),installTypeParmReqDTO.getOrganizationNumber());
                updateMerchantList = BeanMapping.copyList(installTypeMerchantResList, InstallTypeSupportMerchant.class);
                for (InstallTypeSupportMerchant in : updateMerchantList){
                    long uid = numberIdGenerator.generateId(TenantUtils.getTenantId());
                    in.setType(installTypeParm.getType());
                    in.setId(uid+"");
                    in.setStatus(installTypeParmReqDTO.getStatus());
                    in.initCreateDateTime();
                    in.initUpdateDateTime();
                    in.initApplyBy();installTypeParmReqDTO.getUpdateBy();
                    in.setOrganizationNumber(installTypeParm.getOrganizationNumber());
                }
                installTypeSupportMerchantSelfMapper.insertList(updateMerchantList);
            }else {
                installTypeSupportMerchantSelfMapper.deleteByTypeAndOrgNum(installTypeParmReqDTO.getType(),installTypeParmReqDTO.getOrganizationNumber());
            }

            InstallTypeParmResDTO installTypeParmResDTO = BeanMapping.copy(installTypeParm, InstallTypeParmResDTO.class);
            List<InstallTypeSupportTxnResDTO> typeSupportTxnResDtos = BeanMapping.copyList(updateList, InstallTypeSupportTxnResDTO.class);
            installTypeParmResDTO.setInstallTypeSupportTxnResList(typeSupportTxnResDtos);
            List<InstallTypeMccResDTO> typeMccResDtos = BeanMapping.copyList(updateMccList, InstallTypeMccResDTO.class);
            installTypeParmResDTO.setInstallTypeMccResList(typeMccResDtos);
            List<InstallTypeMerchantResDTO> typeMerchantResDtos = BeanMapping.copyList(updateMerchantList, InstallTypeMerchantResDTO.class);
            installTypeParmResDTO.setInstallTypeMerchantResList(typeMerchantResDtos);
        } catch (Exception exce) {
            logger.warn("修改分期类型参数 修改数据库失败",exce);
            throw new AnyTxnParameterException(AnyTxnParameterRespCodeEnum.D_MODIFY_INSTALL_TYPE_SUPPORT_FAULT);
        }
    return true;
    }

    @Override
    @Transactional(rollbackFor = Exception.class,propagation = Propagation.REQUIRED)
    //@ShardingSphereTransactionType(TransactionType.XA)
    protected boolean insertDb(ParmModificationRecord parmModificationRecord) {
        InstallTypeParmReqDTO installTypeParmReqDTO = JSON.parseObject(parmModificationRecord.getParmBody(), InstallTypeParmReqDTO.class);
        InstallTypeParm installTypeParm = BeanMapping.copy(installTypeParmReqDTO, InstallTypeParm.class);

        //设置默认值
        installTypeParm.initCreateDateTime();
        installTypeParm.initUpdateDateTime();
        installTypeParm.initApplyBy();
        installTypeParm.setVersionNumber(installTypeParmReqDTO.getVersionNumber());
        //插入数据库
        try {
            int result = installTypeParmMapper.insertSelective(installTypeParm);
            //插入结果判断
            if (result == 0) {
                logger.warn("添加分期类型参数失败,organizationNumber={}, tableId={}", installTypeParmReqDTO.getOrganizationNumber(), installTypeParmReqDTO.getType());
                throw new AnyTxnParameterException(AnyTxnParameterRespCodeEnum.D_INSERT_INSTALL_TYPE_FAULT);
            }
            List<InstallTypeSupportTxnResDTO> typeSupportTxnResList = installTypeParmReqDTO.getInstallTypeSupportTxnResList();
            if(null !=typeSupportTxnResList && !typeSupportTxnResList.isEmpty()){
                for(InstallTypeSupportTxnResDTO typeSupportTxnResDTO : typeSupportTxnResList){
                    long uid = numberIdGenerator.generateId(TenantUtils.getTenantId());
                    typeSupportTxnResDTO.setId(uid+"");
                    typeSupportTxnResDTO.setOrganizationNumber(installTypeParmReqDTO.getOrganizationNumber());
                    typeSupportTxnResDTO.setType(installTypeParmReqDTO.getType());
                    typeSupportTxnResDTO.setStatus(installTypeParmReqDTO.getStatus());
                    typeSupportTxnResDTO.setCreateTime(LocalDateTime.now());
                    typeSupportTxnResDTO.setUpdateTime(LocalDateTime.now());
                    typeSupportTxnResDTO.setUpdateBy(Constants.DEFAULT_USER);
                    typeSupportTxnResDTO.setVersionNumber(1);
                }
                List<InstallTypeSupportTxn> installTypeSupportTxns = BeanMapping.copyList(typeSupportTxnResList, InstallTypeSupportTxn.class);
                int i = installTypeSupportTxnSelfMapper.insertList(installTypeSupportTxns);
                if(i != typeSupportTxnResList.size()){
                    throw new AnyTxnParameterException(AnyTxnParameterRespCodeEnum.D_INSERT_INSTALL_TYPE_SUPPORT_FAULT);
                }
            }
            //不支持的MCC
            List<InstallTypeMccResDTO> typeMccResList = installTypeParmReqDTO.getInstallTypeMccResList();
            if(null !=typeMccResList && !typeMccResList.isEmpty()){
                for(InstallTypeMccResDTO typeMccResDTO : typeMccResList){
                    long uid = numberIdGenerator.generateId(TenantUtils.getTenantId());
                    typeMccResDTO.setId(uid+"");
                    typeMccResDTO.setOrganizationNumber(installTypeParmReqDTO.getOrganizationNumber());
                    typeMccResDTO.setType(installTypeParmReqDTO.getType());
                    typeMccResDTO.setStatus(installTypeParmReqDTO.getStatus());
                    typeMccResDTO.setCreateTime(LocalDateTime.now());
                    typeMccResDTO.setUpdateTime(LocalDateTime.now());
                    typeMccResDTO.setUpdateBy(Constants.DEFAULT_USER);
                    typeMccResDTO.setVersionNumber(1);
                }
                List<InstallTypeSupportMcc> installTypeMccs = BeanMapping.copyList(typeMccResList, InstallTypeSupportMcc.class);
                int i = installTypeSupportMccSelfMapper.insertList(installTypeMccs);
                if(i != installTypeMccs.size()){
                    throw new AnyTxnParameterException(AnyTxnParameterRespCodeEnum.D_DATABASE_UPDATE_ERROR, ParameterRepDetailEnum.MCC);
                }
            }
            //不支持的Mmerchant
            List<InstallTypeMerchantResDTO> typeMercahtResList = installTypeParmReqDTO.getInstallTypeMerchantResList();
            if(null !=typeMercahtResList && !typeMercahtResList.isEmpty()){
                for(InstallTypeMerchantResDTO typeMerchantResDTO : typeMercahtResList){
                    long uid = numberIdGenerator.generateId(TenantUtils.getTenantId());
                    typeMerchantResDTO.setId(uid+"");
                    typeMerchantResDTO.setOrganizationNumber(installTypeParmReqDTO.getOrganizationNumber());
                    typeMerchantResDTO.setType(installTypeParmReqDTO.getType());
                    typeMerchantResDTO.setStatus(installTypeParmReqDTO.getStatus());
                    typeMerchantResDTO.setCreateTime(LocalDateTime.now());
                    typeMerchantResDTO.setUpdateTime(LocalDateTime.now());
                    typeMerchantResDTO.setUpdateBy(Constants.DEFAULT_USER);
                    typeMerchantResDTO.setVersionNumber(1);
                }
                List<InstallTypeSupportMerchant> installTypeMerchants = BeanMapping.copyList(typeMercahtResList, InstallTypeSupportMerchant.class);
                int i = installTypeSupportMerchantSelfMapper.insertList(installTypeMerchants);
                if(i != installTypeMerchants.size()){
                    throw new AnyTxnParameterException(AnyTxnParameterRespCodeEnum.D_DATABASE_UPDATE_ERROR, ParameterRepDetailEnum.MERCHANT);
                }
            }

            InstallTypeParmReqDTO typeParmReqDTO = BeanMapping.copy(installTypeParm, InstallTypeParmReqDTO.class);
            List<InstallTypeSupportTxnResDTO> installTypeSupportTxnResDtos
                    = BeanMapping.copyList(typeSupportTxnResList, InstallTypeSupportTxnResDTO.class);
            List<InstallTypeMccResDTO> installTypeMccResDTOS = BeanMapping.copyList(typeMccResList, InstallTypeMccResDTO.class);
            typeParmReqDTO.setInstallTypeSupportTxnResList(installTypeSupportTxnResDtos);
            typeParmReqDTO.setInstallTypeMccResList(installTypeMccResDTOS);
        } catch (Exception exce) {
            logger.error(exce.getMessage(), exce);
            throw new AnyTxnParameterException(AnyTxnParameterRespCodeEnum.D_INSERT_INSTALL_TYPE_FAULT,exce);
        }
    return true;
    }

    @Override
    @Transactional(rollbackFor = Exception.class,propagation = Propagation.REQUIRED)
    //@ShardingSphereTransactionType(TransactionType.XA)
    protected boolean deleteDb(ParmModificationRecord parmModificationRecord) {
        InstallTypeParm installTypeParm = JSON.parseObject(parmModificationRecord.getParmBody(), InstallTypeParm.class);
        try {
            //先删除分期类型交易码
            boolean res1;
            List<InstallTypeSupportTxn> installTypeSupportTxns = installTypeSupportTxnSelfMapper.selectByTypeAndOrgNum(installTypeParm.getType(), installTypeParm.getOrganizationNumber());
            if(!installTypeSupportTxns.isEmpty()){
                res1 = installTypeSupportTxnSelfMapper.deleteByTypeAndOrgNum(installTypeParm.getType(), installTypeParm.getOrganizationNumber()) > 0;
            }else {
                res1 = true;
            }
            //删除不支持的MCC
            boolean res2;
            List<InstallTypeSupportMcc> installTypeMccs = installTypeSupportMccSelfMapper.selectByTypeAndOrgNum(installTypeParm.getType(), installTypeParm.getOrganizationNumber());
            if(!installTypeMccs.isEmpty()){
                res2 = installTypeSupportMccSelfMapper.deleteByTypeAndOrgNum(installTypeParm.getType(), installTypeParm.getOrganizationNumber()) > 0;
            }else {
                res2 = true;
            }

            //删除不支持的Merchant
            boolean res4;
            List<InstallTypeSupportMerchant> installTypeMerchants = installTypeSupportMerchantSelfMapper.selectByTypeAndOrgNum(installTypeParm.getType(), installTypeParm.getOrganizationNumber());
            if(!installTypeMerchants.isEmpty()){
                res4 = installTypeSupportMerchantSelfMapper.deleteByTypeAndOrgNum(installTypeParm.getType(), installTypeParm.getOrganizationNumber()) > 0;
            }else {
                res4 = true;
            }

            boolean res3 = installTypeParmMapper.deleteByPrimaryKey(installTypeParm.getId()) > 0;

            return res1 && res2 && res3 && res4;
        } catch (Exception exce) {
            logger.warn("根据id删除分期类型参数 删除数据库失败,id{}", installTypeParm.getId());
            throw new AnyTxnParameterException(AnyTxnParameterRespCodeEnum.D_DELETE_INSTALL_TYPE_SUPPORT_FAULT);
        }    }
}
