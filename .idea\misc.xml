<?xml version="1.0" encoding="UTF-8"?>
<project version="4">
  <component name="ExternalStorageConfigurationManager" enabled="true" />
  <component name="MavenProjectsManager">
    <option name="originalFiles">
      <list>
        <option value="$PROJECT_DIR$/pom.xml" />
        <option value="$PROJECT_DIR$/anytxn-parent/pom.xml" />
        <option value="$PROJECT_DIR$/anytxn-business-core/pom.xml" />
        <option value="$PROJECT_DIR$/anytxn-third-party-service/pom.xml" />
        <option value="$PROJECT_DIR$/anytxn-parameter/pom.xml" />
      </list>
    </option>
  </component>
  <component name="ProjectRootManager" version="2" languageLevel="JDK_17" default="true" project-jdk-name="17" project-jdk-type="JavaSDK">
    <output url="file://$PROJECT_DIR$/out" />
  </component>
</project>