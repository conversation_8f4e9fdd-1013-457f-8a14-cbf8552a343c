package com.anytech.anytxn.parameter.authorization.controller;

import io.swagger.v3.oas.annotations.tags.Tag;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.Operation;
import com.anytech.anytxn.common.core.base.ParameterCompare;
import com.anytech.anytxn.common.core.base.PageResultDTO;
import com.anytech.anytxn.common.core.base.AnyTxnHttpResponse;
import com.anytech.anytxn.common.core.base.BizBaseController;
import com.anytech.anytxn.parameter.base.authorization.domain.dto.VelocityDefinitionDTO;
import com.anytech.anytxn.parameter.base.authorization.service.IVelocityDefinitionService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import jakarta.validation.Valid;
import java.util.List;
import java.util.Map;

/**
 * @description 流量检查定义api
 * <AUTHOR>
 * @date 2019/4/4
 */

@RestController
@Tag(name = "流量检查定义服务")
public class VelocityDefinitionController extends BizBaseController {

    @Autowired
    private IVelocityDefinitionService velocityDefinitionService;

    /**
     * @description 分页查询流量检查列表
     * <AUTHOR>
     * @date 2019/4/4
     * @param page, rows
     * @return jrx.anytxn.biz.common.web.AnyTxnHttpResponse<jrx.anytxn.biz.common.dto.PageResultDTO<jrx.anytxn.auth.dto.VelocityDefinitionDTO>>
     */
    @Operation(summary = "分页查询流量检查定义信息")
    @GetMapping("/param/velocityDefinitions/{page}/{rows}")
    public AnyTxnHttpResponse<PageResultDTO<VelocityDefinitionDTO>> getVelocityDefinitionList(@Parameter(name = "page", description = "当前页", example = "1")@PathVariable("page") int page,
                                                                                              @Parameter(name = "rows", description = "每页大小", example = "8")@PathVariable("rows") int rows,
                                                                                              VelocityDefinitionDTO velocityDefinitionDTO) {
        PageResultDTO<VelocityDefinitionDTO> result = velocityDefinitionService.findListVelocityDefinition(page, rows,velocityDefinitionDTO);
        return AnyTxnHttpResponse.success(result);
    }


    /**
     * @description 获取所有流量检查码
     * <AUTHOR>
     * @date 2019/4/13
     * @param
     * @return jrx.anytxn.biz.common.web.AnyTxnHttpResponse<java.util.List<java.lang.String>>
     */
    @Operation(summary = "获取所有流量检查码")
    @GetMapping("/param/velocityDefinition/velocityCodes")
    public AnyTxnHttpResponse<List<Map<String,String>>> getVelocityCodes(String organizationNumber) {
        List<Map<String,String>> velocityCodes = velocityDefinitionService.getVelocityCodes(organizationNumber);
        return AnyTxnHttpResponse.success(velocityCodes);
    }

    /**
     * @description 根据主键查询流量检查定义信息
     * <AUTHOR>
     * @date 2019/4/4
     * @param id
     * @return jrx.anytxn.biz.common.web.AnyTxnHttpResponse<jrx.anytxn.auth.dto.VelocityDefinitionDTO>
     */

    @Operation(summary = "根据主键查询流量检查定义信息")
    @GetMapping("/param/velocityDefinition/{id}")
    @Parameter(name = "id", description = "技术主键")
    public AnyTxnHttpResponse<VelocityDefinitionDTO> getVelocityDefinition(@PathVariable String id) {
        VelocityDefinitionDTO velocityDefinitionDTO = velocityDefinitionService.findVelocityDefinition(id);
        return AnyTxnHttpResponse.success(velocityDefinitionDTO);
    }


    /**
     * @description 删除流量检查定义信息
     * <AUTHOR>
     * @date 2019/4/4
     * @param id
     * @return jrx.anytxn.biz.common.web.AnyTxnHttpResponse
     */

    @Operation(summary = "删除流量检查定义信息")
    @DeleteMapping("/param/velocityDefinition/{id}")
    public AnyTxnHttpResponse cancelVelocityDefinition(@PathVariable String id) {
        ParameterCompare parameterCompare = velocityDefinitionService.removeVelocityDefinition(id);
        return AnyTxnHttpResponse.success(parameterCompare);
    }

    /**
     * @description 新增流量检查定义信息
     * <AUTHOR>
     * @date 2019/4/4
     * @param velocityDefinitionDTO
     * @return jrx.anytxn.biz.common.web.AnyTxnHttpResponse
     */

    @Operation(summary = "新增流量检查定义信息")
    @PostMapping("/param/velocityDefinition")
    public AnyTxnHttpResponse create(@Valid @RequestBody VelocityDefinitionDTO velocityDefinitionDTO) {
        ParameterCompare parameterCompare = velocityDefinitionService.addVelocityDefinition(velocityDefinitionDTO);
        return AnyTxnHttpResponse.success(parameterCompare);
    }

    /**
     * @description 流量检查定义信息修改
     * <AUTHOR>
     * @date 2019/4/4
     * @param velocityDefinitionDTO
     * @return jrx.anytxn.biz.common.web.AnyTxnHttpResponse
     */

    @Operation(summary = "流量检查定义信息修改")
    @PutMapping("/param/velocityDefinition")
    public AnyTxnHttpResponse modify(@Valid @RequestBody VelocityDefinitionDTO velocityDefinitionDTO) {
        ParameterCompare parameterCompare = velocityDefinitionService.modifyVelocityDefinition(velocityDefinitionDTO);
        return AnyTxnHttpResponse.success(parameterCompare);
    }

}
