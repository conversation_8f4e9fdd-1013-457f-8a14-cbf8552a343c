package com.anytech.anytxn.parameter.common.service;

import com.anytech.anytxn.common.core.utils.BeanMapping;
import com.anytech.anytxn.parameter.base.common.domain.dto.CommonSelectResDTO;
import com.anytech.anytxn.parameter.base.common.service.ICommonService;
import com.anytech.anytxn.parameter.base.common.enums.AnyTxnParameterRespCodeEnum;
import com.anytech.anytxn.parameter.base.common.exception.AnyTxnParameterException;
import com.anytech.anytxn.parameter.common.mapper.broadcast.product.ParmAcctProductMainInfoSelfMapper;
import com.anytech.anytxn.parameter.common.mapper.broadcast.ParmCommonSelfMapper;
import com.anytech.anytxn.parameter.base.common.domain.model.CommonSelectList;
import com.anytech.anytxn.parameter.base.common.domain.model.ParmAcctProductMainInfo;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import org.springframework.util.ObjectUtils;

import java.util.*;
import java.util.stream.Collectors;

/**
 * Description   公共下拉框
 * Copyright:	Copyright (c) 2018
 * Company:		江融信
 * Author:		lichao
 * Version:		1.0
 * Create at:	2018/9/26 下午3:38
 * <p>
 * 修改历史:
 * 日期    作者    版本  修改描述
 * ------------------------------------------------------------------
 **/
@Service
public class CommonServiceImpl implements ICommonService {

    private Logger logger = LoggerFactory.getLogger(CommonServiceImpl.class);

    @Autowired
    private ParmCommonSelfMapper parmCommonSelfMapper;
    @Autowired
    private ParmAcctProductMainInfoSelfMapper parmAcctProductMainInfoSelfMapper;

    /**
     * @param tableName          表名
     * @param columnName、tableId 列名
     * @return 下拉框
     * @throws AnyTxnParameterException
     */
    @Override
    public List<CommonSelectResDTO> findAll(String tableName, String columnName, String tableId, String param, String organizationNumber) {
        List<CommonSelectList> commonSelectLists = new ArrayList<>();
        List<ParmAcctProductMainInfo> acctProductMainInfos;
        String proInfoTableName = "PARM_PRODUCT_INFO";
        if (!ObjectUtils.isEmpty(tableName)) {
            if (proInfoTableName.equals(tableName) || "parm_product_info".equals(tableName)) {
                acctProductMainInfos = parmAcctProductMainInfoSelfMapper.selectByOrgNum(organizationNumber);
                if (!CollectionUtils.isEmpty(acctProductMainInfos)){
                    for (ParmAcctProductMainInfo acctProductMainInfo : acctProductMainInfos){
                        CommonSelectList commonSelectList = new CommonSelectList();
                        commonSelectList.setTableName(proInfoTableName);
                        commonSelectList.setId(acctProductMainInfo.getProductNumber());
                        if (!ObjectUtils.isEmpty(acctProductMainInfo)) {
                            commonSelectList.setName(acctProductMainInfo.getDescription());
                        }
                        if (!commonSelectLists.contains(commonSelectList)) {
                            commonSelectLists.add(commonSelectList);
                        }
                    }
                }
            } else {
                commonSelectLists = parmCommonSelfMapper.selectAll(tableName, columnName, tableId, param, organizationNumber);
            }
        }

        if (commonSelectLists.isEmpty()) {
            logger.error("未查询到信息");
//            throw new AnyTxnParameterException(AnyTxnParameterRespCodeEnum.D_GET_IS_NULL_FAULT);
        }
        List<CommonSelectResDTO> commonSelectRes = null;
        try {
            commonSelectRes = BeanMapping.copyList(commonSelectLists, CommonSelectResDTO.class);
        } catch (Exception e) {
            logger.error(" 部分数据有空值");
            throw new AnyTxnParameterException(AnyTxnParameterRespCodeEnum.D_QUERY_COMMON_SELECT_FAULT);
        }
        // 基于ID去重
        return commonSelectRes.stream().collect(
                Collectors.collectingAndThen(
                        Collectors.toCollection(() -> new TreeSet<>(Comparator.comparing(CommonSelectResDTO::getId,Comparator.nullsFirst(Comparator.naturalOrder())))), ArrayList::new));
    }

    @Override
    public List<CommonSelectResDTO> findAllNew(List<String> tableNames, String columnName, String tableId, String organizationNumber) {
        List<CommonSelectResDTO> commonSelectRes = new ArrayList<>();

        tableNames.forEach(tableName -> {

            List<CommonSelectList> commonSelectList = parmCommonSelfMapper.selectAll(tableName, columnName, tableId, null, organizationNumber);

            if (commonSelectList.isEmpty()) {
                logger.error("{}未查询到信息", tableName);
                return;
            }
            commonSelectList.forEach(common ->
                    common.setTableName(tableName)
            );

            List<CommonSelectResDTO> commonSelectResL = null;

            try {
                commonSelectResL = BeanMapping.copyList(commonSelectList, CommonSelectResDTO.class);
                commonSelectRes.addAll(commonSelectResL);
            } catch (Exception e) {
                logger.error("{}部分数据有空值", tableName);
            }

        });

        if (commonSelectRes.isEmpty()) {
            throw new AnyTxnParameterException(AnyTxnParameterRespCodeEnum.D_GET_IS_NULL_FAULT);
        }


        return commonSelectRes;
    }

    /**
     * 公共下拉查询去重，
     *
     * @param tableName          表名
     * @param columnName、tableId 列名
     * @return HttpApiResponse<ArrayList < CommonSelectRes>>
     */
    @Override
    public List<CommonSelectResDTO> findAllDuplicate(String tableName, String columnName, String tableId, String organizationNumber) {
        List<CommonSelectList> allDuplicate = new ArrayList<>();
        String proInfoTableName = "PARM_PRODUCT_INFO";
        List<ParmAcctProductMainInfo> acctProductMainInfos;
        if (!ObjectUtils.isEmpty(tableName)) {
            if (proInfoTableName.equals(tableName) || "parm_product_info".equals(tableName)) {
                acctProductMainInfos = parmAcctProductMainInfoSelfMapper.selectByOrgNum(organizationNumber);
                if (!CollectionUtils.isEmpty(acctProductMainInfos)){
                    for (ParmAcctProductMainInfo acctProductMainInfo : acctProductMainInfos){
                        CommonSelectList commonSelectList = new CommonSelectList();
                        commonSelectList.setTableName(proInfoTableName);
                        commonSelectList.setId(acctProductMainInfo.getProductNumber());
                        if (!ObjectUtils.isEmpty(acctProductMainInfo)) {
                            commonSelectList.setName(acctProductMainInfo.getDescription());
                        }
                        if (!allDuplicate.contains(commonSelectList)) {
                            allDuplicate.add(commonSelectList);
                        }
                    }
                }
            } else {
                allDuplicate = parmCommonSelfMapper.findAllDuplicate(tableName, tableId, columnName, organizationNumber);
            }
        }
        if (allDuplicate.isEmpty()) {
            logger.error("未查询到信息");
//            throw new AnyTxnParameterException(AnyTxnParameterRespCodeEnum.D_GET_IS_NULL_FAULT);
        }
        List<CommonSelectResDTO> commonSelectRes = null;
        try {
            commonSelectRes = BeanMapping.copyList(allDuplicate, CommonSelectResDTO.class);
        } catch (Exception e) {
            logger.error("部分数据有空值");
            throw new AnyTxnParameterException(AnyTxnParameterRespCodeEnum.D_QUERY_COMMON_SELECT_FAULT);
        }
        //TODO  这里id和name被改来改去，究竟应该用哪个。id要注意有null的情况 to peidong
        return commonSelectRes.stream().collect(
                Collectors.collectingAndThen(
                        Collectors.toCollection(() -> new TreeSet<>(Comparator.comparing(CommonSelectResDTO::getId,Comparator.nullsFirst(Comparator.naturalOrder())))), ArrayList::new));
    }
     /**
          * @Description 根据机构号,列，列值动态判断是否存在表数据
          * @Method
          * @Params
          * @Return
          * @Date 2021/2/3
          **/
     @Override
     public int isExist(String tableName,String orgNumber,String columnName ,String columnValue){
        return parmCommonSelfMapper.isExists(tableName,orgNumber,columnName,columnValue);
     }
}
