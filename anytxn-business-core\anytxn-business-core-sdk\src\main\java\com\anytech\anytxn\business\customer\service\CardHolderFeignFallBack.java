package com.anytech.anytxn.business.customer.service;

import com.anytech.anytxn.business.base.card.domain.dto.AccountOpeningDTO;
import com.anytech.anytxn.business.base.card.domain.dto.AutoEbitSignUpInforDTO;
import com.anytech.anytxn.business.base.card.domain.dto.CardAuthorizationDTO;
import com.anytech.anytxn.business.base.customer.domain.dto.CustomerAddressInfoDTO;
import com.anytech.anytxn.business.base.customer.domain.dto.CustomerAuthorizationInfoDTO;
import com.anytech.anytxn.business.base.customer.domain.dto.CustomerBasicInfoDTO;
import com.anytech.anytxn.common.core.constants.RespCodePrefix;
import com.anytech.anytxn.common.core.base.AnyTxnHttpResponse;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import org.springframework.stereotype.Component;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * @author: sumingyue
 * @create: 2020/07/13 14:59
 */
@Component
@RequestMapping({"/anytxn/v2/api"})
public class CardHolderFeignFallBack {

    @PostMapping(path = "/cardholder/openCardSubsidiaryCustomer")
    public AnyTxnHttpResponse<Void> openCardSubsidiaryCustomer(AccountOpeningDTO record, @RequestHeader("cid") String cid, @RequestHeader("organizationNumber") String organizationNumber) {
        return AnyTxnHttpResponse.fail(RespCodePrefix.PREFIX_CARDHOLDER_S, "GATEWAY项目服务不可用");
    }

    @GetMapping("/cardholder/basicinfos/{organizationNumber}/{customerId}")
    public AnyTxnHttpResponse<CustomerBasicInfoDTO> getCustomerBasicInfoByOrgAndCid(
            @Parameter(name = "organizationNumber", description = "组织机构号")@PathVariable("organizationNumber") String organizationNumber,
            @Parameter(name = "customerId", description = "客户id")@PathVariable("customerId") String customerId,
            @RequestHeader("cid") String cid,
            @RequestHeader("organizationNumber") String organizationNumberHeader){
        return AnyTxnHttpResponse.fail(RespCodePrefix.PREFIX_CARDHOLDER_S, "GATEWAY项目服务不可用");
    }

    @GetMapping("/cardholder/addr/{organizationNumber}/{customerId}/{type}")
    AnyTxnHttpResponse<CustomerAddressInfoDTO> findCustomerAddressByOrgCidAndType(
            @Parameter(name = "organizationNumber", description = "组织机构号")@PathVariable("organizationNumber") String organizationNumber,
            @Parameter(name = "customerId", description = "客户id")@PathVariable("customerId") String customerId,
            @Parameter(name = "type", description = "type")@PathVariable("type") String type,
            @RequestHeader("cid") String cid,
            @RequestHeader("organizationNumber") String organizationNumberHeader){
        return AnyTxnHttpResponse.fail(RespCodePrefix.PREFIX_CARDHOLDER_S, "GATEWAY项目服务不可用");

    }

    @GetMapping("/cardholder/authorinfosById/{organizationNumber}/{customerId}")
    AnyTxnHttpResponse<CustomerAuthorizationInfoDTO> getCustomerAuthInfoOrgAndCid(
            @Parameter(name = "organizationNumber", description = "组织机构号")@PathVariable("organizationNumber") String organizationNumber,
            @Parameter(name = "customerId", description = "客户id")@PathVariable("customerId") String customerId,
            @RequestHeader("cid") String cid,
            @RequestHeader("organizationNumber") String organizationNumberHeader){
        return AnyTxnHttpResponse.fail(RespCodePrefix.PREFIX_CARDHOLDER_S, "GATEWAY项目服务不可用");

    }

    @GetMapping("/card/feign/authorizations")
    public AnyTxnHttpResponse<List<CardAuthorizationDTO>> findCardAuthorizationsByFeign(
            @Parameter(name = "organizationNumber", description = "组织机构号")@RequestParam("organizationNumber") String organizationNumber,
            @Parameter(name = "customerId", description = "客户id")@RequestParam("customerId") String customerId,
            @RequestHeader("cid") String cid,
            @RequestHeader("organizationNumber") String organizationNumberHeader) {
        return AnyTxnHttpResponse.fail(RespCodePrefix.PREFIX_CARDHOLDER_S, "GATEWAY项目服务不可用");
    }

    @Operation(summary = "卡片授权信息详细查询")
    @GetMapping("/card/authorization/detail/{cardNumber}")
    AnyTxnHttpResponse<CardAuthorizationDTO> queryCardAuthInfoDetail(
            @Parameter(name = "cardNumber", description = "卡号")@PathVariable("cardNumber") String cardNumber,
            @RequestHeader("bcard") String bcard,@RequestHeader("organizationNumber") String organizationNumber){
        return AnyTxnHttpResponse.fail(RespCodePrefix.PREFIX_CARDHOLDER_S, "GATEWAY项目服务不可用");
    }


    @Operation(summary = "约定扣款签约信息")
    @PostMapping(path = "/account/updateAutoInfo")
    public AnyTxnHttpResponse batchTransactionEntry(@RequestBody AutoEbitSignUpInforDTO autoEbitSignUpInforDTO) {
        return AnyTxnHttpResponse.fail(RespCodePrefix.PREFIX_CARDHOLDER_S, "GATEWAY项目服务不可用");
    }


}
