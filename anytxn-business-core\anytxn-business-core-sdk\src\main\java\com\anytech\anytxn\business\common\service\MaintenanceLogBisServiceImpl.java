package com.anytech.anytxn.business.common.service;

import com.anytech.anytxn.common.core.utils.BeanMapping;
import com.anytech.anytxn.common.core.utils.LoginUserUtils;
import com.anytech.anytxn.business.base.customer.enums.DeleteIndicatorEnum;
import com.anytech.anytxn.business.base.customer.domain.dto.CustomerAddressInfoDTO;
import com.anytech.anytxn.business.dao.customer.mapper.MaintenanceLogMapper;
import com.anytech.anytxn.business.dao.customer.mapper1.ParmSysCodeTypeBisMapper;
import com.anytech.anytxn.business.dao.customer.model.CustomerAddressInfo;
import com.anytech.anytxn.business.dao.customer.model.MaintenanceLog;
import com.anytech.anytxn.business.dao.customer.model.ParmSysCode;
import com.anytech.anytxn.business.base.common.domain.dto.AnytxnLogDTO;
import com.anytech.anytxn.business.base.common.constants.MaintenanceConstant;
import com.anytech.anytxn.business.base.common.domain.dto.MaintenanceLogDTO;
import com.anytech.anytxn.business.base.common.service.IMaintenanceLogBisService;
import com.anytech.anytxn.common.core.utils.TenantUtils;
import com.anytech.anytxn.common.sequence.utils.SequenceIdGen;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.lang.reflect.Field;
import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.Objects;

/**
 * 维护日志表业务逻辑层
 * <AUTHOR>
 * @date 2019-11-23
 */
@Service
public class MaintenanceLogBisServiceImpl implements IMaintenanceLogBisService {

    private static Logger log = LoggerFactory.getLogger(MaintenanceLogBisServiceImpl.class);

    @Autowired
    private MaintenanceLogMapper maintenanceLogMapper;
    @Autowired
    private ParmSysCodeTypeBisMapper parmSysCodeTypeMapper;
    @Autowired
    private SequenceIdGen sequenceIdGen;


    @Override
    public  <T> void insertMaintenanceLog(T newObj, T oldObj, LocalDate nextProcessingDay,
                                          String cardNumber,String table) {

        LocalDateTime nextProcessingDay1 = nextProcessingDay.atTime(0, 0);
        //添加卡片基础信息的业务维护日志
        if(Objects.nonNull(newObj) && Objects.nonNull(oldObj)){
            MaintenanceLogDTO basicMaintenanceLog = new MaintenanceLogDTO();
            basicMaintenanceLog.setOperationTimestamp(nextProcessingDay1);
            basicMaintenanceLog.setPrimaryKeyValue(cardNumber);
            basicMaintenanceLog.setOperationType(MaintenanceConstant.OPERATION_U);
            basicMaintenanceLog.setTransactionDataType(MaintenanceConstant.DATA_P);
            basicMaintenanceLog.setOperatorId(LoginUserUtils.getLoginUserName());
            basicMaintenanceLog.setUpdateBy(LoginUserUtils.getLoginUserName());
            this.add(basicMaintenanceLog, newObj, oldObj, table);
        }
    }

    /**
     * 新建维护日志表
     * @param maintenanceLogDTO 维护日志请求参数
     * @param obj1 新值
     * @param obj2 原值
     * @param tableName
     * @param <T>
     */
    @Override
    public <T> void add(MaintenanceLogDTO maintenanceLogDTO,T obj1, T obj2,String tableName) {
        if (null == maintenanceLogDTO){
            log.error("维护日志请求参数不能为空");
            return;
        }

        if (null != maintenanceLogDTO.getId()){
            return;
        }
        List<AnytxnLogDTO> list = new ArrayList();
        try {
            list = compare(obj1,obj2);
        } catch (IllegalAccessException e) {
            log.error("新建维护日志比较异常");
        }
        log.info("更新日志信息，更新表：{}，对比结果：{}", tableName, list);
        for (int i = 0;i<list.size();i++){
            String name = humpToUnderline(list.get(i).getFiled());
            String newResult = list.get(i).getNewResult();
            String oldResult = list.get(i).getOldResult();

            ParmSysCode parmSysCode = parmSysCodeTypeMapper.selectByTypeIdAndTypeName(tableName,name);

            MaintenanceLog maintenanceLog = BeanMapping.copy(maintenanceLogDTO, MaintenanceLog.class);
            maintenanceLog.setId(Long.parseLong(sequenceIdGen.generateId(TenantUtils.getTenantId())));
            maintenanceLog.setDeleteIndicator(DeleteIndicatorEnum.NOT_DELETE.getCode());
            maintenanceLog.setCreateTime(LocalDateTime.now());
            maintenanceLog.setUpdateTime(LocalDateTime.now());
            //存储修改的表
            maintenanceLog.setMemo(tableName);
            //如果是地址信息，则需要记录地址类型
            if (obj1 instanceof CustomerAddressInfo) {
                //由于创建地址时旧地址为空，删除地址时新地址为空，所以取type时需要判断
                maintenanceLog.setSourceId(StringUtils.isBlank(((CustomerAddressInfo) obj1).getType()) ? ((CustomerAddressInfo) obj2).getType() : ((CustomerAddressInfo) obj1).getType());
            }
            //创建地址信息时使用的是CustomerAddressInfoDTO类型
            if (obj1 instanceof CustomerAddressInfoDTO) {
                maintenanceLog.setSourceId(StringUtils.isBlank(((CustomerAddressInfoDTO) obj1).getType()) ? ((CustomerAddressInfoDTO) obj2).getType() : ((CustomerAddressInfoDTO) obj1).getType());
            }
            //todo
            maintenanceLog.setUpdateBy(LoginUserUtils.getLoginUserName());
            if(parmSysCode != null){
                maintenanceLog.setColumnName(parmSysCode.getCodeDesc());
            }else{
                maintenanceLog.setColumnName(name);
                //continue;
            }
            maintenanceLog.setVersionNumber(1L);
            maintenanceLog.setOriginalValue(oldResult);
            maintenanceLog.setUpdatedValue(newResult);

            try {
                maintenanceLogMapper.insert(maintenanceLog);
            } catch (Exception e) {
                log.error("新建维护日志表插入数据库失败",e);
            }
        }

    }


    /**
     * 必输项检查
     * @param maintenanceLogDTO 维护日志请求参数
     */
    private void checkRequired(MaintenanceLogDTO maintenanceLogDTO){
        if (StringUtils.isEmpty(maintenanceLogDTO.getOperatorId())){
            log.error("操作员id必输");
            //throw new AnyTxnCardholderException(AnyTxnCardholderResCode.S_MAINTENANACELOG_OPERATORID, "操作员id必输");
        }
        if (StringUtils.isEmpty(maintenanceLogDTO.getOperationType())){
            log.error("操作类型必输");
            //throw new AnyTxnCardholderException(AnyTxnCardholderResCode.S_MAINTENANACELOG_OPERATIONTYPE, "操作类型必输");
        }
        if (StringUtils.isEmpty(maintenanceLogDTO.getTransactionDataType())){
            log.error("业务数据类型必输");
            //throw new AnyTxnCardholderException(AnyTxnCardholderResCode.S_MAINTENANACELOG_TRANSACTIODATATYPE, "业务数据类型必输");
        }
        if (StringUtils.isEmpty(maintenanceLogDTO.getColumnName())){
            log.error("字段名称必输");
            //throw new AnyTxnCardholderException(AnyTxnCardholderResCode.S_MAINTENANACELOG_CLOLUMNNAME, "字段名称必输");
        }
        if (StringUtils.isEmpty(maintenanceLogDTO.getUpdatedValue())){
            log.error("更新后的值必输");
            //throw new AnyTxnCardholderException(AnyTxnCardholderResCode.S_MAINTENANACELOG_UPDATEDVALUE, "更新后的值必输");
        }
        if (StringUtils.isEmpty(maintenanceLogDTO.getPrimaryKeyValue())){
            log.error("主键值必输");
            //throw new AnyTxnCardholderException(AnyTxnCardholderResCode.S_MAINTENANACELOG_PRIIMARYKEYVALUE, "主键值必输");
        }
    }


    private static <T>  List<AnytxnLogDTO> compare(T obj1, T obj2) throws IllegalAccessException {
        String[] strings = {"createTime","updateTime","updateBy","versionNumber","blockCodeSetDate",
                "previousBlockCodeSetDate","blockCodeDate","previousBlockDate",
                "accountStatusSetDate","financeStatus","financeStatusSetDate","previousFinanceStatus",
                "previousFinanceStatusDate","previousProductNumber","transferEffectiveDate",
                "lastStatementDate","previousStatementDate","statementDueAmount",
                "totalGracePaymentAmount","lastCycleCreditAdjAmount","cycleDue",
                "lastAgedDate","pastDueCount","day30DueCount","day60DueCount","day90DueCount",
                "day120DueCount","day150DueCount","day180DueCount","day210DueCount","chargeOffReason","chargeOffDate",
                "lastCycleCreditAdjAmount","lastAgedDate","paymentHistory",
                "inCollectionIndicator","partitionKey","CUSTOMER_ID","ORGANIZATION_NUMBER","ID"};

        List<AnytxnLogDTO> result = new ArrayList<>();

        Field[] fs = obj1.getClass().getDeclaredFields();
        for (Field f : fs) {
            f.setAccessible(true);
            Object v1 = f.get(obj1);
            Object v2 = f.get(obj2);
            if( ! equals(v1, v2) ){
                String v1s = v1==null?"":v1.toString();
                String v2s = v2==null?"":v2.toString();
                if(Arrays.asList(strings).contains(f.getName()) || v1s.equals(v2s)){
                    continue;
                }
                AnytxnLogDTO anytxnLogDTO = new AnytxnLogDTO();
                anytxnLogDTO.setFiled(f.getName());
                anytxnLogDTO.setNewResult(v1==null?"":v1.toString());
                anytxnLogDTO.setOldResult(v2==null?"":v2.toString());
                result.add(anytxnLogDTO);
            }
        }
        return result;
    }

    private static boolean equals(Object obj1, Object obj2) {
        if(obj1 instanceof BigDecimal && obj2 instanceof BigDecimal){
            if(((BigDecimal) obj1).compareTo((BigDecimal)obj2) == 0){
                return true;
            }else{
                return false;
            }
        }else {

            if (obj1 == obj2) {
                return true;
            }
            if (obj1 == null || obj2 == null) {
                return false;
            }
            return obj1.equals(obj2);
        }
    }

    /***
     * 驼峰命名转为下划线命名
     *
     * @param para
     *        驼峰命名的字符串
     */

    private static String humpToUnderline(String para){
        StringBuilder sb=new StringBuilder(para);
        // 定位
        int temp=0;
        if (!para.contains("_")) {
            for(int i=0;i<para.length();i++){
                if(Character.isUpperCase(para.charAt(i))){
                    sb.insert(i+temp, " ");
                    temp+=1;
                }
            }
        }
        return sb.toString().toUpperCase();
    }


    public static void main(String[] args) {
        MaintenanceLogDTO maintenanceLogDTO  = new MaintenanceLogDTO();
        maintenanceLogDTO.setColumnName("1");
        maintenanceLogDTO.setId(1L);

        MaintenanceLogDTO maintenanceLogDto1  = new MaintenanceLogDTO();
        maintenanceLogDto1.setColumnName("2");
        maintenanceLogDto1.setId(2L);
    }

}
