package com.anytech.anytxn.business.monetary.service;

import com.anytech.anytxn.business.base.monetary.domain.bo.*;
import com.anytech.anytxn.common.core.base.BaseEntity;
import com.anytech.anytxn.business.base.monetary.service.ICustAccountJdbcService;
import com.anytech.anytxn.common.core.utils.BeanMapping;
import com.anytech.anytxn.business.base.monetary.annotation.InsertCacheAnnotation;
import com.anytech.anytxn.business.base.monetary.annotation.UpdateCacheAnnotation;
import com.anytech.anytxn.business.base.monetary.enums.AnyTxnCustAccountRespCodeEnum;
import com.anytech.anytxn.business.base.monetary.exception.AnyTxnCustAccountException;
import com.anytech.anytxn.business.base.monetary.utils.ReflectSqlUtils;
import com.anytech.anytxn.business.base.monetary.domain.dto.SqlCacheDTO;
import com.anytech.anytxn.business.base.customer.domain.dto.CustomerAuthorizationInfoDTO;
import com.anytech.anytxn.business.base.customer.domain.dto.CustomerBasicInfoDTO;
import com.anytech.anytxn.business.dao.customer.mapper.CustomerAuthorizationInfoMapper;
import com.anytech.anytxn.business.dao.customer.mapper.CustomerBasicInfoMapper;
import com.anytech.anytxn.business.dao.customer.model.CustomerAuthorizationInfo;
import com.anytech.anytxn.business.dao.customer.model.CustomerBasicInfo;
import com.anytech.anytxn.business.dao.installment.mapper.InstallmentLimitUnitCrossSelfMapper;
import com.anytech.anytxn.business.dao.installment.model.InstallmentLimitUnitCross;
import com.anytech.anytxn.business.base.installment.domain.dto.InstallmentLimitUnitCrossDTO;
import com.anytech.anytxn.business.base.limit.domain.dto.LimitCustCreditInfoDTO;
import com.anytech.anytxn.business.base.limit.domain.dto.LimitCustUsedInfoDTO;
import com.anytech.anytxn.business.base.limit.domain.dto.LimitSynRequestLogDTO;
import com.anytech.anytxn.business.base.limit.domain.dto.PartnerMarginDTO;
import com.anytech.anytxn.business.dao.limit.mapper.LimitCustCreditInfoSelfMapper;
import com.anytech.anytxn.business.dao.limit.mapper.PartnerMarginMapper;
import com.anytech.anytxn.business.dao.limit.model.LimitCustCreditInfo;
import com.anytech.anytxn.business.dao.limit.model.PartnerMargin;
import com.anytech.anytxn.business.base.transaction.domain.dto.RejectedTransactionDTO;
import com.anytech.anytxn.common.core.utils.TenantUtils;
import jakarta.annotation.PostConstruct;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.jdbc.core.namedparam.NamedParameterJdbcTemplate;
import org.springframework.jdbc.core.namedparam.SqlParameterSourceUtils;
import org.springframework.stereotype.Service;

import java.lang.reflect.Method;
import java.lang.reflect.Proxy;
import java.util.*;

/**
 * 目前支持bo抽取需要批量插入、更新的数据通过NamedParameterJdbcTemplate执行批量sql
 * 批量不支持部分更新操作
 * <p>
 * 后续有时间可加强封装成一个工具类放到common工程
 *
 * <AUTHOR>
 * @date 2020/7/16
 */
@Slf4j
@Service
public class CustAccountWriterService {

    @Autowired
    private NamedParameterJdbcTemplate bizJdbcTemplate;
    @Autowired
    private CustomerBasicInfoMapper customerBasicInfoMapper;
    @Autowired
    private CustomerAuthorizationInfoMapper customerAuthorizationInfoMapper;
    @Autowired
    private CustReconciliationControlServiceImpl custReconciliationControlService;
    @Autowired
    private LimitCustCreditInfoSelfMapper limitCustCreditInfoSelfMapper;

    private ICustAccountJdbcService custAccountJdbcService;

    @Autowired
    private InstallmentLimitUnitCrossSelfMapper installmentLimitUnitCrossSelfMapper;

    @Autowired
    private PartnerMarginMapper partnerMarginMapper;

    /**
     * sql缓存，初始化完成后只读
     */
    private Map<String, Map<String, SqlCacheDTO>> cache = new HashMap<>(4);

    @PostConstruct
    public void init() {
        this.custAccountJdbcService = (ICustAccountJdbcService) Proxy.newProxyInstance(
                ICustAccountJdbcService.class.getClassLoader(),
                new Class[]{ICustAccountJdbcService.class},
                new JdbcServiceProxy(bizJdbcTemplate));
        log.info("初始化custAccount代理jdbcService完成...");

        Class[] clazzs = new Class[]{CustAccountBO.class, AuthBO.class, BalanceBO.class, CardBO.class, CustomerBO.class, InstallBO.class, LimitBO.class, RecordedBO.class};

        for (Class clazz : clazzs) {
            cache.put(clazz.getName(), ReflectSqlUtils.getSql(clazz));
        }
        log.info("初始化构建缓存sql完成...");
        log.info("已处理类: {}", clazzs);
    }


    /**
     * custAccountBo存储（不含参数表）
     *
     * @param custAccountBO
     */
    public void writer(CustAccountBO custAccountBO) {
        // 更新客户基础表
        CustomerBasicInfoDTO customerBasicInfoDTO = custAccountBO.getCustomerBasicInfoDTO();
        if (customerBasicInfoDTO != null) {
            customerBasicInfoMapper.updateByPrimaryKeySelective(BeanMapping.copy(customerBasicInfoDTO, CustomerBasicInfo.class));
        }
        // 更新客户授权表
        CustomerAuthorizationInfoDTO customerAuthorizationInfo = custAccountBO.getCustomerAuthorizationInfo();
        if (custAccountBO.isUpdateCustomerAuthorizationInfo() && customerAuthorizationInfo != null) {
            customerAuthorizationInfoMapper.updateByPrimaryKeySelective(BeanMapping.copy(customerAuthorizationInfo, CustomerAuthorizationInfo.class));
        }
        // 更新客户授权表
        PartnerMarginDTO partnerMargin = custAccountBO.getPartnerMarginDTO();
        if (Objects.nonNull(partnerMargin)) {
            partnerMarginMapper.updateByPrimaryKeySelective(BeanMapping.copy(partnerMargin, PartnerMargin.class));
        }
        // 代理方式更新bo中需要更新的表
        custAccountBO.getCustomerBO().sortUpdateBalanceInfoIdsForInterest();
        writeCache(custAccountBO);
        writeCache(custAccountBO.getAuthBO());
        writeCache(custAccountBO.getBalanceBO());
        writeCache(custAccountBO.getCardBO());
        writeCache(custAccountBO.getCustomerBO());
        writeCache(custAccountBO.getInstallBO());
        //部分sql不用反射，手写sql
        //额度部分
        writeLimitCustUsedInfo(custAccountBO.getLimitBO());
        writeLimit(custAccountBO.getLimitBO());

        //压测不需要执行快照表
//        writeCache(custAccountBO.getCustomerAccountAdditionInfoBO());
        writeCache(custAccountBO.getRecordedBO(), custAccountBO.getRecordedBO().getExcludedSet());


        // 更新客户对账表
        custReconciliationControlService.commitLock(custAccountBO.getCustReconciliationControl());
    }

    private void writeLimit(LimitBO limitBO) {
        List<LimitCustCreditInfoDTO> updateLimitCustCreditInfos = limitBO.getUpdateLimitCustCreditInfos();
        if (updateLimitCustCreditInfos != null && updateLimitCustCreditInfos.size() > 0) {

            int i = limitCustCreditInfoSelfMapper.batchUpdateLimitCustCredit(BeanMapping.copyList(updateLimitCustCreditInfos, LimitCustCreditInfo.class));
            log.info("writeLimit更新条数" + i);
        }

        List<LimitCustCreditInfoDTO> limitCustCreditInfoDTOList = limitBO.getInsertLimitCustCreditInfos();
        if (limitCustCreditInfoDTOList != null && limitCustCreditInfoDTOList.size() > 0) {

            int i = limitCustCreditInfoSelfMapper.batchInsert(BeanMapping.copyList(limitCustCreditInfoDTOList, LimitCustCreditInfo.class));
            log.info("writeLimit更新条数" + i);
        }

        /**
         * 插入额度管控单元
         */
        List<InstallmentLimitUnitCrossDTO> installmentLimitUnitCrossList = limitBO.getInsertInstallmentLimitUnitCross();
        if (installmentLimitUnitCrossList != null && installmentLimitUnitCrossList.size() > 0) {
            int i = installmentLimitUnitCrossSelfMapper.insertBatchInstallLimitUnitCross(BeanMapping.copyList(installmentLimitUnitCrossList, InstallmentLimitUnitCross.class));
            log.info("插入额度管控单元条数" + i);
        }
    }

    /**
     * 批量插入用户额度用信表
     * 或者批量更新用户额度用信表
     */
    public void writeLimitCustUsedInfo(LimitBO limitBO) {
        if (limitBO == null) {
            return;
        }

        List<LimitCustUsedInfoDTO> updateLimitCustUsedInfos = limitBO.getUpdateLimitCustUsedInfos();

        if (CollectionUtils.isNotEmpty(updateLimitCustUsedInfos)) {
            StringBuilder sql = new StringBuilder();
            sql.append("UPDATE limit_cust_used_info set UPDATE_TIME = :updateTime, VERSION_NUMBER = VERSION_NUMBER + 1,");
            sql.append(" LIMIT_USED_AMOUNT = :limitUsedAmount, FROZEN_LIMIT_AMOUNT = :frozenLimitAmount ");
            sql.append("WHERE TENANT_ID = ");
            sql.append(TenantUtils.getTenantId());
            sql.append(" and LIMIT_USED_ID =:limitUsedId and VERSION_NUMBER =:versionNumber");

            bizJdbcTemplate.batchUpdate(sql.toString(), SqlParameterSourceUtils.createBatch(updateLimitCustUsedInfos.toArray()));
        }

        List<LimitCustUsedInfoDTO> insertLimitCustUsedInfos = limitBO.getInsertLimitCustUsedInfos();

        if (CollectionUtils.isNotEmpty(insertLimitCustUsedInfos)) {
            StringBuilder sql = new StringBuilder();
            sql.append("INSERT INTO limit_cust_used_info ( ");
            sql.append("TENANT_ID,");
            sql.append("LIMIT_USED_ID, ORGANIZATION_NUMBER, CUSTOMER_ID, LIMIT_CREDIT_ID, LIMIT_DATA_CURRENCY, CREATE_TIME, UPDATE_TIME, UPDATE_BY ,");
            sql.append("VERSION_NUMBER, LIMIT_TYPE_CODE, LIMIT_USED_AMOUNT, ACCOUNT_PRODUCT_CODE, FROZEN_LIMIT_AMOUNT ");
            sql.append(") VALUES ( ");
            sql.append(TenantUtils.getTenantId());
            sql.append(", :limitUsedId, :organizationNumber, :customerId, :limitCreditId, :limitDataCurrency, ");
            sql.append(" :createTime, :updateTime, :updateBy, :versionNumber, :limitTypeCode, :limitUsedAmount, :accountProductCode, :frozenLimitAmount ");
            sql.append(")");
            bizJdbcTemplate.batchUpdate(sql.toString(), SqlParameterSourceUtils.createBatch(insertLimitCustUsedInfos.toArray()));
        }

        //插入额度流水表
        List<LimitSynRequestLogDTO> insertLimitSynRequestLogs = limitBO.getInsertLimitSynRequestLog();
        if (CollectionUtils.isNotEmpty(insertLimitSynRequestLogs)) {
            StringBuilder sql = new StringBuilder();
            sql.append("INSERT INTO LIMIT_SYN_REQUEST_LOG (LIMIT_UPDATE_LOG_ID, GLOBAL_FLOW_NUMBER, ")
                    .append("TENANT_ID,ORGANIZATION_NUMBER, CUSTOMER_ID, ")
                    .append("ACCOUNT_MANAGEMENT_ID, TRANSACTION_BALANCE_ID, ")
                    .append("POSTED_TRANSACTION_ID, OUTSTANDING_TRANSACTION_ID, ")
                    .append("INSTALMENT_ORDER_ID, LIMIT_UNIT_ID, ")
                    .append("UNIT_VERSION_NUMBER, LIMIT_TYPE_CODE, ")
                    .append("DEBIT_CREDIT_FLAG, USED_AMOUNT, ")
                    .append("USED_CURRENCY, PROCESSED_FLAG, ")
                    .append("REQUEST_TYPE, REQUEST_SOURCE, ")
                    .append("CREATE_TIME, UPDATE_TIME, ")
                    .append("UPDATE_BY, VERSION_NUMBER, ")
                    .append("USED_AMOUNT_SUM, OVERLIMIT_SEQUENCE_NUMBER, ")
                    .append("CARD_NUMBER,AMOUNT_TYPE,ACCOUNT_PRODUCT_CODE) ")
                    .append(" VALUES (:limitUpdateLogId, :globalFlowNumber,")
                    .append(TenantUtils.getTenantId())
                    .append(", :organizationNumber, :customerId, :accountManagementId, :transactionBalanceId, :postedTransactionId, :outstandingTransactionId, :instalmentOrderId, :limitUnitId, :unitVersionNumber, :limitTypeCode, :debitCreditFlag, :usedAmount, :usedCurrency, :processedFlag, :requestType, :requestSource, :createTime, :updateTime, :updateBy, :versionNumber, :usedAmountSum, :overlimitSequenceNumber, :cardNumber, :amountType, :accountProductCode)");
            bizJdbcTemplate.batchUpdate(sql.toString(), SqlParameterSourceUtils.createBatch(insertLimitSynRequestLogs.toArray()));

        }
    }


    /**
     * 入账拒绝写入，仅写入入账拒绝
     *
     * @param custAccountBO
     */
    public void rejectedWriter(CustAccountBO custAccountBO) {
        List<RejectedTransactionDTO> insertRejectedTransactions = custAccountBO.getRecordedBO().getInsertRejectedTransactions();

        CustAccountBO newBo = new CustAccountBO();
        insertRejectedTransactions.forEach(x -> newBo.getRecordedBO().insertRejectedTransactionList(x));

        writeCache(newBo.getRecordedBO());
    }

    /**
     * 缓存写入
     *
     * @param obj 承载数据的对象，可承载多个数据
     */
    public void writeCache(Object obj) {
        writeCache(obj, null);
    }

    private void writeCache(Object obj, Set<String> excludedSet) {
        // 遍历所有方法，根据注解获取更新数据(方法必须是public!!!)
        Method[] methods = obj.getClass().getMethods();
        for (Method method : methods) {
//            log.debug("method.getName() = "+ method.getName());
            if (excludedSet != null && excludedSet.contains(method.getName())) {
                log.debug("跳过method.getName() = " + method.getName());
                continue;
            }
//            log.debug("开始执行method.getName() = "+ method.getName());
            InsertCacheAnnotation insertCache = method.getAnnotation(InsertCacheAnnotation.class);
            UpdateCacheAnnotation updateCache = method.getAnnotation(UpdateCacheAnnotation.class);
            // 需要进行插入操作
            if (insertCache != null) {
                doBatch(method, obj);
            } else if (updateCache != null) {
                doBatch(method, obj);
            }
        }
    }


    /**
     * 批量操作数据
     *
     * @param method 获取批量插入的数据的方法
     * @param obj    承载数据的对象，通过method.invoke(obj)获取数据
     */
    public void doBatch(Method method, Object obj) {
        // 获取需要插入的数据列表(结果必须是list类型数据)
        Object data = null;
        try {
            data = method.invoke(obj);
        } catch (Exception e) {
            log.error("批量保存获取数据方法异常，方法名: {}", method.getName(), e);
            throw new AnyTxnCustAccountException(AnyTxnCustAccountRespCodeEnum.P_ERROR, "执行对象【" + obj.getClass().getSimpleName() + "】中获取数据方法【" + method.getName() + "】异常！", e);
        }

        // 空值不处理
        if (data == null) {
            return;
        }

        // 判断类型
        if (data instanceof List) {
            // 强转成列表对象
            List<?> c = ((List<?>) data);
            if (CollectionUtils.isNotEmpty(c)) {

                // 根据对象类型内部注解形成插入的sql
                Map<String, SqlCacheDTO> sqlCacheMap = cache.get(obj.getClass().getName());
                if (sqlCacheMap == null || !sqlCacheMap.containsKey(method.getName())) {
                    throw new AnyTxnCustAccountException(AnyTxnCustAccountRespCodeEnum.P_ERROR, "不支持批量jdbc操作对象类型【" + data.getClass() + "】");
                }

                SqlCacheDTO sqlCacheDTO = sqlCacheMap.get(method.getName());
                // 植入租户
                c.stream().forEach(item -> {
                    if (item instanceof BaseEntity) {
                        BaseEntity baseEntity = (BaseEntity) item;
                        baseEntity.setTenantId(TenantUtils.getTenantId());
                    }
                });
                custAccountJdbcService.commonBatch(c, sqlCacheDTO.getSql(), sqlCacheDTO.getDescription());
            }
            // 其他类抛出异常
        } else {
            throw new AnyTxnCustAccountException(AnyTxnCustAccountRespCodeEnum.P_ERROR, "不支持批量jdbc操作对象类型【" + data.getClass() + "】");
        }
    }
}
