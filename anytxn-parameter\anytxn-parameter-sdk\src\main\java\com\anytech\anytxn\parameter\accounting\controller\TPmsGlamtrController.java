package com.anytech.anytxn.parameter.accounting.controller;

import io.swagger.v3.oas.annotations.tags.Tag;
import io.swagger.v3.oas.annotations.Operation;
import com.anytech.anytxn.common.core.base.PageResultDTO;
import com.anytech.anytxn.common.core.base.AnyTxnHttpResponse;
import com.anytech.anytxn.common.core.base.BizBaseController;
import com.anytech.anytxn.parameter.base.accounting.domain.dto.TPmsGlamtrDTO;
import com.anytech.anytxn.parameter.base.accounting.service.ITPmsGlamtrService;
import com.anytech.anytxn.parameter.base.common.enums.ParameterRepDetailEnum;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

@Tag(name = "增值税率参数表")
@RestController
public class TPmsGlamtrController extends BizBaseController {

    @Autowired
    private ITPmsGlamtrService itPmsGlamtrService;

    @Operation(summary = "分页查询增值税率参数表")
    @GetMapping("/param/mtr/{page}/{rows}")
    public AnyTxnHttpResponse<PageResultDTO<TPmsGlamtrDTO>> page(@PathVariable("page") int page ,
                                                                 @PathVariable("rows") int rows,
                                                                 @RequestParam(value = "organizationNumber",required = false) String organizationNumber,
                                                                 @RequestParam(value = "txnCode",required = false) String txnCode,
                                                                 @RequestParam(value = "taxRate",required = false) String taxRate){
        PageResultDTO<TPmsGlamtrDTO> result = itPmsGlamtrService.page(page, rows,organizationNumber,txnCode,taxRate);
        return AnyTxnHttpResponse.success(result);
    }

    @Operation(summary = "查询增值税率参数表明细")
    @GetMapping("/param/mtr/id/{id}")
    public AnyTxnHttpResponse<TPmsGlamtrDTO> detail(@PathVariable(value = "id") String id){
        TPmsGlamtrDTO result = itPmsGlamtrService.detail(id);
        return AnyTxnHttpResponse.success(result);
    }

    @Operation(summary = "删除增值税率参数表")
    @DeleteMapping("/param/mtr/id/{id}")
    public AnyTxnHttpResponse<Object> remove(@PathVariable(value = "id") String id){
        return AnyTxnHttpResponse.success(itPmsGlamtrService.remove(id), ParameterRepDetailEnum.DEL.message());
    }

    @Operation(summary = "新增增值税率参数表")
    @PostMapping("/param/mtr/add")
    public AnyTxnHttpResponse<Object> add(@RequestBody TPmsGlamtrDTO data){
        return AnyTxnHttpResponse.success(itPmsGlamtrService.add(data),ParameterRepDetailEnum.CREATE.message());
    }


    @Operation(summary = "更新增值税率参数表")
    @PostMapping("/param/mtr/update")
    public AnyTxnHttpResponse<Object> update(@RequestBody TPmsGlamtrDTO data){
        return AnyTxnHttpResponse.success(itPmsGlamtrService.update(data),ParameterRepDetailEnum.UPDATE.message());
    }

}
