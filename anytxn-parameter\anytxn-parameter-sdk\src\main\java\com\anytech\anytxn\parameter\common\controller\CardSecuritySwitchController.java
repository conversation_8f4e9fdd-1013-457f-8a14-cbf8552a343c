package com.anytech.anytxn.parameter.common.controller;

import io.swagger.v3.oas.annotations.tags.Tag;
import io.swagger.v3.oas.annotations.Operation;
import com.anytech.anytxn.common.core.base.PageResultDTO;
import com.anytech.anytxn.common.core.base.AnyTxnHttpResponse;
import com.anytech.anytxn.common.core.base.BizBaseController;
import com.anytech.anytxn.parameter.base.common.domain.dto.CardSecuritySwitchReqDTO;
import com.anytech.anytxn.parameter.base.common.domain.dto.CardSecuritySwitchResDTO;
import com.anytech.anytxn.parameter.base.common.service.ICardSecuritySwitchService;
import com.anytech.anytxn.parameter.base.common.enums.ParameterRepDetailEnum;
import com.anytech.anytxn.parameter.base.common.exception.AnyTxnParameterException;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import jakarta.validation.Valid;
import java.util.ArrayList;

/**
 * 持卡人安全开关特别检查表
 *
 * <AUTHOR>
 * @date 2019-04-10
 **/
@RestController
@Tag(name = "持卡人安全开关特别检查")
public class CardSecuritySwitchController extends BizBaseController {

    @Autowired
    private ICardSecuritySwitchService cardSecuritySwitchService;

    /**
     * business需要的接口
     * 根据卡号
     * @param cardNumber 卡号
     * @return HttpApiResponse<ArrayList<CardSecuritySwitchResDTO>>
     */
    @GetMapping(value = "/param/cardSecuritySwitch/cardNumber/{cardNumber}")
    @Operation(summary = "根据卡号，查询持卡人安全开关特别检查")
    public AnyTxnHttpResponse<ArrayList<CardSecuritySwitchResDTO>> getCardSecuritySwitchsByCardNum(@PathVariable(value = "cardNumber") String cardNumber) {
        ArrayList<CardSecuritySwitchResDTO> cardSecuritySwitchResDtos = (ArrayList) cardSecuritySwitchService.findListByCardNum(cardNumber);
        return AnyTxnHttpResponse.success(cardSecuritySwitchResDtos);
    }

    @Operation(summary = "新增持卡人安全开关特别检查参数", description = "新增持卡人安全开关特别检查参数")
    @PostMapping(value = "/param/cardSecuritySwitch")
    public AnyTxnHttpResponse<CardSecuritySwitchResDTO> create(@Valid @RequestBody CardSecuritySwitchReqDTO cardSecuritySwitchReq){
        CardSecuritySwitchResDTO res;
        res = cardSecuritySwitchService.add(cardSecuritySwitchReq);
        return AnyTxnHttpResponse.success(res, ParameterRepDetailEnum.CREATE.message());
    }

    @Operation(summary = "删除持卡人安全开关特别检查参数", description = "删除持卡人安全开关特别检查参数")
    @DeleteMapping(value = "/param/cardSecuritySwitch/id/{id}")
    public AnyTxnHttpResponse<Boolean> remove(@PathVariable(value = "id") Long id) throws AnyTxnParameterException {
        Boolean deleted = cardSecuritySwitchService.remove(id);
        return AnyTxnHttpResponse.success(deleted,ParameterRepDetailEnum.DEL.message());

    }

    @Operation(summary = "修改持卡人安全开关特别检查参数", description = "修改持卡人安全开关特别检查参数")
    @PutMapping(value = "/param/cardSecuritySwitch")
    public AnyTxnHttpResponse<CardSecuritySwitchResDTO> modify(@Valid @RequestBody CardSecuritySwitchReqDTO cardSecuritySwitchReqDTO){
        CardSecuritySwitchResDTO cardSecuritySwitchRes = cardSecuritySwitchService.modify(cardSecuritySwitchReqDTO);
        return AnyTxnHttpResponse.success(cardSecuritySwitchRes,ParameterRepDetailEnum.UPDATE.message());

    }

    @Operation(summary = "分页查询持卡人安全开关特别检查参数", description = "分页查询持卡人安全开关特别检查参数")
    @GetMapping(value = "/param/cardSecuritySwitch/pageNum/{pageNum}/pageSize/{pageSize}")
    public AnyTxnHttpResponse<PageResultDTO<CardSecuritySwitchResDTO>> getPage(@PathVariable(value = "pageNum")Integer pageNum,
                                                                            @PathVariable(value = "pageSize")Integer pageSize){
        PageResultDTO<CardSecuritySwitchResDTO> pageResultDto = cardSecuritySwitchService.findPage(pageNum, pageSize);
        return AnyTxnHttpResponse.success(pageResultDto);

    }

    @Operation(summary = "根据id查询汇率参数", description = "根据id查询汇率参数")
    @GetMapping(value = "/param/cardSecuritySwitch/id/{id}")
    public AnyTxnHttpResponse<CardSecuritySwitchResDTO> getById(@PathVariable(value = "id") Long id){
        CardSecuritySwitchResDTO cardSecuritySwitchResDTO = cardSecuritySwitchService.findOne(id);
        return AnyTxnHttpResponse.success(cardSecuritySwitchResDTO);

    }
}
