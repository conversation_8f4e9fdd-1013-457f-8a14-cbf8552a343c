package com.anytech.anytxn.parameter.account.service;

import com.alibaba.fastjson.JSON;
import com.github.pagehelper.Page;
import com.github.pagehelper.PageHelper;
import com.anytech.anytxn.common.core.annotation.db.DeleteParameterAnnotation;
import com.anytech.anytxn.common.core.annotation.db.InsertParameterAnnotation;
import com.anytech.anytxn.common.core.annotation.db.UpdateParameterAnnotation;
import com.anytech.anytxn.business.base.audit.AbstractParameterService;
import com.anytech.anytxn.common.core.base.ParameterCompare;
import com.anytech.anytxn.business.base.audit.ParmModificationRecord;
import com.anytech.anytxn.common.core.utils.OrgNumberUtils;
import com.anytech.anytxn.common.core.base.PageResultDTO;
import com.anytech.anytxn.common.core.utils.BeanMapping;
import com.anytech.anytxn.common.sequence.utils.Number16IdGen;
import com.anytech.anytxn.common.core.utils.TenantUtils;
import com.anytech.anytxn.parameter.base.common.enums.AnyTxnParameterRespCodeEnum;
import com.anytech.anytxn.parameter.base.common.enums.ParameterRepDetailEnum;
import com.anytech.anytxn.parameter.base.common.exception.AnyTxnParameterException;
import com.anytech.anytxn.parameter.account.mapper.ParmLargeGraceInfoMapper;
import com.anytech.anytxn.parameter.account.mapper.ParmLargeGraceInfoSelfMapper;
import com.anytech.anytxn.parameter.base.account.domain.model.ParmLargeGraceInfo;
import com.anytech.anytxn.parameter.base.account.domain.dto.LargeGraceInfoReqDTO;
import com.anytech.anytxn.parameter.base.account.domain.dto.LargeGraceInfoResDTO;
import com.anytech.anytxn.parameter.base.account.service.ILargeGraceInfoService;
//import org.apache.shardingsphere.transaction.annotation.ShardingSphereTransactionType;
//import org.apache.shardingsphere.transaction.core.TransactionType;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.StringUtils;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 超长免息期 业务实现类
 * <AUTHOR>
 * @date 2020-02-07
 */
@Service(value = "parm_large_grace_info_serviceImpl")
public class LargeGraceInfoServiceImpl extends AbstractParameterService implements ILargeGraceInfoService {

    private final Logger log = LoggerFactory.getLogger(LargeGraceInfoServiceImpl.class);

    @Autowired
    private ParmLargeGraceInfoSelfMapper parmLargeGraceInfoSelfMapper;
    @Autowired
    private ParmLargeGraceInfoMapper parmLargeGraceInfoMapper;
    @Autowired
    private Number16IdGen numberIdGenerator;

    /**
     * 新建超长免息期参数记录
     * @param largeGraceInfoReqDTO 超长免息期参数
     */
    @Override
    @InsertParameterAnnotation(tableName = "parm_large_grace_info", tableDesc = "Long Grace Period")
    public ParameterCompare add(LargeGraceInfoReqDTO largeGraceInfoReqDTO) {
        log.debug("新建超长免息期参数记录");
        //必输项校验
        checkRequiredItem(largeGraceInfoReqDTO);
        //检查索引
        ParmLargeGraceInfo parmLargeGraceInfo = parmLargeGraceInfoSelfMapper.
                selectByIndex(largeGraceInfoReqDTO.getOrganizationNumber(), largeGraceInfoReqDTO.getTableId());
        if (null != parmLargeGraceInfo){
            log.error("根据索引查询超长免息期参数表信息，数据已存在, orgNum:{}, tableId:{}",
                    largeGraceInfoReqDTO.getOrganizationNumber(), largeGraceInfoReqDTO.getTableId());

            throw new AnyTxnParameterException(AnyTxnParameterRespCodeEnum.D_EXIST_PARM_LARGE_GRACE_INFO_FAULT);
        }
        ParmLargeGraceInfo largeGraceInfo = BeanMapping.copy(largeGraceInfoReqDTO, ParmLargeGraceInfo.class);
        largeGraceInfo.setId(String.valueOf(numberIdGenerator.generateId(TenantUtils.getTenantId())));
        try {
            return ParameterCompare.getBuilder().withAfter(largeGraceInfo).build(ParmLargeGraceInfo.class);
        } catch (Exception e) {
            log.error("新建超长免息期参数 插入数据库失败",e);

            throw new AnyTxnParameterException(AnyTxnParameterRespCodeEnum.D_INSERT_PARM_LARGE_GRACE_INFO_FAULT,e);
        }
    }

    /**
     * 修改超长免息期参数记录
     * @param largeGraceInfoReqDTO 超长免息期参数
     */
    @Override
    @UpdateParameterAnnotation(tableName = "parm_large_grace_info", tableDesc = "Long Grace Period")
    public ParameterCompare update(LargeGraceInfoReqDTO largeGraceInfoReqDTO) {
        log.debug("修改超长免息期参数记录");
        if (null == largeGraceInfoReqDTO){
            log.error("修改超长免息期参数 传入参数不能为空");

            throw new AnyTxnParameterException(AnyTxnParameterRespCodeEnum.P_PARAMETER_EMPTY_FAULT);
        }
        ParmLargeGraceInfo parmLargeGraceInfo = parmLargeGraceInfoMapper.selectByPrimaryKey(largeGraceInfoReqDTO.getId());
        if (null == parmLargeGraceInfo){
            log.error("修改超长免息期参数 通过主键未查询到数据, id:{}", largeGraceInfoReqDTO.getId());

            throw new AnyTxnParameterException(AnyTxnParameterRespCodeEnum.D_QUERY_PARM_LARGE_GRACE_INFO_BY_ID_FAULT);
        }
        ParmLargeGraceInfo largeGraceInfo = BeanMapping.copy(largeGraceInfoReqDTO, ParmLargeGraceInfo.class);
        return ParameterCompare.getBuilder().withAfter(largeGraceInfo).withBefore(parmLargeGraceInfo).build(ParmLargeGraceInfo.class);
    }

    /**
     * 删除超长免息期参数记录
     * @param id 技术主键
     */
    @Override
    @DeleteParameterAnnotation(tableName = "parm_large_grace_info", tableDesc = "Long Grace Period")
    public ParameterCompare delete(String id) {
        log.debug("删除超长免息期参数记录");
        if (null == id){
            log.error("删除超长免息期参数记录, id不能为空, id:{}", id);

            throw new AnyTxnParameterException(AnyTxnParameterRespCodeEnum.P_PARAMETER_EMPTY_FAULT);
        }
        ParmLargeGraceInfo parmLargeGraceInfo = parmLargeGraceInfoMapper.selectByPrimaryKey(id);
        if (null == parmLargeGraceInfo){
            log.error("删除超长免息期参数信息，通过主键未查到数据, id:{}", id);

            throw new AnyTxnParameterException(AnyTxnParameterRespCodeEnum.D_QUERY_PARM_LARGE_GRACE_INFO_BY_ID_FAULT);
        }
        return ParameterCompare.getBuilder().withBefore(parmLargeGraceInfo).build(ParmLargeGraceInfo.class);
    }

    /**
     * 根据机构号分页查询超长免息期参数记录
     * @param pageNum            当前页数
     * @param pageSize           每页显示条数
     * @param organizationNumber 机构号
     * @return PageResultDTO<LargeGraceInfoResDTO>
     */
    @Override
    public PageResultDTO<LargeGraceInfoResDTO> findAll(Integer pageNum, Integer pageSize, String tableId,String description,String delayedGraceCycle, String organizationNumber) {
        Short delayedGraceCycleShort = null;
        if(!StringUtils.isEmpty(delayedGraceCycle)){
            try {
                delayedGraceCycleShort = Short.parseShort(delayedGraceCycle);
            }catch (NumberFormatException e){
                throw new AnyTxnParameterException(AnyTxnParameterRespCodeEnum.PARAMETER_EXCEPTION, ParameterRepDetailEnum.NUMBER_ERROR);
            }
        }
        Map<String,Object> map = new HashMap<>(8);
        map.put("organizationNumber", StringUtils.isEmpty(organizationNumber) ? OrgNumberUtils.getOrg() : organizationNumber);
        map.put("tableId",tableId);
        map.put("description",description);
        map.put("delayedGraceCycle",delayedGraceCycle);
        map.put("delayedGraceCycleShort",delayedGraceCycleShort);
        log.debug("根据机构号分页查询超长免息期参数信息");
        Page<ParmLargeGraceInfo> page = PageHelper.startPage(pageNum, pageSize);
        List<ParmLargeGraceInfo> parmLargeGraceInfos = parmLargeGraceInfoSelfMapper.selectByCondition(map);
        List<LargeGraceInfoResDTO> largeGraceInfoDtos = BeanMapping.copyList(parmLargeGraceInfos, LargeGraceInfoResDTO.class);
        return new PageResultDTO<>(pageNum, pageSize, page.getTotal(), page.getPages(), largeGraceInfoDtos);
    }

    /**
     * 根据id查询超长免息期记录明细
     * @param id 技术主键
     * @return LargeGraceInfoResDTO
     */
    @Override
    public LargeGraceInfoResDTO findById(String id) {
        log.debug("根据id查询超长免息期参数记录");
        if (null == id){
            log.error("主键id不能为空");

            throw new AnyTxnParameterException(AnyTxnParameterRespCodeEnum.P_PARAMETER_EMPTY_FAULT);
        }
        ParmLargeGraceInfo parmLargeGraceInfo = parmLargeGraceInfoMapper.selectByPrimaryKey(id);
        if (null == parmLargeGraceInfo){
            log.error("根据id查询超长免息期明细失败");

            throw new AnyTxnParameterException(AnyTxnParameterRespCodeEnum.D_QUERY_PARM_LARGE_GRACE_INFO_BY_ID_FAULT);
        }
        return BeanMapping.copy(parmLargeGraceInfo, LargeGraceInfoResDTO.class);
    }

    /**
     * 必输项检查
     * @param largeGraceInfoReqDTO 超长免息期参数
     */
    private void checkRequiredItem(LargeGraceInfoReqDTO largeGraceInfoReqDTO){
        if (null == largeGraceInfoReqDTO.getTableId() || "".equals(largeGraceInfoReqDTO.getTableId())){
            log.error("参数表id不能为空或空字符");

            throw new AnyTxnParameterException(AnyTxnParameterRespCodeEnum.D_PARM_DELAYED_CLOSE_DAYS_ID_NULL_FAULT);
        }
        if (null == largeGraceInfoReqDTO.getDelayedGraceCycle()){
            log.error("延迟免息期周期不能为空或空字符");

            throw new AnyTxnParameterException(AnyTxnParameterRespCodeEnum.D_PARM_DELAYED_GRACE_CYCLE_NULL_FAULT);
        }
        if (null == largeGraceInfoReqDTO.getDescription() || "".equals(largeGraceInfoReqDTO.getDescription())){
            log.error("参数表描述不能为空或空字符");

            throw new AnyTxnParameterException(AnyTxnParameterRespCodeEnum.D_PARM_DELAYED_CLOSE_DAYS_DESCRIPTION_NULL_FAULT);
        }
    }

    @Override
    public LargeGraceInfoResDTO findByOrgAndTableId(String organizationNumber, String tableId) {
        ParmLargeGraceInfo parmLargeGraceInfo = parmLargeGraceInfoSelfMapper.selectByIndex(organizationNumber, tableId);
        if(null == parmLargeGraceInfo){
            log.error("根据机构号：{},tableId:{},没有查出数据。",organizationNumber,tableId);
            return null;
        }
        return BeanMapping.copy(parmLargeGraceInfo, LargeGraceInfoResDTO.class);
    }

    @Override
    @Transactional(rollbackFor = Exception.class,propagation = Propagation.REQUIRED)
    //@ShardingSphereTransactionType(TransactionType.XA)
    protected boolean updateDb(ParmModificationRecord parmModificationRecord) {
        ParmLargeGraceInfo parmLargeGraceInfo = JSON.parseObject(parmModificationRecord.getParmBody(), ParmLargeGraceInfo.class);
        parmLargeGraceInfo.initUpdateDateTime();
        int i = parmLargeGraceInfoMapper.updateByPrimaryKeySelective(parmLargeGraceInfo);
        return i > 0;
    }

    @Override
    @Transactional(rollbackFor = Exception.class,propagation = Propagation.REQUIRED)
    //@ShardingSphereTransactionType(TransactionType.XA)
    protected boolean insertDb(ParmModificationRecord parmModificationRecord) {
        ParmLargeGraceInfo parmLargeGraceInfo = JSON.parseObject(parmModificationRecord.getParmBody(), ParmLargeGraceInfo.class);
        parmLargeGraceInfo.initCreateDateTime();
        parmLargeGraceInfo.initUpdateDateTime();
        int i = parmLargeGraceInfoMapper.insertSelective(parmLargeGraceInfo);
        return i > 0;
    }

    @Override
    @Transactional(rollbackFor = Exception.class,propagation = Propagation.REQUIRED)
    //@ShardingSphereTransactionType(TransactionType.XA)
    protected boolean deleteDb(ParmModificationRecord parmModificationRecord) {
        ParmLargeGraceInfo parmLargeGraceInfo = JSON.parseObject(parmModificationRecord.getParmBody(), ParmLargeGraceInfo.class);
        int i = parmLargeGraceInfoMapper.deleteByPrimaryKey(parmLargeGraceInfo.getId());
        return i > 0;
    }
}
