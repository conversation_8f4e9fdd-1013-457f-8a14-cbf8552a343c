package com.anytech.anytxn.parameter.card.service;

import com.alibaba.fastjson.JSON;
import com.github.pagehelper.Page;
import com.github.pagehelper.PageHelper;
import com.anytech.anytxn.common.core.annotation.db.DeleteParameterAnnotation;
import com.anytech.anytxn.common.core.annotation.db.InsertParameterAnnotation;
import com.anytech.anytxn.common.core.annotation.db.UpdateParameterAnnotation;
import com.anytech.anytxn.business.base.audit.AbstractParameterService;
import com.anytech.anytxn.common.core.base.ParameterCompare;
import com.anytech.anytxn.business.base.audit.ParmModificationRecord;
import com.anytech.anytxn.common.core.base.PageResultDTO;
import com.anytech.anytxn.common.core.utils.BeanMapping;
import com.anytech.anytxn.common.core.utils.OrgNumberUtils;
import com.anytech.anytxn.common.sequence.utils.Number16IdGen;
import com.anytech.anytxn.common.core.utils.TenantUtils;
import com.anytech.anytxn.parameter.base.card.domain.dto.BinCardNumberUsedDTO;
import com.anytech.anytxn.parameter.base.card.service.IBinCardNumberUsedService;
import com.anytech.anytxn.parameter.base.common.enums.AnyTxnParameterRespCodeEnum;
import com.anytech.anytxn.parameter.base.common.exception.AnyTxnParameterException;
import com.anytech.anytxn.parameter.card.mapper.unicast.BinCardNumberUsedMapper;
import com.anytech.anytxn.parameter.card.mapper.unicast.BinCardNumberUsedSelfMapper;
import com.anytech.anytxn.parameter.base.card.domain.model.BinCardNumberUsed;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2019-11-12
 */
@Service(value = "cm_bin_card_number_used_serviceImpl")
public class BinCardNumberUsedServiceImpl extends AbstractParameterService implements IBinCardNumberUsedService {

    private Logger logger = LoggerFactory.getLogger(BinCardNumberUsedServiceImpl.class);

    @Autowired
    private BinCardNumberUsedSelfMapper binCardNumberUsedSelfMapper;

    @Autowired
    private BinCardNumberUsedMapper binCardNumberUsedMapper;

    @Autowired
    private Number16IdGen numberIdGenerator;

    /**
     * 分页查询已用卡号信息
     *
     * @param pageNum  当前页
     * @param pageSize 当前页容量
     * @return PageResultDTO<BinCardNumberUsedDTO>
     */
    @Override
    public PageResultDTO<BinCardNumberUsedDTO> findAll(Integer pageNum, Integer pageSize, BinCardNumberUsedDTO binCardNumberUsedDTO) {
        if(null == binCardNumberUsedDTO){
            binCardNumberUsedDTO = new BinCardNumberUsedDTO();
        }
        binCardNumberUsedDTO.setOrganizationNumber(StringUtils.isEmpty(binCardNumberUsedDTO.getOrganizationNumber()) ? OrgNumberUtils.getOrg() : binCardNumberUsedDTO.getOrganizationNumber());
        logger.debug("分页查询已用卡号信息, pageNum:{}, pageSize:{}", pageNum, pageSize);
        Page<BinCardNumberUsed> page = PageHelper.startPage(pageNum, pageSize);
        List<BinCardNumberUsed> binCardNumberUseds = binCardNumberUsedSelfMapper.selectByCondition(binCardNumberUsedDTO);
        List<BinCardNumberUsedDTO> binCardNumberUsedDtos = BeanMapping.copyList(binCardNumberUseds, BinCardNumberUsedDTO.class);
        return new PageResultDTO<>(pageNum, pageSize, page.getTotal(), page.getPages(), binCardNumberUsedDtos);
    }

    /**
     * 根据id查询已用卡号明细
     *
     * @param id 技术主键
     * @return BinCardNumberUsedDTO
     */
    @Override
    public BinCardNumberUsedDTO findById(String id) {
        logger.debug("根据id查询已用卡号明细");
        if (null == id) {
            logger.error("主键id不能为空");
            throw new AnyTxnParameterException(AnyTxnParameterRespCodeEnum.P_PRIMARY_KEY_NULL_FAULT);
        }
        BinCardNumberUsed binCardNumberUsed = binCardNumberUsedMapper.selectByPrimaryKey(id);
        if (null == binCardNumberUsed) {
            logger.error("根据id查询已用卡号明细失败");
            throw new AnyTxnParameterException(AnyTxnParameterRespCodeEnum.D_QUERY_BIN_CARD_NUMBER_USED_BY_ID_FAULT);
        }
        return BeanMapping.copy(binCardNumberUsed, BinCardNumberUsedDTO.class);
    }

    /**
     * 添加已用卡号记录
     *
     * @param binCardNumberUsedDTO 页面传入已用卡号参数
     */
    @Override
    @InsertParameterAnnotation(tableName = "cm_bin_card_number_used", tableDesc = "Bin Usage")
    public ParameterCompare add(BinCardNumberUsedDTO binCardNumberUsedDTO) {
        checkRequired(binCardNumberUsedDTO);
        BinCardNumberUsed cardNumberUsed = binCardNumberUsedSelfMapper.selectLastUsedNumberByIndex(OrgNumberUtils.getOrg(),
                binCardNumberUsedDTO.getTableId(), binCardNumberUsedDTO.getBinSequence());
        if (null != cardNumberUsed) {
            logger.error("根据索引查询已用卡号表信息，数据已存在, orgNum:{}, tableId:{}, binSequence:{}",
                    binCardNumberUsedDTO.getOrganizationNumber(), binCardNumberUsedDTO.getTableId(),
                    binCardNumberUsedDTO.getBinSequence());
            throw new AnyTxnParameterException(AnyTxnParameterRespCodeEnum.D_EXIST_BIN_CARD_NUMBER_USED_FAULT);
        }
        BinCardNumberUsed binCardNumberUsed = BeanMapping.copy(binCardNumberUsedDTO, BinCardNumberUsed.class);
        binCardNumberUsed.setId(String.valueOf(numberIdGenerator.generateId(TenantUtils.getTenantId())));
        return ParameterCompare.getBuilder().withAfter(binCardNumberUsed).build(BinCardNumberUsed.class);
    }

    /**
     * 必输项检查
     *
     * @param binCardNumberUsedDTO 页面传入已用卡号参数
     */
    private void checkRequired(BinCardNumberUsedDTO binCardNumberUsedDTO) {
        if (null == binCardNumberUsedDTO.getTableId() || "".equals(binCardNumberUsedDTO.getTableId())) {
            logger.error("参数表id不能为空或空字符");
            throw new AnyTxnParameterException(AnyTxnParameterRespCodeEnum.P_PARAMETER_NOT_EMPTY_AND_NULL_FAULT);
        }
        if (null == binCardNumberUsedDTO.getBinSequence() || "".equals(binCardNumberUsedDTO.getBinSequence())) {
            logger.error("卡bin顺序号不能为空或空字符");
            throw new AnyTxnParameterException(AnyTxnParameterRespCodeEnum.P_BIN_CARD_NUMBER_NOT_EMPTY_AND_NULL_FAULT);
        }
//        if (null == binCardNumberUsedDTO.getLastUsedNumber() || "".equals(binCardNumberUsedDTO.getLastUsedNumber())){
//            logger.error("上次使用卡号不能为空或空字符");
//            throw new AnyTxnParameterException(AnyTxnParameterRespCodeEnum.P_LAST_USED_CARD_NOT_EMPTY_AND_NULL_FAULT);
//        }
        if (null == binCardNumberUsedDTO.getFinishedIndicator() || "".equals(binCardNumberUsedDTO.getFinishedIndicator())) {
            logger.error("卡号用完标识不能为空或空字符");
            throw new AnyTxnParameterException(AnyTxnParameterRespCodeEnum.P_CARD_FINISHED_INDICATOR_NOT_EMPTY_AND_NULL_FAULT);
        }
        if (null == binCardNumberUsedDTO.getStatus() || "".equals(binCardNumberUsedDTO.getStatus())) {
            logger.error("状态不能为空或空字符");
            throw new AnyTxnParameterException(AnyTxnParameterRespCodeEnum.P_CARD_STATUS_NOT_EMPTY_AND_NULL_FAULT);
        }
    }

    /**
     * 修改已用卡号记录
     *
     * @param binCardNumberUsedDTO 页面传入已用卡号参数
     */
    @Override
    @UpdateParameterAnnotation(tableName = "cm_bin_card_number_used", tableDesc = "Bin Usage")
    public ParameterCompare update(BinCardNumberUsedDTO binCardNumberUsedDTO) {
        if (null == binCardNumberUsedDTO) {
            logger.error("传入参数不能为空");
            throw new AnyTxnParameterException(AnyTxnParameterRespCodeEnum.P_PARAMETER_EMPTY_FAULT);
        }
        BinCardNumberUsed binCardNumberUsed = binCardNumberUsedMapper.selectByPrimaryKey(binCardNumberUsedDTO.getId());
        if (null == binCardNumberUsed) {
            logger.error("修改已用卡号记录, 通过主键未查询到数据, id:{}", binCardNumberUsedDTO.getId());
            throw new AnyTxnParameterException(AnyTxnParameterRespCodeEnum.D_QUERY_BIN_CARD_NUMBER_USED_BY_ID_FAULT);
        }
        BinCardNumberUsed cardNumberUsed = BeanMapping.copy(binCardNumberUsedDTO, BinCardNumberUsed.class);
        return ParameterCompare.getBuilder()
                .withAfter(cardNumberUsed)
                .withBefore(binCardNumberUsed)
                .build(BinCardNumberUsed.class);
    }

    /**
     * 删除已用卡号记录
     *
     * @param id 技术主键
     */
    @Override
    @DeleteParameterAnnotation(tableName = "cm_bin_card_number_used", tableDesc = "Bin Usage")
    public ParameterCompare delete(String id) {
        if (null == id) {
            logger.error("传入参数不能为空");
            throw new AnyTxnParameterException(AnyTxnParameterRespCodeEnum.P_PARAMETER_EMPTY_FAULT);
        }
        BinCardNumberUsed binCardNumberUsed = binCardNumberUsedMapper.selectByPrimaryKey(id);
        if (null == binCardNumberUsed) {
            logger.error("删除已用卡号信息，通过主键未查到数据, id:{}", id);
            throw new AnyTxnParameterException(AnyTxnParameterRespCodeEnum.D_QUERY_BIN_CARD_NUMBER_USED_BY_ID_FAULT);
        }
        return ParameterCompare.getBuilder()
                .withBefore(binCardNumberUsed)
                .build(BinCardNumberUsed.class);
    }

    @Override
    protected boolean updateDb(ParmModificationRecord parmModificationRecord) throws Exception {
        BinCardNumberUsed cardNumberUsed = JSON.parseObject(parmModificationRecord.getParmBody(), BinCardNumberUsed.class);
        cardNumberUsed.initUpdateDateTime();
        cardNumberUsed.setUpdateBy(parmModificationRecord.getApplicationBy());
        int i = binCardNumberUsedMapper.updateByPrimaryKeySelective(cardNumberUsed);
        return i>0;
    }

    @Override
    protected boolean insertDb(ParmModificationRecord parmModificationRecord) throws Exception {
        BinCardNumberUsed binCardNumberUsed = JSON.parseObject(parmModificationRecord.getParmBody(), BinCardNumberUsed.class);
        binCardNumberUsed.initCreateDateTime();
        binCardNumberUsed.setVersionNumber(1L);
        binCardNumberUsed.initUpdateDateTime();
        binCardNumberUsed.setUpdateBy(parmModificationRecord.getApplicationBy());
        int i = binCardNumberUsedMapper.insertSelective(binCardNumberUsed);
        return i > 0;
    }

    @Override
    protected boolean deleteDb(ParmModificationRecord parmModificationRecord) throws Exception {
        BinCardNumberUsed binCardNumberUsed = JSON.parseObject(parmModificationRecord.getParmBody(), BinCardNumberUsed.class);
        int i = binCardNumberUsedMapper.deleteByPrimaryKey(binCardNumberUsed.getId());
        return i > 0;
    }
}
