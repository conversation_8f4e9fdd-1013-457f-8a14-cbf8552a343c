package com.anytech.anytxn.business.account.service;

import com.anytech.anytxn.common.core.enums.*;
import com.anytech.anytxn.common.core.utils.*;
import com.anytech.anytxn.common.sequence.utils.ManageAccountIdGen;
import com.anytech.anytxn.common.sequence.utils.SequenceIdGen;
import com.anytech.anytxn.business.base.accounting.enums.CycleDueEnum;
import com.anytech.anytxn.business.base.account.enums.FinanceStatusEnum;
import com.anytech.anytxn.business.base.account.constants.AccountBusinessConstant;
import com.anytech.anytxn.business.base.account.enums.TransactionTypeCodeEnum;
import com.anytech.anytxn.business.base.account.domain.dto.AccMaDTO;
import com.anytech.anytxn.business.base.account.domain.dto.AccStaDTO;
import com.anytech.anytxn.business.base.account.domain.dto.AccountManagementInfoDTO;
import com.anytech.anytxn.business.base.account.domain.dto.AccountStatisticsInfoDTO;
import com.anytech.anytxn.business.dao.account.mapper.AccountManagementInfoMapper;
import com.anytech.anytxn.business.dao.account.mapper.AccountManagementInfoSelfMapper;
import com.anytech.anytxn.business.dao.account.mapper.AccountStatisticsInfoMapper;
import com.anytech.anytxn.business.dao.account.mapper.AccountStatisticsInfoSelfMapper;
import com.anytech.anytxn.business.dao.account.model.AccountManagementInfo;
import com.anytech.anytxn.business.dao.account.model.AccountStatisticsInfo;
import com.anytech.anytxn.business.base.account.service.IAccountCommonService;
import com.anytech.anytxn.business.base.card.domain.dto.CardCustSpecialInfoDTO;
import com.anytech.anytxn.business.base.card.service.ICardCustSpecialInfoService;
import com.anytech.anytxn.business.dao.customer.mapper.CorporateCustomerInfoMapper;
import com.anytech.anytxn.business.dao.customer.model.CorporateCustomerInfo;
import com.anytech.anytxn.business.base.monetary.domain.bo.CustAccountBO;
import com.anytech.anytxn.business.base.monetary.enums.AnyTxnCustAccountRespCodeEnum;
import com.anytech.anytxn.business.base.monetary.exception.AnyTxnAddAccountException;
import com.anytech.anytxn.business.base.monetary.exception.AnyTxnCustAccountException;
import com.anytech.anytxn.business.dao.customer.mapper.CustomerBasicInfoSelfMapper;
import com.anytech.anytxn.business.dao.customer.mapper1.ParmCardCurrencyMapper;
import com.anytech.anytxn.business.dao.customer.model.CustomerBasicInfo;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;

/**
 * 创建账户和统计信息的接口
 * <AUTHOR>
 * @since 2020/8/12 10:34
 **/
@Service
public class AccountCommonServiceImpl implements IAccountCommonService {

    private static final String PAYMENT_HISTORY = "XXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXX";
    private static final Logger log = LoggerFactory.getLogger(AccountCommonServiceImpl.class);

    @Autowired
    private AccountManagementInfoMapper accountManagementInfoMapper;

    @Autowired
    private AccountStatisticsInfoMapper accountStatisticsInfoMapper;
    @Autowired
    private AccountStatisticsInfoSelfMapper accountStatisticsInfoSelfMapper;

    @Autowired
    private ParmCardCurrencyMapper parmCardCurrencyMapper;

    @Autowired
    private AccountManagementInfoSelfMapper accountManagementInfoSelfMapper;

    @Autowired
    private CustomerBasicInfoSelfMapper customerBasicInfoSelfMapper;
    @Autowired
    private CorporateCustomerInfoMapper corporateCustomerInfoMapper;

    @Autowired
    private SequenceIdGen sequenceIdGen;

    @Autowired
    ManageAccountIdGen manageAccountIdGen;

    @Autowired
    private ICardCustSpecialInfoService cardCustSpecialInfoService;

    /**
     * 统一添加账户及统计信息
     * @param accMaDto 账户
     * @param accStaDTO 统计信息
     * @return String 0：新建成功 ，1：更新成功 -1或其它：创建账户失败,失败原因
     */
    @Transactional(propagation = Propagation.REQUIRED,noRollbackFor = AnyTxnAddAccountException.class)
    @Override
    public String createAccountAndStatisticInfo(AccMaDTO accMaDto, AccStaDTO accStaDTO) {
        return createAccountAndStatisticInfo(accMaDto, accStaDTO, false);
    }

    @Transactional(propagation = Propagation.REQUIRED,noRollbackFor = AnyTxnAddAccountException.class)
    @Override
    public String createAccountAndStatisticInfo(AccMaDTO accMaDto, AccStaDTO accStaDTO, boolean cache) {
        String result;
        String customerId = accMaDto.getCustomerId();
        String accountProductNumber = accMaDto.getProductNumber();
        String orgNumber = accMaDto.getOrganizationNumber();
        String currency = accMaDto.getCurrency();
        accStaDTO.setCustomerId(customerId);
        /*参数校验*/
        if (StringUtils.isEmpty(customerId)) {
            throw new AnyTxnAddAccountException(AnyTxnCustAccountRespCodeEnum.P_ERROR, "创建账户时客户ID不能为空");
        }
        if (StringUtils.isEmpty(accountProductNumber)) {
            throw new AnyTxnAddAccountException(AnyTxnCustAccountRespCodeEnum.P_ERROR, "创建账户时机构号不能为空");
        }
        if (StringUtils.isEmpty(orgNumber)) {
            throw new AnyTxnAddAccountException(AnyTxnCustAccountRespCodeEnum.P_ERROR, "创建账户时账产品ID不能为空");
        }
        if (StringUtils.isEmpty(currency)) {
            throw new AnyTxnAddAccountException(AnyTxnCustAccountRespCodeEnum.P_ERROR, "创建账户时币种不能为空");
        }
        if (StringUtils.isEmpty(accMaDto.getAccountStatus())) {
            throw new AnyTxnAddAccountException(AnyTxnCustAccountRespCodeEnum.P_ERROR, "创建账户时账户状态赋值错误");
        }

        CustomerBasicInfo customerBasicInfo  = new CustomerBasicInfo();
        CorporateCustomerInfo corporateCustomerInfo = null;
        //2-卡户人服务
        if(AccountBusinessConstant.S_TWO.equals(accMaDto.getServerType())){
            customerBasicInfo  = customerBasicInfoSelfMapper.selectByOrgAndCustId(orgNumber,customerId);
            corporateCustomerInfo = corporateCustomerInfoMapper.selectByPrimaryKey(customerId, OrgNumberUtils.getOrg());
        }else {
            CardCustSpecialInfoDTO cardCustSpecialInfoDTO = cardCustSpecialInfoService.selectByCustomerId(customerId);
            customerBasicInfo.setCycleDay(cardCustSpecialInfoDTO.getCycleDay());
        }
        if (customerBasicInfo == null && corporateCustomerInfo == null) {
            log.info("创建账户，客户或者公司客户基本信息为空,csutomerid={}", customerId);
            throw new AnyTxnAddAccountException(AnyTxnCustAccountRespCodeEnum.P_ERROR, "创建账户，客户基本信息为空");
        }else {
            /* 设置cycleday*/
            if (accMaDto.getCycleDay() == null) {
                accMaDto.setCycleDay(customerBasicInfo != null ? customerBasicInfo.getCycleDay() : corporateCustomerInfo.getCycleDay());
            }
            if (StringUtils.isEmpty(accMaDto.getStatementAddressType())) {
                /* 设置账单地址类型 */
                accMaDto.setStatementAddressType(customerBasicInfo != null ?customerBasicInfo.getStatementAddressType():null);
            }

            if (StringUtils.isEmpty(accMaDto.getBranchNumber())) {
                accMaDto.setBranchNumber(customerBasicInfo != null ?customerBasicInfo.getBranchNumber():corporateCustomerInfo.getBranchNumber());
            }

            if (StringUtils.isEmpty(accMaDto.getNetpointNumber())) {
                accMaDto.setNetpointNumber(customerBasicInfo != null ?customerBasicInfo.getNetworkPointNumber():null);
            }

            if (accMaDto.getOpenDate() == null) {
                accMaDto.setOpenDate(customerBasicInfo != null ?customerBasicInfo.getOpenDate():corporateCustomerInfo.getOpenDate());
            }

        }


        //TODO ******** poc性能时注释
//        List<ParmProductInfoVO> accountProductList = parmCardCurrencyMapper.selectParmProductByProductAndOrg(accountProductNumber, orgNumber);
//        if (CollectionUtils.isEmpty(accountProductList)) {
//            log.info("创建账户，系统帐产品币种为空");
//            throw new AnyTxnAddAccountException(AnyTxnCustAccountRespCode.P_ERROR, "创建账户，系统帐产品币种为空");
//        }else{
//            long count = accountProductList.stream().filter(vo -> vo.getCurrencyCode().equals(currency)).count();
//            if (count == 0) {
//                log.error("创建账户失败，系统帐产品币种不包含当前币种{}",currency);
//                throw new AnyTxnAddAccountException(AnyTxnCustAccountRespCode.P_ERROR, "创建账户失败，系统帐产品币种不包含当前币种"+currency);
//            }
//        }

        // 公司清偿账户处理
        boolean isCorporateAccount = accMaDto.getIsCorpoAccount() != null?accMaDto.getIsCorpoAccount():Boolean.FALSE;
        if (isCorporateAccount) {
            log.info("公司清偿账户创建账户,{}", accMaDto);
        }else {
            List<AccountManagementInfo> historyAccounts = accountManagementInfoSelfMapper.selectByCusIdProNumAndOrgList(customerId,accountProductNumber,orgNumber);
            AccountManagementInfo old = null;
            if (CollectionUtils.isNotEmpty(historyAccounts)) {
                for (AccountManagementInfo account : historyAccounts) {
                    if (Objects.equals(currency, account.getCurrency())) {
                        old = account;
                        break;
                    }
                }
            }
            /*已存在对应账户，不需要重新创建*/
            if (old != null) {
                String status8 = "8", status9 = "9";
                if (Objects.equals(old.getAccountStatus(), status8) || Objects.equals(old.getAccountStatus(), status9)) {
                    old.setAccountStatus("1");
                    old.setPreviousBlockCode(old.getBlockCode());
                    old.setPreviousBlockCodeSetDate(old.getBlockCodeSetDate());
                    old.setBlockCode("");
                    old.setExternalReferenceNumber(accMaDto.getExternalReferenceNumber());
                    if (cache) {
                        CustAccountBO.threadCustAccountBO.get().getCustomerBO().updateManagement(BeanMapping.copy(old, AccountManagementInfoDTO.class));
                    } else {
                        accountManagementInfoMapper.updateByPrimaryKeySelective(old);
                    }
                } else {
                    if (cache) {
                        CustAccountBO.threadCustAccountBO.get().getCustomerBO().updateManagement(BeanMapping.copy(old, AccountManagementInfoDTO.class));
                    }
                }
                result = "1";
                accMaDto.setAccountManagementId(old.getAccountManagementId());
                accStaDTO.setAccountManagementId(old.getAccountManagementId());
                return result;
            }
        }

        AccountManagementInfo accountManagementInfo = buildAccountManagementInfo(accMaDto);
        if (StringUtils.isEmpty(accStaDTO.getAccountManagementId())) {
            accStaDTO.setAccountManagementId(accMaDto.getAccountManagementId());
        }
        if(!ObjectUtils.anyNotNull(accStaDTO.getCreateDate())){
            accStaDTO.setCreateDate(accMaDto.getAccountStatusSetDate());
        }
        accStaDTO.setCurrency(currency);
        accStaDTO.setOrganizationNumber(orgNumber);

        //公司卡-统计账户处理
        //汇总账户
        AccountStatisticsInfo accountStatisticsInfo = buildAccountStatisticsInfo(accStaDTO, TransactionTypeCodeEnum.AGGREGATION.getCode());
        //借方全汇总
        AccountStatisticsInfo debitSumAcctStatisticsInfo = buildAccountStatisticsInfo(accStaDTO, TransactionTypeCodeEnum.DEBIT_SUM.getCode());
        //贷方全汇总
        AccountStatisticsInfo creditSumAcctStatisticsInfo = buildAccountStatisticsInfo(accStaDTO, TransactionTypeCodeEnum.CREDIT_SUM.getCode());

        if (Objects.nonNull(accMaDto.getIsCorpoAccount()) && accMaDto.getIsCorpoAccount()) {
            accountStatisticsInfo.setCorporateCustomerId(accMaDto.getCorporateCustomerId());
            debitSumAcctStatisticsInfo.setCorporateCustomerId(accMaDto.getCorporateCustomerId());
            creditSumAcctStatisticsInfo.setCorporateCustomerId(accMaDto.getCorporateCustomerId());

        }
        if (StringUtils.isEmpty(accountStatisticsInfo.getCustomerId())) {
            accountStatisticsInfo.setCustomerId(accountManagementInfo.getCustomerId());
            debitSumAcctStatisticsInfo.setCustomerId(accountManagementInfo.getCustomerId());
            creditSumAcctStatisticsInfo.setCustomerId(accountManagementInfo.getCustomerId());
        }

        try {
            if (cache) {
                CustAccountBO.threadCustAccountBO.get().getCustomerBO().insertManagement(BeanMapping.copy(accountManagementInfo, AccountManagementInfoDTO.class));
            } else {
                int temp = accountManagementInfoMapper.insertSelective(accountManagementInfo);
                if (temp == 0) {
                    throw new AnyTxnCustAccountException(AnyTxnCustAccountRespCodeEnum.D_DATABASE_INSERT_ERROR, "创建账户信息失败");
                }
            }
        } catch (AnyTxnCustAccountException e) {
            log.warn("创建账户信息失败 : {}", e.getErrMsg());
            throw e;
        }

        try {
            if (cache) {
                CustAccountBO.threadCustAccountBO.get().getCustomerBO().updateStatisticsInfo(BeanMapping.copy(accountStatisticsInfo, AccountStatisticsInfoDTO.class), false);
                CustAccountBO.threadCustAccountBO.get().getCustomerBO().updateStatisticsInfo(BeanMapping.copy(debitSumAcctStatisticsInfo, AccountStatisticsInfoDTO.class), false);
                CustAccountBO.threadCustAccountBO.get().getCustomerBO().updateStatisticsInfo(BeanMapping.copy(creditSumAcctStatisticsInfo, AccountStatisticsInfoDTO.class), false);

            } else {
                List<AccountStatisticsInfo> accountStatisticsInfos = new ArrayList<>();
                accountStatisticsInfos.add(accountStatisticsInfo);
                accountStatisticsInfos.add(debitSumAcctStatisticsInfo);
                accountStatisticsInfos.add(creditSumAcctStatisticsInfo);
                int temp = accountStatisticsInfoSelfMapper.batchInsertAcctStatis(accountStatisticsInfos);
                if (temp == 0) {
                    throw new AnyTxnCustAccountException(AnyTxnCustAccountRespCodeEnum.D_DATABASE_INSERT_ERROR, "创建账户统计信息失败");
                }
            }
            result = "0";
        } catch (AnyTxnCustAccountException e) {
            log.warn("创建账户统计信息失败 : {}", e.getErrMsg());
            throw e;
        }

        return result;
    }

    /**
     * build 账户信息 如果未设置账户id，该方法会设置
     * @param accMaDto 账户
     * @return AccountManagementInfo
     */
    private AccountManagementInfo buildAccountManagementInfo(AccMaDTO accMaDto) {
        AccountManagementInfo accountManagementInfo = new AccountManagementInfo();

        if (Objects.nonNull(accMaDto.getIsCorpoAccount()) && accMaDto.getIsCorpoAccount()) {
            accountManagementInfo.setCorporateIndicator(accMaDto.getCorporateIndicator());
            accountManagementInfo.setCorporateCustomerId(accMaDto.getCorporateCustomerId());
            accountManagementInfo.setLiability(accMaDto.getLiability());
        }

        String mid = accMaDto.getAccountManagementId();
        if (StringUtils.isEmpty(mid)) {
            mid = String.valueOf(manageAccountIdGen.generateId(TenantUtils.getTenantId()));
            accMaDto.setAccountManagementId(mid);
        }
        accountManagementInfo.setAccountManagementId(mid);
        /* 设置地址类型 */
        accountManagementInfo.setStatementAddressType(accMaDto.getStatementAddressType());
        accountManagementInfo.setCurrency(accMaDto.getCurrency());
        accountManagementInfo.setCloseDate(AccountBusinessConstant.INITIAL_DATE);
        accountManagementInfo.setAutoExchangeIndicator(AutoExIndicatorEnum.CLOSE.getCode());
        /* 开户改造 */
        if (StringUtils.isEmpty(accMaDto.getAccountStatus())) {
            accountManagementInfo.setAccountStatus(AccountStatusEnum.NEW.getCode());
        }else {
            accountManagementInfo.setAccountStatus(accMaDto.getAccountStatus());
        }

        if (accMaDto.getAccountStatusSetDate() == null) {
            accountManagementInfo.setAccountStatusSetDate(AccountBusinessConstant.INITIAL_DATE);
        }else {
            accountManagementInfo.setAccountStatusSetDate(accMaDto.getAccountStatusSetDate());
        }

        accountManagementInfo.setFinanceStatus(FinanceStatusEnum.NORMAL.getCode());
        accountManagementInfo.setFinanceStatusSetDate(accMaDto.getOpenDate());
        accountManagementInfo.setWaiveLateFeeFlg(WaiveLateFeeFlgEnum.NOT_EXEMPT.getCode());
        accountManagementInfo.setWaiveInterestFlg(WaiveInterestFlgEnum.ZERO.getCode());
        accountManagementInfo.setWaiveLateFeeNum(0);
        accountManagementInfo.setLastStatementDate(AccountBusinessConstant.INITIAL_DATE);
        accountManagementInfo.setPreviousStatementDate(AccountBusinessConstant.INITIAL_DATE);
        accountManagementInfo.setBlockCodeSetDate(AccountBusinessConstant.INITIAL_DATE);
        accountManagementInfo.setPreviousBlockCodeSetDate(AccountBusinessConstant.INITIAL_DATE);
        accountManagementInfo.setChargeOffDate(AccountBusinessConstant.INITIAL_DATE);
        accountManagementInfo.setLastCycleCreditAdjAmount(BigDecimal.ZERO);
        accountManagementInfo.setTotalGracePaymentAmount(BigDecimal.ZERO);
        accountManagementInfo.setTotalDueAmount(BigDecimal.ZERO);
        accountManagementInfo.setCurrentDueAmount(BigDecimal.ZERO);
        accountManagementInfo.setPastDueAmount(BigDecimal.ZERO);
        accountManagementInfo.setDay30DueAmount(BigDecimal.ZERO);
        accountManagementInfo.setDay60DueAmount(BigDecimal.ZERO);
        accountManagementInfo.setDay90DueAmount(BigDecimal.ZERO);
        accountManagementInfo.setDay120DueAmount(BigDecimal.ZERO);
        accountManagementInfo.setDay150DueAmount(BigDecimal.ZERO);
        accountManagementInfo.setDay180DueAmount(BigDecimal.ZERO);
        accountManagementInfo.setDay210DueAmount(BigDecimal.ZERO);
        accountManagementInfo.setDay240DueAmount(BigDecimal.ZERO);
        accountManagementInfo.setDay270DueAmount(BigDecimal.ZERO);
        accountManagementInfo.setDay300DueAmount(BigDecimal.ZERO);
        accountManagementInfo.setDay330DueAmount(BigDecimal.ZERO);
        accountManagementInfo.setDay360DueAmount(BigDecimal.ZERO);
        accountManagementInfo.setDay390DueAmount(BigDecimal.ZERO);
        accountManagementInfo.setCycleDue(CycleDueEnum.NO_DEBIT.getCode());
        accountManagementInfo.setLastAgedDate(AccountBusinessConstant.INITIAL_DATE);
        accountManagementInfo.setPastDueCount(0);
        accountManagementInfo.setDay30DueCount(0);
        accountManagementInfo.setDay60DueCount(0);
        accountManagementInfo.setDay90DueCount(0);
        accountManagementInfo.setDay120DueCount(0);
        accountManagementInfo.setDay150DueCount(0);
        accountManagementInfo.setDay180DueCount(0);
        accountManagementInfo.setDay210DueCount(0);
        accountManagementInfo.setDay240DueCount(0);
        accountManagementInfo.setDay270DueCount(0);
        accountManagementInfo.setDay300DueCount(0);
        accountManagementInfo.setDay330DueCount(0);
        accountManagementInfo.setDay360DueCount(0);
        accountManagementInfo.setDay390DueCount(0);
        accountManagementInfo.setPaymentHistory(PAYMENT_HISTORY);
        accountManagementInfo.setInCollectionIndicator(InCollectionIndicatorEnum.NOT_COLLECTION.getCode());
        accountManagementInfo.setAutoPaymentFirstDate(AccountBusinessConstant.INITIAL_DATE);
        accountManagementInfo.setAutoPaymentSecondDate(AccountBusinessConstant.INITIAL_DATE);
        accountManagementInfo.setCreateTime(LocalDateTime.now());
        accountManagementInfo.setUpdateTime(LocalDateTime.now());
        accountManagementInfo.setUpdateBy(LoginUserUtils.getLoginUserName());
        accountManagementInfo.setVersionNumber(1L);

        accountManagementInfo.setCustomerId(accMaDto.getCustomerId());
        accountManagementInfo.setOrganizationNumber(accMaDto.getOrganizationNumber());
        accountManagementInfo.setProductNumber(accMaDto.getProductNumber());
        accountManagementInfo.setBranchNumber(accMaDto.getBranchNumber());
        accountManagementInfo.setNetpointNumber(accMaDto.getNetpointNumber());
        accountManagementInfo.setOpenDate(accMaDto.getOpenDate());
        accountManagementInfo.setCycleDay(accMaDto.getCycleDay());
        /* 账单剩余未还金额 */
        accountManagementInfo.setStatementDueAmount(BigDecimal.ZERO);
        /* 一次约定扣款日期 */
        accountManagementInfo.setAutoPaymentFirstDate(AccountBusinessConstant.INITIAL_LOCALDATE);
        /* 二次约定扣款日期 */
        accountManagementInfo.setAutoPaymentSecondDate(AccountBusinessConstant.INITIAL_LOCALDATE);
        /* 自动购汇还款类型 */
        accountManagementInfo.setAutoExchangePaymentType(null);
        if (accountManagementInfo.getFinanceStatusInd() == null) {
            accountManagementInfo.setFinanceStatusInd("Y");
        }
         /*新增ABS类型 首次封包资产包编号  当前资产包编号 全账户abs截至日期 核算状态和cycledue联动状态 核算状态和cycledue联动状态更新日期
         账户管理信息表新增6个字段，插入新记录时，给他们赋一下值：
         ABS_TYPE='0' （未abs）
         ABS_PRODUCT_CODE_FIRST=空
         ABS_PRODUCT_CODE_CURR=空
         ABS_END_DATE=空
         FINANCE_STATUS_IND='Y' （联动）*/
        accountManagementInfo.setAbsType("0");
        accountManagementInfo.setFinanceStatusInd("Y");
        accountManagementInfo.setFinanceStatusIndDate(accountManagementInfo.getOpenDate());
        //五级分类,1:正常类资产
        accountManagementInfo.setFiveTypeIndicator("1");
        //当前逾期总额
        accountManagementInfo.setCurrentAmountOverdue(BigDecimal.ZERO);
        //累计逾期期数
        accountManagementInfo.setAccumulatedOverdueNumber(BigDecimal.ZERO);
        //最高逾期期数
        accountManagementInfo.setMaxOverdueNumber(BigDecimal.ZERO);
        accountManagementInfo.setPreferentialInterestRateIndicator(accMaDto.getPreferentialInterestRateIndicator());
        accountManagementInfo.setAutoPaymentType(accMaDto.getAutoPayIndicator());
        accountManagementInfo.setAutoPaymentAcctNumber(accMaDto.getAutoPayAccountNumberLocal());
        accountManagementInfo.setLocalAutoPayRate(accMaDto.getLocalAutoPayRate());
        accountManagementInfo.setLocalAutoPayBankName(accMaDto.getLocalAutoPayBankName());
        accountManagementInfo.setAutoPayAccountNameLocal(accMaDto.getAutoPayAccountNameLocal());
        accountManagementInfo.setAutoPayRateForeign(accMaDto.getAutoPayRateForeign());
        accountManagementInfo.setAutoPayBankNameForeign(accMaDto.getAutoPayBankNameForeign());
        accountManagementInfo.setAutoPayAccountNumberForeign(accMaDto.getAutoPayAccountNumberForeign());
        accountManagementInfo.setAutoPayAccountNameForeign(accMaDto.getAutoPayAccountNameForeign());
        accountManagementInfo.setOnlineStatementIndicator(accMaDto.getOnlineStatementIndicator());
        accountManagementInfo.setExternalReferenceNumber(accMaDto.getExternalReferenceNumber());
        accountManagementInfo.setVaNumber(accMaDto.getVaNumber());
        accountManagementInfo.setVaGuaranteeAmount(BigDecimal.ZERO);
        //开VA账户时，如果VA Withhold Amount为0，说明不需要担保金额，账户担保状态默认给1,，否则给0
        if (Objects.nonNull(accMaDto.getVaWithholdAmount())) {
            accountManagementInfo.setVaAcctStatus(accMaDto.getVaWithholdAmount().compareTo(BigDecimal.ZERO) == 0 ? "1" : "0");
        }
        //设置分区键值
        Long pt = PartitionKeyUtils.partitionKey(accMaDto.getCustomerId());
        if (pt!= null) {
            accountManagementInfo.setPartitionKey(pt.intValue());
        } else {
            log.error("获取分区键失败,账户信息={}", accMaDto.toString());
        }
        return accountManagementInfo;
    }


    /**
     * build 账户统计信息
     * @param accStaDto 参数
     * @return AccountStatisticsInfo
     */
    private AccountStatisticsInfo buildAccountStatisticsInfo(AccStaDTO accStaDto,String transactionType) {
        AccountStatisticsInfo accountStatisticsInfo = new AccountStatisticsInfo();
        accountStatisticsInfo.setAccountManagementId(accStaDto.getAccountManagementId());
        String sid = sequenceIdGen.generateId(TenantUtils.getTenantId());
        accountStatisticsInfo.setStatisticsId(sid);
        accountStatisticsInfo.setCurrency(accStaDto.getCurrency());
        accountStatisticsInfo.setTransactionTypeCode(transactionType);
        accountStatisticsInfo.setBalance(BigDecimal.ZERO);
        accountStatisticsInfo.setStatementBalance(BigDecimal.ZERO);
        accountStatisticsInfo.setLastStatementDate(AccountBusinessConstant.INITIAL_DATE);
        accountStatisticsInfo.setLastActivityDate(AccountBusinessConstant.INITIAL_DATE);
        accountStatisticsInfo.setLastActivityAmount(BigDecimal.ZERO);
        accountStatisticsInfo.setCycleToDateDebitAmount(BigDecimal.ZERO);
        accountStatisticsInfo.setCycleToDateCreditAmount(BigDecimal.ZERO);
        accountStatisticsInfo.setLastCycleDebitAmount(BigDecimal.ZERO);
        accountStatisticsInfo.setLastCycleCreditAmount(BigDecimal.ZERO);
        accountStatisticsInfo.setDebitTotalAmount(BigDecimal.ZERO);
        accountStatisticsInfo.setCreditTotalAmount(BigDecimal.ZERO);
        accountStatisticsInfo.setCreateTime(LocalDateTime.now());
        accountStatisticsInfo.setUpdateTime(LocalDateTime.now());
        accountStatisticsInfo.setUpdateBy(LoginUserUtils.getLoginUserName());
        accountStatisticsInfo.setVersionNumber(1L);
        accountStatisticsInfo.setOrganizationNumber(accStaDto.getOrganizationNumber());
        accountStatisticsInfo.setCreateDate(accStaDto.getCreateDate());
        accountStatisticsInfo.setCustomerId(accStaDto.getCustomerId());
        accountStatisticsInfo.setPartitionKey(PartitionKeyUtils.partitionKey(accStaDto.getCustomerId()).intValue());

        return accountStatisticsInfo;
    }


}
