package com.anytech.anytxn.business.monetary.service;

import com.anytech.anytxn.business.base.monetary.service.ICustAccountJdbcService;
import com.anytech.anytxn.common.core.utils.JacksonUtils;
import com.anytech.anytxn.business.base.monetary.annotation.MapperColumnAnnotation;
import com.anytech.anytxn.business.base.monetary.annotation.MapperTableAnnotation;
import com.anytech.anytxn.business.base.monetary.enums.AnyTxnCustAccountRespCodeEnum;
import com.anytech.anytxn.business.base.monetary.exception.AnyTxnCustAccountException;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.jdbc.core.namedparam.NamedParameterJdbcTemplate;
import org.springframework.jdbc.core.namedparam.SqlParameterSource;
import org.springframework.jdbc.core.namedparam.SqlParameterSourceUtils;

import java.lang.reflect.Field;
import java.lang.reflect.InvocationHandler;
import java.lang.reflect.Method;
import java.util.List;

/**
 * jdbc批量插入/更新代理对象
 * <AUTHOR>
 * @date 2020/7/23
 */
@Slf4j
public class JdbcServiceProxy implements InvocationHandler {

    /**
     * jdbcService方法参数均为3
     */
    private final int JDBC_SERVICE_ARG_LENGTH = 3;

    private NamedParameterJdbcTemplate bizJdbcTemplate;

    public JdbcServiceProxy(NamedParameterJdbcTemplate bizJdbcTemplate){
        this.bizJdbcTemplate = bizJdbcTemplate;
    }

    @Override
    public Object invoke(Object proxy, Method method, Object[] args) throws Throwable {
        if (proxy instanceof ICustAccountJdbcService) {
            if (args.length != JDBC_SERVICE_ARG_LENGTH) {
                throw new AnyTxnCustAccountException(AnyTxnCustAccountRespCodeEnum.P_ERROR, "调用批量更新方法参数错误！");
            }

            // 数据
            List data = (List) args[0];
            // sql
            String sql = (String) args[1];
            // 描述
            String des = (String) args[2];

            if (StringUtils.equalsAny(method.getName(),"insertBatch", "updateBatch", "commonBatch")) {
                SqlParameterSource[] beanSources = SqlParameterSourceUtils.createBatch(data.toArray());
                int[] ints = bizJdbcTemplate.batchUpdate(sql, beanSources);
                checkSuccess(ints, args,  des);
            }
        }
        return null;
    }

    private void checkSuccess(int[] result, Object[] args, String name){
        for (int i : result) {
            // 存储异常反射比较出sql
            if (i == 0) {
                Object data = args[i];
                Class<?> dataClass = data.getClass();
                MapperTableAnnotation annotation = dataClass.getAnnotation(MapperTableAnnotation.class);
                if (annotation == null) {
//                    log.error("执行更新语句不成功返回0,name=[{}],args=[{}]",name,args);
//                    continue;
                    log.error("反射更新失败！！更新表：{} 失败{}, data{}, dataClass {}", name, args[i], JacksonUtils.toJsonStr(args),dataClass);
                    throw new AnyTxnCustAccountException(AnyTxnCustAccountRespCodeEnum.D_DATABASE_UPDATE_ERROR);
                }
                String tableName = annotation.tableName();
                Field[] f = dataClass.getDeclaredFields();
                StringBuilder condition = new StringBuilder();
                for (Field field : f) {
                    MapperColumnAnnotation annotation1 = field.getAnnotation(MapperColumnAnnotation.class);
                    if (annotation1 != null) {
                        if (annotation1.isKey() || annotation1.isLock() || annotation1.isCondition()) {
                            String getMethodName = "get" + field.getName().substring(0, 1).toUpperCase() + field.getName().substring(1);
                            try {
                                Method method = dataClass.getMethod(getMethodName);
                                Object value = method.invoke(data);
                                condition.append(field.getName()).append(": ").append(value).append(" ");
                            } catch (Exception e) {
                                log.error("反射更新失败，condition组装遇到未知问题！！{} 表：{} 失败，条件：{}", "更新" , name + "(" + tableName + ")", condition.toString());
                                throw new AnyTxnCustAccountException(AnyTxnCustAccountRespCodeEnum.D_DATABASE_UPDATE_ERROR);
                            }
                        }
                    }
                }

                log.error("{} 表：{} 失败，条件：{}", "更新" , name + "(" + tableName + ")", condition.toString());
                throw new AnyTxnCustAccountException(AnyTxnCustAccountRespCodeEnum.D_DATABASE_UPDATE_ERROR);
            }
        }
    }
}
