package com.anytech.anytxn.parameter.account.service;

import com.alibaba.fastjson.JSON;
import com.github.pagehelper.Page;
import com.github.pagehelper.PageHelper;
import com.anytech.anytxn.common.core.annotation.db.DeleteParameterAnnotation;
import com.anytech.anytxn.common.core.annotation.db.InsertParameterAnnotation;
import com.anytech.anytxn.common.core.annotation.db.UpdateParameterAnnotation;
import com.anytech.anytxn.business.base.audit.AbstractParameterService;
import com.anytech.anytxn.common.core.base.ParameterCompare;
import com.anytech.anytxn.business.base.audit.ParmModificationRecord;
import com.anytech.anytxn.common.core.utils.OrgNumberUtils;
import com.anytech.anytxn.common.core.base.PageResultDTO;
import com.anytech.anytxn.common.core.utils.BeanMapping;
import com.anytech.anytxn.common.sequence.utils.Number16IdGen;
import com.anytech.anytxn.common.core.utils.TenantUtils;
import com.anytech.anytxn.parameter.base.account.domain.dto.ParmBalancePricingTableResDTO;
import com.anytech.anytxn.parameter.base.account.service.IParmBalancePricingTableService;
import com.anytech.anytxn.parameter.base.common.enums.AnyTxnParameterRespCodeEnum;
import com.anytech.anytxn.parameter.base.common.exception.AnyTxnParameterException;
import com.anytech.anytxn.parameter.account.mapper.ParmBalancePricingTableMapper;
import com.anytech.anytxn.parameter.account.mapper.ParmBalancePricingTableSelfMapper;
import com.anytech.anytxn.parameter.base.common.domain.model.system.ParmAccountProductInfo;
import com.anytech.anytxn.parameter.base.account.domain.model.ParmBalancePricingTable;
//import org.apache.shardingsphere.transaction.annotation.ShardingSphereTransactionType;
//import org.apache.shardingsphere.transaction.core.TransactionType;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.StringUtils;

import java.util.ArrayList;
import java.util.List;

/***
 * @Author: zy
 * @Description: 余额定价参数 业务实现
 * @Date: 2019/5/5 11:47
 **/
@Service(value = "parm_balance_pricing_table_serviceImpl")
public class ParmBalancePricingTableServiceImpl extends AbstractParameterService implements IParmBalancePricingTableService {

    private Logger logger = LoggerFactory.getLogger(ParmBalancePricingTableServiceImpl.class);

    @Autowired
    private ParmBalancePricingTableMapper parmBalancePricingTableMapper;
    @Autowired
    private ParmBalancePricingTableSelfMapper parmBalancePricingTableSelfMapper;

    @Autowired
    private Number16IdGen numberIdGenerator;

    /**
     * 新建余额定价参数
     * @param parmBalancePricingTableResDTO 余额定价参数传入
     * @return ParmBalancePricingTableResDTO
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    @InsertParameterAnnotation(tableName = "parm_balance_pricing_table", tableDesc = "Balance Pricing")
    public ParameterCompare add(ParmBalancePricingTableResDTO parmBalancePricingTableResDTO){
        int exists = parmBalancePricingTableSelfMapper.isExists(parmBalancePricingTableResDTO.getOrganizationNumber(),
                parmBalancePricingTableResDTO.getTableId(),
                parmBalancePricingTableResDTO.getTransactionTypeCode());
        if(exists > 0)
        {
            logger.warn("余额定价参数已存在，organizationNumber={},tableId={}，transactionTypeCode={}",
                    parmBalancePricingTableResDTO.getOrganizationNumber(),
                    parmBalancePricingTableResDTO.getTableId(),
                    parmBalancePricingTableResDTO.getTransactionTypeCode());
            throw new AnyTxnParameterException(AnyTxnParameterRespCodeEnum.D_EXIST_PARM_BALANCE_PRICING_TABLE_FAULT);
        }
        try {
            ParmBalancePricingTable parmBalancePricingTable = BeanMapping.copy(parmBalancePricingTableResDTO, ParmBalancePricingTable.class);
            parmBalancePricingTable.setId(String.valueOf(numberIdGenerator.generateId(TenantUtils.getTenantId())));
            return ParameterCompare.getBuilder().withAfter(parmBalancePricingTable).build(ParmBalancePricingTable.class);
        }catch (Exception e)
        {
            throw new AnyTxnParameterException(AnyTxnParameterRespCodeEnum.D_ADD_PARM_BALANCE_PRICING_TABLE_FAULT);
        }

    }
    /**
     * 修改余额定价参数
     * @param parmBalancePricingTableResDTO 余额定价参数
     * @return ParmBalancePricingTableResDTO
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    @UpdateParameterAnnotation(tableName = "parm_balance_pricing_table", tableDesc ="Balance Pricing")
    public ParameterCompare modify(ParmBalancePricingTableResDTO parmBalancePricingTableResDTO) {
        parmBalancePricingTableResDTO.setOrganizationNumber(OrgNumberUtils.getOrg(parmBalancePricingTableResDTO.getOrganizationNumber()));
        if(null == parmBalancePricingTableResDTO.getId()) {
            throw new AnyTxnParameterException(AnyTxnParameterRespCodeEnum.P_PARAMETER_ID_EMPTY_FAULT);
        }
        ParmBalancePricingTable pricingTable = parmBalancePricingTableMapper.selectByPrimaryKey(parmBalancePricingTableResDTO.getId());
        if (null == pricingTable) {
            logger.error("修改余额定价参数, 通过主键id({})未找到数据", parmBalancePricingTableResDTO.getId());
            throw new AnyTxnParameterException(AnyTxnParameterRespCodeEnum.D_QUERY_PARM_BALANCE_PRICING_TABLE_BY_ID_FAULT);
        }
        // 拷贝修改的数据并更新
        ParmBalancePricingTable copy = BeanMapping.copy(parmBalancePricingTableResDTO, ParmBalancePricingTable.class);
        ParmBalancePricingTable parmBalancePricingTable = parmBalancePricingTableSelfMapper.selectByIndex(parmBalancePricingTableResDTO.getOrganizationNumber(),
                parmBalancePricingTableResDTO.getTableId(),
                parmBalancePricingTableResDTO.getTransactionTypeCode());
        if (null != parmBalancePricingTable && !parmBalancePricingTable.getId().equals(copy.getId())) {
            logger.error("账户产品信息已存在，orgNum={},accountProductNumber={},TransactionTypeCode={}",parmBalancePricingTableResDTO.getOrganizationNumber(),
                    parmBalancePricingTableResDTO.getTableId(),
                    parmBalancePricingTableResDTO.getTransactionTypeCode());
            throw new AnyTxnParameterException(AnyTxnParameterRespCodeEnum.D_EXIST_PARM_BALANCE_PRICING_TABLE_FAULT);
        }
        return ParameterCompare.getBuilder().withAfter(copy).withBefore(pricingTable).build(ParmBalancePricingTable.class);
    }

    /**
     * 删除余额定价参数
     * @param id 余额定价参数id
     * @return Boolean
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    @DeleteParameterAnnotation(tableName = "parm_balance_pricing_table", tableDesc = "Balance Pricing")
    public ParameterCompare remove(String id) {
        if(null == id && "".equals(String.valueOf(id)))
        {
            throw new AnyTxnParameterException(AnyTxnParameterRespCodeEnum.P_PARAMETER_EMPTY_FAULT);
        }
        ParmBalancePricingTable pricingTable = parmBalancePricingTableMapper.selectByPrimaryKey(id);
        if (null == pricingTable) {
            logger.error("删除余额定价参数，通过id:{}未查到数据", id);
            throw new AnyTxnParameterException(AnyTxnParameterRespCodeEnum.D_QUERY_PARM_BALANCE_PRICING_TABLE_BY_ID_FAULT);
        }
        return ParameterCompare.getBuilder().withBefore(pricingTable).build(ParmBalancePricingTable.class);
    }
    /**
     * 查询余额定价参数
     * @param organizationNumber tableId transactionTypeCode 余额定价参数传入
     * @return ParmBalancePricingTableResDTO
     */
    @Override
    //@PreGetProcess(byPrimaryKey = true)
    public ParmBalancePricingTableResDTO findByIndex(String organizationNumber, String tableId, String transactionTypeCode) {
        if ( null == organizationNumber || null == tableId || null ==transactionTypeCode) {
            throw new AnyTxnParameterException(AnyTxnParameterRespCodeEnum.P_PARAMETER_NOT_EMPTY_FAULT);
        }

        ParmBalancePricingTable pricingTable = parmBalancePricingTableSelfMapper.selectByIndex(
                organizationNumber,tableId,transactionTypeCode);
        if ( null == pricingTable) {
            logger.error("查询余额定价参数表，通过机构号:{}、参数表ID:{},交易类型代码:{}未查到数据",  organizationNumber,tableId,transactionTypeCode);
            throw new AnyTxnParameterException(AnyTxnParameterRespCodeEnum.D_QUERY_PARM_BALANCE_PRICING_TABLE_FAULT);
        }
        return BeanMapping.copy(pricingTable, ParmBalancePricingTableResDTO.class);
    }
    /**
     * 分页查询
     * @param  pageNum pageSize
     * @return ParmBalancePricingTableResDTO
     */
    @Override
    public PageResultDTO<ParmBalancePricingTableResDTO> findPage(Integer pageNum, Integer pageSize,ParmBalancePricingTableResDTO parmBalancePricingTableResDTO) {
        logger.debug("分页余额定价参数信息");
        if(null == parmBalancePricingTableResDTO){
            parmBalancePricingTableResDTO = new ParmBalancePricingTableResDTO();
        }
        parmBalancePricingTableResDTO.setOrganizationNumber(StringUtils.isEmpty(parmBalancePricingTableResDTO.getOrganizationNumber()) ? OrgNumberUtils.getOrg() : parmBalancePricingTableResDTO.getOrganizationNumber());
        Page<ParmAccountProductInfo> page = PageHelper.startPage(pageNum, pageSize);
        List<ParmBalancePricingTable> parmBalancePricingTables = parmBalancePricingTableSelfMapper.selectByCondition(parmBalancePricingTableResDTO);

        if (parmBalancePricingTables.isEmpty()) {
            logger.error("未查余额定价参数信息");
            parmBalancePricingTables = new ArrayList<>();
            //throw new AnyTxnParameterException(AnyTxnParameterRespCodeEnum.D_PAGE_QUERY_PARM_BALANCE_PRICING_TABLE_FAULT);
        }
        List<ParmBalancePricingTableResDTO> currencyRateRes = BeanMapping.copyList(parmBalancePricingTables, ParmBalancePricingTableResDTO.class);
        return new PageResultDTO<>(pageNum, pageSize, page.getTotal(), page.getPages(), currencyRateRes);
    }

    @Override
    //@PreGetProcess(byPrimaryKey = true)
    public ParmBalancePricingTableResDTO findById(String id) {
        if ( null == id ) {
            throw new AnyTxnParameterException(AnyTxnParameterRespCodeEnum.P_PARAMETER_NOT_EMPTY_FAULT);
        }
        ParmBalancePricingTable pricingTable = parmBalancePricingTableMapper.selectByPrimaryKey(id);
        if ( null == pricingTable) {
            logger.error("查询余额定价参数表，通过余额定价id:{}",  id);
            throw new AnyTxnParameterException(AnyTxnParameterRespCodeEnum.D_QUERY_PARM_BALANCE_PRICING_TABLE_BY_ID_FAULT);
        }
        return BeanMapping.copy(pricingTable, ParmBalancePricingTableResDTO.class);
    }

    @Override
    @Transactional(rollbackFor = Exception.class,propagation = Propagation.REQUIRED)
    //@ShardingSphereTransactionType(TransactionType.XA)
    protected boolean updateDb(ParmModificationRecord parmModificationRecord) {
        ParmBalancePricingTable parmBalancePricingTable = JSON.parseObject(parmModificationRecord.getParmBody(), ParmBalancePricingTable.class);
        parmBalancePricingTable.initUpdateDateTime();
        int i = parmBalancePricingTableMapper.updateByPrimaryKeySelective(parmBalancePricingTable);
        return i > 0;
    }

    @Override
    @Transactional(rollbackFor = Exception.class,propagation = Propagation.REQUIRED)
    //@ShardingSphereTransactionType(TransactionType.XA)
    protected boolean insertDb(ParmModificationRecord parmModificationRecord) {
        ParmBalancePricingTable parmBalancePricingTable = JSON.parseObject(parmModificationRecord.getParmBody(), ParmBalancePricingTable.class);
        parmBalancePricingTable.initCreateDateTime();
        parmBalancePricingTable.initUpdateDateTime();
        int i = parmBalancePricingTableMapper.insertSelective(parmBalancePricingTable);
        return i > 0;
    }

    @Override
    @Transactional(rollbackFor = Exception.class,propagation = Propagation.REQUIRED)
    //@ShardingSphereTransactionType(TransactionType.XA)
    protected boolean deleteDb(ParmModificationRecord parmModificationRecord) {
        ParmBalancePricingTable parmBalancePricingTable = JSON.parseObject(parmModificationRecord.getParmBody(), ParmBalancePricingTable.class);
        int i = parmBalancePricingTableMapper.deleteByPrimaryKey(parmBalancePricingTable.getId());
        return i > 0;
    }
}
