package com.anytech.anytxn.parameter.authorization.controller;

import com.anytech.anytxn.parameter.base.authorization.domain.dto.MccGroupDefinitionDTO;
import com.anytech.anytxn.parameter.base.authorization.service.IMccGroupDefinitionService;
import com.anytech.anytxn.parameter.base.authorization.service.IMccGroupDetailService;
import com.anytech.anytxn.parameter.base.common.enums.ParameterRepDetailEnum;
import com.anytech.anytxn.common.core.base.PageResultDTO;
import com.anytech.anytxn.common.core.base.AnyTxnHttpResponse;
import com.anytech.anytxn.common.core.base.BizBaseController;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.Valid;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;


/**
 * <AUTHOR>
 */
@RestController
@Tag(name = "MCC组维护服务")
public class MccGroupController extends BizBaseController {

    @Autowired
    private IMccGroupDefinitionService mccGroupDefinitionService;
    @Autowired
    private IMccGroupDetailService mccGroupDetailService;


    /**
     *分页查询商户类别群列表
     */
    @Operation(summary = "分页查询商户类别群信息")
    @GetMapping("/param/mccGroup/{page}/{rows}")
    public AnyTxnHttpResponse<PageResultDTO<MccGroupDefinitionDTO>> getMccGroupList(@Parameter(name = "page", description = "当前页", example = "1")@PathVariable("page") int page,
                                                                                    @Parameter(name = "rows", description = "每页大小", example = "8") @PathVariable("rows") int rows,
                                                                                              @RequestParam(value = "mccGroup",required = false) String mccGroup,
                                                                                              @RequestParam(value = "description",required = false) String description,
                                                                                              @RequestParam(required = false) String organizationNumber) {
        PageResultDTO<MccGroupDefinitionDTO> result = mccGroupDefinitionService.findListMccGroupDefinition(page, rows,mccGroup,description, organizationNumber);
        return AnyTxnHttpResponse.success(result);
    }


    /**
     *根据主键查询商户类别群信息及详情
     */
    @Operation(summary = "根据主键查询商户类别群信息包含详情")
    @GetMapping("/param/mccGroupAndDetail/{id}")
    @Parameter(name = "id", description = "技术主键")
    public AnyTxnHttpResponse<MccGroupDefinitionDTO> getMccGroupAndDetail(@PathVariable Long id) {
        MccGroupDefinitionDTO mccGroupDefinitionAndDetil = mccGroupDefinitionService.findMccGroupDefinitionAndDetil(id);
        return AnyTxnHttpResponse.success(mccGroupDefinitionAndDetil);
    }


    /**
     *新增商户类别群信息以及详细内容
     */
    @Operation(summary = "新增商户类别群信息以及详细内容")
    @PostMapping("/param/addMccGroupAndDetail")
    public AnyTxnHttpResponse createMccGroupAndDetail(@Valid @RequestBody MccGroupDefinitionDTO mccGroupDefinitionDTO) {
        Boolean flag = mccGroupDefinitionService.addMccGroupDefinitionAndDtails(mccGroupDefinitionDTO);
        return AnyTxnHttpResponse.success(flag,ParameterRepDetailEnum.CREATE.message());
    }

    /**
     *商户类别群信息修改以及详细内容
     */
    @Operation(summary = "商户类别群信息修改以及详细内容")
    @PutMapping("/param/modifyMccGroupAndDetail")
    public AnyTxnHttpResponse modifyMccGroupAndDetail(@Valid @RequestBody MccGroupDefinitionDTO mccGroupDefinitionDTO) {
        Boolean flag = mccGroupDefinitionService.modifyMccGroupDefinitionAndUtils(mccGroupDefinitionDTO);
        return AnyTxnHttpResponse.success(flag,ParameterRepDetailEnum.UPDATE.message());
    }

    /**
     *删除商户类别群ƒ信息以及详情
     */
    @Operation(summary = "删除商户类别群信息以及详情")
    @DeleteMapping("/param/cancelMccGroupAndDtail/{id}")
    public AnyTxnHttpResponse cancelMccGroupAndDtail(@PathVariable Long id) {
        Boolean flag = mccGroupDefinitionService.removeMccGroupDefinitionAndDtails(id);
        return AnyTxnHttpResponse.success(flag,ParameterRepDetailEnum.DEL.message());
    }


}
