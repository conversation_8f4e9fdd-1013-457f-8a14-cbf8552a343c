package com.anytech.anytxn.parameter.authorization.controller;

import io.swagger.v3.oas.annotations.tags.Tag;
import io.swagger.v3.oas.annotations.Operation;
import com.anytech.anytxn.common.core.base.ParameterCompare;
import com.anytech.anytxn.common.core.base.PageResultDTO;
import com.anytech.anytxn.common.core.base.AnyTxnHttpResponse;
import com.anytech.anytxn.common.core.base.BizBaseController;
import com.anytech.anytxn.parameter.base.authorization.domain.dto.AuthCheckDefinitionReqDTO;
import com.anytech.anytxn.parameter.base.authorization.domain.dto.AuthCheckDefinitionResDTO;
import com.anytech.anytxn.parameter.base.authorization.service.IAuthCheckDefinitionService;
import com.anytech.anytxn.parameter.base.common.enums.ParameterRepDetailEnum;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import jakarta.validation.Valid;

/**
 * 授权检查
 *
 * <AUTHOR>
 * @date 2018-12-06
 */
@Tag(name = "授权检查定义与授权检查控制信息")
@RestController
public class AuthCheckDefinitionController extends BizBaseController {

    @Autowired
    private IAuthCheckDefinitionService authCheckDefinitionService;

    /**
     * 创建授权检查定义与授权检查控制信息
     *
     * @param authCheckDefinitionReq 对授权检查定义请求信息+授权检查控制信息
     * @return 对应授权检查控制信息详情
     */
    @Operation(summary = "创建授权检查定义与授权检查控制信息")
    @PostMapping(value = "/param/authCheckDefinition")
    public AnyTxnHttpResponse<Object> create(@Valid @RequestBody AuthCheckDefinitionReqDTO authCheckDefinitionReq) {
        ParameterCompare blockCodeDefinitionRes = authCheckDefinitionService.add(authCheckDefinitionReq);
        return AnyTxnHttpResponse.success(blockCodeDefinitionRes, ParameterRepDetailEnum.CREATE.message());

    }

    /**
     * 更新授权检查定义与授权检查控制信息
     *
     * @param authCheckDefinitionReq 对授权检查定义请求信息+授权检查控制信息
     * @return 对应授权检查控制信息详情
     */
    @Operation(summary = "更新授权检查定义与授权检查控制信息")
    @PutMapping(value = "/param/authCheckDefinition")
    public AnyTxnHttpResponse<Object> modify(@Valid @RequestBody AuthCheckDefinitionReqDTO authCheckDefinitionReq) {
        ParameterCompare blockCodeDefinitionRes = authCheckDefinitionService.modify(authCheckDefinitionReq);
        return AnyTxnHttpResponse.success(blockCodeDefinitionRes,ParameterRepDetailEnum.UPDATE.message());

    }

    /**
     * 分页查询，授权控制信息
     *
     * @param pageNum        页码
     * @param pageSize       每页大小
     * @return
     */
    @Operation(summary = "分页查询，授权控制信息")
    @GetMapping(value = "/param/authCheckDefinition/pageNum/{pageNum}/pageSize/{pageSize}")
    public AnyTxnHttpResponse<PageResultDTO<AuthCheckDefinitionResDTO>> getPageByOrgNumber(@PathVariable(value = "pageNum") Integer pageNum,
                                                                                        @PathVariable(value = "pageSize") Integer pageSize,
                                                                                           AuthCheckDefinitionReqDTO authCheckDefinitionReq) {
        PageResultDTO<AuthCheckDefinitionResDTO> response = null;
        response = authCheckDefinitionService.findAllByPage(pageNum, pageSize,authCheckDefinitionReq);
        return AnyTxnHttpResponse.success(response);

    }

    /**
     * 通过id获详情
     *
     * @param id 获取授权检查数据
     * @return 层封锁码详情
     */
    @Operation(summary = "通过id获取授权检查定义和授权检查控制数据详情")
    @GetMapping(value = "/param/authCheckDefinition/id/{id}")
    public AnyTxnHttpResponse<AuthCheckDefinitionResDTO> get(@PathVariable(value = "id") String id) {
        AuthCheckDefinitionResDTO authCheckDefinitionRes = authCheckDefinitionService.findById(id);
        return AnyTxnHttpResponse.success(authCheckDefinitionRes);

    }

    /**
     * 删除授权检查定义，通过id
     *
     * @param id 授权检查定义id
     * @return
     */
    @Operation(summary = "通过id删除授权检查定义表信息")
    @DeleteMapping(value = "/param/authCheckDefinition/id/{id}")
    public AnyTxnHttpResponse<Object> remove(@PathVariable(value = "id") String id) {
        ParameterCompare deleted = authCheckDefinitionService.remove(id);
        return AnyTxnHttpResponse.success(deleted,ParameterRepDetailEnum.DEL.message());

    }

    /**
     * 根据机构号、参数表id,查询授权检查定义池
     *
     * @param organizationNumber 机构号
     * @param tableId            参数表id
     * @return AnyTxnHttpResponse<AuthCheckDefinitionResDTO>
     */
    @GetMapping(value = "/param/authCheckDefinition/organizationNumber/{organizationNumber}/tableId/{tableId}")
    @Operation(summary = "根据机构号、参数表id,查询授权检查定义")
    public AnyTxnHttpResponse<AuthCheckDefinitionResDTO> getAuthDefinByOrgAndTableId(@PathVariable(value = "organizationNumber") String organizationNumber,
                                                                                  @PathVariable(value = "tableId") String tableId) {
        AuthCheckDefinitionResDTO authCheckDefinitionRes = authCheckDefinitionService.findByOrgAndTableId(organizationNumber, tableId);
        return AnyTxnHttpResponse.success(authCheckDefinitionRes);

    }

}
