package com.anytech.anytxn.parameter.account.service;

import com.alibaba.fastjson.JSON;
import com.github.pagehelper.Page;
import com.github.pagehelper.PageHelper;
import com.anytech.anytxn.common.core.annotation.db.DeleteParameterAnnotation;
import com.anytech.anytxn.common.core.annotation.db.InsertParameterAnnotation;
import com.anytech.anytxn.common.core.annotation.db.UpdateParameterAnnotation;
import com.anytech.anytxn.business.base.audit.AbstractParameterService;
import com.anytech.anytxn.common.core.base.ParameterCompare;
import com.anytech.anytxn.business.base.audit.ParmModificationRecord;
import com.anytech.anytxn.common.core.base.PageResultDTO;
import com.anytech.anytxn.common.core.utils.BeanMapping;
import com.anytech.anytxn.common.core.utils.OrgNumberUtils;
import com.anytech.anytxn.common.sequence.utils.Number16IdGen;
import com.anytech.anytxn.common.core.utils.TenantUtils;
import com.anytech.anytxn.parameter.base.account.domain.dto.InterestReqDTO;
import com.anytech.anytxn.parameter.base.account.domain.dto.InterestResDTO;
import com.anytech.anytxn.parameter.base.account.domain.dto.InterestSearchDTO;
import com.anytech.anytxn.parameter.base.account.service.IInterestService;
import com.anytech.anytxn.parameter.base.common.constants.Constants;
import com.anytech.anytxn.parameter.base.common.enums.AnyTxnParameterRespCodeEnum;
import com.anytech.anytxn.parameter.base.common.enums.ParameterRepDetailEnum;
import com.anytech.anytxn.parameter.base.common.exception.AnyTxnParameterException;
import com.anytech.anytxn.parameter.account.mapper.ParmInterestMapper;
import com.anytech.anytxn.parameter.account.mapper.ParmInterestSelfMapper;
import com.anytech.anytxn.parameter.account.mapper.PmInterestHisMapper;
import com.anytech.anytxn.parameter.account.mapper.PmInterestHisSelfMapper;
import com.anytech.anytxn.parameter.common.mapper.broadcast.system.ParmOrganizationInfoSelfMapper;
import com.anytech.anytxn.parameter.base.account.domain.model.ParmInterest;
import com.anytech.anytxn.parameter.base.account.domain.model.PmInterestHis;
import com.anytech.anytxn.parameter.base.common.domain.model.system.ParmOrganizationInfo;
import lombok.extern.slf4j.Slf4j;
//import org.apache.shardingsphere.transaction.annotation.ShardingSphereTransactionType;
//import org.apache.shardingsphere.transaction.core.TransactionType;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.StringUtils;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;

/**
 * 利率参数 业务接口实现
 * <AUTHOR> tingting
 * @date 2018/8/15
 */
@Service(value = "parm_interest_serviceImpl")
@Slf4j
public class InterestServiceImpl extends AbstractParameterService implements IInterestService {

    @Autowired
    private ParmInterestMapper parmInterestMapper;
    @Autowired
    private ParmInterestSelfMapper parmInterestSelfMapper;

    @Autowired
    private Number16IdGen numberIdGenerator;
    @Autowired
    private ParmOrganizationInfoSelfMapper parmOrganizationInfoSelfMapper;
    @Autowired
    private PmInterestHisMapper pmInterestHisMapper;
    @Autowired
    private PmInterestHisSelfMapper pmInterestHisSelfMapper;


    /**
     * 通过机构号,tableId查询利率参数信息
     *
     * @param organizationNumber 机构号
     * @param tableId            表Id
     * @return InterestRes
     */
    @Override
    public InterestResDTO findByOrgAndTableId(String organizationNumber, String tableId){
        ParmInterest parmInterest = parmInterestSelfMapper.isExists(organizationNumber, tableId);
        if (parmInterest == null) {

            throw new AnyTxnParameterException(AnyTxnParameterRespCodeEnum.D_QUERY_PARM_INTEREST_FAULT);
        }
        return BeanMapping.copy(parmInterest, InterestResDTO.class);
    }

    /**
     * 通过Id查询利率参数信息
     * @param id 主键id
     * @return InterestRes
     */
    @Override
    public InterestResDTO findById(String id){
        if (id == null) {

            throw new AnyTxnParameterException(AnyTxnParameterRespCodeEnum.P_PARAMETER_EMPTY_FAULT);
        }
        ParmInterest parmInterest = parmInterestMapper.selectByPrimaryKey(id);
        if (parmInterest == null) {

            throw new AnyTxnParameterException(AnyTxnParameterRespCodeEnum.D_QUERY_PARM_INTEREST_BY_ID_FAULT);
        }
        return BeanMapping.copy(parmInterest, InterestResDTO.class);
    }

    /**
     * 通过表状态查询利率参数信息
     * @param status 状态
     * @return List<InterestRes>
     */
    @Override
    public List<InterestResDTO> findByStatus(String status) {
        if (StringUtils.isEmpty(status)) {

            throw new AnyTxnParameterException(AnyTxnParameterRespCodeEnum.P_PARAMETER_EMPTY_FAULT);
        }
        List<ParmInterest> parmInterests = parmInterestSelfMapper.selectByStatus(status, OrgNumberUtils.getOrg());
        if (parmInterests.isEmpty()) {

            throw new AnyTxnParameterException(AnyTxnParameterRespCodeEnum.D_QUERY_PARM_INTEREST_BY_STATUS_FAULT);
        }

        return BeanMapping.copyList(parmInterests, InterestResDTO.class);
    }

    /**
     * 添加利率参数
     * @param interestReq InterestReqDTO
     * @return 利率参数响应参数
     */
    @Override
    @Transactional(rollbackFor = Exception.class,propagation = Propagation.REQUIRED)
    @InsertParameterAnnotation(tableName = "parm_interest", tableDesc = "Interest Rate")
    public ParameterCompare addParmInterest(InterestReqDTO interestReq){
        ParmInterest isExsit = parmInterestSelfMapper.isExists(OrgNumberUtils.getOrg(interestReq.getOrganizationNumber()), interestReq.getTableId());
        if (isExsit != null) {

            throw new AnyTxnParameterException(AnyTxnParameterRespCodeEnum.D_EXIST_PARM_INTEREST_FAULT);
        }
        ParmInterest parmInterest = BeanMapping.copy(interestReq, ParmInterest.class);
        parmInterest.setId(String.valueOf(numberIdGenerator.generateId(TenantUtils.getTenantId())));
        return ParameterCompare.getBuilder().withAfter(parmInterest).build(InterestReqDTO.class);
    }

    /**
     * 更新利率参数
     * @param  interestReq 利率参数入参对象
     * @return 利率参数响应参数
     */
    @Transactional(rollbackFor = Exception.class,propagation = Propagation.REQUIRED)
    @Override
    @UpdateParameterAnnotation(tableName = "parm_interest", tableDesc = "Interest Rate")
    public ParameterCompare modifyParmInterest(InterestReqDTO interestReq){
        // 查询此记录是否存在
        ParmInterest interest = parmInterestMapper.selectByPrimaryKey(interestReq.getId());
        if (null == interest) {

            throw new AnyTxnParameterException(AnyTxnParameterRespCodeEnum.D_QUERY_PARM_INTEREST_BY_ID_FAULT);
        }

        // 判断修改后的数据在表里是否是以机构号,表id唯一确定一条数据
        if (!interest.getOrganizationNumber().equals(interestReq.getOrganizationNumber())
                || !interest.getTableId().equals(interestReq.getTableId())) {
            ParmInterest isExsit = parmInterestSelfMapper.isExists(interestReq.getOrganizationNumber(), interestReq.getTableId());
            if (isExsit != null) {

                throw new AnyTxnParameterException(AnyTxnParameterRespCodeEnum.D_EXIST_PARM_INTEREST_FAULT);
            }
        }

        ParmInterest parmInterest = BeanMapping.copy(interestReq, ParmInterest.class);

        return ParameterCompare.getBuilder().withAfter(parmInterest).withBefore(interest).build(ParmInterest.class);
    }



    /**
     * 通过Id主键删除利率参数
     * @param  id 主键
     * @return Boolean
     */
    @Transactional(rollbackFor = Exception.class,propagation = Propagation.REQUIRED)
    @Override
    @DeleteParameterAnnotation(tableName = "parm_interest", tableDesc = "Interest Rate")
    public ParameterCompare removeParmInterest(String id){
        ParmInterest parmInterest=new ParmInterest();
        // 如果id不存在，则查询该记录
        if (null != id) {
            parmInterest = parmInterestMapper.selectByPrimaryKey(id);
        }
        // 如果该记录不存在，抛出异常，否则就删除该记录
        if (null == parmInterest) {

            throw new AnyTxnParameterException(AnyTxnParameterRespCodeEnum.D_QUERY_PARM_INTEREST_BY_ID_FAULT);
        }
        return ParameterCompare.getBuilder().withBefore(parmInterest).build(ParmInterest.class);
    }

    /**
     * 查询所有利率参数
     * @return 利率参数响应参数
     * @param pageNum Integer
     * @param pageSize Integer
     */
    @Override
    public PageResultDTO<InterestResDTO> findAll(Integer pageNum, Integer pageSize){
        Page<ParmInterest> page = PageHelper.startPage(pageNum, pageSize);
        List<ParmInterest> parmInterests = parmInterestSelfMapper.selectAll(false, OrgNumberUtils.getOrg());
        if (parmInterests.isEmpty()) {

            throw new AnyTxnParameterException(AnyTxnParameterRespCodeEnum.D_GET_IS_NULL_FAULT);
        }
        List<InterestResDTO> res = BeanMapping.copyList(parmInterests, InterestResDTO.class);
        return new PageResultDTO<>(pageNum,pageSize,page.getTotal(), page.getPages(), res);
    }

    @Override
    public PageResultDTO<InterestResDTO> findByCondition(Integer pageNum, Integer pageSize, InterestSearchDTO interestSearch){
        if(null == interestSearch){
            interestSearch = new InterestSearchDTO();
        }
        if(!StringUtils.isEmpty(interestSearch.getBaseRate())){
            BigDecimal baseRate = null;
            try {
                baseRate = new BigDecimal(interestSearch.getBaseRate());
            }catch (NumberFormatException e){
                throw new AnyTxnParameterException(AnyTxnParameterRespCodeEnum.PARAMETER_EXCEPTION, ParameterRepDetailEnum.NUMBER_ERROR);
            }
            interestSearch.setBaseRateDeci(baseRate);
        }

        Page<ParmInterest> page = PageHelper.startPage(pageNum, pageSize);
        interestSearch.setOrganizationNumber(StringUtils.isEmpty(interestSearch.getOrganizationNumber()) ? OrgNumberUtils.getOrg() : interestSearch.getOrganizationNumber());
        List<ParmInterest> parmInterests = parmInterestSelfMapper.selectByCondition(interestSearch);

        List<InterestResDTO> res = BeanMapping.copyList(parmInterests, InterestResDTO.class);
        return new PageResultDTO<>(pageNum,pageSize,page.getTotal(), page.getPages(), res);
    }



    /**
     * 插入利率历史数据
     * @param interestReq InterestReqDTO
     */
    private void insertPmInterestHis(InterestReqDTO interestReq,String operateType) {
        PmInterestHis pmInterestHis = new PmInterestHis();
        pmInterestHis.setId(numberIdGenerator.generateId(TenantUtils.getTenantId()));
        pmInterestHis.setOrganizationNumber(interestReq.getOrganizationNumber());
        pmInterestHis.setInterestTableId(interestReq.getTableId());
        pmInterestHis.setJsonValue(JSON.toJSONString(interestReq));
        ParmOrganizationInfo organizationInfo = parmOrganizationInfoSelfMapper.selectByOrganizationNumber(interestReq.getOrganizationNumber());
        pmInterestHis.setOperateDate(organizationInfo.getAccruedThruDay().plusDays(1));
        pmInterestHis.setOperateType(operateType);
        pmInterestHis.setStatus(Constants.ENABLED);
        pmInterestHis.setCreateTime(LocalDateTime.now());
        pmInterestHis.setUpdateBy(Constants.DEFAULT_USER);
        pmInterestHis.setVersionNumber(1L);
        int i = pmInterestHisMapper.insert(pmInterestHis);
        if(i <1 ){
            log.error("插入利息历史表失败,org:{},interestTableId:{}",pmInterestHis.getOrganizationNumber(),pmInterestHis.getInterestTableId());
            throw new AnyTxnParameterException(AnyTxnParameterRespCodeEnum.D_DATABASE_UPDATE_ERROR);

        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class,propagation = Propagation.REQUIRED)
    //@ShardingSphereTransactionType(TransactionType.XA)
    protected boolean updateDb(ParmModificationRecord parmModificationRecord) {
        ParmInterest parmInterest = JSON.parseObject(parmModificationRecord.getParmBody(), ParmInterest.class);
        parmInterest.initUpdateDateTime();
        parmInterestMapper.updateByPrimaryKeySelective(parmInterest);
        InterestReqDTO copy = BeanMapping.copy(parmInterest, InterestReqDTO.class);
        insertPmInterestHis(copy,Constants.OPERATE_UPDATE);
        return true;
    }

    @Override
    @Transactional(rollbackFor = Exception.class,propagation = Propagation.REQUIRED)
    //@ShardingSphereTransactionType(TransactionType.XA)
    protected boolean insertDb(ParmModificationRecord parmModificationRecord) {
        ParmInterest parmInterest = JSON.parseObject(parmModificationRecord.getParmBody(), ParmInterest.class);
        parmInterest.initCreateDateTime();
        parmInterest.initUpdateDateTime();
        parmInterestMapper.insertSelective(parmInterest);
        InterestReqDTO copy = BeanMapping.copy(parmInterest, InterestReqDTO.class);
        insertPmInterestHis(copy,Constants.OPERATE_INSERT);
        return true;
    }

    @Override
    @Transactional(rollbackFor = Exception.class,propagation = Propagation.REQUIRED)
    //@ShardingSphereTransactionType(TransactionType.XA)
    protected boolean deleteDb(ParmModificationRecord parmModificationRecord) {
        ParmInterest parmInterest = JSON.parseObject(parmModificationRecord.getParmBody(), ParmInterest.class);
        insertPmInterestHis(BeanMapping.copy(parmInterest,InterestReqDTO.class),Constants.OPERATE_DELETE);
        parmInterestMapper.deleteByPrimaryKey(parmInterest.getId());
        return true;
    }
}
