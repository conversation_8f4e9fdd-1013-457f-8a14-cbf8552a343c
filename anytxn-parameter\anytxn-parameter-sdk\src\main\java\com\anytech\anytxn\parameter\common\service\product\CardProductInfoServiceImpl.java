package com.anytech.anytxn.parameter.common.service.product;

import com.alibaba.fastjson.JSON;
import com.github.pagehelper.Page;
import com.github.pagehelper.PageHelper;
import com.anytech.anytxn.common.core.annotation.db.DeleteParameterAnnotation;
import com.anytech.anytxn.common.core.annotation.db.InsertParameterAnnotation;
import com.anytech.anytxn.common.core.annotation.db.UpdateParameterAnnotation;
import com.anytech.anytxn.business.base.audit.AbstractParameterService;
import com.anytech.anytxn.common.core.base.ParameterCompare;
import com.anytech.anytxn.business.base.audit.ParmModificationRecord;
import com.anytech.anytxn.common.core.base.PageResultDTO;
import com.anytech.anytxn.common.core.utils.BeanMapping;
import com.anytech.anytxn.common.core.utils.OrgNumberUtils;
import com.anytech.anytxn.common.sequence.utils.Number16IdGen;
import com.anytech.anytxn.common.core.utils.TenantUtils;
import com.anytech.anytxn.parameter.base.common.constants.Constants;
import com.anytech.anytxn.parameter.base.common.enums.AnyTxnParameterRespCodeEnum;
import com.anytech.anytxn.parameter.base.common.enums.ParameterRepDetailEnum;
import com.anytech.anytxn.parameter.base.common.exception.AnyTxnParameterException;
import com.anytech.anytxn.parameter.card.mapper.broadcast.*;
import com.anytech.anytxn.parameter.common.mapper.broadcast.product.ParmProductInfoSelfMapper;
import com.anytech.anytxn.parameter.base.card.domain.model.CarCurrencyStatus;
import com.anytech.anytxn.parameter.base.card.domain.model.ParmCardCurrencyInfo;
import com.anytech.anytxn.parameter.base.card.domain.model.ParmCardProductInfo;
import com.anytech.anytxn.parameter.base.card.domain.model.ParmCardProductTicketInfo;
import com.anytech.anytxn.parameter.base.common.domain.model.ParmProductInfo;
import com.anytech.anytxn.parameter.base.common.domain.dto.CarCurrencyStatusDTO;
import com.anytech.anytxn.parameter.base.common.domain.dto.product.CardProductInfoReqDTO;
import com.anytech.anytxn.parameter.base.common.domain.dto.product.CardProductInfoResDTO;
import com.anytech.anytxn.parameter.base.common.domain.dto.product.CardProductTicketInfoDTO;
import com.anytech.anytxn.parameter.base.common.service.product.ICardProductInfoService;
import org.apache.commons.lang3.StringUtils;
//import org.apache.shardingsphere.transaction.annotation.ShardingSphereTransactionType;
//import org.apache.shardingsphere.transaction.core.TransactionType;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import jakarta.annotation.Resource;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 卡产品信息
 *
 * <AUTHOR>
 * @date 2018-12-04
 **/
@Service("parm_card_product_info_serviceImpl")
public class CardProductInfoServiceImpl extends AbstractParameterService implements ICardProductInfoService {

    private final Logger logger = LoggerFactory.getLogger(CardProductInfoServiceImpl.class);

    @Resource
    private ParmCardProductInfoMapper parmCardProductInfoMapper;
    @Autowired
    private ParmCardProductInfoSelfMapper parmCardProductInfoSelfMapper;
    @Autowired
    private ParmCardCurrencyInfoMapper parmCardCurrencyInfoMapper;
    @Autowired
    private ParmCardCurrencyInfoSelfMapper parmCardCurrencyInfoSelfMapper;
    @Autowired
    private ParmProductInfoSelfMapper parmProductInfoSelfMapper;
    @Autowired
    private Number16IdGen numberIdGenerator;
    @Resource
    private ParmCardProductTicketInfoMapper parmCardProductTicketInfoMapper;

    /**
     * 根据机构号、卡产品编号查询卡产品信息
     * @param organizationNumber 机构号
     * @param productNumber 卡产品编号
     * @return 卡产品信息返回参数
     * @throws AnyTxnParameterException
     */
    @Override
    public CardProductInfoResDTO findByOrgAndProductNum(String organizationNumber, String productNumber){

        logger.info("机构号:{},卡产品号:{}",organizationNumber,productNumber);
        ParmCardProductInfo productInfo = parmCardProductInfoSelfMapper.selectByOrgAndProductNum(organizationNumber, productNumber);
        if (productInfo==null) {
            logger.error("获得卡产品参数, 通过organizationNumber={}, productNumber={} 未查到数据", organizationNumber, productNumber);
            return null;
        }
        CardProductInfoResDTO productInfoResDTO = BeanMapping.copy(productInfo, CardProductInfoResDTO.class);
        ParmCardProductTicketInfo parmCardProductTicketInfo = parmCardProductTicketInfoMapper.selectByOrgAndCardProduct(organizationNumber, productNumber);
        CardProductTicketInfoDTO productTicketInfo = new CardProductTicketInfoDTO();
        if(null != parmCardProductTicketInfo){
            BeanMapping.copy(parmCardProductTicketInfo,productTicketInfo);
        }
        productInfoResDTO.setCardProductTicket(productTicketInfo);
        return productInfoResDTO;
    }

    @Override
    public List<CarCurrencyStatusDTO> findForeignCardCurrencySwitch(String organizationNumber, List<String> cardNumbers) {
        List<CarCurrencyStatus> productInfo = parmCardProductInfoSelfMapper.findForeignCardCurrencySwitch(organizationNumber, cardNumbers);
        return BeanMapping.copyList(productInfo, CarCurrencyStatusDTO.class);
    }

    /**
     * 增加卡产品
     * @param cardProductInfoReq 卡产品请求信息
     * @return CardProductInfoRes 卡产品响应信息
     * @throws AnyTxnParameterException 异常
     */
    @Override
    @InsertParameterAnnotation(tableName = "parm_card_product_info", tableDesc = "Card product", isJoinTable = true)
    public ParameterCompare add(CardProductInfoReqDTO cardProductInfoReq){
        ParmCardProductInfo productInfo = parmCardProductInfoSelfMapper.selectByOrgAndProductNum(cardProductInfoReq.getOrganizationNumber(), cardProductInfoReq.getProductNumber());
        if (productInfo != null) {
            logger.warn("卡产品信息已存在, Organization ={} ProductNumber={}",
                    cardProductInfoReq.getOrganizationNumber(), cardProductInfoReq.getProductNumber());
            throw new AnyTxnParameterException(AnyTxnParameterRespCodeEnum.S_EXIST_PARM_CARD_PRODUCT_INFO_FAULT);
        }
        cardProductInfoReq.setId(String.valueOf(numberIdGenerator.generateId(TenantUtils.getTenantId())));
        checkCurrencyCode(cardProductInfoReq);

        return ParameterCompare.getBuilder()
                .withAfter(cardProductInfoReq)
                .withMainParmId(cardProductInfoReq.getProductNumber()).build(CardProductInfoReqDTO.class);
    }

    private void addCardProductTicket(CardProductInfoReqDTO cardProductInfoReq) {
        CardProductTicketInfoDTO cardProductTicket = cardProductInfoReq.getCardProductTicket();
        if(null != cardProductTicket){
            ParmCardProductTicketInfo parmCardProductTicketInfo = BeanMapping.copy(cardProductTicket,ParmCardProductTicketInfo.class);
            parmCardProductTicketInfo.setId(String.valueOf(numberIdGenerator.generateId(TenantUtils.getTenantId())));
            parmCardProductTicketInfo.setOrganizationNumber(cardProductInfoReq.getOrganizationNumber());
            parmCardProductTicketInfo.setCardProductNumber(cardProductInfoReq.getProductNumber());
            parmCardProductTicketInfo.setCreateTime(LocalDateTime.now());
            parmCardProductTicketInfo.setUpdateBy(Constants.DEFAULT_USER);
            parmCardProductTicketInfo.setUpdateTime(LocalDateTime.now());
            parmCardProductTicketInfo.setVersionNumber(1L);
            parmCardProductTicketInfoMapper.insert(parmCardProductTicketInfo);
        }
    }

    private void checkCurrencyCode(CardProductInfoReqDTO cardProductInfoReq) {
        List<ParmProductInfo> parmProductInfos = parmProductInfoSelfMapper.selectByProdNumberAndOrganizationNumber(cardProductInfoReq.getAccountProductNumber(), cardProductInfoReq.getOrganizationNumber());
        List<String> accountCurrencyCodes = parmProductInfos.stream().map(ParmProductInfo::getCurrencyCode).distinct().collect(Collectors.toList());
        List<String> currencyList = cardProductInfoReq.getCurrencyList();
        for (String currency : currencyList) {
            if(!accountCurrencyCodes.contains(currency)){
                logger.error("卡产品币种超出账产品币种范围");
                throw new AnyTxnParameterException(AnyTxnParameterRespCodeEnum.D_GET_IS_NULL_FAULT, ParameterRepDetailEnum.CARD_PRODUCT_CURRENCY);
            }
        }
    }

    /**
     * 删除卡产品信息条目，通过id
     * @param id 技术id
     * @return Boolean
     */
    @Override
    @DeleteParameterAnnotation(tableName = "parm_card_product_info", tableDesc = "Card product")
    public ParameterCompare remove(String id){
        if (null == id) {
            throw new AnyTxnParameterException(AnyTxnParameterRespCodeEnum.P_PARAMETER_EMPTY_FAULT);
        }

        ParmCardProductInfo cardProductInfo = parmCardProductInfoMapper.selectByPrimaryKey(id);

        if (cardProductInfo == null) {
            logger.error("删除卡产品信息, 通过主键id({})未找到数据", id);
            throw new AnyTxnParameterException(AnyTxnParameterRespCodeEnum.S_QUERY_PARM_CARD_PRODUCT_INFO_BY_ID_FAULT);
        }
        return ParameterCompare.getBuilder()
                .withBefore(cardProductInfo)
                .build(ParmCardProductInfo.class);
    }

    /**
     * 通过id获取详情
     *
     * @param id 主键id
     * @return 卡产品信息详情
     * @throws AnyTxnParameterException
     */
    @Override
    public CardProductInfoResDTO find(String id){
        if (id == null) {
            throw new AnyTxnParameterException(AnyTxnParameterRespCodeEnum.P_PARAMETER_EMPTY_FAULT);
        }

        ParmCardProductInfo cardProductInfo = parmCardProductInfoMapper.selectByPrimaryKey(id);

        if (cardProductInfo == null) {
            logger.error("查询卡产品信息, 通过主键id({})未找到数据", id);
            throw new AnyTxnParameterException(AnyTxnParameterRespCodeEnum.S_QUERY_PARM_CARD_PRODUCT_INFO_BY_ID_FAULT);
        }

        List<ParmCardCurrencyInfo> cardCurrencyInfoList = parmCardCurrencyInfoSelfMapper.selectByOrgAndProductNum(cardProductInfo.getOrganizationNumber(),cardProductInfo.getProductNumber());

        if (cardCurrencyInfoList.size()==0) {
            logger.error("根据机构号和产品编码查询卡币对照关系, organizationNumber={},productNumber={},", cardProductInfo.getOrganizationNumber(),cardProductInfo.getProductNumber());
            throw new AnyTxnParameterException(AnyTxnParameterRespCodeEnum.S_QUERY_PARM_CARD_CURRENCY_INFO_BY_ORGNUM_PRODUCTNUM_FAULT);
        }

        List<String> currencyList = new ArrayList<>();
        for (ParmCardCurrencyInfo item : cardCurrencyInfoList){
            currencyList.add(item.getCurrencyCode());
        }
        CardProductInfoResDTO copy = BeanMapping.copy(cardProductInfo, CardProductInfoResDTO.class);
        copy.setCurrencyList(currencyList);
        ParmCardProductTicketInfo parmCardProductTicketInfo = parmCardProductTicketInfoMapper.selectByOrgAndCardProduct(cardProductInfo.getOrganizationNumber(), cardProductInfo.getProductNumber());
        CardProductTicketInfoDTO productTicketInfo = new CardProductTicketInfoDTO();
        if(null != parmCardProductTicketInfo){
            BeanMapping.copy(parmCardProductTicketInfo,productTicketInfo);
        }
        copy.setCardProductTicket(productTicketInfo);
        return copy;
    }

    /**
     * 更新卡产品信息
     * @param cardProductInfoReq 卡产品信息请求数据
     * @return CardProductInfoRes
     */
    @Override
    @UpdateParameterAnnotation(tableName = "parm_card_product_info", tableDesc = "Card product", isJoinTable = true)
    public ParameterCompare modify(CardProductInfoReqDTO cardProductInfoReq){
        if (cardProductInfoReq.getId() == null) {
            throw new AnyTxnParameterException(AnyTxnParameterRespCodeEnum.P_PARAMETER_EMPTY_FAULT);
        }

        CardProductInfoResDTO beforeCardProductInfo = find(String.valueOf(cardProductInfoReq.getId()));

        //检查币种
        checkCurrencyCode(cardProductInfoReq);

        return ParameterCompare.getBuilder()
                .withAfter(cardProductInfoReq)
                .withBefore(BeanMapping.copy(beforeCardProductInfo,CardProductInfoReqDTO.class))
                .withMainParmId(cardProductInfoReq.getProductNumber())
                .build(CardProductInfoReqDTO.class);
    }

    /**
     *
     * @param pageNum 页数
     * @param pageSize 每页展示条数
     * @return PageResultDTO<CardProductInfoRes>
     * @throws AnyTxnParameterException
     */
    @Override
    public PageResultDTO<CardProductInfoResDTO> findAll(Integer pageNum, Integer pageSize, CardProductInfoReqDTO cardProductInfoReq){
        if(null == cardProductInfoReq){
            cardProductInfoReq = new CardProductInfoReqDTO();
        }
        cardProductInfoReq.setOrganizationNumber(StringUtils.isEmpty(cardProductInfoReq.getOrganizationNumber()) ? OrgNumberUtils.getOrg() : cardProductInfoReq.getOrganizationNumber());
        Page<ParmCardProductInfo> page = PageHelper.startPage(pageNum, pageSize);
        List<ParmCardProductInfo> cardProductList = parmCardProductInfoSelfMapper.selectByCondition(cardProductInfoReq);

        /*if (cardProductList.isEmpty()) {
            throw new AnyTxnParameterException(AnyTxnParameterRespCodeEnum.S_PAGE_QUERY_PARM_CARD_PRODUCT_INFO_FAULT);
        }*/
        List<CardProductInfoResDTO> res = BeanMapping.copyList(cardProductList, CardProductInfoResDTO.class);
        return new PageResultDTO<>(pageNum, pageSize, page.getTotal(), page.getPages(),res);
    }

    /**
     * 根据机构号查询卡产品信息
     * @param organizationNumber 机构号
     * @return 卡产品信息返回参数
     * @throws AnyTxnParameterException 异常
     */
    @Override
    public List<CardProductInfoResDTO> findCardProductsByOrg(String organizationNumber){
        List<ParmCardProductInfo> productInfoList = parmCardProductInfoSelfMapper.selectCardProductsByOrg(organizationNumber);
        if (!CollectionUtils.isEmpty(productInfoList)) {
            return BeanMapping.copyList(productInfoList, CardProductInfoResDTO.class);
        }
        return null;
    }

    @Override
    @Transactional(rollbackFor = Exception.class,propagation = Propagation.REQUIRED)
    //@ShardingSphereTransactionType(TransactionType.XA)
    protected boolean updateDb(ParmModificationRecord parmModificationRecord) {
        CardProductInfoReqDTO cardProductInfoReq = JSON.parseObject(parmModificationRecord.getParmBody(), CardProductInfoReqDTO.class);

        //检查币种
        checkCurrencyCode(cardProductInfoReq);

        // 拷贝修改的数据并更新
        ParmCardProductInfo parmProductInfo = BeanMapping.copy(cardProductInfoReq, ParmCardProductInfo.class);
        parmProductInfo.initUpdateDateTime();
        parmProductInfo.setUpdateBy(parmModificationRecord.getApplicationBy());
        parmProductInfo.setContactlessCardFlag(cardProductInfoReq.getContactlessCardFlag());
        parmCardProductInfoMapper.updateByPrimaryKeySelective(parmProductInfo);

        //更新卡币对照关系表（先delete后insert）
        List<ParmCardCurrencyInfo> cardCurrencyInfoList = parmCardCurrencyInfoSelfMapper.selectByOrgAndProductNum(cardProductInfoReq.getOrganizationNumber(), cardProductInfoReq.getProductNumber());
        if(cardCurrencyInfoList==null || cardCurrencyInfoList.size()==0){
            logger.error("修改卡币对照关系，通过机构号和产品编码，（organizationNumber={},productNumber={}）未找到数据源",cardProductInfoReq.getOrganizationNumber(),cardProductInfoReq.getProductNumber());
            throw new AnyTxnParameterException(AnyTxnParameterRespCodeEnum.S_QUERY_PARM_CARD_CURRENCY_INFO_BY_ORGNUM_PRODUCTNUM_FAULT);
        }
        for (ParmCardCurrencyInfo item : cardCurrencyInfoList){
            parmCardCurrencyInfoMapper.deleteByPrimaryKey(item.getId());
        }
        ParmCardCurrencyInfo parmCardCurrencyInfo = BeanMapping.copy(cardProductInfoReq, ParmCardCurrencyInfo.class);
        parmCardCurrencyInfo.setUpdateTime(LocalDateTime.now());
        parmCardCurrencyInfo.setUpdateBy(Constants.DEFAULT_USER);
        parmCardCurrencyInfo.setVersionNumber(1L);
        List<String> currencyList = cardProductInfoReq.getCurrencyList();
        for (String currencyCode : currencyList){
            parmCardCurrencyInfo.setId(numberIdGenerator.generateId(TenantUtils.getTenantId()));
            parmCardCurrencyInfo.setCurrencyCode(currencyCode);
            parmCardCurrencyInfoMapper.insert(parmCardCurrencyInfo);
        }
        CardProductTicketInfoDTO cardProductTicket = cardProductInfoReq.getCardProductTicket();
        if(null != cardProductTicket){
            ParmCardProductTicketInfo productTicketInfo = parmCardProductTicketInfoMapper.selectByOrgAndCardProduct(cardProductInfoReq.getOrganizationNumber(), cardProductInfoReq.getProductNumber());
            if(null == productTicketInfo){
                addCardProductTicket(cardProductInfoReq);
            }else {
                cardProductTicket.setOrganizationNumber(cardProductInfoReq.getOrganizationNumber());
                cardProductTicket.setCardProductNumber(cardProductInfoReq.getProductNumber());
                parmCardProductTicketInfoMapper.updateByOrgAndCardProduct(BeanMapping.copy(cardProductTicket,ParmCardProductTicketInfo.class));
            }

        }

        return true;
    }

    @Override
    @Transactional(rollbackFor = Exception.class,propagation = Propagation.REQUIRED)
    //@ShardingSphereTransactionType(TransactionType.XA)
    protected boolean insertDb(ParmModificationRecord parmModificationRecord) {

        CardProductInfoReqDTO cardProductInfoReq = JSON.parseObject(parmModificationRecord.getParmBody(), CardProductInfoReqDTO.class);

        checkCurrencyCode(cardProductInfoReq);

        //构建卡产品详情
        ParmCardProductInfo cardProductInfo = BeanMapping.copy(cardProductInfoReq, ParmCardProductInfo.class);
        cardProductInfo.initCreateDateTime();
        cardProductInfo.setVersionNumber(1L);
        cardProductInfo.initUpdateDateTime();
        cardProductInfo.setUpdateBy(parmModificationRecord.getApplicationBy());
        cardProductInfo.setId(cardProductInfoReq.getId());

        parmCardProductInfoMapper.insertSelective(cardProductInfo);

        cardProductInfoReq.setId(cardProductInfo.getId());

        ParmCardCurrencyInfo parmCardCurrencyInfo = BeanMapping.copy(cardProductInfoReq, ParmCardCurrencyInfo.class);
        for(String currencyCode : cardProductInfoReq.getCurrencyList()){
            //判断卡币对照关系是否已存在
            boolean exists = parmCardCurrencyInfoSelfMapper.isExists(cardProductInfoReq.getOrganizationNumber(), cardProductInfoReq.getProductNumber(), currencyCode);
            if(exists){
                logger.error("卡币对照关系已存在,organizationNumber={},productNumber={},currencyCode={}",cardProductInfoReq.getOrganizationNumber(),cardProductInfoReq.getProductNumber(),currencyCode);
                throw new AnyTxnParameterException(AnyTxnParameterRespCodeEnum.S_QUERY_PARM_CARD_CURRENCY_IS_EXISTED);
            }
            //构建卡币对照关系
            parmCardCurrencyInfo.setCurrencyCode(currencyCode);
            parmCardCurrencyInfo.setId(numberIdGenerator.generateId(TenantUtils.getTenantId()));
            parmCardCurrencyInfo.setCreateTime(LocalDateTime.now());
            parmCardCurrencyInfo.setUpdateTime(LocalDateTime.now());
            parmCardCurrencyInfo.setUpdateBy(Constants.DEFAULT_USER);
            parmCardCurrencyInfo.setVersionNumber(1L);
            parmCardCurrencyInfoMapper.insert(parmCardCurrencyInfo);
        }
        /**
         * 票证卡相关信息
         */
        addCardProductTicket(cardProductInfoReq);

        return true;
    }


    @Override
    @Transactional(rollbackFor = Exception.class,propagation = Propagation.REQUIRED)
    //@ShardingSphereTransactionType(TransactionType.XA)
    protected boolean deleteDb(ParmModificationRecord parmModificationRecord) {
        ParmCardProductInfo cardProductInfo = JSON.parseObject(parmModificationRecord.getParmBody(), ParmCardProductInfo.class);

        logger.warn("通过id删除卡产品信息, {}", cardProductInfo);
        int deleteRows = parmCardProductInfoMapper.deleteByPrimaryKey(cardProductInfo.getId());

        List<ParmCardCurrencyInfo> cardCurrencyInfoList = parmCardCurrencyInfoSelfMapper.selectByOrgAndProductNum(cardProductInfo.getOrganizationNumber(),cardProductInfo.getProductNumber());

        if(cardCurrencyInfoList==null || cardCurrencyInfoList.size()==0){
            logger.error("删除卡币对照关系，通过机构号和产品编码，（organizationNumber={},productNumber={}）未找到数据源",cardProductInfo.getOrganizationNumber(),cardProductInfo.getProductNumber());
            throw new AnyTxnParameterException(AnyTxnParameterRespCodeEnum.S_QUERY_PARM_CARD_CURRENCY_INFO_BY_ORGNUM_PRODUCTNUM_FAULT);
        }

        logger.info("通过机构号和产品编码删除卡币对照关系，organizationNumber={}，productNumber={}",cardProductInfo.getOrganizationNumber(),cardProductInfo.getProductNumber());
        boolean result = false;
        for (ParmCardCurrencyInfo item : cardCurrencyInfoList){
            result = parmCardCurrencyInfoMapper.deleteByPrimaryKey(item.getId()) > 0;
        }
        parmCardProductTicketInfoMapper.deleteByOrgAndCardProduct(cardProductInfo.getOrganizationNumber(),cardProductInfo.getProductNumber());
        return deleteRows > 0 && result;
    }
}
