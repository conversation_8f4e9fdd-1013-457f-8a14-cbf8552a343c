package com.anytech.anytxn.parameter.settlement.controller;

import com.anytech.anytxn.common.core.base.PageResultDTO;
import com.anytech.anytxn.common.core.base.AnyTxnHttpResponse;
import com.anytech.anytxn.common.core.base.BizBaseController;
import com.anytech.anytxn.parameter.base.common.constants.Constants;
import com.anytech.anytxn.parameter.base.common.enums.AnyTxnParameterRespCodeEnum;
import com.anytech.anytxn.parameter.base.settlement.domain.dto.DinersFranchiseCycleRangeDTO;
import com.anytech.anytxn.parameter.base.settlement.service.IDinersFranchiseCycleRangeService;
import io.swagger.v3.oas.annotations.tags.Tag;
import io.swagger.v3.oas.annotations.Operation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * <AUTHOR>
 */
@RestController
@Tag(name = "DinersFranchiseCycleRangeController")
public class DinersFranchiseCycleRangeController extends BizBaseController {

    @Autowired
    private IDinersFranchiseCycleRangeService dinersFranchiseCycleRangeService;

    @Operation(summary = "FranchiseCycleRange添加")
    @PostMapping(value="/param/franchise/cyrg/add")
    public AnyTxnHttpResponse addDinersCpDccyrg(@RequestBody DinersFranchiseCycleRangeDTO dinersFranchiseCycleRangeDTO){
        Integer crBinStart = dinersFranchiseCycleRangeDTO.getCrBinStart();
        Integer crBinEnd = dinersFranchiseCycleRangeDTO.getCrBinEnd();
        if (crBinStart != null && crBinEnd != null && crBinEnd < crBinStart){
            return AnyTxnHttpResponse.fail(AnyTxnParameterRespCodeEnum.DINERS_FRANCHISE_CYCLERANGE_BIN_ILLEGAL.getCode(),AnyTxnParameterRespCodeEnum.DINERS_FRANCHISE_CYCLERANGE_BIN_ILLEGAL.getMsg());
        }
        List<DinersFranchiseCycleRangeDTO> list = dinersFranchiseCycleRangeService.select(dinersFranchiseCycleRangeDTO);
        if (list.size()==0){
            dinersFranchiseCycleRangeDTO.setUpdateBy(Constants.DEFAULT_USER);
            dinersFranchiseCycleRangeService.add(dinersFranchiseCycleRangeDTO);
            return AnyTxnHttpResponse.success();
        }else {
            return AnyTxnHttpResponse.fail(AnyTxnParameterRespCodeEnum.DINERS_FRANCHISE_CYCLERANGE_BIN_OCCUPIED.getCode(),AnyTxnParameterRespCodeEnum.DINERS_FRANCHISE_CYCLERANGE_BIN_OCCUPIED.getMsg());
        }
    }

    @Operation(summary = "FranchiseCycleRange更新")
    @PostMapping(value="/param/franchise/cyrg/update")
    public AnyTxnHttpResponse updateDinersCpDccyrg(@RequestBody DinersFranchiseCycleRangeDTO dinersFranchiseCycleRangeDTO) {
        dinersFranchiseCycleRangeDTO.setUpdateBy(Constants.DEFAULT_USER);
        dinersFranchiseCycleRangeService.modify(dinersFranchiseCycleRangeDTO);
        return AnyTxnHttpResponse.success();
    }

    @Operation(summary = "FranchiseCycleRange根据bin搜索")
    @GetMapping(value="/param/franchise/cyrg/select/{pageNum}/{pageSize}")
    public AnyTxnHttpResponse<PageResultDTO<DinersFranchiseCycleRangeDTO>> selectDinersCpDccyrg(@PathVariable(value = "pageNum") Integer pageNum,
                                                                                                @PathVariable(value = "pageSize") Integer pageSize,
                                                                                                @RequestParam String crBin){
        DinersFranchiseCycleRangeDTO dinersFranchiseCycleRangeDTO = new DinersFranchiseCycleRangeDTO();
        if("".equals(crBin)){
            dinersFranchiseCycleRangeDTO.setCrBinStart(null);
        }else {
            dinersFranchiseCycleRangeDTO.setCrBinStart(Integer.valueOf(crBin));
        }
        PageResultDTO<DinersFranchiseCycleRangeDTO> dinersFranchiseCycleRangeList = dinersFranchiseCycleRangeService.page(pageNum,pageSize, dinersFranchiseCycleRangeDTO);
        return AnyTxnHttpResponse.success(dinersFranchiseCycleRangeList);
    }

    @Operation(summary = "FranchiseCycleRange查询")
    @GetMapping(value="/param/franchise/cyrg/enquiry")
    public AnyTxnHttpResponse<DinersFranchiseCycleRangeDTO> enquiryDinersCpDccyrg(@RequestParam String crBinStart,
                                                                                  @RequestParam String crBinEnd){
        return AnyTxnHttpResponse.success(dinersFranchiseCycleRangeService.findOne(crBinStart,crBinEnd));
    }

    @Operation(summary = "FranchiseCycleRange删除")
    @DeleteMapping(value="/param/franchise/cyrg/delete")
    public AnyTxnHttpResponse deleteDinersCpDccyrg(@RequestParam String crBinStart,
                                                   @RequestParam String crBinEnd){
        dinersFranchiseCycleRangeService.remove(crBinStart,crBinEnd);
        return AnyTxnHttpResponse.success();
    }

    @Operation(summary = "FranchiseCycleRange根据franchise code搜索")
    @GetMapping(value="/param/franchise/cyrg/search/{pageNum}/{pageSize}")
    public AnyTxnHttpResponse<PageResultDTO<DinersFranchiseCycleRangeDTO>> selectByFranchiseCode(@PathVariable(value = "pageNum") Integer pageNum,
                                                                                                 @PathVariable(value = "pageSize") Integer pageSize,
                                                                                                 @RequestParam String franchiseCode){
        PageResultDTO<DinersFranchiseCycleRangeDTO> dinersFranchiseCycleRangeList = dinersFranchiseCycleRangeService.selectByFranchiseCode(pageNum,pageSize,franchiseCode);
        return AnyTxnHttpResponse.success(dinersFranchiseCycleRangeList);
    }
}
