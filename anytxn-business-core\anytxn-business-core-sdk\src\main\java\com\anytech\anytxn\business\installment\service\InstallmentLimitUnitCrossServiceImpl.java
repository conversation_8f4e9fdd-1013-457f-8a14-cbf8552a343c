package com.anytech.anytxn.business.installment.service;

import com.anytech.anytxn.business.dao.installment.mapper.InstallmentLimitUnitCrossMapper;
import com.anytech.anytxn.business.dao.installment.mapper.InstallmentLimitUnitCrossSelfMapper;
import com.anytech.anytxn.business.dao.installment.model.InstallmentLimitUnitCross;
import com.anytech.anytxn.business.base.installment.domain.dto.InstallmentLimitUnitCrossDTO;
import com.anytech.anytxn.business.base.installment.service.InstallmentLimitUnitCrossService;
import com.anytech.anytxn.common.core.enums.CommonRepDetailEnum;
import com.github.pagehelper.Page;
import com.github.pagehelper.PageHelper;
import com.anytech.anytxn.common.core.enums.AnyTxnCommonRespCodeEnum;
import com.anytech.anytxn.common.core.base.PageResultDTO;
import com.anytech.anytxn.common.core.exception.AnyTxnCommonException;
import com.anytech.anytxn.common.core.utils.BeanMapping;
import com.anytech.anytxn.common.core.utils.OrgNumberUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.util.ArrayList;
import java.util.List;
import java.util.Objects;

/**
 * @ClassName InstallmentLimitUnitCrossServiceImpl.java
 * <AUTHOR>
 * @Version 1.0.0
 * @Description 分期订单管控单元关联表servie实现类
 * @CreateTime 2020年05月07日 15:11:00
 */
@Service
public class InstallmentLimitUnitCrossServiceImpl implements InstallmentLimitUnitCrossService {

    private static final Logger log = LoggerFactory.getLogger(InstallmentLimitUnitCrossServiceImpl.class);
    @Autowired
    private InstallmentLimitUnitCrossMapper installmentLimitUnitCrossMapper;
    @Autowired
    private InstallmentLimitUnitCrossSelfMapper installmentLimitUnitCrossSelfMapper;

    @Override
    public int insert(InstallmentLimitUnitCrossDTO installmentLimitUnitCrossDTO){
        if(installmentLimitUnitCrossDTO == null){
            log.error("分期订单管控单元关联表 add 入参为空");
            throw new AnyTxnCommonException(AnyTxnCommonRespCodeEnum.P_ERR, CommonRepDetailEnum.PARAM_NULL);
        }
        try{
            int res = installmentLimitUnitCrossMapper.insertSelective
                    (BeanMapping.copy(installmentLimitUnitCrossDTO, InstallmentLimitUnitCross.class));
            if(res != 1){
                log.error("分期订单管控单元关联表 add 异常:结果不为1:{}",res);
                throw new AnyTxnCommonException(AnyTxnCommonRespCodeEnum.D_ERR, CommonRepDetailEnum.DATABASE_INSERT);
            }
        }catch (Exception e){
            log.error("分期订单管控单元关联表 add 异常:",e);
            throw new AnyTxnCommonException(AnyTxnCommonRespCodeEnum.D_ERR, CommonRepDetailEnum.DATABASE,e);
        }
        return 0;
    }

    @Override
    public List<InstallmentLimitUnitCrossDTO> selectByInstallOrderId(String installmentOrderId) {
        if(installmentOrderId == null){
            log.error("订单号为空!");
            throw new AnyTxnCommonException(AnyTxnCommonRespCodeEnum.P_ERR, CommonRepDetailEnum.PARAM_NULL);
        }
        List<InstallmentLimitUnitCross> installmentLimitUnitCrosses = installmentLimitUnitCrossSelfMapper
                .selectByInstallOrderId(installmentOrderId);
        return BeanMapping.copyList(installmentLimitUnitCrosses,InstallmentLimitUnitCrossDTO.class);
    }

    /**
     * 分页查询分期订单管控单元关联表
     * @param orgNumber
     * @param installmentOrderId
     * @param page
     * @param pageSize
     * @return
     */
    @Override
    public PageResultDTO<InstallmentLimitUnitCrossDTO> selectByOrgAndInstallOrderId(String orgNumber, String installmentOrderId, int page, int pageSize) {
        if(page < 1){
           page = 1;
        }
        if(pageSize < 1){
            pageSize = 8;
        }
        if(Objects.isNull(installmentOrderId)){
            log.error("订单号为空!");
            throw new AnyTxnCommonException(AnyTxnCommonRespCodeEnum.P_ERR, CommonRepDetailEnum.PARAM_NULL);
        }
        if(Objects.isNull(orgNumber)){
            orgNumber = OrgNumberUtils.getOrg();
        }
        try {
            // 分页插件
            Page<Object> pageInfo = PageHelper.startPage(page, pageSize);
            List<InstallmentLimitUnitCross> installmentLimitUnitCross = installmentLimitUnitCrossSelfMapper.selectByOrgInstallOrderId(orgNumber,installmentOrderId);
            if(CollectionUtils.isEmpty(installmentLimitUnitCross)){
                return new PageResultDTO<>(page, pageSize, pageInfo.getTotal(), pageInfo.getPages(),
                        new ArrayList<>());
            }
            return new PageResultDTO<>(page, pageSize, pageInfo.getTotal(), pageInfo.getPages(),
                    BeanMapping.copyList(installmentLimitUnitCross,InstallmentLimitUnitCrossDTO.class));
        } catch (Exception e) {
            log.error("分页查询分期订单管控单元关联表异常！installmentOrderId={}", installmentOrderId, e);
            throw new AnyTxnCommonException(AnyTxnCommonRespCodeEnum.D_ERR);
        }
    }
}
