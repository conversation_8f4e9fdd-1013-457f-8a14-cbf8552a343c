package com.anytech.anytxn.parameter.card.service;

import com.github.pagehelper.Page;
import com.github.pagehelper.PageHelper;
import com.anytech.anytxn.common.core.base.PageResultDTO;
import com.anytech.anytxn.common.core.utils.BeanMapping;
import com.anytech.anytxn.common.core.utils.OrgNumberUtils;
import com.anytech.anytxn.common.sequence.utils.Number16IdGen;
import com.anytech.anytxn.common.core.utils.TenantUtils;
import com.anytech.anytxn.parameter.base.card.domain.dto.CardProductCurrencyRelationReqDTO;
import com.anytech.anytxn.parameter.base.card.domain.dto.CardProductCurrencyRelationResDTO;
import com.anytech.anytxn.parameter.base.common.domain.dto.system.OrganizationInfoResDTO;
import com.anytech.anytxn.parameter.base.card.service.ICardProductCurrencyRelationService;
import com.anytech.anytxn.parameter.base.common.constants.Constants;
import com.anytech.anytxn.parameter.base.common.enums.AnyTxnParameterRespCodeEnum;
import com.anytech.anytxn.parameter.base.common.exception.AnyTxnParameterException;
import com.anytech.anytxn.parameter.card.mapper.broadcast.ParmCardCurrencyInfoMapper;
import com.anytech.anytxn.parameter.card.mapper.broadcast.ParmCardCurrencyInfoSelfMapper;
import com.anytech.anytxn.parameter.base.card.domain.model.ParmCardCurrencyInfo;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;

/**
 * 卡币对照关系
 *
 * <AUTHOR>
 * @date 2020-08-07 6:07 下午
 **/
@Service
public class CardProductCurrencyRelationServiceImpl implements ICardProductCurrencyRelationService {

    private Logger logger = LoggerFactory.getLogger(CardProductCurrencyRelationServiceImpl.class);


    @Autowired
    ParmCardCurrencyInfoMapper parmCardCurrencyInfoMapper;

    @Autowired
    ParmCardCurrencyInfoSelfMapper parmCardCurrencyInfoSelfMapper;

    @Autowired
    private Number16IdGen numberIdGenerator;

    /**
     * 创建卡币对照关系
     * @param cardCurrencyReq
     * @return
     */
    @Override
    public List<CardProductCurrencyRelationResDTO> add(CardProductCurrencyRelationReqDTO cardCurrencyReq) {
        //非空参数检查
        if(StringUtils.isEmpty(cardCurrencyReq.getOrganizationNumber())||
        StringUtils.isEmpty(cardCurrencyReq.getProductNumber())|| cardCurrencyReq.getCurrencyList().size()==0){
            logger.error("非空参数为空,organizationNumber={},productNumber={},currencyList={}",cardCurrencyReq.getOrganizationNumber(),cardCurrencyReq.getProductNumber(),cardCurrencyReq.getCurrencyList());
        }
        List<ParmCardCurrencyInfo> resList = new ArrayList<>();
        for(String currencyCode : cardCurrencyReq.getCurrencyList()){
            //判断卡币对照关系是否已存在
            boolean exists = parmCardCurrencyInfoSelfMapper.isExists(cardCurrencyReq.getOrganizationNumber(), cardCurrencyReq.getProductNumber(), currencyCode);
            if(exists){
                logger.error("卡币对照关系已存在,organizationNumber={},productNumber={},currencyCode={}",cardCurrencyReq.getOrganizationNumber(),cardCurrencyReq.getProductNumber(),currencyCode);
                throw new AnyTxnParameterException(AnyTxnParameterRespCodeEnum.S_QUERY_PARM_CARD_CURRENCY_IS_EXISTED);
            }
            //构建卡币对照关系
            ParmCardCurrencyInfo parmCardCurrencyInfo = BeanMapping.copy(cardCurrencyReq,ParmCardCurrencyInfo.class);
            parmCardCurrencyInfo.setCurrencyCode(currencyCode);
            parmCardCurrencyInfo.setId(numberIdGenerator.generateId(TenantUtils.getTenantId()));
            parmCardCurrencyInfo.setCreateTime(LocalDateTime.now());
            parmCardCurrencyInfo.setUpdateTime(LocalDateTime.now());
            parmCardCurrencyInfo.setUpdateBy(Constants.DEFAULT_USER);
            parmCardCurrencyInfo.setVersionNumber(1L);
            parmCardCurrencyInfoMapper.insert(parmCardCurrencyInfo);
            resList.add(parmCardCurrencyInfo);
        }

        return BeanMapping.copyList(resList, CardProductCurrencyRelationResDTO.class);
    }

    /**
     * 删除卡产品信息条目，通过id
     * @param organizationNumber,productNumber
     * @return
     */
    @Override
    public Boolean remove(String organizationNumber,String productNumber) {
        List<ParmCardCurrencyInfo> cardCurrencyInfoList = parmCardCurrencyInfoSelfMapper.selectByOrgAndProductNum(organizationNumber,productNumber);

        if(cardCurrencyInfoList==null || cardCurrencyInfoList.size()==0){
            logger.error("删除卡币对照关系，通过机构号和产品编码，（organizationNumber={},productNumber={}）未找到数据源",organizationNumber,productNumber);
            throw new AnyTxnParameterException(AnyTxnParameterRespCodeEnum.S_QUERY_PARM_CARD_CURRENCY_INFO_BY_ORGNUM_PRODUCTNUM_FAULT);
        }

        logger.info("通过机构号和产品编码删除卡币对照关系，organizationNumber={}，productNumber={}",organizationNumber,productNumber);
        boolean result = false;
        for (ParmCardCurrencyInfo item : cardCurrencyInfoList){
            result = parmCardCurrencyInfoMapper.deleteByPrimaryKey(item.getId())>0?true:false;
        }
        return result;
    }

    /**
     * 更新卡产品信息
     * @param cardCurrencyReq
     * @return
     */
    @Transactional
    @Override
    public CardProductCurrencyRelationResDTO modify(CardProductCurrencyRelationReqDTO cardCurrencyReq) {

        List<ParmCardCurrencyInfo> cardCurrencyInfoList = parmCardCurrencyInfoSelfMapper.selectByOrgAndProductNum(cardCurrencyReq.getOrganizationNumber(), cardCurrencyReq.getProductNumber());

        if(cardCurrencyInfoList==null || cardCurrencyInfoList.size()==0){
            logger.error("修改卡币对照关系，通过机构号和产品编码，（organizationNumber={},productNumber={}）未找到数据源",cardCurrencyReq.getOrganizationNumber(),cardCurrencyReq.getProductNumber());
            throw new AnyTxnParameterException(AnyTxnParameterRespCodeEnum.S_QUERY_PARM_CARD_CURRENCY_INFO_BY_ORGNUM_PRODUCTNUM_FAULT);
        }

        for (ParmCardCurrencyInfo item : cardCurrencyInfoList){
            parmCardCurrencyInfoMapper.deleteByPrimaryKey(item.getId());
        }

        ParmCardCurrencyInfo parmCardCurrencyInfo = BeanMapping.copy(cardCurrencyReq, ParmCardCurrencyInfo.class);
        parmCardCurrencyInfo.setUpdateTime(LocalDateTime.now());
        parmCardCurrencyInfo.setUpdateBy(Constants.DEFAULT_USER);
        List<String> currencyList = cardCurrencyReq.getCurrencyList();

        for (String currencyCode : currencyList){
            parmCardCurrencyInfo.setId(numberIdGenerator.generateId(TenantUtils.getTenantId()));
            parmCardCurrencyInfo.setCurrencyCode(currencyCode);
            parmCardCurrencyInfoMapper.insert(parmCardCurrencyInfo);
        }

        CardProductCurrencyRelationResDTO resDTO = BeanMapping.copy(parmCardCurrencyInfo, CardProductCurrencyRelationResDTO.class);
        resDTO.setCurrencyList(currencyList);
        return resDTO;
    }

    /**
     * 根据机构号和产品编码查询卡币对照关系
     * @param organizationNumber,productNumber
     * @return
     */
    @Override
    public CardProductCurrencyRelationResDTO find(String organizationNumber,String productNumber) {
        if (StringUtils.isEmpty(organizationNumber) || StringUtils.isEmpty(productNumber)) {
            throw new AnyTxnParameterException(AnyTxnParameterRespCodeEnum.P_PARAMETER_EMPTY_FAULT);
        }

        //ParmCardCurrencyInfo parmCardCurrencyInfo = parmCardCurrencyInfoMapper.selectByPrimaryKey(id);
        List<ParmCardCurrencyInfo> cardCurrencyInfoList = parmCardCurrencyInfoSelfMapper.selectByOrgAndProductNum(organizationNumber, productNumber);

        if (cardCurrencyInfoList.size()==0) {
            logger.error("根据机构号和产品编码查询卡币对照关系, organizationNumber={},productNumber={},", organizationNumber,productNumber);
            throw new AnyTxnParameterException(AnyTxnParameterRespCodeEnum.S_QUERY_PARM_CARD_CURRENCY_INFO_BY_ORGNUM_PRODUCTNUM_FAULT);
        }

        List<String> currencyList = new ArrayList<>();
        for (ParmCardCurrencyInfo item : cardCurrencyInfoList){
            currencyList.add(item.getCurrencyCode());
        }
        CardProductCurrencyRelationResDTO cardProductCurrencyRelationResDTO = BeanMapping.copy(cardCurrencyInfoList.get(0), CardProductCurrencyRelationResDTO.class);
        cardProductCurrencyRelationResDTO.setCurrencyList(currencyList);

        return cardProductCurrencyRelationResDTO;
    }

    /**
     * 查询所有卡产品参数信息
     * @param pageNum
     * @param pageSize
     * @return
     */
    @Override
    public PageResultDTO<CardProductCurrencyRelationResDTO> findAll(Integer pageNum, Integer pageSize) {
        Page<ParmCardCurrencyInfo> page = PageHelper.startPage(pageNum, pageSize);
        List<ParmCardCurrencyInfo> cardCurrencyInfoList = parmCardCurrencyInfoSelfMapper.selectAllWithDesc(false, OrgNumberUtils.getOrg());
        if (cardCurrencyInfoList.isEmpty()) {
            throw new AnyTxnParameterException(AnyTxnParameterRespCodeEnum.S_PAGE_QUERY_PARM_CARD_CURRENCY_INFO_FAULT);
        }
        List<CardProductCurrencyRelationResDTO> res = BeanMapping.copyList(cardCurrencyInfoList, CardProductCurrencyRelationResDTO.class);
        return new PageResultDTO<>(pageNum, pageSize, page.getTotal(), page.getPages(),res);
    }


    /**
     * 获取币种
     * 1、根据卡片授权信息表中的机构号、产品编号，读取卡片币种对照表（parm_card_currency_info）：
     *  1）如果读不到记录，或者读到多条记录，赋值为机构主币种。
     *  2）如果读到1条记录，赋值为卡片币种对照表（parm_card_currency_info）中的入账币种（currency_code）
     * @param orgInfo
     * @param productNumber
     * @return
     */
    @Override
    public String findDefaultCurrency(OrganizationInfoResDTO orgInfo, String productNumber) {
        String orgNum = orgInfo.getOrganizationNumber();
        List<ParmCardCurrencyInfo> cardCurrencyInfoList = parmCardCurrencyInfoSelfMapper.selectByOrgAndProductNum(orgNum, productNumber);
        if (cardCurrencyInfoList != null && cardCurrencyInfoList.size() == 1) {
            return cardCurrencyInfoList.get(0).getCurrencyCode();
        }else {
            return orgInfo.getOrganizationCurrency();
        }
    }
}
