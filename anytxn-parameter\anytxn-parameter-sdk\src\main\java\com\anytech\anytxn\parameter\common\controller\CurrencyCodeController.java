package com.anytech.anytxn.parameter.common.controller;

import com.anytech.anytxn.common.core.base.ParameterCompare;
import com.anytech.anytxn.common.core.base.PageResultDTO;
import com.anytech.anytxn.common.core.base.AnyTxnHttpResponse;
import com.anytech.anytxn.common.core.base.BizBaseController;
import com.anytech.anytxn.parameter.base.common.domain.dto.ParmCurrencyCodeDTO;
import com.anytech.anytxn.parameter.base.common.service.ICurrencyCodeService;
import com.anytech.anytxn.parameter.base.common.enums.ParameterRepDetailEnum;
import io.swagger.v3.oas.annotations.tags.Tag;
import io.swagger.v3.oas.annotations.Operation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

/**
 *
 * <AUTHOR>
 * @date 2021-10-19
 */
@RestController
@Tag(name = "货币码操作接口")
public class CurrencyCodeController extends BizBaseController {

    @Autowired
    private ICurrencyCodeService currencyCodeService;

    /**
     * 查询货币码基本信息
     */
    @Operation(summary = "查询货币基本信息")
    @GetMapping("/param/selectCurrencyCode")
    public AnyTxnHttpResponse<PageResultDTO<ParmCurrencyCodeDTO>> selectCountryCode(
            @RequestParam(value = "page", defaultValue = "1") Integer page,
            @RequestParam(value = "rows", defaultValue = "10") Integer rows) {
        PageResultDTO<ParmCurrencyCodeDTO> countryCode = currencyCodeService.selectCurrencyCodeBasic(page,rows);
        return AnyTxnHttpResponse.success(countryCode);
    }


    /**
     * 根据主键查询货币码基本信息
     */
    @Operation(summary = "查询货币码基本信息")
    @GetMapping("/param/selectCurrencyCodeById")
    public AnyTxnHttpResponse<ParmCurrencyCodeDTO> getByIndex(@RequestParam(value = "id") String id){
        ParmCurrencyCodeDTO countryCodeDTO = currencyCodeService.selectById(id);
        return AnyTxnHttpResponse.success(countryCodeDTO);

    }


    /**
     * 增加货币码基本信息
     */
    @Operation(summary = "增加货币码基本信息")
    @PostMapping("/param/insertCurrencyCode")
    public AnyTxnHttpResponse<ParameterCompare> addCurrencyCode(@RequestBody ParmCurrencyCodeDTO parmCurrencyCodeDTO){
        ParameterCompare parmCurrencyCodeDTO1=currencyCodeService.add(parmCurrencyCodeDTO);
        return AnyTxnHttpResponse.success(parmCurrencyCodeDTO1);
    }

    /**
     * 删除货币码基本信息
     */
    @Operation(summary = "删除货币码基本信息")
    @DeleteMapping("/param/deleteCurrencyCode")
    public AnyTxnHttpResponse<ParameterCompare> delCountry(@RequestParam("id") String id){
        currencyCodeService.delete(id);
        return AnyTxnHttpResponse.successDetail(ParameterRepDetailEnum.DEL.message());
    }

    /**
     * 修改货币码基本信息
     */
    @Operation(summary = "修改货币码基本信息")
    @PutMapping("/param/updatCurrencyCode")
    public AnyTxnHttpResponse<ParmCurrencyCodeDTO> modifyCurrency(@RequestBody ParmCurrencyCodeDTO parmCurrencyCodeDTO){
        currencyCodeService.update(parmCurrencyCodeDTO) ;
        return AnyTxnHttpResponse.successDetail(ParameterRepDetailEnum.UPDATE.message());
    }

}
