package com.anytech.anytxn.business.account.service;

import com.anytech.anytxn.business.base.account.domain.dto.CommonAccountDTO;
import com.anytech.anytxn.business.dao.account.mapper.AccountManagementInfoSelfMapper;
import com.anytech.anytxn.business.dao.account.model.AccountManagementInfo;
import com.anytech.anytxn.common.core.utils.OrgNumberUtils;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockedStatic;
import org.mockito.junit.jupiter.MockitoExtension;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.*;

/**
 * CommonAccountService 单元测试类
 * 测试账户查询相关功能
 */
@ExtendWith(MockitoExtension.class)
class CommonAccountServiceTest {

    @InjectMocks
    private CommonAccountService commonAccountService;

    @Mock
    private AccountManagementInfoSelfMapper managementInfoSelfMapper;

    /**
     * 测试用例初始化
     */
    @BeforeEach
    void setUp() {
        // 初始化测试数据或配置
    }

    /**
     * 测试方法：shouldReturnAccountList_whenValidCustomerIdAndOrgProvided
     * 用来测试 CommonAccountService 方法 selectByCustomerIdAndOrg
     * 验证当提供有效客户ID和机构号时，能够返回账户列表
     */
    @Test
    void shouldReturnAccountList_whenValidCustomerIdAndOrgProvided() {
        try (MockedStatic<OrgNumberUtils> orgUtilsMock = mockStatic(OrgNumberUtils.class)) {
            // Mock OrgNumberUtils for BaseEntity constructor
            orgUtilsMock.when(OrgNumberUtils::getOrg).thenReturn("TEST_ORG");
            
            // Arrange - 准备最简单的测试数据
            CommonAccountDTO commonAccountDTO = new CommonAccountDTO();
            commonAccountDTO.setCustomerId("CUST001");
            commonAccountDTO.setOrganizationNumber("ORG001");
            
            // 创建简单的Mock账户信息
            AccountManagementInfo mockAccount = new AccountManagementInfo();
            mockAccount.setAccountManagementId("ACC001");
            mockAccount.setProductNumber("PROD001");
            mockAccount.setCurrency("CNY");
            mockAccount.setAccountStatus("1"); // 非8状态，表示有效账户
            
            List<AccountManagementInfo> mockAccountList = new ArrayList<>();
            mockAccountList.add(mockAccount);
            
            when(managementInfoSelfMapper.selectByCustomerId(anyString(), anyString()))
                .thenReturn(mockAccountList);

            // Act - 执行被测方法
            List<AccountManagementInfo> result = commonAccountService.selectByCustomerIdAndOrg(commonAccountDTO);

            // Assert - 验证结果
            assertNotNull(result, "结果不应为null");
            assertFalse(result.isEmpty(), "结果不应为空列表");
            assertEquals(1, result.size(), "应该返回1个账户");
            verify(managementInfoSelfMapper).selectByCustomerId("ORG001", "CUST001");
        }
    }

    /**
     * 测试方法：shouldReturnEmptyList_whenCustomerIdIsEmpty
     * 用来测试 CommonAccountService 方法 selectByCustomerIdAndOrg 空参数处理
     * 验证当客户ID为空时，返回空列表
     */
    @Test
    void shouldReturnEmptyList_whenCustomerIdIsEmpty() {
        try (MockedStatic<OrgNumberUtils> orgUtilsMock = mockStatic(OrgNumberUtils.class)) {
            // Mock OrgNumberUtils for BaseEntity constructor
            orgUtilsMock.when(OrgNumberUtils::getOrg).thenReturn("TEST_ORG");
            
            // Arrange - 准备测试数据
            CommonAccountDTO commonAccountDTO = new CommonAccountDTO();
            commonAccountDTO.setCustomerId(""); // 设置空的客户ID
            commonAccountDTO.setOrganizationNumber("ORG001");

            // Act - 执行被测方法
            List<AccountManagementInfo> result = commonAccountService.selectByCustomerIdAndOrg(commonAccountDTO);

            // Assert - 验证结果
            assertNotNull(result, "结果不应为null");
            assertTrue(result.isEmpty(), "当客户ID为空时应返回空列表");
            verify(managementInfoSelfMapper, never()).selectByCustomerId(anyString(), anyString());
        }
    }

    /**
     * 测试方法：shouldReturnEmptyList_whenOrganizationNumberIsEmpty
     * 用来测试 CommonAccountService 方法 selectByCustomerIdAndOrg 空参数处理
     * 验证当机构号为空时，返回空列表
     */
    @Test
    void shouldReturnEmptyList_whenOrganizationNumberIsEmpty() {
        try (MockedStatic<OrgNumberUtils> orgUtilsMock = mockStatic(OrgNumberUtils.class)) {
            // Mock OrgNumberUtils for BaseEntity constructor
            orgUtilsMock.when(OrgNumberUtils::getOrg).thenReturn("TEST_ORG");
            
            // Arrange - 准备测试数据
            CommonAccountDTO commonAccountDTO = new CommonAccountDTO();
            commonAccountDTO.setCustomerId("CUST001");
            commonAccountDTO.setOrganizationNumber(""); // 设置空的机构号

            // Act - 执行被测方法
            List<AccountManagementInfo> result = commonAccountService.selectByCustomerIdAndOrg(commonAccountDTO);

            // Assert - 验证结果
            assertNotNull(result, "结果不应为null");
            assertTrue(result.isEmpty(), "当机构号为空时应返回空列表");
            verify(managementInfoSelfMapper, never()).selectByCustomerId(anyString(), anyString());
        }
    }

    /**
     * 测试方法：shouldReturnAccountInfo_whenValidParametersForSpecificAccount
     * 用来测试 CommonAccountService 方法 selectByCusIdProNumAndCurr
     * 验证当提供有效参数查询指定账户时，能够返回账户信息
     */
    @Test
    void shouldReturnAccountInfo_whenValidParametersForSpecificAccount() {
        try (MockedStatic<OrgNumberUtils> orgUtilsMock = mockStatic(OrgNumberUtils.class)) {
            // Mock OrgNumberUtils for BaseEntity constructor
            orgUtilsMock.when(OrgNumberUtils::getOrg).thenReturn("TEST_ORG");
            
            // Arrange - 准备测试数据
            CommonAccountDTO commonAccountDTO = new CommonAccountDTO();
            commonAccountDTO.setCustomerId("CUST001");
            commonAccountDTO.setOrganizationNumber("ORG001");
            commonAccountDTO.setAcctProductNumber("PROD001");
            commonAccountDTO.setCurrency("CNY");
            
            // 创建简单的Mock账户信息
            AccountManagementInfo mockAccount = new AccountManagementInfo();
            mockAccount.setAccountManagementId("ACC001");
            mockAccount.setProductNumber("PROD001");
            mockAccount.setCurrency("CNY");
            mockAccount.setAccountStatus("1");
            
            List<AccountManagementInfo> mockAccountList = new ArrayList<>();
            mockAccountList.add(mockAccount);
            
            when(managementInfoSelfMapper.selectByCustomerId(anyString(), anyString()))
                .thenReturn(mockAccountList);

            // Act - 执行被测方法
            AccountManagementInfo result = commonAccountService.selectByCusIdProNumAndCurr(commonAccountDTO);

            // Assert - 验证结果
            assertNotNull(result, "结果不应为null");
            assertEquals("PROD001", result.getProductNumber(), "产品号应该匹配");
            assertEquals("CNY", result.getCurrency(), "币种应该匹配");
            verify(managementInfoSelfMapper).selectByCustomerId("ORG001", "CUST001");
        }
    }

    /**
     * 测试方法：shouldReturnNull_whenRequiredParametersAreMissing
     * 用来测试 CommonAccountService 方法 selectByCusIdProNumAndCurr 参数校验
     * 验证当必需参数缺失时，返回null
     */
    @Test
    void shouldReturnNull_whenRequiredParametersAreMissing() {
        try (MockedStatic<OrgNumberUtils> orgUtilsMock = mockStatic(OrgNumberUtils.class)) {
            // Mock OrgNumberUtils for BaseEntity constructor
            orgUtilsMock.when(OrgNumberUtils::getOrg).thenReturn("TEST_ORG");
            
            // Arrange - 准备测试数据
            CommonAccountDTO commonAccountDTO = new CommonAccountDTO();
            commonAccountDTO.setCustomerId("CUST001");
            commonAccountDTO.setOrganizationNumber("ORG001");
            commonAccountDTO.setAcctProductNumber("PROD001");
            commonAccountDTO.setCurrency(""); // 设置空的币种

            // Act - 执行被测方法
            AccountManagementInfo result = commonAccountService.selectByCusIdProNumAndCurr(commonAccountDTO);

            // Assert - 验证结果
            assertNull(result, "当必需参数缺失时应返回null");
            verify(managementInfoSelfMapper, never()).selectByCustomerId(anyString(), anyString());
        }
    }

    /**
     * 测试方法：shouldReturnValidAccount_whenMultipleAccountsExist
     * 用来测试 CommonAccountService 方法 selectByCustomerIdAndOrg 多账户处理
     * 验证当存在多个账户时，能够正确处理和返回
     */
    @Test
    void shouldReturnValidAccount_whenMultipleAccountsExist() {
        try (MockedStatic<OrgNumberUtils> orgUtilsMock = mockStatic(OrgNumberUtils.class)) {
            // Mock OrgNumberUtils for BaseEntity constructor
            orgUtilsMock.when(OrgNumberUtils::getOrg).thenReturn("TEST_ORG");
            
            // Arrange - 准备测试数据
            CommonAccountDTO commonAccountDTO = new CommonAccountDTO();
            commonAccountDTO.setCustomerId("CUST001");
            commonAccountDTO.setOrganizationNumber("ORG001");
            
            // 创建多个Mock账户信息
            AccountManagementInfo mockAccount1 = new AccountManagementInfo();
            mockAccount1.setAccountManagementId("ACC001");
            mockAccount1.setProductNumber("PROD001");
            mockAccount1.setCurrency("CNY");
            mockAccount1.setAccountStatus("1");
            
            AccountManagementInfo mockAccount2 = new AccountManagementInfo();
            mockAccount2.setAccountManagementId("ACC002");
            mockAccount2.setProductNumber("PROD002");
            mockAccount2.setCurrency("USD");
            mockAccount2.setAccountStatus("1");
            
            List<AccountManagementInfo> mockAccountList = new ArrayList<>();
            mockAccountList.add(mockAccount1);
            mockAccountList.add(mockAccount2);
            
            when(managementInfoSelfMapper.selectByCustomerId(anyString(), anyString()))
                .thenReturn(mockAccountList);

            // Act - 执行被测方法
            List<AccountManagementInfo> result = commonAccountService.selectByCustomerIdAndOrg(commonAccountDTO);

            // Assert - 验证结果
            assertNotNull(result, "结果不应为null");
            assertEquals(2, result.size(), "应该返回2个账户");
            verify(managementInfoSelfMapper).selectByCustomerId("ORG001", "CUST001");
        }
    }
} 