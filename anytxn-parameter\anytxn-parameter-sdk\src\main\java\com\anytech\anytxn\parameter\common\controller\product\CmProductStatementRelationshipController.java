package com.anytech.anytxn.parameter.common.controller.product;

import com.anytech.anytxn.common.core.base.PageResultDTO;
import com.anytech.anytxn.common.core.base.AnyTxnHttpResponse;
import com.anytech.anytxn.common.core.base.BizBaseController;
import com.anytech.anytxn.parameter.base.common.domain.dto.product.CmProductStatementRelationshipAddDTO;
import com.anytech.anytxn.parameter.base.common.domain.dto.product.CmProductStatementRelationshipDTO;
import com.anytech.anytxn.parameter.base.common.domain.dto.product.CmProductStatementRelationshipDetailDTO;
import com.anytech.anytxn.parameter.base.common.domain.dto.product.CmProductStatementRelationshipPageRequest;
import com.anytech.anytxn.parameter.base.common.service.product.ICmProductStatementRelationshipService;
import io.swagger.v3.oas.annotations.tags.Tag;
import io.swagger.v3.oas.annotations.Operation;
import jakarta.annotation.Resource;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 *
 * <p>Title: CmProductStatementRelationshipController </p>
 * <p>Description: (CmProductStatementRelationship)前端控制器 </p>
 *
 * <AUTHOR>
 * @version v1.0.0
 * @classname CmProductStatementRelationshipController
 * @date 2022/09/13
 */
@Tag(name = "产品参数定义关联表")
@RestController
public class CmProductStatementRelationshipController extends BizBaseController {

    @Resource
    private ICmProductStatementRelationshipService cmProductStatementRelationshipService;
    /**
     * 保存
     */
    @PostMapping(value = "/parm/relationship/add")
    public AnyTxnHttpResponse<Boolean> add(@RequestBody @Validated List<CmProductStatementRelationshipAddDTO> relationshipAddDTOS) {
        Boolean resp = cmProductStatementRelationshipService.save(relationshipAddDTOS);
        return AnyTxnHttpResponse.success(resp);
    }

    /**
     * 删除
     */
    @DeleteMapping(value = "/parm/relationship/delete/{id}")
    public AnyTxnHttpResponse<Boolean> delete( @PathVariable("id") String id) {
        Boolean resp = cmProductStatementRelationshipService.delete(id);
        return AnyTxnHttpResponse.success(resp);
    }

    /**
     * 更新
     */
    @PostMapping(value = "/parm/relationship/edit")
    public AnyTxnHttpResponse<Boolean> update(@RequestBody @Validated CmProductStatementRelationshipAddDTO request) {
        Boolean resp = cmProductStatementRelationshipService.update(request);
        return AnyTxnHttpResponse.success(resp);
    }

    /**
     * 详情
     */
    @GetMapping(value = "/parm/relationship/detail/{id}")
    public AnyTxnHttpResponse<CmProductStatementRelationshipDetailDTO> detail(@PathVariable("id") String id) {
        CmProductStatementRelationshipDetailDTO resp = cmProductStatementRelationshipService.info(id);
        return AnyTxnHttpResponse.success(resp);
    }

    /**
     * 列表
     */
    @PostMapping(value = "/parm/relationship/list")
    public AnyTxnHttpResponse<List<CmProductStatementRelationshipDTO>> list(@RequestBody @Validated CmProductStatementRelationshipDTO request) {
        List<CmProductStatementRelationshipDTO> resp = cmProductStatementRelationshipService.list(request);
        return AnyTxnHttpResponse.success(resp);
    }

    /**
     * 分页列表
     */
    @Operation(summary = "卡产品参数分页查询",description = "卡产品参数分页查询")
    @PostMapping(value = "/parm/relationship/querypage")
    public AnyTxnHttpResponse page(@RequestBody @Validated CmProductStatementRelationshipPageRequest request) {
        PageResultDTO<CmProductStatementRelationshipDTO> resp = cmProductStatementRelationshipService.page(request);
        return AnyTxnHttpResponse.success(resp);
    }
    /**
     * 列表
     */
    @GetMapping(value = "/parm/relationship/checkaccountproduct/{accountProductCode}")
    public AnyTxnHttpResponse<Boolean> checkAccountProduct(@PathVariable("accountProductCode")  String accountProductCode) {
        Boolean checkResult = cmProductStatementRelationshipService.checkAccountProduct(accountProductCode);
        return AnyTxnHttpResponse.success(checkResult);
    }
}
