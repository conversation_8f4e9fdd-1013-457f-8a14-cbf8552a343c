package com.anytech.anytxn.parameter.authorization.service;

import com.alibaba.fastjson.JSON;
import com.github.pagehelper.Page;
import com.github.pagehelper.PageHelper;
import com.anytech.anytxn.common.core.annotation.db.DeleteParameterAnnotation;
import com.anytech.anytxn.common.core.annotation.db.InsertParameterAnnotation;
import com.anytech.anytxn.common.core.annotation.db.UpdateParameterAnnotation;
import com.anytech.anytxn.business.base.audit.AbstractParameterService;
import com.anytech.anytxn.common.core.base.ParameterCompare;
import com.anytech.anytxn.business.base.audit.ParmModificationRecord;
import com.anytech.anytxn.common.core.base.PageResultDTO;
import com.anytech.anytxn.common.core.utils.BeanMapping;
import com.anytech.anytxn.common.core.utils.OrgNumberUtils;
import com.anytech.anytxn.common.sequence.utils.Number16IdGen;
import com.anytech.anytxn.common.core.utils.TenantUtils;
import com.anytech.anytxn.parameter.base.authorization.domain.dto.VelocityDefinitionDTO;
import com.anytech.anytxn.parameter.base.authorization.service.IVelocityDefinitionService;
import com.anytech.anytxn.parameter.base.common.enums.AnyTxnParameterRespCodeEnum;
import com.anytech.anytxn.parameter.base.common.exception.AnyTxnParameterException;
import com.anytech.anytxn.parameter.authorization.mapper.ParmVelocityDefinitionMapper;
import com.anytech.anytxn.parameter.authorization.mapper.ParmVelocityDefinitionSelfMapper;
import com.anytech.anytxn.parameter.base.authorization.domain.model.ParmVelocityDefinition;
//import org.apache.shardingsphere.transaction.annotation.ShardingSphereTransactionType;
//import org.apache.shardingsphere.transaction.core.TransactionType;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.cglib.beans.BeanCopier;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * @ClassName VelocityDefinitionServiceImpl
 * @Description 流量检查定义具体实现类
 * <AUTHOR>
 * @Date 2019/4/4 3:44 PM
 * Version 1.0
 **/
@Service(value = "parm_velocity_definition_serviceImpl")
public class VelocityDefinitionServiceImpl extends AbstractParameterService implements IVelocityDefinitionService {

    private static final Logger logger = LoggerFactory.getLogger(VelocityDefinitionServiceImpl.class);

    @Autowired
    private ParmVelocityDefinitionMapper parmVelocityDefinitionMapper;
    @Autowired
    private ParmVelocityDefinitionSelfMapper parmVelocityDefinitionSelfMapper;

    @Autowired
    private Number16IdGen numberIdGenerator;


    @Override
    public PageResultDTO<VelocityDefinitionDTO> findListVelocityDefinition(Integer page, Integer rows, VelocityDefinitionDTO velocityDefinitionDTO) {
        logger.info("分页查询流量检查定义表，当前页：{}，每页展示条数：{}", page, rows);
        if (null == velocityDefinitionDTO) {
            velocityDefinitionDTO = new VelocityDefinitionDTO();
        }
        velocityDefinitionDTO.setOrganizationNumber(StringUtils.isEmpty(velocityDefinitionDTO.getOrganizationNumber()) ? OrgNumberUtils.getOrg() : velocityDefinitionDTO.getOrganizationNumber());
        Page pageInfo = PageHelper.startPage(page, rows);
        List<VelocityDefinitionDTO> velocityDefinitionDTOList =new ArrayList<>();
        try {
            List<ParmVelocityDefinition> parmVelocityDefinitionList = parmVelocityDefinitionSelfMapper.selectByCondition(velocityDefinitionDTO);
            if (!CollectionUtils.isEmpty(parmVelocityDefinitionList)) {
                velocityDefinitionDTOList = new ArrayList<>();
                BeanCopier beanCopier = BeanCopier.create(ParmVelocityDefinition.class, VelocityDefinitionDTO.class, false);
                for (ParmVelocityDefinition parmVelocityDefinition : parmVelocityDefinitionList) {
                    VelocityDefinitionDTO velocityDefinition = new VelocityDefinitionDTO();
                    beanCopier.copy(parmVelocityDefinition, velocityDefinition, null);
                    velocityDefinitionDTOList.add(velocityDefinition);
                }
            }
            return new PageResultDTO<>(page, rows, pageInfo.getTotal(), pageInfo.getPages(), velocityDefinitionDTOList);
        } catch (Exception e) {
            logger.error("分页查询流量检查定义表信息失败，当前页：{}，每页展示条数：{},错误信息：{}", page, rows, e.getMessage());
            /*throw new AnyTXNBusRuntimeException(TransactionEnum.DATABASE_ERROR.getCode(),
                    TransactionEnum.DATABASE_ERROR.getReason());*/
            throw new AnyTxnParameterException(AnyTxnParameterRespCodeEnum.D_PAGE_VELOCITY_DEFINITION_FAULT);
        }
    }

    @Override
    public VelocityDefinitionDTO findVelocityDefinition(String id) {
        logger.info("根据主键:{},获取流量检查定义表信息", id);
        VelocityDefinitionDTO velocityDefinitionDTO = null;
        try {
            ParmVelocityDefinition parmVelocityDefinition = parmVelocityDefinitionMapper.selectByPrimaryKey(id);
            if (parmVelocityDefinition != null) {
                velocityDefinitionDTO = new VelocityDefinitionDTO();
                BeanCopier beanCopier = BeanCopier.create(ParmVelocityDefinition.class, VelocityDefinitionDTO.class, false);
                beanCopier.copy(parmVelocityDefinition, velocityDefinitionDTO, null);
            }
        } catch (Exception e) {
            logger.error("根据主键:{},获取流量检查信息失败,错误信息:{}", id, e.getMessage());
            /*throw new AnyTXNBusRuntimeException(TransactionEnum.DATABASE_ERROR.getCode(),
                    TransactionEnum.DATABASE_ERROR.getReason());*/
            throw new AnyTxnParameterException(AnyTxnParameterRespCodeEnum.D_QUERY_VELOCITY_DEFINITION_BY_ID_FAULT);

        }
        if (velocityDefinitionDTO == null) {
            logger.error("根据主键:{},获取流量检查定义信息失败", id);

            throw new AnyTxnParameterException(AnyTxnParameterRespCodeEnum.D_QUERY_VELOCITY_DEFINITION_NULL_BY_ID_FAULT);
        }
        return velocityDefinitionDTO;
    }

    @Override
    @UpdateParameterAnnotation(tableName = "parm_velocity_definition", tableDesc = "Velocity Check Parameter")
    public ParameterCompare modifyVelocityDefinition(VelocityDefinitionDTO velocityDefinitionDTO) {
        logger.info("修改流量检查定义表,流量检查吗:{} 流量检查状态:{}", velocityDefinitionDTO.getVelocityCde(), velocityDefinitionDTO.getStatus());
//        try {
//            ParmVelocityDefinition parmVelocityDefinition = new ParmVelocityDefinition();
//            BeanCopier beanCopier = BeanCopier.create(VelocityDefinitionDTO.class, ParmVelocityDefinition.class, false);
//            beanCopier.copy(velocityDefinitionDTO, parmVelocityDefinition, null);
//            parmVelocityDefinition.setUpdateTime(LocalDateTime.now());
//            return parmVelocityDefinitionMapper.updateByPrimaryKeySelective(parmVelocityDefinition);
//        } catch (Exception e) {
//            logger.error("调用[{}]更新商户类别群定义表[{}]失败,错误信息[{}]",
//                    "updateByPrimaryKeySelective", "VELOCITY_DEFINITION", e);
//
//            throw new AnyTxnParameterException(AnyTxnParameterRespCodeEnum.D_UPDATE_VELOCITY_DEFINITION_FAULT);
//        }
        if (velocityDefinitionDTO.getId() == null) {
            throw new AnyTxnParameterException(AnyTxnParameterRespCodeEnum.D_QUERY_VELOCITY_DEFINITION_BY_ID_FAULT);
        }
        ParmVelocityDefinition parmVelocityDefinition1 = parmVelocityDefinitionMapper.selectByPrimaryKey(velocityDefinitionDTO.getId());
        if (parmVelocityDefinition1 == null) {
            throw new AnyTxnParameterException(AnyTxnParameterRespCodeEnum.D_QUERY_VELOCITY_DEFINITION_BY_ID_FAULT);
        }
        ParmVelocityDefinition copy = BeanMapping.copy(velocityDefinitionDTO, ParmVelocityDefinition.class);
        return ParameterCompare.getBuilder().withMainParmId(velocityDefinitionDTO.getVelocityCde()).withAfter(copy).withBefore(parmVelocityDefinition1).build(ParmVelocityDefinition.class);

    }

    @Override
    @DeleteParameterAnnotation(tableName = "parm_velocity_definition", tableDesc = "Velocity Check Parameter")
    public ParameterCompare removeVelocityDefinition(String id) {
        ParmVelocityDefinition parmVelocityDefinition = parmVelocityDefinitionMapper.selectByPrimaryKey(id);
        logger.info("查询流量检查定义信息 id:{}", id);
        if (parmVelocityDefinition == null) {
            logger.error("待删除流量检查定义不存在。 id:{}", id);

            throw new AnyTxnParameterException(AnyTxnParameterRespCodeEnum.D_QUERY_VELOCITY_DEFINITION_NULL_BY_ID_FAULT);
        }
        return ParameterCompare.getBuilder().withMainParmId(parmVelocityDefinition.getVelocityCde()).withBefore(parmVelocityDefinition).build(ParmVelocityDefinition.class);

    }

    @Override
    @InsertParameterAnnotation(tableName = "parm_velocity_definition", tableDesc = "Velocity Check Parameter")
    public ParameterCompare addVelocityDefinition(VelocityDefinitionDTO velocityDefinitionDTO) {
        ParmVelocityDefinition parmVelocityDefinition;
        //转换
        if (velocityDefinitionDTO == null) {
            logger.error("velocityDefinitionDTO is null！");
            /*throw new AnyTXNBusRuntimeException(ResultEnum.NOT_EMPTY.getCode(),
                    "velocityDefinitionDTO 不能为空");*/
            throw new AnyTxnParameterException(AnyTxnParameterRespCodeEnum.P_PARAMETER_EMPTY_FAULT);
        }
        parmVelocityDefinition = BeanMapping.copy(velocityDefinitionDTO, ParmVelocityDefinition.class);
        //如果gcc_group为空，唯一
        int existsByVelocityCde = parmVelocityDefinitionSelfMapper.isExistsByVelocityCde(parmVelocityDefinition.getOrganizationNumber(), velocityDefinitionDTO.getVelocityCde());
        if (existsByVelocityCde > 0) {
            /*throw new AnyTXNBusRuntimeException(ResultEnum.DATA_EXISTS.getCode(),
                    "流量检查码已存在");*/
            throw new AnyTxnParameterException(AnyTxnParameterRespCodeEnum.D_EXIST_VELOCITY_DEFINITION_FAULT);
        }
        parmVelocityDefinition.setId(String.valueOf(numberIdGenerator.generateId(TenantUtils.getTenantId())));

        return ParameterCompare.getBuilder().withMainParmId(velocityDefinitionDTO.getVelocityCde()).withAfter(parmVelocityDefinition).build(ParmVelocityDefinition.class);

    }

    @Override
    public VelocityDefinitionDTO getVelocityByCode(String orgNum, String velocityCode) {
        logger.info("velocityCode:{},获取流量检查定义表信息", velocityCode);
        VelocityDefinitionDTO velocityDefinitionDTO = null;
        try {
            ParmVelocityDefinition parmVelocityDefinition =
                    parmVelocityDefinitionSelfMapper.selectByVelocityCode(orgNum, velocityCode);
            if (parmVelocityDefinition != null) {
                velocityDefinitionDTO = new VelocityDefinitionDTO();
                BeanCopier beanCopier =
                        BeanCopier.create(ParmVelocityDefinition.class, VelocityDefinitionDTO.class, false);
                beanCopier.copy(parmVelocityDefinition, velocityDefinitionDTO, null);
            }
        } catch (Exception e) {
            logger.error("velocityCode:{},获取流量检查信息失败,错误信息:{}", velocityCode, e.getMessage());
          /*throw new AnyTXNBusRuntimeException(
              TransactionEnum.DATABASE_ERROR.getCode(), TransactionEnum.DATABASE_ERROR.getReason());*/
            throw new AnyTxnParameterException(AnyTxnParameterRespCodeEnum.D_QUERY_VELOCITY_DEFINITION_BY_VELOCITY_CODE_FAULT);
        }
        if (velocityDefinitionDTO == null) {
            logger.error("根据主键:{},获取流量检查定义信息失败", velocityCode);

            throw new AnyTxnParameterException(AnyTxnParameterRespCodeEnum.D_QUERY_VELOCITY_DEFINITION_NULL_BY_VELOCITY_CODE_FAULT);
        }
        return velocityDefinitionDTO;
    }

    @Override
    public List<Map<String, String>> getVelocityCodes(String organizationNumber) {
        logger.info("获取所有授权流量标识码");
        List<ParmVelocityDefinition> parmVelocityDefinitionList = parmVelocityDefinitionSelfMapper.selectAll(true, organizationNumber);
        List<Map<String, String>> list = new ArrayList<>();
        parmVelocityDefinitionList.forEach(parmVelocityDefinition -> {
            Map<String, String> map = new HashMap<>(4);
            map.put("value", parmVelocityDefinition.getVelocityCde());
            map.put("label", parmVelocityDefinition.getDescription());
            list.add(map);
        });
        return list;
    }

    @Override
    @Transactional(rollbackFor = Exception.class, propagation = Propagation.REQUIRED)
    //@ShardingSphereTransactionType(TransactionType.XA)
    protected boolean updateDb(ParmModificationRecord parmModificationRecord) {
        ParmVelocityDefinition parmVelocityDefinition = JSON.parseObject(parmModificationRecord.getParmBody(), ParmVelocityDefinition.class);
        parmVelocityDefinition.initUpdateDateTime();
        int i = parmVelocityDefinitionMapper.updateByPrimaryKeySelective(parmVelocityDefinition);
        return i > 0;
    }

    @Override
    @Transactional(rollbackFor = Exception.class, propagation = Propagation.REQUIRED)
    //@ShardingSphereTransactionType(TransactionType.XA)
    protected boolean insertDb(ParmModificationRecord parmModificationRecord) {
        ParmVelocityDefinition parmVelocityDefinition = JSON.parseObject(parmModificationRecord.getParmBody(), ParmVelocityDefinition.class);
        parmVelocityDefinition.initCreateDateTime();
        parmVelocityDefinition.initUpdateDateTime();
        parmVelocityDefinition.setVersionNumber(1L);
        int i = parmVelocityDefinitionMapper.insertSelective(parmVelocityDefinition);
        return i > 0;
    }

    @Override
    @Transactional(rollbackFor = Exception.class, propagation = Propagation.REQUIRED)
    //@ShardingSphereTransactionType(TransactionType.XA)
    protected boolean deleteDb(ParmModificationRecord parmModificationRecord) {
        ParmVelocityDefinition parmVelocityDefinition = JSON.parseObject(parmModificationRecord.getParmBody(), ParmVelocityDefinition.class);
        int i = parmVelocityDefinitionMapper.deleteByPrimaryKey(parmVelocityDefinition.getId());
        return i > 0;
    }
}
