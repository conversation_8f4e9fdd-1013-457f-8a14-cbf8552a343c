package com.anytech.anytxn.parameter.common.controller.deprecated;

import io.swagger.v3.oas.annotations.tags.Tag;
import io.swagger.v3.oas.annotations.Operation;
import com.anytech.anytxn.common.core.base.PageResultDTO;
import com.anytech.anytxn.common.core.base.AnyTxnHttpResponse;
import com.anytech.anytxn.common.core.base.BizBaseController;
import com.anytech.anytxn.parameter.base.common.enums.ParameterRepDetailEnum;
import com.anytech.anytxn.parameter.base.common.domain.dto.system.SysCodeTypeReqDTO;
import com.anytech.anytxn.parameter.base.common.domain.dto.system.SysCodeTypeResDTO;
import com.anytech.anytxn.parameter.base.common.domain.dto.system.SysCodeTypeSearchKeyDTO;
import com.anytech.anytxn.parameter.base.common.service.system.ISysCodeTypeService;
import com.anytech.anytxn.parameter.base.common.utils.AnyTxnHttpResponseHelper;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.ArrayList;

/**
 * 系统字典类型 api
 * <AUTHOR>
 * @date 2018-08-15
 **/
@Deprecated
@Tag(name = "系统字典类型")
@RestController
public class SysCodeTypeController extends BizBaseController {

    private final Logger logger = LoggerFactory.getLogger(SysCodeTypeController.class);

    @Autowired
    private ISysCodeTypeService sysCodeTypeService;

    /**
     * 查询系统字典类型集合，通过启用状态
     * @param status 启用状态0:禁用|1:启用
     * @return
     */
    @Operation(summary = "查询系统字典类型集合")
    @GetMapping(value = "/param/sysCodeType/list")
    public AnyTxnHttpResponse<ArrayList<SysCodeTypeResDTO>> getListByEnableStatus(@RequestParam(required = false) String status) {
        ArrayList<SysCodeTypeResDTO> sysCodeTypeResList = (ArrayList)sysCodeTypeService.findByEnableStatus(status);
        return AnyTxnHttpResponse.success(sysCodeTypeResList, ParameterRepDetailEnum.QUERY.message());

    }

    @Operation(summary = "分页查询系统字典类型信息",description = "分页查询系统字典类型信息")
    @PostMapping(value = "/param/sysCodeType/findAll")
    public AnyTxnHttpResponse<PageResultDTO<SysCodeTypeResDTO>> getPage(@RequestBody(required = false) SysCodeTypeSearchKeyDTO searchKey)  {
        PageResultDTO<SysCodeTypeResDTO> txnPage = sysCodeTypeService.findPage(searchKey);
        return AnyTxnHttpResponse.success(txnPage,ParameterRepDetailEnum.QUERY.message());
    }

    /**
     * 查询系统字典类型，通过typeId
     * @param typeId 代码类型
     * @return 系统字典类型
     */
    @Operation(summary = "查询系统字典类型，通过typeId")
    @GetMapping(value = "/param/sysCodeType/typeId/{typeId}")
    public AnyTxnHttpResponse<SysCodeTypeResDTO> findByTypeId(@PathVariable(value = "typeId") String typeId) {
        SysCodeTypeResDTO sysCodeTypeRes = sysCodeTypeService.findByTypeId(typeId.trim());
        return AnyTxnHttpResponse.success(sysCodeTypeRes,ParameterRepDetailEnum.QUERY.message());

    }


    /**
     * 查询系统字典类型，通过typeName
     * @param typeName 代码类型名称
     * @return 系统字典类型
     */
    @Operation(summary = "查询系统字典类型，通过typeName")
    @GetMapping(value = "/param/sysCodeType/typeName/{typeName}")
    public AnyTxnHttpResponse<SysCodeTypeResDTO> findByTypeName(@PathVariable(value = "typeName") String typeName) {
        SysCodeTypeResDTO sysCodeTypeRes = sysCodeTypeService.findByTypeName(typeName);
        return AnyTxnHttpResponse.success(sysCodeTypeRes,ParameterRepDetailEnum.QUERY.message());

    }

    /**
     * 查询系统字典类型，通过id
     * @param id 代码类型名称
     * @return 系统字典类型
     */
    @Operation(summary = "查询系统字典类型，通过id")
    @GetMapping(value = "/param/sysCodeType/id/{id}")
    public AnyTxnHttpResponse<SysCodeTypeResDTO> findByTypeName(@PathVariable(value = "id") Long id) {
        SysCodeTypeResDTO sysCodeTypeRes = sysCodeTypeService.findCodeTypeById(id);
        return AnyTxnHttpResponse.success(sysCodeTypeRes,ParameterRepDetailEnum.QUERY.message());

    }

    /**
     * 查查询系统字典类型，以及所包含的枚举值列表，通过typeName
     * @param typeId 代码类型名称
     * @return 系统字典类型
     */
    @Operation(summary = "查询系统字典类型，以及所包含的枚举值列表，typeId")
    @GetMapping(value = "/param/sysCodeType/detail/typeId/{typeId}")
    public AnyTxnHttpResponse<SysCodeTypeResDTO> findCodeDetailByTypeId(@PathVariable(value = "typeId") String typeId) {
        SysCodeTypeResDTO sysCodeTypeRes = sysCodeTypeService.findCodeDetailByTypeId(typeId);
        return AnyTxnHttpResponse.success(sysCodeTypeRes,ParameterRepDetailEnum.QUERY.message());

    }

    /**
     * 修改系统字典类型包含的枚举值列表
     * @param sysCodeTypeReqDTO
     * @return 系统字典类型
     */
    @Operation(summary = "修改系统字典类型包含的枚举值列表")
    @PutMapping(value = "/param/sysCodeType")
    public AnyTxnHttpResponse<SysCodeTypeResDTO> modifySysCode(@RequestBody SysCodeTypeReqDTO sysCodeTypeReqDTO) {
        SysCodeTypeResDTO sysCode = sysCodeTypeService.modifySysCodeType(sysCodeTypeReqDTO);
        return AnyTxnHttpResponse.success(sysCode,ParameterRepDetailEnum.UPDATE.message());

    }

    /**
     * 修改系统字典类型
     * @param sysCodeTypeReqDTO
     * @return 系统字典类型
     */
    @Operation(summary = "修改系统字典类型")
    @PutMapping(value = "/param/update/sysCodeType")
    public AnyTxnHttpResponse<SysCodeTypeResDTO> updateSysCode(@RequestBody SysCodeTypeReqDTO sysCodeTypeReqDTO) {
        SysCodeTypeResDTO sysCode = sysCodeTypeService.updateSysCodeTypeAndCodeList(sysCodeTypeReqDTO);
        return AnyTxnHttpResponse.success(sysCode,ParameterRepDetailEnum.UPDATE.message());

    }

    /**
     * 添加系统字典类型
     * @param sysCodeTypeReqDTO
     * @return 系统字典类型
     */
    @Operation(summary = "添加系统字典类型")
    @PostMapping(value = "/param/sysCodeType")
    public AnyTxnHttpResponse<SysCodeTypeResDTO> addSysCodeType(@RequestBody SysCodeTypeReqDTO sysCodeTypeReqDTO) {
        SysCodeTypeResDTO sysCode = sysCodeTypeService.addSysCodeType(sysCodeTypeReqDTO);
        return AnyTxnHttpResponse.success(sysCode,ParameterRepDetailEnum.CREATE.message());

    }

    /**
     * 删除系统字典类型，通过typeId
     * @param typeId 代码类型
     * @return 系统字典类型
     */
    @Operation(summary = "删除系统字典类型，通过typeId")
    @DeleteMapping(value = "/param/sysCodeType/typeId/{typeId}")
    public AnyTxnHttpResponse deleteSysCode(@PathVariable(value = "typeId") String typeId) {
        sysCodeTypeService.deleteByTypeId(typeId);
        return AnyTxnHttpResponseHelper.succ(ParameterRepDetailEnum.DEL.message());

    }

    /**
     * 删除系统字典类型，通过id
     * @param id 代码类型
     * @return 系统字典类型
     */
    @Operation(summary = "删除系统字典类型，通过id")
    @DeleteMapping(value = "/param/sysCodeType/id/{id}")
    public AnyTxnHttpResponse deleteSysCode(@PathVariable(value = "id") Long id) {
        sysCodeTypeService.deleteById(id);
        return AnyTxnHttpResponseHelper.succ(ParameterRepDetailEnum.DEL.message());

    }
}
