package com.anytech.anytxn.parameter.authorization.service;


import com.alibaba.fastjson.JSON;
import com.github.pagehelper.Page;
import com.github.pagehelper.PageHelper;
import com.anytech.anytxn.common.core.annotation.db.DeleteParameterAnnotation;
import com.anytech.anytxn.common.core.annotation.db.InsertParameterAnnotation;
import com.anytech.anytxn.common.core.annotation.db.UpdateParameterAnnotation;
import com.anytech.anytxn.business.base.audit.AbstractParameterService;
import com.anytech.anytxn.common.core.base.ParameterCompare;
import com.anytech.anytxn.business.base.audit.ParmModificationRecord;
import com.anytech.anytxn.common.core.base.PageResultDTO;
import com.anytech.anytxn.common.core.utils.OrgNumberUtils;
import com.anytech.anytxn.common.sequence.utils.Number16IdGen;
import com.anytech.anytxn.common.core.utils.TenantUtils;
import com.anytech.anytxn.parameter.base.authorization.domain.dto.VelocityDefinitionDTO;
import com.anytech.anytxn.parameter.base.authorization.domain.dto.VelocitySetDTO;
import com.anytech.anytxn.parameter.base.authorization.domain.dto.VelocitySetDefinitionDTO;
import com.anytech.anytxn.parameter.base.authorization.service.IVelocitySetDefinitionService;
import com.anytech.anytxn.parameter.base.common.enums.AnyTxnParameterRespCodeEnum;
import com.anytech.anytxn.parameter.base.common.exception.AnyTxnParameterException;
import com.anytech.anytxn.parameter.authorization.mapper.ParmVelocityDefinitionSelfMapper;
import com.anytech.anytxn.parameter.authorization.mapper.ParmVelocitySetDefinitionMapper;
import com.anytech.anytxn.parameter.authorization.mapper.ParmVelocitySetDetailMapper;
import com.anytech.anytxn.parameter.base.authorization.domain.model.ParmVelocityDefinition;
import com.anytech.anytxn.parameter.base.authorization.domain.model.ParmVelocitySetDefinition;
import com.anytech.anytxn.parameter.base.authorization.domain.model.ParmVelocitySetDetail;
//import org.apache.shardingsphere.transaction.annotation.ShardingSphereTransactionType;
//import org.apache.shardingsphere.transaction.core.TransactionType;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.cglib.beans.BeanCopier;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * @Description 流量集合具体实现类
 * <AUTHOR>
 * @Date 2020/06/22
 */
@Service(value = "parm_velocity_set_definition_serviceImpl")
public class VelocitySetDefinitionServiceImpl extends AbstractParameterService implements  IVelocitySetDefinitionService {

    private static final Logger logger = LoggerFactory.getLogger(VelocitySetDefinitionServiceImpl.class);

    @Autowired
    private ParmVelocitySetDefinitionMapper parmVelocitySetDefinitionMapper;
    @Autowired
    private ParmVelocitySetDetailMapper parmVelocitySetDetailMapper;
    @Autowired
    private ParmVelocityDefinitionSelfMapper parmVelocityDefinitionSelfMapper;
    @Autowired
    private Number16IdGen numberIdGenerator;


    @Override
    public PageResultDTO<VelocitySetDefinitionDTO> findListVelocitySetDefinition(Integer page, Integer rows, VelocitySetDTO velocitySetDTO) {
        logger.info("分页查询流量集合定义表，当前页：{}，每页展示条数：{}", page, rows);
        if(null == velocitySetDTO){
            velocitySetDTO = new VelocitySetDTO();
        }
        velocitySetDTO.setOrganizationNumber(StringUtils.isEmpty(velocitySetDTO.getOrganizationNumber()) ? OrgNumberUtils.getOrg() : velocitySetDTO.getOrganizationNumber());
        Page<?> pageInfo = PageHelper.startPage(page, rows);
        List<VelocitySetDefinitionDTO> velocitySetDefinitionDTOList = null;
        try {
            List<ParmVelocitySetDefinition> parmVelocitySetDefinitionList = parmVelocitySetDefinitionMapper.selectByCondition(velocitySetDTO);
            if (!CollectionUtils.isEmpty(parmVelocitySetDefinitionList)) {
                velocitySetDefinitionDTOList = new ArrayList<>();
                BeanCopier beanCopier = BeanCopier.create(ParmVelocitySetDefinition.class, VelocitySetDefinitionDTO.class, false);
                for (ParmVelocitySetDefinition parmVelocitySetDefinition : parmVelocitySetDefinitionList) {
                    VelocitySetDefinitionDTO velocitySetDefinitionDTO = new VelocitySetDefinitionDTO();
                    beanCopier.copy(parmVelocitySetDefinition, velocitySetDefinitionDTO, null);
                    velocitySetDefinitionDTOList.add(velocitySetDefinitionDTO);
                }
            }
            return new PageResultDTO<>(page, rows, pageInfo.getTotal(), pageInfo.getPages(), velocitySetDefinitionDTOList);
        } catch (Exception e) {
            logger.error("分页查询流量集合定义表信息失败，当前页：{}，每页展示条数：{},错误信息：{}", page, rows, e.getMessage());
            throw new AnyTxnParameterException(AnyTxnParameterRespCodeEnum.D_PAGE_VELOCITY_SET_DEFINITION_FAULT);
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    @InsertParameterAnnotation(tableName = "parm_velocity_set_definition", tableDesc = "Velocity Check Group", isJoinTable = true)
    public ParameterCompare addVelocitySetDefinition(VelocitySetDTO velocitySetDTO) {
        if (velocitySetDTO == null) {
            logger.error("velocitySetDTO is null！");

            throw new AnyTxnParameterException(AnyTxnParameterRespCodeEnum.P_PARAMETER_EMPTY_FAULT);
        }


        int existsByVelocitySetCode = parmVelocitySetDefinitionMapper.isExistsByVelocitySetCode(velocitySetDTO.getVelocitySetCode(),velocitySetDTO.getOrganizationNumber());
        if (existsByVelocitySetCode > 0) {
            throw new AnyTxnParameterException(AnyTxnParameterRespCodeEnum.D_EXIST_VELOCITY_SET_CODE_DEFINITION_FAULT);
        }

        List<VelocityDefinitionDTO> velocityDefinitionDTOList = velocitySetDTO.getVelocityDefinitionDTOList();
        for (VelocityDefinitionDTO velocityDefinitionDTO : velocityDefinitionDTOList) {
            int existsByVelocityCde = parmVelocitySetDetailMapper.isExistsByVelocityCde(velocityDefinitionDTO.getVelocityCde(), velocitySetDTO.getVelocitySetCode(),velocitySetDTO.getOrganizationNumber());
            if (existsByVelocityCde > 0) {

                throw new AnyTxnParameterException(AnyTxnParameterRespCodeEnum.D_EXIST_VELOCITY_DEFINITION_FAULT);
            }
        }
        velocitySetDTO.setId(numberIdGenerator.generateId(TenantUtils.getTenantId())+"");
        velocitySetDTO.setVersionNumber(1L);

        return ParameterCompare.getBuilder().
                withMainParmId(velocitySetDTO.getVelocitySetCode()).
                withAfter(velocitySetDTO).build(VelocitySetDTO.class);

    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    @UpdateParameterAnnotation(tableName = "parm_velocity_set_definition", tableDesc = "Velocity Check Group", isJoinTable = true)
    public ParameterCompare modifyVelocitySetDefinition(VelocitySetDTO velocitySetDTO) {

        VelocitySetDTO velocitySetDefinition = findVelocitySetDefinition(velocitySetDTO.getId());

        return ParameterCompare.getBuilder()
                .withMainParmId(velocitySetDefinition.getVelocitySetCode())
                .withAfter(velocitySetDTO).withBefore(velocitySetDefinition).build(VelocitySetDTO.class);

    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    @DeleteParameterAnnotation(tableName = "parm_velocity_set_definition", tableDesc = "Velocity Check Group", isJoinTable = true)
    public ParameterCompare removeVelocitySetDefinition(String id) {

        ParmVelocitySetDefinition parmVelocitySetDefinition = parmVelocitySetDefinitionMapper.selectByPrimaryKey(id);

        return ParameterCompare
                .getBuilder()
                .withMainParmId(parmVelocitySetDefinition.getVelocitySetCode())
                .withBefore(parmVelocitySetDefinition).build(ParmVelocityDefinition.class);

    }


    @Override
    public VelocitySetDTO findVelocitySetDefinition(String id) {
        logger.info("根据主键:{},获取流量集合信息", id);
        VelocitySetDTO velocitySetDTO = null;
        List<VelocityDefinitionDTO> velocityDefinitionDTOList = null;
        try {
            ParmVelocitySetDefinition parmVelocitySetDefinition = parmVelocitySetDefinitionMapper.selectByPrimaryKey(id);
            if (parmVelocitySetDefinition != null) {
                velocitySetDTO = new VelocitySetDTO();
                velocitySetDTO.setOrganizationNumber(parmVelocitySetDefinition.getOrganizationNumber());
                velocitySetDTO.setId(parmVelocitySetDefinition.getId());
                velocitySetDTO.setVelocitySetCode(parmVelocitySetDefinition.getVelocitySetCode());
                velocitySetDTO.setDescription(parmVelocitySetDefinition.getDescription());
                velocitySetDTO.setStatus(parmVelocitySetDefinition.getStatus());
                velocitySetDTO.setVersionNumber(parmVelocitySetDefinition.getVersionNumber());
            }

            List<ParmVelocitySetDetail> parmVelocitySetDetailList = parmVelocitySetDetailMapper.selectByOrgNumberAndVelocitySetCode(parmVelocitySetDefinition.getOrganizationNumber(), parmVelocitySetDefinition.getVelocitySetCode());
            if (!CollectionUtils.isEmpty(parmVelocitySetDetailList)) {
                velocityDefinitionDTOList = new ArrayList<>();
                for (ParmVelocitySetDetail parmVelocitySetDetail : parmVelocitySetDetailList) {
                    VelocityDefinitionDTO velocityDefinitionDTO = new VelocityDefinitionDTO();
                    velocityDefinitionDTO.setOrganizationNumber(parmVelocitySetDetail.getOrganizationNumber());
                    velocityDefinitionDTO.setVelocityCde(parmVelocitySetDetail.getVelocityCde());
                    velocityDefinitionDTOList.add(velocityDefinitionDTO);
                }
            }
            velocitySetDTO.setVelocityDefinitionDTOList(velocityDefinitionDTOList);

        } catch (Exception e) {
            logger.error("根据主键:{},获取流量集合信息失败,错误信息:{}", id, e.getMessage());
            throw new AnyTxnParameterException(AnyTxnParameterRespCodeEnum.D_QUERY_VELOCITY_SET_DEFINITION_BY_ID_FAULT);
        }
        if (velocitySetDTO == null) {
            logger.error("根据主键:{},获取流量集合信息失败", id);
            throw new AnyTxnParameterException(AnyTxnParameterRespCodeEnum.D_QUERY_VELOCITY_SET_DEFINITION_NULL_BY_ID_FAULT);
        }
        return velocitySetDTO;
    }


    @Override
    public List<Map<String, String>> getVelocityCodes(String organizationNumber) {
        logger.info("获取所有授权流量检查编号");
        List<ParmVelocityDefinition> parmVelocityDefinitionList = parmVelocityDefinitionSelfMapper.selectAll(true, organizationNumber);
        List<Map<String, String>> list = new ArrayList<>();
        parmVelocityDefinitionList.forEach(parmVelocityDefinition -> {
            Map<String, String> map = new HashMap<>(4);
            map.put("value", parmVelocityDefinition.getVelocityCde());
            map.put("label", parmVelocityDefinition.getDescription());
            list.add(map);
        });
        return list;
    }

    @Override
    public List<VelocitySetDefinitionDTO> getVelocitySetCodes(String organizationNumber) {
        List<VelocitySetDefinitionDTO> velocitySetDefinitionDTOList = null;

        List<ParmVelocitySetDefinition> parmVelocitySetDefinitionList = parmVelocitySetDefinitionMapper.selectByOrganizationNumber(organizationNumber);
        if (!CollectionUtils.isEmpty(parmVelocitySetDefinitionList)){
            velocitySetDefinitionDTOList = new ArrayList<>();
            BeanCopier beanCopier = BeanCopier.create(ParmVelocitySetDefinition.class, VelocitySetDefinitionDTO.class, false);
            for (ParmVelocitySetDefinition parmVelocitySetDefinition : parmVelocitySetDefinitionList) {
                VelocitySetDefinitionDTO velocitySetDefinitionDTO = new VelocitySetDefinitionDTO();
                beanCopier.copy(parmVelocitySetDefinition, velocitySetDefinitionDTO, null);
                velocitySetDefinitionDTOList.add(velocitySetDefinitionDTO);
            }
            return velocitySetDefinitionDTOList;
        }
        return null;
    }

    @Override
    public List<VelocityDefinitionDTO> getVelocityCdes(String organizationNumber, String velocitySetCode) {
        List<VelocityDefinitionDTO> velocityDefinitionDTOList = null;

        List<ParmVelocitySetDetail> parmVelocitySetDetailList = parmVelocitySetDetailMapper.selectByOrgNumberAndVelocitySetCode(organizationNumber, velocitySetCode);
        if (CollectionUtils.isEmpty(parmVelocitySetDetailList)) {
            throw new AnyTxnParameterException(AnyTxnParameterRespCodeEnum.D_QUERY_VELOCITY_SET_CDE_NULL_FAULT);
        }
        velocityDefinitionDTOList = new ArrayList<>();
        for (ParmVelocitySetDetail parmVelocitySetDetail : parmVelocitySetDetailList) {
            ParmVelocityDefinition parmVelocityDefinition = parmVelocityDefinitionSelfMapper.selectByVelocityCode(organizationNumber, parmVelocitySetDetail.getVelocityCde());
            VelocityDefinitionDTO velocityDefinitionDTO = new VelocityDefinitionDTO();
            velocityDefinitionDTO.setVelocityCde(parmVelocityDefinition.getVelocityCde());
            velocityDefinitionDTO.setDescription(parmVelocityDefinition.getDescription());
            velocityDefinitionDTO.setMaxTxnCount(parmVelocityDefinition.getMaxTxnCount());
            velocityDefinitionDTO.setMaxTxnAmount(parmVelocityDefinition.getMaxTxnAmount());
            velocityDefinitionDTO.setOrganizationNumber(parmVelocityDefinition.getOrganizationNumber());
            velocityDefinitionDTOList.add(velocityDefinitionDTO);
        }
        return velocityDefinitionDTOList;
    }

    @Override
    @Transactional(rollbackFor = Exception.class, propagation = Propagation.REQUIRED)
    //@ShardingSphereTransactionType(TransactionType.XA)
    protected boolean updateDb(ParmModificationRecord parmModificationRecord) {
        VelocitySetDTO velocitySetDTO = JSON.parseObject(parmModificationRecord.getParmBody(), VelocitySetDTO.class);


        List<ParmVelocitySetDetail> parmVelocitySetDetailList = parmVelocitySetDetailMapper
                .selectByOrgNumberAndVelocitySetCode(velocitySetDTO.getOrganizationNumber(),
                        velocitySetDTO.getVelocitySetCode());

        for (ParmVelocitySetDetail parmVelocitySetDetail : parmVelocitySetDetailList){
            int i = parmVelocitySetDetailMapper.deleteByPrimaryKey(parmVelocitySetDetail.getId());
            logger.info("删除行数{}",i);
        }
        //更新流量集合定义表
        ParmVelocitySetDefinition parmVelocitySetDefinition = new ParmVelocitySetDefinition();
        parmVelocitySetDefinition.setId(velocitySetDTO.getId());
        parmVelocitySetDefinition.setDescription(velocitySetDTO.getDescription());
        parmVelocitySetDefinition.setStatus(velocitySetDTO.getStatus());
        parmVelocitySetDefinition.initUpdateDateTime();
        parmVelocitySetDefinition.setId(velocitySetDTO.getId());
        parmVelocitySetDefinition.setVersionNumber(velocitySetDTO.getVersionNumber());
        parmVelocitySetDefinitionMapper.updateByPrimaryKeySelective(parmVelocitySetDefinition);

        //存储到流量集合详情表
        List<VelocityDefinitionDTO> velocityDefinitionDTOList = velocitySetDTO.getVelocityDefinitionDTOList();
        for (VelocityDefinitionDTO velocityDefinitionDTO : velocityDefinitionDTOList) {
            ParmVelocitySetDetail parmVelocitySetDetail = new ParmVelocitySetDetail();
            parmVelocitySetDetail.setVelocitySetCode(velocitySetDTO.getVelocitySetCode());
            parmVelocitySetDetail.setVelocityCde(velocityDefinitionDTO.getVelocityCde());
            parmVelocitySetDetail.setStatus(velocitySetDTO.getStatus());
            parmVelocitySetDetail.setOrganizationNumber(velocitySetDTO.getOrganizationNumber());
            parmVelocitySetDetail.setId(numberIdGenerator.generateId(TenantUtils.getTenantId())+"");
            parmVelocitySetDetail.initCreateDateTime();
            parmVelocitySetDetail.initUpdateDateTime();
            parmVelocitySetDetail.setUpdateBy(parmModificationRecord.getApplicationBy());
            int existsByVelocityCde =
                    parmVelocitySetDetailMapper.isExistsByVelocityCde
                            (parmVelocitySetDetail.getVelocityCde(), velocitySetDTO.getVelocitySetCode()
                                    ,velocitySetDTO.getOrganizationNumber());
            if (existsByVelocityCde > 0) {
                throw new AnyTxnParameterException(AnyTxnParameterRespCodeEnum.D_EXIST_VELOCITY_DEFINITION_FAULT);
            }
            try {
                parmVelocitySetDetailMapper.insertSelective(parmVelocitySetDetail);
            } catch (Exception e) {
                logger.error("调用[{}]更新流量集合信息[{}]失败,错误信息[{}]", e);
                throw new AnyTxnParameterException(AnyTxnParameterRespCodeEnum.D_UPDATE_VELOCITY_SET_DEFINITION_FAULT);
            }
        }
    return true;
    }

    @Override
    @Transactional(rollbackFor = Exception.class, propagation = Propagation.REQUIRED)
    //@ShardingSphereTransactionType(TransactionType.XA)
    protected boolean insertDb(ParmModificationRecord parmModificationRecord) {
        VelocitySetDTO parmVelocitySetDefinition1 = JSON.parseObject(parmModificationRecord.getParmBody(), VelocitySetDTO.class);

        //存储到流量集合定义表
        ParmVelocitySetDefinition parmVelocitySetDefinition = new ParmVelocitySetDefinition();
        parmVelocitySetDefinition.setVelocitySetCode(parmVelocitySetDefinition1.getVelocitySetCode());
        parmVelocitySetDefinition.setDescription(parmVelocitySetDefinition1.getDescription());
        parmVelocitySetDefinition.setStatus(parmVelocitySetDefinition1.getStatus());
        parmVelocitySetDefinition.setOrganizationNumber(parmVelocitySetDefinition1.getOrganizationNumber());
        parmVelocitySetDefinition.initCreateDateTime();
        parmVelocitySetDefinition.initUpdateDateTime();
        parmVelocitySetDefinition.setUpdateBy(parmModificationRecord.getApplicationBy());
        parmVelocitySetDefinition.setId(parmVelocitySetDefinition1.getId());
        parmVelocitySetDefinition.setVersionNumber(parmVelocitySetDefinition1.getVersionNumber());

        try {
            parmVelocitySetDefinitionMapper.insertSelective(parmVelocitySetDefinition);
        } catch (Exception e) {
            throw new AnyTxnParameterException(AnyTxnParameterRespCodeEnum.D_INSERT_VELOCITY_SET_DEFINITION_FAULT);
        }
        List<VelocityDefinitionDTO> velocityDefinitionDTOList = parmVelocitySetDefinition1.getVelocityDefinitionDTOList();
        //存储到流量集合详情表
        for (VelocityDefinitionDTO velocityDefinitionDTO : velocityDefinitionDTOList) {
            ParmVelocitySetDetail parmVelocitySetDetail = new ParmVelocitySetDetail();
            parmVelocitySetDetail.setVelocitySetCode(parmVelocitySetDefinition1.getVelocitySetCode());
            parmVelocitySetDetail.setVelocityCde(velocityDefinitionDTO.getVelocityCde());
            parmVelocitySetDetail.setStatus(parmVelocitySetDefinition1.getStatus());
            parmVelocitySetDetail.setOrganizationNumber(parmVelocitySetDefinition1.getOrganizationNumber());
            parmVelocitySetDetail.setId(numberIdGenerator.generateId(TenantUtils.getTenantId())+"");
            parmVelocitySetDetail.initCreateDateTime();
            parmVelocitySetDetail.initUpdateDateTime();
            parmVelocitySetDetail.setUpdateBy(parmModificationRecord.getApplicationBy());

            try {
                parmVelocitySetDetailMapper.insertSelective(parmVelocitySetDetail);
            } catch (Exception e) {
                throw new AnyTxnParameterException(AnyTxnParameterRespCodeEnum.D_INSERT_VELOCITY_SET_DEFINITION_FAULT);
            }
        }
        return true;
    }

    @Override
    @Transactional(rollbackFor = Exception.class, propagation = Propagation.REQUIRED)
    //@ShardingSphereTransactionType(TransactionType.XA)
    protected boolean deleteDb(ParmModificationRecord parmModificationRecord) {
        ParmVelocitySetDefinition parmVelocitySetDefinition = JSON.parseObject(parmModificationRecord.getParmBody(), ParmVelocitySetDefinition.class);
        int i = parmVelocitySetDefinitionMapper.deleteByPrimaryKey(parmVelocitySetDefinition.getId());
        if (i<1){
            logger.warn("未能成功删除，id:{}", parmVelocitySetDefinition.getId());
            throw new AnyTxnParameterException(AnyTxnParameterRespCodeEnum.D_DELETE_VELOCITY_SET_DEFINITION_FAULT);
        }
        parmVelocitySetDetailMapper.deleteByCodeAndOrgan(parmVelocitySetDefinition.getVelocitySetCode(),parmVelocitySetDefinition.getOrganizationNumber());

    return true;
    }
}
