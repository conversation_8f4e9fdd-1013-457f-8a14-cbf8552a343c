package com.anytech.anytxn.parameter.common.controller.product;

import io.swagger.v3.oas.annotations.tags.Tag;
import io.swagger.v3.oas.annotations.Operation;
import com.anytech.anytxn.common.core.base.PageResultDTO;
import com.anytech.anytxn.common.core.base.AnyTxnHttpResponse;
import com.anytech.anytxn.common.core.base.BizBaseController;
import com.anytech.anytxn.parameter.base.common.enums.ParameterRepDetailEnum;
import com.anytech.anytxn.parameter.base.common.domain.dto.product.CardProductInfoReqDTO;
import com.anytech.anytxn.parameter.base.common.domain.dto.product.CardProductInfoResDTO;
import com.anytech.anytxn.parameter.base.common.domain.dto.system.OrganizationInfoResDTO;
import com.anytech.anytxn.parameter.base.common.service.product.ICardProductInfoService;
import com.anytech.anytxn.parameter.base.common.service.system.IOrganizationInfoService;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.format.annotation.DateTimeFormat;
import org.springframework.web.bind.annotation.*;

import jakarta.validation.Valid;
import java.time.LocalDate;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * 卡产品信息
 *
 * <AUTHOR>
 * @date 2018-12-04
 **/
@RestController
@Tag(name = "卡产品信息")
public class CardProductInfoController extends BizBaseController {

    @Autowired
    private ICardProductInfoService cardProductInfoService;
    @Autowired
    private IOrganizationInfoService organizationInfoService;

    /**
     *
     * 根据机构号、卡产品编号查询卡产品信息
     * @param organizationNumber 机构号
     * @param productNumber 卡产品编号
     * @return HttpApiResponse<CardProductInfoRes>
     */
    @GetMapping(value = "/param/cardProductInfo/organizationNumber/{organizationNumber}/productNumber/{productNumber}")
    @Operation(summary = "根据机构号、卡产品编号查询卡产品信息")
    public AnyTxnHttpResponse<CardProductInfoResDTO> getByOrgAndProductNum(@PathVariable(value = "organizationNumber") String organizationNumber,
                                                                           @PathVariable(value = "productNumber") String productNumber) {
        CardProductInfoResDTO cardProductInfoRes = cardProductInfoService.findByOrgAndProductNum(organizationNumber,productNumber);
        return AnyTxnHttpResponse.success(cardProductInfoRes);

    }

    /**
     * 创建卡产品信息条目
     * @param cardProductInfoReq 卡产品信息请求数据
     * @return HttpApiResponse<CardProductInfoRes>
     */
    @PostMapping(value = "/param/cardProductInfo")
    @Operation(summary = "创建卡产品信息")
    public AnyTxnHttpResponse<Object> create(@Valid @RequestBody CardProductInfoReqDTO cardProductInfoReq) {
        return AnyTxnHttpResponse.success(cardProductInfoService.add(cardProductInfoReq), ParameterRepDetailEnum.CREATE.message());

    }

    /**
     * 删除卡产品信息条目，通过id
     * @param id 技术id
     * @return HttpApiResponse<Boolean>
     */
    @DeleteMapping(value = "/param/cardProductInfo/id/{id}")
    @Operation(summary="根据id删除卡产品信息条目", description = "需传入id")
    public AnyTxnHttpResponse<Object> remove(@PathVariable String id) {
        return AnyTxnHttpResponse.success(cardProductInfoService.remove(id),ParameterRepDetailEnum.DEL.message());

    }

    /**
     * 更新卡产品信息
     * @param cardProductInfoReq 卡产品信息请求数据
     * @return HttpApiResponse<CardProductInfoRes>
     */
    @PutMapping(value = "/param/cardProductInfo")
    @Operation(summary="根据id更新卡产品信息", description = "")
    public AnyTxnHttpResponse<Object> modify(@Valid @RequestBody CardProductInfoReqDTO cardProductInfoReq) {
        return AnyTxnHttpResponse.success(cardProductInfoService.modify(cardProductInfoReq),ParameterRepDetailEnum.UPDATE.message());

    }

    /**
     * 根据根据id获取详情，进行编辑
     * @param id 卡产品信息id
     * @return HttpApiResponse<ProductInfoRes>
     */
    @Operation(summary="获取卡产品详情，通过id", description = "")
    @GetMapping(value = "/param/cardProductInfo/id/{id}")
    public AnyTxnHttpResponse<CardProductInfoResDTO> getByIndex(@PathVariable String id){
        CardProductInfoResDTO cardProductInfoRes = cardProductInfoService.find(id);
        return AnyTxnHttpResponse.success(cardProductInfoRes);

    }

    /**
     * 查询所有卡产品参数信息
     *
     * @return TxnTypeControlRes
     */
    @Operation(summary = "卡产品参数分页查询", description = "分页查询")
    @GetMapping(value = "/param/cardProductInfo/pageNum/{pageNum}/pageSize/{pageSize}")
    public AnyTxnHttpResponse<PageResultDTO<CardProductInfoResDTO>> getAllPage(@PathVariable(value = "pageNum") Integer pageNum,
                                                                               @PathVariable(value = "pageSize") Integer pageSize,
                                                                               @RequestParam(required = false) String productNumber,
                                                                               @RequestParam(required = false) String description,
                                                                               @DateTimeFormat(iso = DateTimeFormat.ISO.DATE) @RequestParam(required = false) LocalDate dateStart,
                                                                               @DateTimeFormat(iso = DateTimeFormat.ISO.DATE) @RequestParam(required = false) LocalDate dateEnd,
                                                                               @RequestParam(required = false) String organizationNumber) {
        CardProductInfoReqDTO cardProductInfoReq = new CardProductInfoReqDTO();
        cardProductInfoReq.setProductNumber(productNumber);
        cardProductInfoReq.setDescription(description);
        cardProductInfoReq.setDateStart(dateStart);
        cardProductInfoReq.setDateEnd(dateEnd);
        cardProductInfoReq.setOrganizationNumber(organizationNumber);
        PageResultDTO<CardProductInfoResDTO> response = cardProductInfoService.findAll(pageNum, pageSize,cardProductInfoReq);
        return AnyTxnHttpResponse.success(response);
    }

    /**
     *
     * 根据机构号查询卡产品信息
     * @param organizationNumber 机构号
     * @return HttpApiResponse<List<CardProductInfoResDTO>>
     */
    @GetMapping(value = "/param/cardProductInfo/organizationNumber/{organizationNumber}")
    @Operation(summary = "根据机构号查询卡产品信息")
    public AnyTxnHttpResponse<List<CardProductInfoResDTO>> getCardProductsByOrg(@PathVariable(value = "organizationNumber") String organizationNumber,
                                                                                @RequestParam(value = "state",required = false) String state) {
        List<CardProductInfoResDTO> cardProductInfoResList = cardProductInfoService.findCardProductsByOrg(organizationNumber);
        /* 添加状态的过滤条件和效期 中行poc lvxile 2020/09/09 */
        if (StringUtils.isNotEmpty(state) && !cardProductInfoResList.isEmpty()) {
            OrganizationInfoResDTO organizationInfoResDto = organizationInfoService.findOrganizationInfo(organizationNumber);
            LocalDate nextProcessingDay = organizationInfoResDto.getNextProcessingDay();
            List<CardProductInfoResDTO> list = cardProductInfoResList.stream().filter(it -> Objects.equals(it.getStatus(), state)).
                    filter(it -> !nextProcessingDay.isAfter(it.getDateEnd()) && !nextProcessingDay.isBefore(it.getDateStart()))
                    .collect(Collectors.toList());
            return AnyTxnHttpResponse.success(list);
        }
        return AnyTxnHttpResponse.success(cardProductInfoResList);
    }
}
