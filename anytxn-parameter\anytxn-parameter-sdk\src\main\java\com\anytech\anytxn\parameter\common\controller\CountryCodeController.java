package com.anytech.anytxn.parameter.common.controller;

import com.anytech.anytxn.common.core.base.ParameterCompare;
import com.anytech.anytxn.common.core.base.PageResultDTO;
import com.anytech.anytxn.common.core.base.AnyTxnHttpResponse;
import com.anytech.anytxn.common.core.base.BizBaseController;
import com.anytech.anytxn.parameter.base.common.domain.dto.ParmCountryCodeDTO;
import com.anytech.anytxn.parameter.base.common.service.ICountryCodeService;
import com.anytech.anytxn.parameter.base.common.enums.ParameterRepDetailEnum;
import io.swagger.v3.oas.annotations.tags.Tag;
import io.swagger.v3.oas.annotations.Operation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 *
 * <AUTHOR>
 * @date 2021-10-19
 */
@RestController
@Tag(name = "国家码操作接口")
public class CountryCodeController extends BizBaseController {

    @Autowired
    private ICountryCodeService countryCodeService;

    /**
     * 查询国家码基本信息
     */
    @Operation(summary = "查询国家码基本信息")
    @GetMapping("/param/selectCountryCode")
    public AnyTxnHttpResponse<PageResultDTO<ParmCountryCodeDTO>> selectCountryCode(
            @RequestParam(value = "page", defaultValue = "1") Integer page,
            @RequestParam(value = "rows", defaultValue = "10") Integer rows) {
        PageResultDTO<ParmCountryCodeDTO> countryCode = countryCodeService.selectCountryCodeBasic(page,rows);
        return AnyTxnHttpResponse.success(countryCode);
    }

    /**
     * 根据主键查询国家码基本信息
     */
    @Operation(summary = "查询国家码基本信息")
    @GetMapping("/param/selectCountryCodeById")
    public AnyTxnHttpResponse<ParmCountryCodeDTO> getByIndex(@RequestParam(value = "id") String id){
        ParmCountryCodeDTO countryCodeDTO = countryCodeService.selectById(id);
        return AnyTxnHttpResponse.success(countryCodeDTO);

    }


    /**
     * 增加国家码基本信息
     */
    @Operation(summary = "增加国家码基本信息")
    @PostMapping("/param/insertCountryCode")
    public AnyTxnHttpResponse<ParameterCompare> addCountryCode(@RequestBody ParmCountryCodeDTO countryCodeDTO){
        ParameterCompare countryCodeDTO1=countryCodeService.add(countryCodeDTO);
        return AnyTxnHttpResponse.success(countryCodeDTO1);
    }

    /**
     * 删除国家码基本信息
     */
    @Operation(summary = "删除国家码基本信息")
    @DeleteMapping("/param/deleteCountryCode")
    public AnyTxnHttpResponse<ParameterCompare> delCountry(@RequestParam("id") String id){
        countryCodeService.delete(id);
        return AnyTxnHttpResponse.successDetail(ParameterRepDetailEnum.DEL.message());
    }

    /**
     * 修改国家码基本信息
     */
    @Operation(summary = "修改国家码基本信息")
    @PutMapping("/param/updatCountryCode")
    public AnyTxnHttpResponse<ParameterCompare> modifyCountry(@RequestBody ParmCountryCodeDTO countryCodeDTO){
        countryCodeService.update(countryCodeDTO) ;
        return AnyTxnHttpResponse.successDetail(ParameterRepDetailEnum.UPDATE.message());
    }

    /**
     * 查询全部国家码信息
     */
    @Operation(summary = "查询国家码全部信息")
    @GetMapping("/param/selAllCountryCode")
    public AnyTxnHttpResponse<List<ParmCountryCodeDTO>> selCountry(){
        List<ParmCountryCodeDTO> countryCode = countryCodeService.selectAllCountry();
        return AnyTxnHttpResponse.success(countryCode);
    }

}
