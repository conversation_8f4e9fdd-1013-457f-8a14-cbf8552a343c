package com.anytech.anytxn.business.account.service;

import com.anytech.anytxn.business.base.account.domain.dto.AccMaDTO;
import com.anytech.anytxn.business.base.account.domain.dto.AccStaDTO;
import com.anytech.anytxn.business.base.account.domain.dto.AccountManagementInfoDTO;
import com.anytech.anytxn.business.base.account.domain.dto.AccountStatisticsInfoDTO;
import com.anytech.anytxn.business.base.card.domain.dto.CardCustSpecialInfoDTO;
import com.anytech.anytxn.business.base.card.service.ICardCustSpecialInfoService;
import com.anytech.anytxn.business.base.monetary.domain.bo.CustAccountBO;
import com.anytech.anytxn.business.base.monetary.domain.bo.CustomerBO;
import com.anytech.anytxn.business.base.monetary.exception.AnyTxnAddAccountException;
import com.anytech.anytxn.business.dao.account.mapper.AccountManagementInfoMapper;
import com.anytech.anytxn.business.dao.account.mapper.AccountManagementInfoSelfMapper;
import com.anytech.anytxn.business.dao.account.mapper.AccountStatisticsInfoMapper;
import com.anytech.anytxn.business.dao.account.mapper.AccountStatisticsInfoSelfMapper;
import com.anytech.anytxn.business.dao.account.model.AccountManagementInfo;
import com.anytech.anytxn.business.dao.account.model.AccountStatisticsInfo;
import com.anytech.anytxn.business.dao.customer.mapper.CorporateCustomerInfoMapper;
import com.anytech.anytxn.business.dao.customer.mapper.CustomerBasicInfoSelfMapper;
import com.anytech.anytxn.business.dao.customer.model.CorporateCustomerInfo;
import com.anytech.anytxn.business.dao.customer.model.CustomerBasicInfo;
import com.anytech.anytxn.common.core.enums.AccountStatusEnum;
import com.anytech.anytxn.common.core.utils.BeanMapping;
import com.anytech.anytxn.common.core.utils.OrgNumberUtils;
import com.anytech.anytxn.common.core.utils.PartitionKeyUtils;
import com.anytech.anytxn.common.core.utils.TenantUtils;
import com.anytech.anytxn.common.sequence.utils.ManageAccountIdGen;
import com.anytech.anytxn.common.sequence.utils.SequenceIdGen;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockedStatic;
import org.mockito.junit.jupiter.MockitoExtension;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.*;

/**
 * AccountCommonServiceImpl 单元测试类
 * 测试账户创建和统计信息的服务实现
 *
 * <AUTHOR> Assistant
 * @date 2024-12-19
 * @version 1.0
 */
@ExtendWith(MockitoExtension.class)
class AccountCommonServiceTest {

    @InjectMocks
    private AccountCommonServiceImpl accountCommonService;

    @Mock
    private AccountManagementInfoMapper accountManagementInfoMapper;

    @Mock
    private AccountStatisticsInfoMapper accountStatisticsInfoMapper;

    @Mock
    private AccountStatisticsInfoSelfMapper accountStatisticsInfoSelfMapper;

    @Mock
    private AccountManagementInfoSelfMapper accountManagementInfoSelfMapper;

    @Mock
    private CustomerBasicInfoSelfMapper customerBasicInfoSelfMapper;

    @Mock
    private CorporateCustomerInfoMapper corporateCustomerInfoMapper;

    @Mock
    private SequenceIdGen sequenceIdGen;

    @Mock
    private ManageAccountIdGen manageAccountIdGen;

    @Mock
    private ICardCustSpecialInfoService cardCustSpecialInfoService;

    /**
     * 创建有效的测试数据
     */
    private AccMaDTO createValidAccMaDTO() {
        AccMaDTO accMaDto = new AccMaDTO();
        accMaDto.setCustomerId("CUST001");
        accMaDto.setProductNumber("PROD001");
        accMaDto.setCurrency("CNY");
        accMaDto.setOrganizationNumber("ORG001"); // 必须设置机构号
        accMaDto.setAccountStatus("0"); // 必须设置账户状态：0-新户
        accMaDto.setServerType("1"); // 设置服务类型，避免走卡户人服务分支
        accMaDto.setAccountStatusSetDate(LocalDate.now());
        accMaDto.setOpenDate(LocalDate.now());
        return accMaDto;
    }

    private AccStaDTO createValidAccStaDTO() {
        AccStaDTO accStaDTO = new AccStaDTO();
        accStaDTO.setCustomerId("CUST001");
        accStaDTO.setOrganizationNumber("ORG001");
        accStaDTO.setCurrency("CNY");
        accStaDTO.setCreateDate(LocalDate.now());
        return accStaDTO;
    }

    /**
     * 测试方法：shouldCreateAccountAndStatistics_whenValidParametersProvided
     * 用来测试 AccountCommonServiceImpl 方法 createAccountAndStatisticInfo 正常流程
     * 验证当提供有效参数时，能够成功创建账户和统计信息
     */
    @Test
    void shouldCreateAccountAndStatistics_whenValidParametersProvided() {
        try (MockedStatic<OrgNumberUtils> orgUtilsMock = mockStatic(OrgNumberUtils.class);
             MockedStatic<BeanMapping> beanMappingMock = mockStatic(BeanMapping.class);
             MockedStatic<TenantUtils> tenantUtilsMock = mockStatic(TenantUtils.class);
             MockedStatic<PartitionKeyUtils> partitionMock = mockStatic(PartitionKeyUtils.class)) {
            
            // Mock静态工具类
            orgUtilsMock.when(OrgNumberUtils::getOrg).thenReturn("TEST_ORG");
            tenantUtilsMock.when(TenantUtils::getTenantId).thenReturn("TENANT001");
            partitionMock.when(() -> PartitionKeyUtils.partitionKey(anyString())).thenReturn(1L);
            
            // 准备测试数据
            AccMaDTO accMaDto = createValidAccMaDTO();
            AccStaDTO accStaDTO = createValidAccStaDTO();
            
            // Mock必要的依赖
            CardCustSpecialInfoDTO cardCustSpecialInfo = new CardCustSpecialInfoDTO();
            cardCustSpecialInfo.setCycleDay((short) 15);
            
            when(cardCustSpecialInfoService.selectByCustomerId(anyString()))
                .thenReturn(cardCustSpecialInfo);
            when(accountManagementInfoSelfMapper.selectByCusIdProNumAndOrgList(anyString(), anyString(), anyString()))
                .thenReturn(new ArrayList<>()); // 空列表，表示不存在历史账户
            when(manageAccountIdGen.generateId(anyString())).thenReturn(123456L);
            when(sequenceIdGen.generateId(anyString())).thenReturn("STAT123");
            when(accountManagementInfoMapper.insertSelective(any())).thenReturn(1);
            when(accountStatisticsInfoSelfMapper.batchInsertAcctStatis(anyList())).thenReturn(1);

            // 执行测试
            String result = accountCommonService.createAccountAndStatisticInfo(accMaDto, accStaDTO);

            // 验证结果
            assertEquals("0", result);
            verify(accountManagementInfoMapper).insertSelective(any());
            verify(accountStatisticsInfoSelfMapper).batchInsertAcctStatis(anyList());
        }
    }

    /**
     * 测试方法：shouldCreateAccountAndStatisticsWithCache_whenCacheEnabled
     * 用来测试 AccountCommonServiceImpl 方法 createAccountAndStatisticInfo 缓存模式
     * 验证缓存模式下的账户创建功能
     */
    @Test
    void shouldCreateAccountAndStatisticsWithCache_whenCacheEnabled() {
        try (MockedStatic<OrgNumberUtils> orgUtilsMock = mockStatic(OrgNumberUtils.class);
             MockedStatic<BeanMapping> beanMappingMock = mockStatic(BeanMapping.class);
             MockedStatic<TenantUtils> tenantUtilsMock = mockStatic(TenantUtils.class);
             MockedStatic<PartitionKeyUtils> partitionMock = mockStatic(PartitionKeyUtils.class)) {
            
            // Mock静态工具类
            orgUtilsMock.when(OrgNumberUtils::getOrg).thenReturn("TEST_ORG");
            tenantUtilsMock.when(TenantUtils::getTenantId).thenReturn("TENANT_001");
            partitionMock.when(() -> PartitionKeyUtils.partitionKey(anyString())).thenReturn(1L);
            
            // 创建并设置CustAccountBO到ThreadLocal（缓存模式必需）
            CustAccountBO custAccountBO = new CustAccountBO();
            custAccountBO.setCustomerId("C001"); // 使用更短的客户ID避免分区键太长
            custAccountBO.setIsBatch(false);
            CustAccountBO.threadCustAccountBO.set(custAccountBO);
            
            try {
                // 准备测试数据
                                                 AccMaDTO accMaDto = new AccMaDTO();
                accMaDto.setCustomerId("C001"); // 使用更短的客户ID避免分区键太长
                accMaDto.setProductNumber("P001");
                accMaDto.setCurrency("CNY");
                accMaDto.setAccountStatus("0"); // 新户状态
                
                AccStaDTO accStaDTO = new AccStaDTO();
                accStaDTO.setCustomerId("C001");
                accStaDTO.setCurrency("CNY");

                // Mock CardCustSpecialInfoService
                CardCustSpecialInfoDTO cardCustSpecialInfo = new CardCustSpecialInfoDTO();
                cardCustSpecialInfo.setCycleDay((short) 15);
                when(cardCustSpecialInfoService.selectByCustomerId("C001"))
                    .thenReturn(cardCustSpecialInfo);

                // Mock BeanMapping的copy方法
                AccountManagementInfoDTO mockAccountDTO = new AccountManagementInfoDTO();
                mockAccountDTO.setAccountManagementId("ACC001");
                mockAccountDTO.setCustomerId("C001");
                
                AccountStatisticsInfoDTO mockStatisticsDTO = new AccountStatisticsInfoDTO();
                mockStatisticsDTO.setStatisticsId("STAT001");
                mockStatisticsDTO.setAccountManagementId("ACC001");
                
                beanMappingMock.when(() -> BeanMapping.copy(any(AccountManagementInfo.class), eq(AccountManagementInfoDTO.class)))
                              .thenReturn(mockAccountDTO);
                beanMappingMock.when(() -> BeanMapping.copy(any(AccountStatisticsInfo.class), eq(AccountStatisticsInfoDTO.class)))
                              .thenReturn(mockStatisticsDTO);

                // 执行测试 - 使用缓存模式（cache=true）
                assertDoesNotThrow(() -> {
                    String result = accountCommonService.createAccountAndStatisticInfo(accMaDto, accStaDTO, true);
                    assertNotNull(result);
                    assertEquals("0", result);
                });
                
            } finally {
                // 清理ThreadLocal
                CustAccountBO.clearThreadLocal();
            }
        }
    }

    /**
     * 测试方法：shouldThrowException_whenCustomerIdIsEmpty
     * 用来测试 AccountCommonServiceImpl 方法 createAccountAndStatisticInfo 异常情况
     * 验证当客户ID为空时，应该抛出异常
     */
    @Test
    void shouldThrowException_whenCustomerIdIsEmpty() {
        try (MockedStatic<OrgNumberUtils> orgUtilsMock = mockStatic(OrgNumberUtils.class)) {
            // Mock静态工具类
            orgUtilsMock.when(OrgNumberUtils::getOrg).thenReturn("TEST_ORG");
            
            // 准备空客户ID的测试数据
            AccMaDTO accMaDto = createValidAccMaDTO();
            accMaDto.setCustomerId(""); // 空客户ID
            
            AccStaDTO accStaDTO = createValidAccStaDTO();
            accStaDTO.setCustomerId("");

            // 验证会抛出AnyTxnAddAccountException异常
            assertThrows(AnyTxnAddAccountException.class, () -> {
                accountCommonService.createAccountAndStatisticInfo(accMaDto, accStaDTO);
            });
        }
    }

    /**
     * 测试方法：shouldThrowException_whenAccountProductNumberIsEmpty
     * 用来测试 AccountCommonServiceImpl 方法 createAccountAndStatisticInfo 异常情况
     * 验证当账户产品号为空时，应该抛出异常
     */
    @Test
    void shouldThrowException_whenAccountProductNumberIsEmpty() {
        try (MockedStatic<OrgNumberUtils> orgUtilsMock = mockStatic(OrgNumberUtils.class)) {
            // Mock静态工具类
            orgUtilsMock.when(OrgNumberUtils::getOrg).thenReturn("TEST_ORG");
            
            // 准备空产品号的测试数据
            AccMaDTO accMaDto = createValidAccMaDTO();
            accMaDto.setProductNumber(""); // 空产品号
            
            AccStaDTO accStaDTO = createValidAccStaDTO();

            // 验证会抛出AnyTxnAddAccountException异常
            assertThrows(AnyTxnAddAccountException.class, () -> {
                accountCommonService.createAccountAndStatisticInfo(accMaDto, accStaDTO);
            });
        }
    }

    /**
     * 测试方法：shouldUpdateExistingAccount_whenAccountAlreadyExists
     * 用来测试 AccountCommonServiceImpl 方法 createAccountAndStatisticInfo 更新逻辑
     * 验证当账户已存在时，应该更新现有账户而不是创建新账户
     */
    @Test
    void shouldUpdateExistingAccount_whenAccountAlreadyExists() {
        try (MockedStatic<OrgNumberUtils> orgUtilsMock = mockStatic(OrgNumberUtils.class);
             MockedStatic<BeanMapping> beanMappingMock = mockStatic(BeanMapping.class);
             MockedStatic<TenantUtils> tenantUtilsMock = mockStatic(TenantUtils.class);
             MockedStatic<PartitionKeyUtils> partitionMock = mockStatic(PartitionKeyUtils.class)) {
            
            // Mock静态工具类
            orgUtilsMock.when(OrgNumberUtils::getOrg).thenReturn("TEST_ORG");
            tenantUtilsMock.when(TenantUtils::getTenantId).thenReturn("TENANT001");
            partitionMock.when(() -> PartitionKeyUtils.partitionKey(anyString())).thenReturn(1L);
            
            // 准备测试数据
            AccMaDTO accMaDto = createValidAccMaDTO();
            AccStaDTO accStaDTO = createValidAccStaDTO();
            
            // Mock现有账户
            AccountManagementInfo existingAccount = new AccountManagementInfo();
            existingAccount.setAccountManagementId("EXISTING123");
            existingAccount.setCurrency("CNY");
            existingAccount.setAccountStatus("8"); // 关闭状态，应该被更新为"1"
            
            List<AccountManagementInfo> existingAccounts = new ArrayList<>();
            existingAccounts.add(existingAccount);
            
            // Mock必要的依赖
            CardCustSpecialInfoDTO cardCustSpecialInfo = new CardCustSpecialInfoDTO();
            cardCustSpecialInfo.setCycleDay((short) 15);
            
            when(cardCustSpecialInfoService.selectByCustomerId(anyString()))
                .thenReturn(cardCustSpecialInfo);
            when(accountManagementInfoSelfMapper.selectByCusIdProNumAndOrgList(anyString(), anyString(), anyString()))
                .thenReturn(existingAccounts);
            when(accountManagementInfoMapper.updateByPrimaryKeySelective(any())).thenReturn(1);

            // 执行测试
            String result = accountCommonService.createAccountAndStatisticInfo(accMaDto, accStaDTO);

            // 验证结果
            assertEquals("1", result); // "1"表示更新成功
            verify(accountManagementInfoMapper).updateByPrimaryKeySelective(any());
            verify(accountManagementInfoMapper, never()).insertSelective(any()); // 不应该插入新记录
        }
    }
} 