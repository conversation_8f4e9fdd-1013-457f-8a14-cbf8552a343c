package com.anytech.anytxn.parameter.common.controller.deprecated;

import io.swagger.v3.oas.annotations.tags.Tag;
import io.swagger.v3.oas.annotations.Operation;
import com.anytech.anytxn.common.core.base.ParameterCompare;
import com.anytech.anytxn.common.core.base.PageResultDTO;
import com.anytech.anytxn.common.core.base.AnyTxnHttpResponse;
import com.anytech.anytxn.common.core.base.BizBaseController;
import com.anytech.anytxn.parameter.base.account.domain.dto.LimitOverpayComponentReqDTO;
import com.anytech.anytxn.parameter.base.account.domain.dto.LimitOverpayComponentResDTO;
import com.anytech.anytxn.parameter.base.account.service.ILimitOverpayComponentService;
import com.anytech.anytxn.parameter.base.common.enums.ParameterRepDetailEnum;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

/**
 * <AUTHOR>
 * @date 2020-11-30
 */
@Deprecated
@Tag(name = "溢缴款留用组件信息")
@RestController
public class LimitOverpayComponentController extends BizBaseController {

    @Autowired
    private ILimitOverpayComponentService limitOverpayComponentService;

    /**
     * 新增溢缴款留用组件参数
     * @param limitOverpayComponentReqDTO LimitOverpayComponentReqDTO
     * @return AnyTxnHttpResponse
     */
    @Operation(summary = "新增溢缴款留用组件参数",description = "新增溢缴款留用组件参数")
    @PostMapping(value = "/param/limitOverpayComponent")
    public AnyTxnHttpResponse create(@RequestBody LimitOverpayComponentReqDTO limitOverpayComponentReqDTO) {
        limitOverpayComponentService.addLimitOverpayComponent(limitOverpayComponentReqDTO);
        return AnyTxnHttpResponse.successDetail(ParameterRepDetailEnum.CREATE.message());
    }

    /**
     * 查询溢缴款留用组件参数信息
     * @return PageResultDTO<LimitOverpayComponentResDTO>
     */
    @Operation(summary = "溢缴款留用组件参数分页查询",description = "分页查询")
    @GetMapping(value = "/param/limitOverpayComponent/pageNum/{pageNum}/pageSize/{pageSize}")
    public AnyTxnHttpResponse<PageResultDTO<LimitOverpayComponentResDTO>> getAllPage(@PathVariable(value = "pageNum") Integer pageNum,
                                                                                     @PathVariable(value = "pageSize") Integer pageSize,
                                                                                     LimitOverpayComponentReqDTO limitOverpayComponentReqDTO) {
        PageResultDTO<LimitOverpayComponentResDTO> limitOverpayComponentPage = limitOverpayComponentService.findAll(pageNum,pageSize,limitOverpayComponentReqDTO);
        return AnyTxnHttpResponse.success(limitOverpayComponentPage);
    }

    /**
     * 修改溢缴款留用组件参数信息
     * @param limitOverpayComponentReq LimitOverpayComponentReqDTO
     * @return AnyTxnHttpResponse
     */
    @Operation(summary = "溢缴款留用组件参数信息修改", description = "只更新需要更新的,其他不变")
    @PutMapping(value = "/param/limitOverpayComponent")
    public AnyTxnHttpResponse modify(@RequestBody LimitOverpayComponentReqDTO limitOverpayComponentReq) {
        limitOverpayComponentService.modifyLimitOverpayComponent(limitOverpayComponentReq);
        return AnyTxnHttpResponse.successDetail(ParameterRepDetailEnum.UPDATE.message());
    }

    /**
     * 通过id删除溢缴款留用组件参数信息
     * @param id Long
     * @return Boolean
     */
    @Operation(summary = "删除溢缴款留用组件参数信息", description = "通过id删除溢缴款留用组件参数信息")
    @DeleteMapping(value = "/param/limitOverpayComponent/id/{id}")
    public AnyTxnHttpResponse<Boolean> remove(@PathVariable String id) {
        Boolean flag = limitOverpayComponentService.removeLimitOverpayComponent(id);
        return AnyTxnHttpResponse.success(flag,ParameterRepDetailEnum.DEL.message());
    }

    /**
     * 通过id查询溢缴款留用组件参数信息
     * @param id Long
     * @return limitOverpayComponentResDTO
     */
    @Operation(summary = "通过id查询溢缴款留用组件参数信息", description = "通过id查询溢缴款留用组件参数信息")
    @GetMapping(value = "/param/limitOverpayComponent/id/{id}")
    public AnyTxnHttpResponse<LimitOverpayComponentResDTO> getById(@PathVariable String id) {
        LimitOverpayComponentResDTO limitOverpayComponentResDTO = limitOverpayComponentService.findById(id);
        return AnyTxnHttpResponse.success(limitOverpayComponentResDTO);

    }


    /**
     * 通过tableId和orgNum删除溢缴款留用组件参数信息
     * @param tableId CHAR  orgNumber  CHAR
     * @return Boolean
     */
    @Operation(summary = "删除溢缴款留用组件参数信息", description = "通过tableId和orgNum删除溢缴款留用组件参数信息")
    @DeleteMapping(value = "/param/limitOverpayComponent/orgNumber/{orgNumber}/tableId/{tableId}")
    public AnyTxnHttpResponse<Object> removeTableIdAndOrgNum(@PathVariable String orgNumber,@PathVariable String tableId) {
        ParameterCompare flag = limitOverpayComponentService.removeLimitOverpayComponent(orgNumber, tableId);
        return AnyTxnHttpResponse.success(flag,ParameterRepDetailEnum.DEL.message());
    }

    /**
     * 通过tableId和orgNum查询溢缴款留用组件参数信息
     * @param tableId CHAR  orgNumber  CHAR
     * @return limitOverpayComponentResDTO
     */
    @Operation(summary = "通过tableId和orgNum查询溢缴款留用组件参数信息", description = "通过tableId和orgNum查询溢缴款留用组件参数信息")
    @GetMapping(value = "/param/limitOverpayComponent/orgNumber/{orgNumber}/tableId/{tableId}")
    public AnyTxnHttpResponse<LimitOverpayComponentResDTO> getByTableIdAndOrgNum(@PathVariable String orgNumber,@PathVariable String tableId) {
        LimitOverpayComponentResDTO limitOverpayComponentResDTO = limitOverpayComponentService.findByOrgAndTableId(orgNumber, tableId);
        return AnyTxnHttpResponse.success(limitOverpayComponentResDTO);
    }
}
