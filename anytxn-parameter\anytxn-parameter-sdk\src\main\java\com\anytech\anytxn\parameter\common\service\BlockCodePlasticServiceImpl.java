package com.anytech.anytxn.parameter.common.service;

import com.github.pagehelper.Page;
import com.github.pagehelper.PageHelper;
import com.anytech.anytxn.common.core.base.PageResultDTO;
import com.anytech.anytxn.common.core.utils.BeanMapping;
import com.anytech.anytxn.common.core.utils.OrgNumberUtils;
import com.anytech.anytxn.common.sequence.utils.Number16IdGen;
import com.anytech.anytxn.common.core.utils.TenantUtils;
import com.anytech.anytxn.parameter.base.common.domain.dto.BlockCodePlasticReqDTO;
import com.anytech.anytxn.parameter.base.common.domain.dto.BlockCodePlasticResDTO;
import com.anytech.anytxn.parameter.base.common.service.IBlockCodePlasticService;
import com.anytech.anytxn.parameter.base.common.constants.Constants;
import com.anytech.anytxn.parameter.base.common.enums.AnyTxnParameterRespCodeEnum;
import com.anytech.anytxn.parameter.base.common.exception.AnyTxnParameterException;
import com.anytech.anytxn.parameter.common.mapper.broadcast.ParmBlockCodePlasticMapper;
import com.anytech.anytxn.parameter.common.mapper.broadcast.ParmBlockCodePlasticSelfMapper;
import com.anytech.anytxn.parameter.base.account.domain.model.ParmBlockCodePlastic;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 介质层封锁码 业务接口
 * <AUTHOR>
 * @date 2018-08-16
 **/
@Service
public class BlockCodePlasticServiceImpl implements IBlockCodePlasticService {

    private Logger logger = LoggerFactory.getLogger(BlockCodePlasticServiceImpl.class);

    @Autowired
    private ParmBlockCodePlasticMapper blockCodePlasticMapper;
    @Autowired
    private ParmBlockCodePlasticSelfMapper blockCodePlasticSelfMapper;

    @Autowired
    private Number16IdGen numberIdGenerator;

    /**
     * 添加介质层封锁码
     *
     * @param req 介质层封锁码入参对象
     * @return 介质层封锁码详情
     * @throws AnyTxnParameterException
     */
    @Override
    public BlockCodePlasticResDTO add(BlockCodePlasticReqDTO req) {
        boolean isExists = blockCodePlasticSelfMapper.isExists(req.getOrganizationNumber(), req.getTableId(), req.getBlockCode())>0;
        if(isExists) {
            logger.warn("参数表ID和封锁码已存在, orgNumber={}, tableId={}, blockCode={}",
                    OrgNumberUtils.getOrg(), req.getTableId(), req.getBlockCode());
            throw new AnyTxnParameterException(AnyTxnParameterRespCodeEnum.P_EXIST_BLOCK_CODE_AND_ID_FAULT);
        }

        // 构建介质层封锁码
        ParmBlockCodePlastic blockCodePlastic = BeanMapping.copy(req, ParmBlockCodePlastic.class);
        blockCodePlastic.setCreateTime(LocalDateTime.now());
        blockCodePlastic.setUpdateTime(LocalDateTime.now());
        blockCodePlastic.setUpdateBy(Constants.DEFAULT_USER);
        blockCodePlastic.setVersionNumber(1);
        blockCodePlastic.setStatus(Constants.ENABLED);
        blockCodePlastic.setId(numberIdGenerator.generateId(TenantUtils.getTenantId()));
        blockCodePlasticMapper.insertSelective(blockCodePlastic);

        return BeanMapping.copy(blockCodePlastic, BlockCodePlasticResDTO.class);
    }

    /**
     * 修改介质层封锁码
     *
     * @param req 介质层封锁码入参对象
     * @return 介质层封锁码详情
     * @throws AnyTxnParameterException
     */
    @Override
    public BlockCodePlasticResDTO modify(BlockCodePlasticReqDTO req)  {
        if (req.getId() == null) {
            throw new AnyTxnParameterException(AnyTxnParameterRespCodeEnum.P_QUERY_BLOCK_CODE_PLASTIC_FAULT);
        }


        ParmBlockCodePlastic blockCodePlastic = blockCodePlasticMapper.selectByPrimaryKey(req.getId());
        if (blockCodePlastic == null) {
            logger.error("修改介质层封锁码, 通过主键id({})未找到数据", req.getId());
            throw new AnyTxnParameterException(AnyTxnParameterRespCodeEnum.D_MODIFY_BLOCK_CODE_PLASTIC_BY_ID_FAULT);
        }

        //如果交易码编码被修改，则验证新的交易码编码是否存在
        String oldTableId = blockCodePlastic.getTableId();
        String newTableId = req.getTableId();
        if(!oldTableId.equals(newTableId)) {
            boolean isExists = false;
            isExists = blockCodePlasticSelfMapper.isExists(blockCodePlastic.getOrganizationNumber(), req.getTableId(), req.getBlockCode())>0;
            if(isExists) {
                logger.warn("参数表ID和封锁码已存在, orgNumber={}, tableId={}, blockCode={}",
                        blockCodePlastic.getOrganizationNumber(), req.getTableId(), req.getBlockCode());
                throw new AnyTxnParameterException(AnyTxnParameterRespCodeEnum.P_EXIST_BLOCK_CODE_AND_ID_FAULT);
            }
        }

        // 拷贝修改的数据并更新
        BeanMapping.copy(req, blockCodePlastic);
        blockCodePlastic.setUpdateTime(LocalDateTime.now());
        blockCodePlastic.setUpdateBy(Constants.DEFAULT_USER);
        blockCodePlasticMapper.updateByPrimaryKeySelective(blockCodePlastic);

        //历史表中添加记录

        return BeanMapping.copy(blockCodePlastic, BlockCodePlasticResDTO.class);
    }

    /**
     * 删除介质层封锁码，通过id
     *
     * @param id 介质层封锁码ID
     * @return true:成功|false:失败
     * @throws AnyTxnParameterException
     */
    @Override
    public Boolean remove(Long id)  {
        if (id == null) {
            throw new AnyTxnParameterException(AnyTxnParameterRespCodeEnum.P_PARAMETER_EMPTY_FAULT);
        }

        ParmBlockCodePlastic blockCodePlastic = blockCodePlasticMapper.selectByPrimaryKey(id);
        if (blockCodePlastic == null) {
            logger.error("删除介质层封锁码, 通过主键id({})未找到数据", id);
            throw new AnyTxnParameterException(AnyTxnParameterRespCodeEnum.D_DELETE_BLOCK_CODE_PLASTIC_BY_ID_FAULT);
        }

        logger.warn("通过id删除介质层封锁码, {}", blockCodePlastic);
        int deleteRow = blockCodePlasticMapper.deleteByPrimaryKey(id);

        //历史表中添加记录

        return deleteRow > 0;
    }

    /**
     * 获取介质层封锁码详情，通过id
     *
     * @param id 介质层封锁码ID
     * @return 介质层封锁码详情
     * @throws AnyTxnParameterException
     */
    @Override
    public BlockCodePlasticResDTO find(Long id) {
        if (id == null) {
            throw new AnyTxnParameterException(AnyTxnParameterRespCodeEnum.P_PARAMETER_EMPTY_FAULT);
        }

        ParmBlockCodePlastic blockCodePlastic = blockCodePlasticMapper.selectByPrimaryKey(id);
        if (blockCodePlastic == null) {
            logger.error("获取介质层封锁码详情, 通过主键id({})未找到数据", id);
            throw new AnyTxnParameterException(AnyTxnParameterRespCodeEnum.D_GET_BLOCK_CODE_PLASTIC_BY_ID_FAULT);
        }

        return BeanMapping.copy(blockCodePlastic, BlockCodePlasticResDTO.class);
    }

    /**
     * 查询介质层封锁码集合，通过机构编号及启用状态
     *
     * @param orgNumber 机构编号
     * @param status    介质层封锁码启用状态，不传查询所有状态
     * @return 分页的介质层封锁码
     * @throws AnyTxnParameterException
     */
    @Override
    public List<BlockCodePlasticResDTO> findListByOrgNumber(String orgNumber, String status,String tableId) {
        List<ParmBlockCodePlastic> blockCodeList = blockCodePlasticSelfMapper.selectListByOrgNumber(orgNumber, Constants.ENABLED,false);

        List<ParmBlockCodePlastic> filterResult = blockCodeList.stream().filter(t -> Objects.nonNull(t.getBlockCode()))
                .collect(Collectors.toList())
                .stream()
                .collect(Collectors.collectingAndThen(Collectors.toCollection(() -> new TreeSet<>(
                        Comparator.comparing(ParmBlockCodePlastic::getBlockCode))), ArrayList::new));


        return BeanMapping.copyList(filterResult, BlockCodePlasticResDTO.class);
    }

    /**
     * 分页查询介质层封锁码，通过机构编号及启用状态
     *
     * @param pageNum   页号
     * @param pageSize  每页大小
     * @param orgNumber 机构编号
     * @param status    介质层封锁码启用状态，不传查询所有状态
     * @return 分页的介质层封锁码
     * @throws AnyTxnParameterException
     */
    @Override
    public PageResultDTO<BlockCodePlasticResDTO> findPageByOrgNumber(Integer pageNum, Integer pageSize,
                                                                     String orgNumber, String status) {
        logger.info("分页查询介质层封锁码, pageNum={}, pageSize={}, orgNumber={}, status={}",
                pageNum, pageSize, orgNumber, status);
        Page<ParmBlockCodePlastic> page = PageHelper.startPage(pageNum, pageSize);
        List<ParmBlockCodePlastic> dataList = blockCodePlasticSelfMapper.selectListByOrgNumber(orgNumber, status, false);
        List<BlockCodePlasticResDTO> listData = BeanMapping.copyList(dataList, BlockCodePlasticResDTO.class);
        return new PageResultDTO<>(pageNum, pageSize, page.getTotal(), page.getPages(), listData);
    }

    /**
     * 获取介质层封锁码，通过机构编号和参数表ID,和卡片封锁码
     * @param orgNumber 机构编号
     * @param tableId   参数表ID
     * @param blockCode 卡片封锁码
     * @return BlockCodePlasticRes
     * @throws AnyTxnParameterException
     */
    @Override
    public BlockCodePlasticResDTO findBlockCodePlastic(String orgNumber, String tableId, String blockCode) {
        ParmBlockCodePlastic blockCodePlastic = blockCodePlasticSelfMapper.selectByOrgNumberAndTableIdAndCode(orgNumber, tableId, blockCode);
        if (blockCodePlastic == null) {
            logger.error("获取介质层封锁码参数,通过orgNumber={}, tableId={}, blockCode={}未查到数据",
                    orgNumber, tableId, blockCode);
            return null;
        }
        return BeanMapping.copy(blockCodePlastic, BlockCodePlasticResDTO.class);
    }
}
