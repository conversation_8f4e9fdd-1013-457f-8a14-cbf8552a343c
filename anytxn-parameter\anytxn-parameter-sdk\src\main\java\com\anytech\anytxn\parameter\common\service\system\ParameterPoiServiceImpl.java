package com.anytech.anytxn.parameter.common.service.system;

import com.anytech.anytxn.business.base.audit.AbstractParameterService;
import com.anytech.anytxn.common.core.enums.AnyTxnCommonRespCodeEnum;
import com.anytech.anytxn.common.core.base.PageResultDTO;
import com.anytech.anytxn.common.core.enums.ParamFileTypeEnum;
import com.anytech.anytxn.common.core.exception.AnyTxnException;
import com.anytech.anytxn.business.base.common.domain.dto.ParamExportDTO;
import com.anytech.anytxn.business.base.common.domain.dto.ParamExportPageDTO;
import com.anytech.anytxn.business.base.common.domain.dto.ParamImportDTO;
import com.anytech.anytxn.business.base.common.domain.dto.ParamImportResultDTO;
import com.anytech.anytxn.common.core.utils.BeanMapping;
import com.anytech.anytxn.common.core.utils.OrgNumberUtils;
import com.anytech.anytxn.parameter.base.common.enums.AnyTxnParameterRespCodeEnum;
import com.anytech.anytxn.parameter.base.common.exception.AnyTxnParameterException;
import com.anytech.anytxn.parameter.base.common.service.system.IParameterPoiService;
import com.google.common.util.concurrent.ThreadFactoryBuilder;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.io.IOUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.InitializingBean;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.util.*;
import java.util.concurrent.ScheduledExecutorService;
import java.util.concurrent.ScheduledThreadPoolExecutor;
import java.util.concurrent.TimeUnit;
import java.util.zip.ZipEntry;
import java.util.zip.ZipOutputStream;

/**
 * @Author: sukang
 * @Date: 2021/8/27 10:49
 */
@Service
@Slf4j
public class ParameterPoiServiceImpl implements IParameterPoiService, InitializingBean {

    @Autowired
    private Map<String, AbstractParameterService> beanMaps;


    public static final Map<String,List<ParamImportDTO>> CACHE_BYTE = new HashMap<>(8);

    private ScheduledExecutorService executorService;

    @Override
    public PageResultDTO<ParamExportPageDTO> exportPages(ParamExportDTO paramExportDTO) {

        if (StringUtils.isBlank(paramExportDTO.getParamCodes())){
            throw new AnyTxnParameterException(AnyTxnParameterRespCodeEnum.P_PARAMETER_EMPTY_FAULT);
        }
        paramExportDTO.setOrganizationNumber(OrgNumberUtils.getOrg(paramExportDTO.getOrganizationNumber()));
        AbstractParameterService parameterService = getBeanName(paramExportDTO.getParamCodes().trim());

        return parameterService.exPortPages(paramExportDTO);
    }




    @Override
    public List<ParamImportDTO> prepareData(ParamExportDTO paramExportDTO) {

        if(StringUtils.isBlank(paramExportDTO.getTransactionId())){
            throw new AnyTxnParameterException(AnyTxnParameterRespCodeEnum.P_PARAMETER_EMPTY_FAULT);
        }

        if (ParamFileTypeEnum.getParamFileType(paramExportDTO.getFileType()) == null){
            throw new AnyTxnParameterException(AnyTxnParameterRespCodeEnum.PARAMETER_FILE_TYPE);
        }

        if (paramExportDTO.getParamCodeIds() == null){
            throw new AnyTxnParameterException(AnyTxnParameterRespCodeEnum.P_PARAMETER_EMPTY_FAULT);
        }

        paramExportDTO.setOrganizationNumber(OrgNumberUtils.getOrg(paramExportDTO.getOrganizationNumber()));

        List<ParamImportDTO> list = new ArrayList<>(5);

        paramExportDTO.getParamCodeIds().forEach((key, value) -> {


            ParamImportDTO paramImportDTO;
            AbstractParameterService abstractParameterService = getBeanName(key);
            paramExportDTO.setParamCodes(key);

            if (abstractParameterService == null) {
                paramImportDTO = new ParamImportDTO(key, paramExportDTO.getFileType());
                paramImportDTO.buildErrorInfo(AnyTxnCommonRespCodeEnum.RESPONSE_UNKNOWN, "param illegal");
            } else {
                paramImportDTO = Optional.ofNullable(abstractParameterService.exportData(paramExportDTO)).orElse(new ParamImportDTO());
                paramImportDTO.buildSuccessInfo();
                paramImportDTO.setParamsCode(key);
                paramImportDTO.setFileType(paramExportDTO.getFileType());
                buildCacheData(paramExportDTO.getTransactionId(), paramImportDTO);
            }
            list.add(paramImportDTO);
        });

        return list;
    }



    private void buildCacheData(String transactionId, ParamImportDTO paramImportDTO) {
        ParamImportDTO copy = BeanMapping.copy(paramImportDTO, ParamImportDTO.class);
        copy.setFileName(copy.getParamsCode());
        paramImportDTO.setBytes(null);

        List<ParamImportDTO> paramImportDTOList = Optional.ofNullable(CACHE_BYTE.get(transactionId)).orElse(new ArrayList<>(8));
        paramImportDTOList.add(copy);
        CACHE_BYTE.put(transactionId, paramImportDTOList);

        executorService.schedule(new ParamFileCacheClear(transactionId),5, TimeUnit.MINUTES);

    }






    @Override
    public byte[] getParamByteData(String transactionId) throws IOException {

        if (!CACHE_BYTE.containsKey(transactionId)){
            throw new AnyTxnParameterException(AnyTxnParameterRespCodeEnum.PARAMETER_RETURN_NULL);
        }

        List<ParamImportDTO> paramImportDtos = CACHE_BYTE.get(transactionId);

        ByteArrayOutputStream byteArrayOutputStream = new ByteArrayOutputStream();
        ZipOutputStream zipOutputStream = new ZipOutputStream(byteArrayOutputStream);

        paramImportDtos.forEach(e -> {
            try {
                ZipEntry zipEntry = new ZipEntry(e.getFileName().concat(".").concat(e.getFileType()));
                zipOutputStream.putNextEntry(zipEntry);
                IOUtils.write(e.getBytes(),zipOutputStream);
            } catch (IOException ex) {
                log.error("zip包下载文件异常",ex);
            }
        });

        zipOutputStream.close();
        byte[] bytes = byteArrayOutputStream.toByteArray();
        byteArrayOutputStream.close();
        return bytes;
    }

    @Override
    public List<ParamImportResultDTO> importParamFile(ArrayList<ParamImportResultDTO> list) {

        list.forEach(t -> {
            AbstractParameterService beanName = getBeanName(t.getParamsCode());
            if (beanName == null){
                t.buildErrorInfo(AnyTxnCommonRespCodeEnum.RESPONSE_UNKNOWN,"file name illegal");
            }else {
                try {
                    ParamImportResultDTO paramImportResultDTO = beanName.importData(t);
                    if (paramImportResultDTO == null){
                        t.buildErrorInfo(AnyTxnCommonRespCodeEnum.RESPONSE_UNKNOWN,"nonsupport");
                    }
                }catch (AnyTxnException e){
                    t.buildErrorInfo(e.getErrCode(),e.getErrMsg());
                }catch (Exception e){
                    log.error("解析数据异常",e);
                    t.buildErrorInfo(AnyTxnCommonRespCodeEnum.RESPONSE_UNKNOWN,"data exception");
                }
            }
        });
        //字节数组不返回给前端
        list.forEach(e -> e.setBytes(null));
        return list;
    }




    private AbstractParameterService getBeanName(String paramCode){
        return beanMaps.get(paramCode + "_serviceImpl");
    }


    @Override
    public void afterPropertiesSet(){
        ThreadFactoryBuilder factoryBuilder = new ThreadFactoryBuilder();
        factoryBuilder.setNameFormat("param-Scheduled-Executor");
        executorService = new ScheduledThreadPoolExecutor(5,factoryBuilder.build());
    }
}

@Slf4j
class ParamFileCacheClear implements  Runnable{

    private String transactionId;

    public ParamFileCacheClear(String transactionId) {
        this.transactionId = transactionId;
    }

    @Override
    public void run() {
        try {
            ParameterPoiServiceImpl.CACHE_BYTE.remove(transactionId);
            log.info("clean file data success{}",transactionId);
        }catch (Exception e){
            log.error("clean file data",e);
        }
    }
}
