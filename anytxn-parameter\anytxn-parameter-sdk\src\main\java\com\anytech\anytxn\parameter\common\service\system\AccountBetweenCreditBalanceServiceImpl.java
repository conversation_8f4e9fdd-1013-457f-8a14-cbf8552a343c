package com.anytech.anytxn.parameter.common.service.system;

import com.alibaba.fastjson.JSON;
import com.anytech.anytxn.parameter.common.mapper.broadcast.system.ParmBetweenAccountCreditBalanceOrderDetailMapper;
import com.anytech.anytxn.parameter.common.mapper.broadcast.system.ParmBetweenAccountCreditBalanceOrderMapper;
import com.anytech.anytxn.parameter.common.mapper.broadcast.system.ParmBetweenAccountCreditBalanceOrderSelfMapper;
import com.anytech.anytxn.parameter.common.mapper.broadcast.system.ParmBetweenAccountTypeOrderDetailMapper;
import com.github.pagehelper.Page;
import com.github.pagehelper.PageHelper;
import com.anytech.anytxn.common.core.annotation.db.DeleteParameterAnnotation;
import com.anytech.anytxn.common.core.annotation.db.InsertParameterAnnotation;
import com.anytech.anytxn.common.core.annotation.db.UpdateParameterAnnotation;
import com.anytech.anytxn.business.base.audit.AbstractParameterService;
import com.anytech.anytxn.common.core.base.ParameterCompare;
import com.anytech.anytxn.business.base.audit.ParmModificationRecord;
import com.anytech.anytxn.common.core.base.PageResultDTO;
import com.anytech.anytxn.common.core.utils.BeanMapping;
import com.anytech.anytxn.common.core.utils.OrgNumberUtils;
import com.anytech.anytxn.common.sequence.utils.Number16IdGen;
import com.anytech.anytxn.common.core.utils.TenantUtils;
import com.anytech.anytxn.parameter.base.common.enums.AnyTxnParameterRespCodeEnum;
import com.anytech.anytxn.parameter.base.common.exception.AnyTxnParameterException;
import com.anytech.anytxn.parameter.base.common.domain.model.system.ParmBetweenAccountCreditBalanceOrder;
import com.anytech.anytxn.parameter.base.common.domain.model.system.ParmBetweenAccountCreditBalanceOrderDetail;
import com.anytech.anytxn.parameter.base.common.domain.model.system.ParmBetweenAccountTypeOrderDetail;
import com.anytech.anytxn.parameter.base.common.domain.dto.AccountBetweenCreditBalanceDTO;
import com.anytech.anytxn.parameter.base.common.domain.dto.AccountBetweenCreditBalanceDetailDTO;
import com.anytech.anytxn.parameter.base.common.domain.dto.ParmBetweenAccountTypeOrderDetailDTO;
import com.anytech.anytxn.parameter.base.common.service.IAccountBetweenCreditBalanceService;
import org.apache.commons.lang3.ObjectUtils;
//import org.apache.shardingsphere.transaction.annotation.ShardingSphereTransactionType;
//import org.apache.shardingsphere.transaction.core.TransactionType;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import java.util.ArrayList;
import java.util.List;

/**
 * @Auther: lt
 * @Date: 2021/05/11/9:54
 * @Description:
 */
@Service(value = "parm_between_account_credit_balance_order_serviceImpl")
public class AccountBetweenCreditBalanceServiceImpl extends AbstractParameterService implements IAccountBetweenCreditBalanceService {
    private static final Logger log = LoggerFactory.getLogger(AccountBetweenCreditBalanceServiceImpl.class);

    @Autowired
    private ParmBetweenAccountCreditBalanceOrderDetailMapper parmBetweenAccountCreditBalanceOrderDetailMapper;
    @Autowired
    private ParmBetweenAccountCreditBalanceOrderMapper parmBetweenAccountCreditBalanceOrderMapper;
    @Autowired
    private ParmBetweenAccountTypeOrderDetailMapper parmBetweenAccountTypeOrderDetailMapper;
    @Autowired
    private Number16IdGen numberIdGenerator;
    @Autowired
    private ParmBetweenAccountCreditBalanceOrderSelfMapper parmBetweenAccountCreditBalanceOrderSelfMapper;

    @Override
    @Transactional(rollbackFor = Exception.class, propagation = Propagation.REQUIRED)
    //@ShardingSphereTransactionType(TransactionType.XA)
    protected boolean updateDb(ParmModificationRecord parmModificationRecord) throws Exception {
        AccountBetweenCreditBalanceDTO accountBetweenCreditBalanceDTO = JSON.parseObject(parmModificationRecord.getParmBody(), AccountBetweenCreditBalanceDTO.class);
        ParmBetweenAccountCreditBalanceOrder copy = BeanMapping.copy(accountBetweenCreditBalanceDTO, ParmBetweenAccountCreditBalanceOrder.class);
        List<ParmBetweenAccountCreditBalanceOrderDetail> parmBetweenAccountCreditBalanceOrderDetails = parmBetweenAccountCreditBalanceOrderDetailMapper.selectByOrganAndTableId(copy.getOrganizationNumber(), copy.getTableId());
        List<ParmBetweenAccountTypeOrderDetail> parmBetweenAccountTypeOrderDetails = parmBetweenAccountTypeOrderDetailMapper.selectByOrganAndTableId(copy.getOrganizationNumber(), copy.getTableId());
        if (!CollectionUtils.isEmpty(parmBetweenAccountCreditBalanceOrderDetails)){
            for (ParmBetweenAccountCreditBalanceOrderDetail parmBetweenAccountCreditBalanceOrderDetail:parmBetweenAccountCreditBalanceOrderDetails){
                parmBetweenAccountCreditBalanceOrderDetailMapper.deleteByPrimaryKey(parmBetweenAccountCreditBalanceOrderDetail.getId());
            }
        }
        if (!CollectionUtils.isEmpty(parmBetweenAccountTypeOrderDetails)) {
            for (ParmBetweenAccountTypeOrderDetail parmBetweenAccountTypeOrderDetail:parmBetweenAccountTypeOrderDetails){
                parmBetweenAccountTypeOrderDetailMapper.deleteByOrganAndTableId(parmBetweenAccountTypeOrderDetail.getOrganizationNumber(),parmBetweenAccountTypeOrderDetail.getBetweenAccountCreditBalanceOrderId());
                System.out.println(parmBetweenAccountTypeOrderDetailMapper.deleteByPrimaryKey(parmBetweenAccountTypeOrderDetail.getId())+"================");
            }
        }

        ParmBetweenAccountCreditBalanceOrder parmBetweenAccountCreditBalanceOrder = new ParmBetweenAccountCreditBalanceOrder();
        parmBetweenAccountCreditBalanceOrder.setId(accountBetweenCreditBalanceDTO.getId());
        parmBetweenAccountCreditBalanceOrder.setDescription(copy.getDescription());
        parmBetweenAccountCreditBalanceOrder.setStatus(accountBetweenCreditBalanceDTO.getStatus());
        parmBetweenAccountCreditBalanceOrder.initUpdateDateTime();

        parmBetweenAccountCreditBalanceOrderMapper.updateByPrimaryKeySelective(parmBetweenAccountCreditBalanceOrder);

        List<AccountBetweenCreditBalanceDetailDTO> accountBetweenCreditBalanceDetailDTOS = accountBetweenCreditBalanceDTO.getAccountBetweenCreditBalanceDetailDTOS();
        if (!CollectionUtils.isEmpty(accountBetweenCreditBalanceDetailDTOS)){
            for (AccountBetweenCreditBalanceDetailDTO accountBetweenCreditBalanceDetailDTO:accountBetweenCreditBalanceDetailDTOS){
                ParmBetweenAccountCreditBalanceOrderDetail parmBetweenAccountCreditBalanceOrderDetail = new ParmBetweenAccountCreditBalanceOrderDetail();
                parmBetweenAccountCreditBalanceOrderDetail.setId(numberIdGenerator.generateId(TenantUtils.getTenantId())+"");
                parmBetweenAccountCreditBalanceOrderDetail.setStatus(accountBetweenCreditBalanceDTO.getStatus());
                parmBetweenAccountCreditBalanceOrderDetail.setPriority(accountBetweenCreditBalanceDetailDTO.getPriority());
                parmBetweenAccountCreditBalanceOrderDetail.setBetweenAccountCreditBalanceOrderId(accountBetweenCreditBalanceDTO.getTableId());
                parmBetweenAccountCreditBalanceOrderDetail.setOrganizationNumber(accountBetweenCreditBalanceDTO.getOrganizationNumber());
                parmBetweenAccountCreditBalanceOrderDetail.setCreditTransactionTypeCode(accountBetweenCreditBalanceDetailDTO.getCreditTransactionTypeCode());
                parmBetweenAccountCreditBalanceOrderDetail.initCreateDateTime();
                parmBetweenAccountCreditBalanceOrderDetail.initUpdateDateTime();
                try {
                    parmBetweenAccountCreditBalanceOrderDetailMapper.insertSelective(parmBetweenAccountCreditBalanceOrderDetail);
                } catch (Exception e) {
                    log.error("调用[{}]更新账户间贷方余额使用顺序参数[{}]失败,错误信息[{}]", e);
                    throw new AnyTxnParameterException(AnyTxnParameterRespCodeEnum.D_DATABASE_UPDATE_ERROR);
                }
            }
        }

        List<ParmBetweenAccountTypeOrderDetailDTO> accountTypeOrderDetailDTOS = accountBetweenCreditBalanceDTO.getAccountTypeOrderDetailDTOS();
        if (!CollectionUtils.isEmpty(accountBetweenCreditBalanceDetailDTOS)) {
            for (ParmBetweenAccountTypeOrderDetailDTO accountTypeOrderDetailDTO:accountTypeOrderDetailDTOS){
                ParmBetweenAccountTypeOrderDetail parmBetweenAccountTypeOrderDetail = new ParmBetweenAccountTypeOrderDetail();
                parmBetweenAccountTypeOrderDetail.setId(numberIdGenerator.generateId(TenantUtils.getTenantId())+"");
                parmBetweenAccountTypeOrderDetail.setStatus(accountBetweenCreditBalanceDTO.getStatus());
                parmBetweenAccountTypeOrderDetail.setPriority(accountTypeOrderDetailDTO.getPriority());
                parmBetweenAccountTypeOrderDetail.setBetweenAccountCreditBalanceOrderId(accountBetweenCreditBalanceDTO.getTableId());
                parmBetweenAccountTypeOrderDetail.setOrganizationNumber(accountBetweenCreditBalanceDTO.getOrganizationNumber());
                parmBetweenAccountTypeOrderDetail.setCreditTransactionTypeCode(accountTypeOrderDetailDTO.getCreditTransactionTypeCode());
                parmBetweenAccountTypeOrderDetail.initCreateDateTime();
                parmBetweenAccountTypeOrderDetail.initUpdateDateTime();
                try {
                    parmBetweenAccountTypeOrderDetailMapper.insertSelective(parmBetweenAccountTypeOrderDetail);
                } catch (Exception e) {
                    log.error("调用[{}]更新账户间贷方余额使用顺序参数[{}]失败,错误信息[{}]", e);
                    throw new AnyTxnParameterException(AnyTxnParameterRespCodeEnum.D_DATABASE_UPDATE_ERROR);
                }
            }
        }
        return true;
    }

    @Override
    @Transactional(rollbackFor = Exception.class, propagation = Propagation.REQUIRED)
    //@ShardingSphereTransactionType(TransactionType.XA)
    protected boolean insertDb(ParmModificationRecord parmModificationRecord) throws Exception {
        AccountBetweenCreditBalanceDTO accountBetweenCreditBalanceDTO = JSON.parseObject(parmModificationRecord.getParmBody(), AccountBetweenCreditBalanceDTO.class);
        ParmBetweenAccountCreditBalanceOrder parmBetweenAccountCreditBalanceOrder = new ParmBetweenAccountCreditBalanceOrder();
        parmBetweenAccountCreditBalanceOrder.setTableId(accountBetweenCreditBalanceDTO.getTableId());
        parmBetweenAccountCreditBalanceOrder.setDescription(accountBetweenCreditBalanceDTO.getDescription());
        parmBetweenAccountCreditBalanceOrder.setStatus(accountBetweenCreditBalanceDTO.getStatus());
        parmBetweenAccountCreditBalanceOrder.setOrganizationNumber(accountBetweenCreditBalanceDTO.getOrganizationNumber());
        parmBetweenAccountCreditBalanceOrder.initCreateDateTime();
        parmBetweenAccountCreditBalanceOrder.initUpdateDateTime();
        parmBetweenAccountCreditBalanceOrder.setUpdateBy(parmModificationRecord.getApplicationBy());
        parmBetweenAccountCreditBalanceOrder.setId(accountBetweenCreditBalanceDTO.getId());
        try {
            parmBetweenAccountCreditBalanceOrderMapper.insertSelective(parmBetweenAccountCreditBalanceOrder);
        } catch (Exception e) {
            throw new AnyTxnParameterException(AnyTxnParameterRespCodeEnum.D_DATABASE_UPDATE_ERROR);
        }
        List<AccountBetweenCreditBalanceDetailDTO> accountBetweenCreditBalanceDetailDTOS = accountBetweenCreditBalanceDTO.getAccountBetweenCreditBalanceDetailDTOS();
        for (AccountBetweenCreditBalanceDetailDTO accountBetweenCreditBalanceDetailDTO:accountBetweenCreditBalanceDetailDTOS){
            ParmBetweenAccountCreditBalanceOrderDetail parmBetweenAccountCreditBalanceOrderDetail = new ParmBetweenAccountCreditBalanceOrderDetail();
            parmBetweenAccountCreditBalanceOrderDetail.setId(numberIdGenerator.generateId(TenantUtils.getTenantId())+"");
            parmBetweenAccountCreditBalanceOrderDetail.setBetweenAccountCreditBalanceOrderId(parmBetweenAccountCreditBalanceOrder.getTableId());
            parmBetweenAccountCreditBalanceOrderDetail.setStatus(parmBetweenAccountCreditBalanceOrder.getStatus());
            parmBetweenAccountCreditBalanceOrderDetail.setCreditTransactionTypeCode(accountBetweenCreditBalanceDetailDTO.getCreditTransactionTypeCode());
            parmBetweenAccountCreditBalanceOrderDetail.setOrganizationNumber(parmBetweenAccountCreditBalanceOrder.getOrganizationNumber());
            parmBetweenAccountCreditBalanceOrderDetail.setPriority(accountBetweenCreditBalanceDetailDTO.getPriority());
            parmBetweenAccountCreditBalanceOrderDetail.initCreateDateTime();
            parmBetweenAccountCreditBalanceOrderDetail.initUpdateDateTime();
            parmBetweenAccountCreditBalanceOrderDetail.setUpdateBy(parmModificationRecord.getApplicationBy());
            try {
                parmBetweenAccountCreditBalanceOrderDetailMapper.insertSelective(parmBetweenAccountCreditBalanceOrderDetail);
            } catch (Exception e) {
                throw new AnyTxnParameterException(AnyTxnParameterRespCodeEnum.D_DATABASE_UPDATE_ERROR);
            }
        }
        List<ParmBetweenAccountTypeOrderDetailDTO> accountTypeOrderDetailDTOS = accountBetweenCreditBalanceDTO.getAccountTypeOrderDetailDTOS();
        for (ParmBetweenAccountTypeOrderDetailDTO parmBetweenAccountTypeOrderDetailDTO:accountTypeOrderDetailDTOS){
            ParmBetweenAccountTypeOrderDetail parmBetweenAccountTypeOrderDetail = new ParmBetweenAccountTypeOrderDetail();
            parmBetweenAccountTypeOrderDetail.setId(numberIdGenerator.generateId(TenantUtils.getTenantId())+"");
            parmBetweenAccountTypeOrderDetail.setBetweenAccountCreditBalanceOrderId(parmBetweenAccountCreditBalanceOrder.getTableId());
            parmBetweenAccountTypeOrderDetail.setStatus(parmBetweenAccountCreditBalanceOrder.getStatus());
            parmBetweenAccountTypeOrderDetail.setCreditTransactionTypeCode(parmBetweenAccountTypeOrderDetailDTO.getCreditTransactionTypeCode());
            parmBetweenAccountTypeOrderDetail.setOrganizationNumber(parmBetweenAccountCreditBalanceOrder.getOrganizationNumber());
            parmBetweenAccountTypeOrderDetail.setPriority(parmBetweenAccountTypeOrderDetailDTO.getPriority());
            parmBetweenAccountTypeOrderDetail.initCreateDateTime();
            parmBetweenAccountTypeOrderDetail.initUpdateDateTime();
            parmBetweenAccountTypeOrderDetail.setUpdateBy(parmModificationRecord.getApplicationBy());
            try {
                parmBetweenAccountTypeOrderDetailMapper.insertSelective(parmBetweenAccountTypeOrderDetail);
            } catch (Exception e) {
                throw new AnyTxnParameterException(AnyTxnParameterRespCodeEnum.D_DATABASE_UPDATE_ERROR);
            }
        }
        return true;
    }

    @Override
    @Transactional(rollbackFor = Exception.class, propagation = Propagation.REQUIRED)
    //@ShardingSphereTransactionType(TransactionType.XA)
    protected boolean deleteDb(ParmModificationRecord parmModificationRecord) throws Exception {
        ParmBetweenAccountCreditBalanceOrder parmVelocitySetDefinition = JSON.parseObject(parmModificationRecord.getParmBody(), ParmBetweenAccountCreditBalanceOrder.class);
        int i = parmBetweenAccountCreditBalanceOrderMapper.deleteByPrimaryKey(parmVelocitySetDefinition.getId());
        if (i<1){
            log.warn("未能成功删除，id:{}", parmVelocitySetDefinition.getId());
            throw new AnyTxnParameterException(AnyTxnParameterRespCodeEnum.D_DELETE_VELOCITY_SET_DEFINITION_FAULT);
        }
        parmBetweenAccountCreditBalanceOrderDetailMapper.deleteByOrganAndTable(parmVelocitySetDefinition.getOrganizationNumber(),parmVelocitySetDefinition.getTableId());
        parmBetweenAccountTypeOrderDetailMapper.deleteByOrganAndTableId(parmVelocitySetDefinition.getOrganizationNumber(),parmVelocitySetDefinition.getTableId());
        return true;    }

    @Override
    public AccountBetweenCreditBalanceDTO findByOrgAndType(String orgNumber, String type) throws AnyTxnParameterException {
        return null;
    }

    @Override
    @InsertParameterAnnotation(tableName = "parm_between_account_credit_balance_order", tableDesc = "Use order parameter of credit balance between accounts", isJoinTable = true)
    public ParameterCompare add(AccountBetweenCreditBalanceDTO reqDTO) {
        if (ObjectUtils.isEmpty(reqDTO)) {
            log.info("传入参数不能为空");
            throw new AnyTxnParameterException(AnyTxnParameterRespCodeEnum.P_PARAMETER_EMPTY_FAULT);
        }
        List<AccountBetweenCreditBalanceDetailDTO> accountBetweenCreditBalanceDetailDTOS = reqDTO.getAccountBetweenCreditBalanceDetailDTOS();
        if (!CollectionUtils.isEmpty(accountBetweenCreditBalanceDetailDTOS)) {
            accountBetweenCreditBalanceDetailDTOS.stream().forEach(accountBetweenCreditBalanceDetailDTO -> {
                ParmBetweenAccountCreditBalanceOrderDetail parmBetweenAccountCreditBalanceOrderDetail = parmBetweenAccountCreditBalanceOrderDetailMapper.selectByCodeAndOrgan(accountBetweenCreditBalanceDetailDTO.getCreditTransactionTypeCode(), accountBetweenCreditBalanceDetailDTO.getOrganizationNumber());
                if (ObjectUtils.isNotEmpty(parmBetweenAccountCreditBalanceOrderDetail)) {
                    throw new AnyTxnParameterException(AnyTxnParameterRespCodeEnum.D_DATA_ALREADY_EXISTS_FAULT);
                }
                reqDTO.setId(numberIdGenerator.generateId(TenantUtils.getTenantId()) + "");
            });
        }
        List<ParmBetweenAccountTypeOrderDetailDTO> accountTypeOrderDetailDTOS = reqDTO.getAccountTypeOrderDetailDTOS();
        if (!CollectionUtils.isEmpty(accountTypeOrderDetailDTOS)) {
            accountTypeOrderDetailDTOS.stream().forEach(accountBetweenCreditBalanceDetailDTO -> {
                ParmBetweenAccountTypeOrderDetail parmBetweenAccountTypeOrderDetail = parmBetweenAccountTypeOrderDetailMapper.selectByCodeAndOrgan(accountBetweenCreditBalanceDetailDTO.getCreditTransactionTypeCode(), accountBetweenCreditBalanceDetailDTO.getOrganizationNumber());

                if (ObjectUtils.isNotEmpty(parmBetweenAccountTypeOrderDetail)) {
                    throw new AnyTxnParameterException(AnyTxnParameterRespCodeEnum.D_DATA_ALREADY_EXISTS_FAULT);
                }
                reqDTO.setId(numberIdGenerator.generateId(TenantUtils.getTenantId()) + "");
            });
        }
        return ParameterCompare.getBuilder()
                .withMainParmId(reqDTO.getTableId())
                .withAfter(reqDTO).build(AccountBetweenCreditBalanceDTO.class);
    }

    @Override
    @UpdateParameterAnnotation(tableName = "parm_between_account_credit_balance_order", tableDesc = "Use order parameter of credit balance between accounts", isJoinTable = true)
    public ParameterCompare modify(AccountBetweenCreditBalanceDTO reqDTO) {
        if (ObjectUtils.isEmpty(reqDTO)) {
            log.error("传入参数不能为空");
            throw new AnyTxnParameterException(AnyTxnParameterRespCodeEnum.P_PARAMETER_EMPTY_FAULT);
        }
        ParmBetweenAccountCreditBalanceOrder parmBetweenAccountCreditBalanceOrder = parmBetweenAccountCreditBalanceOrderMapper.selectByOrganAndTableId(reqDTO.getOrganizationNumber(), reqDTO.getTableId());
        if (ObjectUtils.isEmpty(parmBetweenAccountCreditBalanceOrder)) {
            log.error("根据机构号和参数表ID查询账户间贷方余额使用顺序参数 数据不存在");
            throw new AnyTxnParameterException(AnyTxnParameterRespCodeEnum.D_DATA_NOT_EXISTS_FAULT);
        }
        reqDTO.setId(reqDTO.getId());

        return ParameterCompare.getBuilder()
                .withMainParmId(reqDTO.getTableId())
                .withAfter(reqDTO)
                .withBefore(new AccountBetweenCreditBalanceDTO())
                .build(AccountBetweenCreditBalanceDTO.class);
    }

    @Override
    public AccountBetweenCreditBalanceDTO findById(String id) {
        if (null == id) {
            log.error("传入参数不能为空");
            throw new AnyTxnParameterException(AnyTxnParameterRespCodeEnum.P_PARAMETER_EMPTY_FAULT);
        }
        AccountBetweenCreditBalanceDTO accountBetweenCreditBalanceDTO = new AccountBetweenCreditBalanceDTO();
        ParmBetweenAccountCreditBalanceOrder parmBetweenAccountCreditBalanceOrder = parmBetweenAccountCreditBalanceOrderMapper.selectByPrimaryKey(id);
        if (parmBetweenAccountCreditBalanceOrder == null) {
            log.error("获取账户间贷方余额使用顺序参数详情, 通过主键id({})未找到数据", id);
            throw new AnyTxnParameterException(AnyTxnParameterRespCodeEnum.D_DATA_NOT_EXISTS_FAULT);
        }
        BeanMapping.copy(parmBetweenAccountCreditBalanceOrder, accountBetweenCreditBalanceDTO);
        List<AccountBetweenCreditBalanceDetailDTO> accountBetweenCreditBalanceDetailDTOS = new ArrayList<>();
        List<ParmBetweenAccountCreditBalanceOrderDetail> parmBetweenAccountCreditBalanceOrderDetail = parmBetweenAccountCreditBalanceOrderDetailMapper.selectByOrganAndTableId(parmBetweenAccountCreditBalanceOrder.getOrganizationNumber(), parmBetweenAccountCreditBalanceOrder.getTableId());

        if (!parmBetweenAccountCreditBalanceOrderDetail.isEmpty()) {
            accountBetweenCreditBalanceDetailDTOS = BeanMapping.copyList(parmBetweenAccountCreditBalanceOrderDetail, AccountBetweenCreditBalanceDetailDTO.class);
        }
        List<ParmBetweenAccountTypeOrderDetailDTO> parmBetweenAccountTypeOrderDetailDTOS = new ArrayList<>();
        List<ParmBetweenAccountTypeOrderDetail> parmBetweenAccountTypeOrderDetails = parmBetweenAccountTypeOrderDetailMapper.selectByOrganAndTableId(parmBetweenAccountCreditBalanceOrder.getOrganizationNumber(), parmBetweenAccountCreditBalanceOrder.getTableId());
        if (!parmBetweenAccountTypeOrderDetails.isEmpty()) {
            parmBetweenAccountTypeOrderDetailDTOS = BeanMapping.copyList(parmBetweenAccountTypeOrderDetails, ParmBetweenAccountTypeOrderDetailDTO.class);
        }
        accountBetweenCreditBalanceDTO.setAccountBetweenCreditBalanceDetailDTOS(accountBetweenCreditBalanceDetailDTOS);
        accountBetweenCreditBalanceDTO.setAccountTypeOrderDetailDTOS(parmBetweenAccountTypeOrderDetailDTOS);
        return accountBetweenCreditBalanceDTO;
    }

    /**
     * 批量账户间贷方余额分配用，根据机构号查询唯一的顺序参数
     * @param org
     * @return
     */
    @Override
    public AccountBetweenCreditBalanceDTO findOnlyOneByOrg(String org) {
        AccountBetweenCreditBalanceDTO accountBetweenCreditBalanceDTO = new AccountBetweenCreditBalanceDTO();
        ParmBetweenAccountCreditBalanceOrder parmBetweenAccountCreditBalanceOrder = parmBetweenAccountCreditBalanceOrderSelfMapper.selectByOrg(org);
        if (parmBetweenAccountCreditBalanceOrder == null) {
            log.error("获取账户间贷方余额使用顺序参数详情, 通过机构号({})未找到数据", org);
            throw new AnyTxnParameterException(AnyTxnParameterRespCodeEnum.D_DATA_NOT_EXISTS_FAULT);
        }
        searechDetailInfo(parmBetweenAccountCreditBalanceOrder,accountBetweenCreditBalanceDTO);
        return accountBetweenCreditBalanceDTO;
    }
    private void searechDetailInfo(ParmBetweenAccountCreditBalanceOrder parmBetweenAccountCreditBalanceOrder,AccountBetweenCreditBalanceDTO accountBetweenCreditBalanceDTO){
        BeanMapping.copy(parmBetweenAccountCreditBalanceOrder, accountBetweenCreditBalanceDTO);
        List<AccountBetweenCreditBalanceDetailDTO> accountBetweenCreditBalanceDetailDTOS = new ArrayList<>();
        List<ParmBetweenAccountCreditBalanceOrderDetail> parmBetweenAccountCreditBalanceOrderDetail = parmBetweenAccountCreditBalanceOrderDetailMapper.selectByOrganAndTableId(parmBetweenAccountCreditBalanceOrder.getOrganizationNumber(), parmBetweenAccountCreditBalanceOrder.getTableId());

        if (!parmBetweenAccountCreditBalanceOrderDetail.isEmpty()) {
            accountBetweenCreditBalanceDetailDTOS = BeanMapping.copyList(parmBetweenAccountCreditBalanceOrderDetail, AccountBetweenCreditBalanceDetailDTO.class);
        }
        List<ParmBetweenAccountTypeOrderDetailDTO> parmBetweenAccountTypeOrderDetailDTOS = new ArrayList<>();
        List<ParmBetweenAccountTypeOrderDetail> parmBetweenAccountTypeOrderDetails = parmBetweenAccountTypeOrderDetailMapper.selectByOrganAndTableId(parmBetweenAccountCreditBalanceOrder.getOrganizationNumber(), parmBetweenAccountCreditBalanceOrder.getTableId());
        if (!parmBetweenAccountTypeOrderDetails.isEmpty()) {
            parmBetweenAccountTypeOrderDetailDTOS = BeanMapping.copyList(parmBetweenAccountTypeOrderDetails, ParmBetweenAccountTypeOrderDetailDTO.class);
        }
        accountBetweenCreditBalanceDTO.setAccountBetweenCreditBalanceDetailDTOS(accountBetweenCreditBalanceDetailDTOS);
        accountBetweenCreditBalanceDTO.setAccountTypeOrderDetailDTOS(parmBetweenAccountTypeOrderDetailDTOS);
    }

    @Override
    @DeleteParameterAnnotation(tableName = "parm_between_account_credit_balance_order", tableDesc = "Use order parameter of credit balance between accounts", isJoinTable = true)
    public ParameterCompare remove(String id) {
        if (null == id) {
            log.error("传入参数不能为空");
            throw new AnyTxnParameterException(AnyTxnParameterRespCodeEnum.P_PARAMETER_EMPTY_FAULT);
        }
        ParmBetweenAccountCreditBalanceOrder parmBetweenAccountCreditBalanceOrder = parmBetweenAccountCreditBalanceOrderMapper.selectByPrimaryKey(id);
        if (null == parmBetweenAccountCreditBalanceOrder) {
            log.error("通过ID未查到数据");
            throw new AnyTxnParameterException(AnyTxnParameterRespCodeEnum.D_DATA_NOT_EXISTS_FAULT);
        }
        return ParameterCompare.getBuilder()
                .withMainParmId(parmBetweenAccountCreditBalanceOrder.getTableId())
                .withBefore(parmBetweenAccountCreditBalanceOrder).build(ParmBetweenAccountCreditBalanceOrder.class);
    }

    @Override
    public PageResultDTO<AccountBetweenCreditBalanceDTO> findByPage(Integer pageNum, Integer pageSize, AccountBetweenCreditBalanceDTO reqDTO) {
        if (null == reqDTO) {
            reqDTO = new AccountBetweenCreditBalanceDTO();
        }
        List<AccountBetweenCreditBalanceDTO> accountBetweenCreditBalanceDTOS= new ArrayList<>();
        reqDTO.setOrganizationNumber(StringUtils.isEmpty(reqDTO.getOrganizationNumber()) ? OrgNumberUtils.getOrg() : reqDTO.getOrganizationNumber());
        Page<ParmBetweenAccountCreditBalanceOrder> page = PageHelper.startPage(pageNum, pageSize);
        List<ParmBetweenAccountCreditBalanceOrder> authCheckDefinitionList = parmBetweenAccountCreditBalanceOrderMapper.selectByCondition(reqDTO);

        if (!CollectionUtils.isEmpty(authCheckDefinitionList)){
            for (ParmBetweenAccountCreditBalanceOrder parmBetweenAccountCreditBalanceOrder:authCheckDefinitionList){
                AccountBetweenCreditBalanceDTO copy = BeanMapping.copy(parmBetweenAccountCreditBalanceOrder, AccountBetweenCreditBalanceDTO.class);
                accountBetweenCreditBalanceDTOS.add(copy);
            }
        }
//        List<AccountBetweenCreditBalanceDTO> listData = BeanMapping.copyList(authCheckDefinitionList, AccountBetweenCreditBalanceDTO.class);

        return new PageResultDTO<>(pageNum, pageSize, page.getTotal(), page.getPages(), accountBetweenCreditBalanceDTOS);
    }
}
