package com.anytech.anytxn.parameter.authorization.controller;

import com.anytech.anytxn.parameter.base.authorization.domain.dto.MccGroupDefinitionDTO;
import com.anytech.anytxn.parameter.base.authorization.service.IMccGroupDefinitionService;
import com.anytech.anytxn.parameter.base.common.enums.ParameterRepDetailEnum;
import com.anytech.anytxn.common.core.base.PageResultDTO;
import com.anytech.anytxn.common.core.base.AnyTxnHttpResponse;
import com.anytech.anytxn.common.core.base.BizBaseController;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.Valid;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.Map;

/**
 * @description 商户类别群api
 * <AUTHOR>
 * @date 2019/4/4
 */

@RestController
@Tag(name = "商户类别群服务")
public class MccGroupDefinitionController extends BizBaseController {

    @Autowired
    private IMccGroupDefinitionService mccGroupDefinitionService;

    /**
     * @description 分页查询商户类别群列表
     * <AUTHOR>
     * @date 2019/4/4
     * @param page, rows 
     * @return jrx.anytxn.biz.common.web.AnyTxnHttpResponse<jrx.anytxn.biz.common.dto.PageResultDTO<jrx.anytxn.auth.dto.MccGroupDefinitionDTO>>
     */
    @Operation(summary = "分页查询商户类别群信息")
    @GetMapping("/param/mccGroupDefinitions/{page}/{rows}")
    public AnyTxnHttpResponse<PageResultDTO<MccGroupDefinitionDTO>> getMccGroupDefinitionList(
            @Parameter(name = "page", description = "当前页", example = "1") @PathVariable("page") int page,
            @Parameter(name = "rows", description = "每页大小", example = "8") @PathVariable("rows") int rows,
                                                                                              @RequestParam(value = "mccGroup",required = false) String mccGroup,
                                                                                              @RequestParam(value = "description",required = false) String description,
                                                                                              @RequestParam(required = false) String organizationNumber) {
        PageResultDTO<MccGroupDefinitionDTO> result = mccGroupDefinitionService.findListMccGroupDefinition(page, rows,mccGroup,description, organizationNumber);
        return AnyTxnHttpResponse.success(result);
    }


    @Operation(summary = "获取所有商户类别群")
    @GetMapping("/param/mccGroupDefinitions/mccGroups")

    public AnyTxnHttpResponse<List<Map<String,String>>> getMccGroups() {
        List<Map<String,String>> mccGroups = mccGroupDefinitionService.getMccGroups();
        return AnyTxnHttpResponse.success(mccGroups);
    }

    
    /**
     * @description 根据主键查询商户类别群信息
     * <AUTHOR>
     * @date 2019/4/4
     * @param id 
     * @return jrx.anytxn.biz.common.web.AnyTxnHttpResponse<jrx.anytxn.auth.dto.MccGroupDefinitionDTO>
     */
    
    @Operation(summary = "根据主键查询商户类别群信息")
    @GetMapping("/param/mccGroupDefinition/{id}")
    @Parameter(name = "id", description = "技术主键")
    public AnyTxnHttpResponse<MccGroupDefinitionDTO> getMccGroupDefinition(@PathVariable Long id) {
        MccGroupDefinitionDTO mccGroupDefinitionDTO = mccGroupDefinitionService.findMccGroupDefinition(id);
        return AnyTxnHttpResponse.success(mccGroupDefinitionDTO);
    }

    /**
     * @description 根据主键查询商户类别群信息
     * <AUTHOR>
     * @date 2019/4/4
     * @param id
     * @return jrx.anytxn.biz.common.web.AnyTxnHttpResponse<jrx.anytxn.auth.dto.MccGroupDefinitionDTO>
     */

    @Operation(summary = "根据主键查询商户类别群信息包含详情")
    @GetMapping("/param/mccGroupDefinitionAndDetail/{id}")
    @Parameter(name = "id", description = "技术主键")
    public AnyTxnHttpResponse<MccGroupDefinitionDTO> getMccGroupDefinitionAndDetail(@PathVariable Long id) {
        MccGroupDefinitionDTO mccGroupDefinitionAndDetil = mccGroupDefinitionService.findMccGroupDefinitionAndDetil(id);
        return AnyTxnHttpResponse.success(mccGroupDefinitionAndDetil);
    }


    /**
     * @description 删除商户类别群ƒ信息
     * <AUTHOR>
     * @date 2019/4/4
     * @param id 
     * @return jrx.anytxn.biz.common.web.AnyTxnHttpResponse
     */
    
    @Operation(summary = "删除商户类别群信息")
    @DeleteMapping("/param/mccGroupDefinition/{id}")
    public AnyTxnHttpResponse cancelMccGroupDefinition(@PathVariable Long id) {
        Boolean flag = mccGroupDefinitionService.removeMccGroupDefinitionAndDtails(id);
        return AnyTxnHttpResponse.success(flag, ParameterRepDetailEnum.DEL.message());
    }

    /**
     * @description 新增商户类别群信息
     * <AUTHOR>
     * @date 2019/4/4
     * @param mccGroupDefinitionDTO
     * @return jrx.anytxn.biz.common.web.AnyTxnHttpResponse
     */
    
    @Operation(summary = "新增商户类别群信息")
    @PostMapping("/param/mccGroupDefinition")
    public AnyTxnHttpResponse create(@Valid @RequestBody MccGroupDefinitionDTO mccGroupDefinitionDTO) {
        Boolean flag = mccGroupDefinitionService.addMccGroupDefinition(mccGroupDefinitionDTO);
        return AnyTxnHttpResponse.success(flag,ParameterRepDetailEnum.CREATE.message());
    }

    /**
     * @description 商户类别群信息修改
     * <AUTHOR>
     * @date 2019/4/4
     * @param mccGroupDefinitionDTO
     * @return jrx.anytxn.biz.common.web.AnyTxnHttpResponse
     */
    
    @Operation(summary = "商户类别群信息修改")
    @PutMapping("/param/mccGroupDefinition")
    public AnyTxnHttpResponse modify(@Valid @RequestBody MccGroupDefinitionDTO mccGroupDefinitionDTO) {
        Boolean flag = mccGroupDefinitionService.modifyMccGroupDefinition(mccGroupDefinitionDTO);
        return AnyTxnHttpResponse.success(flag,ParameterRepDetailEnum.UPDATE.message());
    }



    /**
     * @description 新增商户类别群信息以及详细内容
     * <AUTHOR>
     * @date 2019/4/4
     * @param mccGroupDefinitionDTO
     * @return jrx.anytxn.biz.common.web.AnyTxnHttpResponse
     */

    @Operation(summary = "新增商户类别群信息以及详细内容")
    @PostMapping("/param/mccGroupDefinitionAndDetail")
    public AnyTxnHttpResponse createV2(@Valid @RequestBody MccGroupDefinitionDTO mccGroupDefinitionDTO) {
        Boolean flag = mccGroupDefinitionService.addMccGroupDefinitionAndDtails(mccGroupDefinitionDTO);
        return AnyTxnHttpResponse.success(flag,ParameterRepDetailEnum.CREATE.message());
    }

    /**
     * @description 商户类别群信息修改以及详细内容
     * <AUTHOR>
     * @date 2019/4/4
     * @param mccGroupDefinitionDTO
     * @return jrx.anytxn.biz.common.web.AnyTxnHttpResponse
     */

    @Operation(summary = "商户类别群信息修改以及详细内容")
    @PutMapping("/param/mccGroupDefinitionAndDetail")
    public AnyTxnHttpResponse modifyV2(@Valid @RequestBody MccGroupDefinitionDTO mccGroupDefinitionDTO) {
        Boolean flag = mccGroupDefinitionService.modifyMccGroupDefinitionAndUtils(mccGroupDefinitionDTO);
        return AnyTxnHttpResponse.success(flag,ParameterRepDetailEnum.UPDATE.message());
    }

    /**
     * @description 删除商户类别群ƒ信息以及详情
     * <AUTHOR>
     * @date 2019/4/15
     * @param id
     * @return jrx.anytxn.biz.common.web.AnyTxnHttpResponse
     */

    @Operation(summary = "删除商户类别群信息以及详情")
    @DeleteMapping("/param/mccGroupDefinitionAndDtail/{id}")
    public AnyTxnHttpResponse cancelMccGroupDefinitionAndDtail(@PathVariable Long id) {
        Boolean flag = mccGroupDefinitionService.removeMccGroupDefinitionAndDtails(id);
        return AnyTxnHttpResponse.success(flag,ParameterRepDetailEnum.DEL.message());
    }

}
