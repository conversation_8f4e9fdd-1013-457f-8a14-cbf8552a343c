package com.anytech.anytxn.parameter.account.service;


import com.alibaba.fastjson.JSON;
import com.anytech.anytxn.common.core.annotation.db.DeleteParameterAnnotation;
import com.anytech.anytxn.common.core.annotation.db.InsertParameterAnnotation;
import com.anytech.anytxn.common.core.annotation.db.UpdateParameterAnnotation;
import com.anytech.anytxn.business.base.audit.AbstractParameterService;
import com.anytech.anytxn.common.core.base.ParameterCompare;
import com.anytech.anytxn.business.base.audit.ParmModificationRecord;
import com.anytech.anytxn.common.core.base.PageResultDTO;
import com.anytech.anytxn.common.core.utils.BeanMapping;
import com.anytech.anytxn.common.core.utils.OrgNumberUtils;
import com.anytech.anytxn.common.sequence.utils.Number16IdGen;
import com.anytech.anytxn.common.core.utils.TenantUtils;
import com.anytech.anytxn.parameter.base.account.domain.dto.ChequeBackDTO;
import com.anytech.anytxn.parameter.base.account.domain.dto.ChequeBackResDTO;
import com.anytech.anytxn.parameter.base.account.service.IParmChequeBackService;
import com.anytech.anytxn.parameter.base.common.enums.AnyTxnParameterRespCodeEnum;
import com.anytech.anytxn.parameter.base.common.exception.AnyTxnParameterException;
import com.anytech.anytxn.parameter.account.mapper.ParmChequeBackMapper;
import com.anytech.anytxn.parameter.account.mapper.ParmChequeBackSelfMapper;
import com.anytech.anytxn.parameter.base.account.domain.model.ParmChequeBack;
import com.github.pagehelper.Page;
import com.github.pagehelper.PageHelper;
//import org.apache.shardingsphere.transaction.annotation.ShardingSphereTransactionType;
//import org.apache.shardingsphere.transaction.core.TransactionType;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.StringUtils;

import java.math.BigDecimal;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * Description   退票费用实现类
 * Copyright:	Copyright (c) 2021
 * Company:		江融信
 * Author:		liurui
 * Version:		1.0
 * Create at:	2021/10/23 2:42 PM
 *
 * 修改历史:
 * 日期    作者    版本  修改描述
 * ------------------------------------------------------------------
 *
 **/
@Service(value = "parm_refund_transaction_fee_serviceImpl")
public class ChequeBackServiceImpl extends AbstractParameterService implements IParmChequeBackService {
    private final Logger logger = LoggerFactory.getLogger(ChequeBackServiceImpl.class);

    @Autowired
    private ParmChequeBackMapper parmChequeBackMapper;
    @Autowired
    private ParmChequeBackSelfMapper parmChequeBackSelfMapper;
    @Autowired
    private Number16IdGen numberIdGenerator;
    @Override
    @Transactional(rollbackFor = Exception.class,propagation = Propagation.REQUIRED)
    //@ShardingSphereTransactionType(TransactionType.XA)
    protected boolean updateDb(ParmModificationRecord parmModificationRecord) throws Exception {
        ParmChequeBack parmChequeBack = JSON.parseObject(parmModificationRecord.getParmBody(), ParmChequeBack.class);
        parmChequeBack.initUpdateDateTime();
        int i = parmChequeBackMapper.updateByPrimaryKeySelective(parmChequeBack);
        return i > 0;
    }

    @Override
    @Transactional(rollbackFor = Exception.class,propagation = Propagation.REQUIRED)
    //@ShardingSphereTransactionType(TransactionType.XA)
    protected boolean insertDb(ParmModificationRecord parmModificationRecord) throws Exception {
        ParmChequeBack parmChequeBack = JSON.parseObject(parmModificationRecord.getParmBody(), ParmChequeBack.class);
        parmChequeBack.initCreateDateTime();
        parmChequeBack.initUpdateDateTime();
        parmChequeBack.setUpdateBy(parmModificationRecord.getApplicationBy());
        int i = parmChequeBackMapper.insertSelective(parmChequeBack);
        return i > 0;
    }

    @Override
    @Transactional(rollbackFor = Exception.class,propagation = Propagation.REQUIRED)
    //@ShardingSphereTransactionType(TransactionType.XA)
    protected boolean deleteDb(ParmModificationRecord parmModificationRecord) throws Exception {
        ParmChequeBack parmChequeBack = JSON.parseObject(parmModificationRecord.getParmBody(), ParmChequeBack.class);
        int i = parmChequeBackMapper.deleteByPrimaryKey(parmChequeBack.getId());
        return i > 0;
    }

    @Override
    public ChequeBackResDTO findByTableIdAndOrgNo(String tableId, String organizationNumber) {
        if (tableId == null || StringUtils.isEmpty(organizationNumber)) {
            throw new AnyTxnParameterException(AnyTxnParameterRespCodeEnum.P_PARAMETER_EMPTY_FAULT);
        }
        ParmChequeBack parmChequeBack = parmChequeBackSelfMapper.queryByChequeBackTableIdAndOrgNo(tableId,organizationNumber );
        if(parmChequeBack==null){
            throw new AnyTxnParameterException(AnyTxnParameterRespCodeEnum.D_QUERY_PARA_AUTO_PAYMENT_TABLE_FAULT);

        }
        return BeanMapping.copy(parmChequeBack, ChequeBackResDTO.class);
    }

    @Override
    @InsertParameterAnnotation(tableName = "parm_refund_transaction_fee", tableDesc = "Refund Transaction Fee")
    public ParameterCompare add(ChequeBackDTO chequeBackDTO) {
        ParmChequeBack parmChequeBack = parmChequeBackSelfMapper.queryByChequeBackTableIdAndOrgNo(chequeBackDTO.getTableId(), OrgNumberUtils.getOrg(chequeBackDTO.getOrganizationNumber()));
        if (parmChequeBack!=null) {
            logger.warn("退票费用信息已存在, tableId={} Organization ={}",
                    chequeBackDTO.getTableId(), OrgNumberUtils.getOrg());
            throw new AnyTxnParameterException(AnyTxnParameterRespCodeEnum.D_EXIST_PARA_AUTO_PAYMENT_TABLE_FAULT);
        }

        //构建退票费用信息
        ParmChequeBack chequeBack = BeanMapping.copy(chequeBackDTO, ParmChequeBack.class);
        chequeBack.setId(String.valueOf(numberIdGenerator.generateId(TenantUtils.getTenantId())));

        return ParameterCompare.getBuilder().withMainParmId(chequeBack.getTableId()).withAfter(chequeBack).build(ParmChequeBack.class);
    }

    @Override
    @UpdateParameterAnnotation(tableName = "parm_refund_transaction_fee", tableDesc = "Refund Transaction Fee")
    public ParameterCompare modify(ChequeBackDTO chequeBackDTO) {
        if (chequeBackDTO.getId() == null) {
            throw new AnyTxnParameterException(AnyTxnParameterRespCodeEnum.P_PARAMETER_EMPTY_PARA_AUTO_PAYMENT_TABLE_FAULT);
        }
        ParmChequeBack parmChequeBack = parmChequeBackMapper.selectByPrimaryKey(chequeBackDTO.getId());

        if (parmChequeBack == null) {
            logger.error("修改退票费用数据, 通过主键id({})未找到数据", chequeBackDTO.getId());
            throw new AnyTxnParameterException(AnyTxnParameterRespCodeEnum.D_MODIFY_PARA_AUTO_PAYMENT_TABLE_BY_ID_FAULT);
        }
        // 拷贝修改的数据并更新
        ParmChequeBack chequeBack = BeanMapping.copy(chequeBackDTO, ParmChequeBack.class);

        return ParameterCompare.getBuilder().withAfter(chequeBack).withBefore(parmChequeBack).build(ParmChequeBack.class);
    }

    @Override
    @DeleteParameterAnnotation(tableName = "parm_refund_transaction_fee", tableDesc = "Refund Transaction Fee")
    public ParameterCompare remove(String id) {
        if (id == null) {
            throw new AnyTxnParameterException(AnyTxnParameterRespCodeEnum.P_DELETE_EMPTY_PARA_AUTO_PAYMENT_TABLE_FAULT);
        }

        ParmChequeBack chequeBack = parmChequeBackMapper.selectByPrimaryKey(id);
        if (chequeBack == null) {
            logger.error("删除退票费用数据, 通过主键id({})未找到数据", id);
            throw new AnyTxnParameterException(AnyTxnParameterRespCodeEnum.D_DELETE_PARA_AUTO_PAYMENT_TABLE_BY_ID_FAULT);
        }

        return ParameterCompare.getBuilder().withBefore(chequeBack).build(ParmChequeBack.class);
    }

    @Override
    @DeleteParameterAnnotation(tableName = "parm_refund_transaction_fee", tableDesc = "Refund Transaction Fee")
    public ParameterCompare remove(String tableId,String orgNum) {
        if (tableId == null || orgNum == null) {
            throw new AnyTxnParameterException(AnyTxnParameterRespCodeEnum.P_DELETE_EMPTY_PARA_AUTO_PAYMENT_TABLE_FAULT);
        }

        ParmChequeBack chequeBack = parmChequeBackMapper.selectByTableIdAndOrgNum(tableId,orgNum);
        if (chequeBack == null) {
            logger.error("删除退票费用数据, 通过tableId({})和orgNum({})未找到数据", tableId,orgNum);
            throw new AnyTxnParameterException(AnyTxnParameterRespCodeEnum.D_DELETE_PARA_AUTO_PAYMENT_TABLE_BY_ID_FAULT);
        }

        return ParameterCompare.getBuilder().withBefore(chequeBack).build(ParmChequeBack.class);
    }

    @Override
    public ChequeBackResDTO find(String id) {
        if (id == null) {
            throw new AnyTxnParameterException(AnyTxnParameterRespCodeEnum.P_PRIMARY_KEY_NULL_FAULT);
        }

        ParmChequeBack chequeBack = parmChequeBackMapper.selectByPrimaryKey(id);

        if (chequeBack == null) {
            logger.error("查询退票费用信息, 通过主键id:{} 未找到数据", id);
            throw new AnyTxnParameterException(AnyTxnParameterRespCodeEnum.D_QUERY_PARA_AUTO_PAYMENT_TABLE_FAULT);
        }

        return BeanMapping.copy(chequeBack, ChequeBackResDTO.class);
    }

    @Override
    public ChequeBackResDTO find(String tableId,String orgNum) {
        if (tableId == null || orgNum == null) {
            throw new AnyTxnParameterException(AnyTxnParameterRespCodeEnum.P_PRIMARY_KEY_NULL_FAULT);
        }

        ParmChequeBack chequeBack = parmChequeBackMapper.selectByTableIdAndOrgNum(tableId,orgNum);

        if (chequeBack == null) {
            logger.error("查询退票费用信息, 通过tableId:{} 和orgNum:{} 未找到数据", tableId,orgNum);
            throw new AnyTxnParameterException(AnyTxnParameterRespCodeEnum.D_QUERY_PARA_AUTO_PAYMENT_TABLE_FAULT);
        }

        return BeanMapping.copy(chequeBack, ChequeBackResDTO.class);
    }

    @Override
    public PageResultDTO<ChequeBackResDTO> findAll(Integer pageNum, Integer pageSize, String tableId, String description, String feeIndicator,String status, String transactionCode, String interestIndicator, BigDecimal fixedFee, String organizationNumber) {
        Map<String,Object> map = new HashMap<>(9);
        map.put("tableId",tableId);
        map.put("description",description);
        map.put("feeIndicator",feeIndicator);
        map.put("status",status);
        map.put("transactionCode",transactionCode);
        map.put("interestIndicator",interestIndicator);
        map.put("fixedFee",fixedFee);
        map.put("organizationNumber", StringUtils.isEmpty(organizationNumber) ? OrgNumberUtils.getOrg() : organizationNumber);
        Page<ParmChequeBack> page = PageHelper.startPage(pageNum, pageSize);
        List<ParmChequeBack> allocatedList = parmChequeBackSelfMapper.selectAll(map);
        List<ChequeBackResDTO> res = BeanMapping.copyList(allocatedList, ChequeBackResDTO.class);
        return new PageResultDTO<>(pageNum,pageSize,page.getTotal(),page.getPages(),res);
    }

    @Override
    public List<ChequeBackResDTO> findByStatus(String status) {
        List<ChequeBackResDTO> paymentAllocatedResList = null;
        if (!StringUtils.isEmpty(status)) {
            List<ParmChequeBack> chequeBacksList = parmChequeBackSelfMapper.selectByStatus(status, OrgNumberUtils.getOrg());
            paymentAllocatedResList = BeanMapping.copyList(chequeBacksList, ChequeBackResDTO.class);
        }

        return paymentAllocatedResList;
    }
}
