package com.anytech.anytxn.parameter.common.service.system;

import com.alibaba.fastjson.JSON;
import com.github.pagehelper.Page;
import com.github.pagehelper.PageHelper;
import com.anytech.anytxn.common.core.annotation.db.DeleteParameterAnnotation;
import com.anytech.anytxn.common.core.annotation.db.InsertParameterAnnotation;
import com.anytech.anytxn.common.core.annotation.db.UpdateParameterAnnotation;
import com.anytech.anytxn.business.base.audit.AbstractParameterService;
import com.anytech.anytxn.common.core.base.ParameterCompare;
import com.anytech.anytxn.business.base.audit.ParmModificationRecord;
import com.anytech.anytxn.common.core.base.PageResultDTO;
import com.anytech.anytxn.common.core.utils.BeanMapping;
import com.anytech.anytxn.common.core.utils.OrgNumberUtils;
import com.anytech.anytxn.common.sequence.utils.Number16IdGen;
import com.anytech.anytxn.common.core.utils.TenantUtils;
import com.anytech.anytxn.parameter.base.common.enums.AnyTxnParameterRespCodeEnum;
import com.anytech.anytxn.parameter.base.common.exception.AnyTxnParameterException;
import com.anytech.anytxn.parameter.common.mapper.broadcast.system.ParmSysOrgDictMapper;
import com.anytech.anytxn.parameter.common.mapper.broadcast.system.ParmSysOrgDictSelfMapper;
import com.anytech.anytxn.parameter.base.common.domain.model.system.ParmSysCodeType;
import com.anytech.anytxn.parameter.base.common.domain.model.system.ParmSysOrgDict;
import com.anytech.anytxn.parameter.base.common.domain.dto.system.ParmSysDictDTO;
import com.anytech.anytxn.parameter.base.common.domain.dto.system.ParmSysDictSearchKeyDTO;
import com.anytech.anytxn.parameter.base.common.domain.dto.system.ParmSysDictSimpleResDTO;
import com.anytech.anytxn.parameter.base.common.service.system.IParmSysOrgDictService;
//import org.apache.shardingsphere.transaction.annotation.ShardingSphereTransactionType;
//import org.apache.shardingsphere.transaction.core.TransactionType;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;
import org.springframework.util.ObjectUtils;
import org.springframework.util.StringUtils;

import java.time.LocalDateTime;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 系统机构字典 业务接口实现
 * <AUTHOR>
 * @date 2021-01-21
 **/
@Service("parm_sys_org_dict_serviceImpl")
public class ParmSysOrgDictServiceImpl extends AbstractParameterService implements IParmSysOrgDictService {

    private Logger logger = LoggerFactory.getLogger(ParmSysOrgDictServiceImpl.class);

    @Autowired
    private Number16IdGen numberIdGenerator;
    @Autowired
    private ParmSysOrgDictSelfMapper parmSysOrgDictSelfMapper;
    @Autowired
    private ParmSysOrgDictMapper parmSysOrgDictMapper;

    /**
     * 通过分类ID获取字典列表，按分类ID分组
     * @param typeIds
     * @return
     */
    @Override
    public HashMap<String, List<ParmSysDictSimpleResDTO>> findMapByTypeIds(List<String> typeIds, String organizationNumber) {
        if (null != typeIds && !typeIds.isEmpty()) {
            HashMap<String, List<ParmSysDictSimpleResDTO>> retMap = new HashMap<>(16);
            List<ParmSysOrgDict> list = parmSysOrgDictSelfMapper.selectListByTypeIds(typeIds, organizationNumber);
            if (null == list || list.isEmpty()) {
                return retMap;
            }
            Map<String, List<ParmSysOrgDict>> groupList = list.stream().collect(Collectors.groupingBy(ParmSysOrgDict::getTypeId));
            for(Map.Entry<String, List<ParmSysOrgDict>> entry : groupList.entrySet()) {
                List<ParmSysDictSimpleResDTO> allList = BeanMapping.copyList(entry.getValue(), ParmSysDictSimpleResDTO.class);
                List<ParmSysDictSimpleResDTO> alist = allList.stream().filter(e -> "0".equals(e.getPid())).collect(Collectors.toList());
                handlerNestingSimpleDict(alist, allList);
                retMap.put(entry.getKey(), alist.get(0).getChildren());
            }
            return retMap;
        }
        return null;
    }

    /**
     * 查询所有字典数据
     * @return
     */
    @Override
    public List<ParmSysDictDTO> selectAll(String organizationNumber) {
        List<ParmSysOrgDict> list = parmSysOrgDictSelfMapper.selectAll(organizationNumber);
        if (null != list && !list.isEmpty()) {
            List<ParmSysDictDTO> allList = BeanMapping.copyList(list, ParmSysDictDTO.class);
            List<ParmSysDictDTO> alist = allList.stream().filter(e -> "0".equals(e.getPid())).collect(Collectors.toList());
            handlerNestingDict(alist, allList);
            return alist;
        }
        return null;
    }

    @Override
    public PageResultDTO<ParmSysDictDTO> findPage(ParmSysDictSearchKeyDTO searchKeyDTO) {
        Page<ParmSysCodeType> page = PageHelper.startPage(searchKeyDTO.getPage(), searchKeyDTO.getRows());
        Map<String, Object> searchKeyMap = new HashMap<>(4);
        String typeName = searchKeyDTO.getTypeName()==null?searchKeyDTO.getTypeName():"%"+searchKeyDTO.getTypeName().trim()+"%";
        String typeId = searchKeyDTO.getTypeId()==null? searchKeyDTO.getTypeId() : "%"+searchKeyDTO.getTypeId().trim().toUpperCase()+"%";
        searchKeyMap.put("typeId", typeId);
        searchKeyMap.put("typeName",typeName);
        searchKeyMap.put("organizationNumber", StringUtils.isEmpty(searchKeyDTO.getOrganizationNumber()) ? OrgNumberUtils.getOrg() : searchKeyDTO.getOrganizationNumber());
        List<ParmSysOrgDict> parmSysDictList = parmSysOrgDictSelfMapper.selectByOptions(searchKeyMap);

        if (parmSysDictList.isEmpty()) {
            logger.error("未查询到系统字典类型信息");
            throw new AnyTxnParameterException(AnyTxnParameterRespCodeEnum.D_PAGE_PARM_SYS_CODE_TYPE_NULL_FAULT);
        }
        List<ParmSysDictDTO> currencyRateRes = BeanMapping.copyList(parmSysDictList, ParmSysDictDTO.class);
        for (ParmSysDictDTO sysCodeTypeRes: currencyRateRes) {
            List<ParmSysOrgDict> childList = parmSysOrgDictSelfMapper.selectListByPid(sysCodeTypeRes.getId(), OrgNumberUtils.getOrg());
            if (childList != null && !childList.isEmpty()) {
                // 该字典类型包含具体的枚举值定义
                sysCodeTypeRes.setEditFlag("1");
            } else {
                // 该字典类型没有包含具体的枚举值定义
                sysCodeTypeRes.setEditFlag("0");
            }
        }
        return new PageResultDTO<>(searchKeyDTO.getPage(), searchKeyDTO.getRows(), page.getTotal(), page.getPages(),currencyRateRes);
    }

    /**
     * 查询某类型下的所有(单级)
     * @param pid 父id
     * @return
     */
    @Override
    public List<ParmSysDictDTO> selectListByPid(String pid) {
        if (Long.parseLong(pid) >= 0) {
            List<ParmSysOrgDict> list = parmSysOrgDictSelfMapper.selectListByPid(pid, OrgNumberUtils.getOrg());
            return BeanMapping.copyList(list, ParmSysDictDTO.class);
        }
        return null;
    }

    /**
     * 查询某类型下的所有(包括所有层级)
     * @param typeId 类型
     * @return
     */
    @Override
    public List<ParmSysDictDTO> selectListByTypeId(String typeId, String organizationNumber) {
        if (!StringUtils.isEmpty(typeId)) {
            List<ParmSysOrgDict> list = parmSysOrgDictSelfMapper.selectListByTypeId(typeId, organizationNumber);
            if (null != list && !list.isEmpty()) {
                List<ParmSysDictDTO> allList = BeanMapping.copyList(list, ParmSysDictDTO.class);
                allList.forEach(x->x.setTableCode("parm_sys_org_dict"));
                List<ParmSysDictDTO> alist = allList.stream().filter(e -> "0".equals(e.getPid())).collect(Collectors.toList());
                if (list.size() >= 2) {
                    alist.get(0).setEditFlag("1");
                } else {
                    alist.get(0).setEditFlag("0");
                }
                handlerNestingDict(alist, allList);
                return alist;
            }
        }
        return null;
    }
    //嵌套处理字典数据
    private void handlerNestingDict(List<ParmSysDictDTO> alist, List<ParmSysDictDTO> allList) {
        for (ParmSysDictDTO dto : alist) {
            List<ParmSysDictDTO> blist = allList.stream().filter(e -> Objects.equals(e.getPid(), dto.getId())).collect(Collectors.toList());
            if (null != blist && !blist.isEmpty()) {
                dto.setChildren(blist);
                handlerNestingDict(blist, allList);
            }
        }
    }
    private void handlerNestingSimpleDict(List<ParmSysDictSimpleResDTO> alist, List<ParmSysDictSimpleResDTO> allList) {
        for (ParmSysDictSimpleResDTO dto : alist) {
            List<ParmSysDictSimpleResDTO> blist = allList.stream().filter(e -> Objects.equals(e.getPid(), dto.getId())).collect(Collectors.toList());
            if (null != blist && !blist.isEmpty()) {
                dto.setChildren(blist);
                handlerNestingSimpleDict(blist, allList);
            }
        }
    }

    /**
     * 通过id查询
     * @param id 代码类型名称
     * @return
     */
    @Override
    public ParmSysDictDTO findSysOrgDictById(String id) {
        if (id == null) {
            throw new AnyTxnParameterException(AnyTxnParameterRespCodeEnum.P_PARAMETER_ID_EMPTY_FAULT);
        }
        return BeanMapping.copy(parmSysOrgDictMapper.selectByPrimaryKey(id), ParmSysDictDTO.class);
    }

    /**
     * 添加
     * @param parmSysDictDTO
     * @return
     */
    @Override
    @InsertParameterAnnotation(tableName = "parm_sys_org_dict",tableDesc = "System Organization Dictionary",isJoinTable = true)
    public ParameterCompare addParmSysOrgDict(ParmSysDictDTO parmSysDictDTO) {
        if (parmSysDictDTO == null) {
            throw new AnyTxnParameterException(AnyTxnParameterRespCodeEnum.P_PARAMETER_EMPTY_FAULT);
        }
        List<ParmSysOrgDict> parmSysOrgDicts = parmSysOrgDictSelfMapper.selectListByTypeId(parmSysDictDTO.getTypeId(), parmSysDictDTO.getOrganizationNumber());
        if (!CollectionUtils.isEmpty(parmSysOrgDicts)){
            throw new AnyTxnParameterException(AnyTxnParameterRespCodeEnum.D_DATA_ALREADY_EXISTS_FAULT);
        }
        return ParameterCompare
                .getBuilder()
                .withAfter(parmSysDictDTO)
                .withMainParmId(parmSysDictDTO.getTypeId())
                .build(ParmSysDictDTO.class);


    }

    //级联插入
    private void insertChildDict(String typeId, List<ParmSysDictDTO> childList, String pid, String organizationNumber) {
       if (null != childList && !childList.isEmpty()) {
           for (ParmSysDictDTO reqDTO : childList) {
               ParmSysOrgDict dict = parmSysOrgDictSelfMapper.getOneByTypeIdAndCodeIdAndPid(typeId, reqDTO.getCodeId(), pid, organizationNumber);
               if (null == dict) {
                   dict = BeanMapping.copy(reqDTO, ParmSysOrgDict.class);
                   dict.setId(numberIdGenerator.generateId(TenantUtils.getTenantId())+"");
                   dict.setTypeId(typeId);
                   dict.setCreateTime(LocalDateTime.now());
                   dict.setPid(pid);
                   dict.setEnableStatus("1");
                   dict.setOrganizationNumber(organizationNumber);
                   parmSysOrgDictMapper.insert(dict);
               }
               //是否有子集
               List<ParmSysDictDTO> childNextList = reqDTO.getChildren();
               if (null != childNextList && !childNextList.isEmpty() && Long.parseLong(dict.getId()) >= 1) {
                   insertChildDict(typeId, childNextList, dict.getId(), organizationNumber);
               }
           }
       }
    }


    /**
     * 修改
     * @param parmSysDictDTO
     * @return
     */
    @Override
    @UpdateParameterAnnotation(tableName = "parm_sys_org_dict",tableDesc = "System Organization Dictionary",isJoinTable = true)
    public ParameterCompare modifyParmSysOrgDict(ParmSysDictDTO parmSysDictDTO) {
        if (null == parmSysDictDTO || parmSysDictDTO.getId() == null) {
            throw new AnyTxnParameterException(AnyTxnParameterRespCodeEnum.P_PARAMETER_ID_EMPTY_FAULT);
        }
//        ParmSysDict paraSysDict = parmSysDictMapper.selectByPrimaryKey(parmSysDictReqDTO.getId());
//        if (paraSysDict == null) {
//            logger.error("更改系统字典表信息，通过id:{}未查到数据", parmSysDictReqDTO.getId());
//            throw new AnyTxnParameterException(AnyTxnParameterRespCodeEnum.D_QUERY_PARM_SYS_CODE_NULL_FAULT);
//        }
//        BeanMapping.copy(parmSysDictReqDTO, paraSysDict);
//        parmSysDictMapper.updateByPrimaryKeySelective(paraSysDict);
//        return BeanMapping.copy(paraSysDict, ParmSysDictDTO.class);

        List<ParmSysDictDTO> parmSysDictDTOS = selectListByTypeId(parmSysDictDTO.getTypeId(), parmSysDictDTO.getOrganizationNumber());
        if (CollectionUtils.isEmpty(parmSysDictDTOS)){
            throw new AnyTxnParameterException(AnyTxnParameterRespCodeEnum.D_DATA_NOT_EXISTS_FAULT);
        }
        ParmSysDictDTO before = parmSysDictDTOS.get(0);
        return ParameterCompare
                .getBuilder()
                .withAfter(parmSysDictDTO)
                .withBefore(before)
                .withMainParmId(parmSysDictDTO.getTypeId())
                .build(ParmSysDictDTO.class);

    }

    //级联插入
    private void updateChildDict(String typeId, List<ParmSysDictDTO> childList, String pid, String organizationNumber) {
        if (null != childList && !childList.isEmpty()) {
            for (ParmSysDictDTO reqDTO : childList) {
                long id = reqDTO.getId() == null ? 0L : Long.parseLong(reqDTO.getId());
                ParmSysOrgDict dict = null;
                if (id >= 1) {
                    dict = parmSysOrgDictMapper.selectByPrimaryKey(String.valueOf(id));
                }
                if (null != dict) {
                    dict.setCodeId(reqDTO.getCodeId());
                    dict.setCodeName(reqDTO.getCodeName());
                    dict.setCodeDesc(reqDTO.getCodeDesc());
                    dict.setEnableStatus("1");
                    parmSysOrgDictMapper.updateByPrimaryKey(dict);
                } else {
                    dict = BeanMapping.copy(reqDTO, ParmSysOrgDict.class);
                    dict.setId(numberIdGenerator.generateId(TenantUtils.getTenantId())+"");
                    dict.setTypeId(typeId);
                    dict.setCreateTime(LocalDateTime.now());
                    dict.setPid(pid);
                    dict.setEnableStatus("1");
                    dict.setOrganizationNumber(organizationNumber);
                    parmSysOrgDictMapper.insert(dict);
                }
                //是否有子集
                List<ParmSysDictDTO> childNextList = reqDTO.getChildren();
                if (null != childNextList && !childNextList.isEmpty() && Long.parseLong(dict.getId()) >= 1) {
                    updateChildDict(typeId, childNextList, dict.getId(), organizationNumber);
                }
            }
        }
    }

    //查找id集合
    private void findElementIdList(List<ParmSysDictDTO> list, List<String> idList) {
        if (null != list && !list.isEmpty()) {
            for (ParmSysDictDTO parmSysDictDTO : list) {
                if (!StringUtils.isEmpty(parmSysDictDTO.getId())) {
                    idList.add(parmSysDictDTO.getId());
                    List<ParmSysDictDTO> childNextList = parmSysDictDTO.getChildren();
                    findElementIdList(childNextList, idList);
                }
            }
        }
    }

    /**
     * 删除
     * @param id
     * @return
     */
    @Override
    @DeleteParameterAnnotation(tableName = "parm_sys_org_dict",tableDesc = "System Organization Dictionary",isJoinTable = true)
    public ParameterCompare deleteParmSysOrgDict(String id) {
        if (id == null) {
            throw new AnyTxnParameterException(AnyTxnParameterRespCodeEnum.P_PARAMETER_ID_EMPTY_FAULT);
        }
        ParmSysOrgDict parmSysOrgDict = parmSysOrgDictMapper.selectByPrimaryKey(id);
        if (ObjectUtils.isEmpty(parmSysOrgDict)) {
            throw new AnyTxnParameterException(AnyTxnParameterRespCodeEnum.P_PARAMETER_DATA_NOT_EXIST);
        }
        return ParameterCompare
                .getBuilder()
                .withBefore(parmSysOrgDict)
                .build(ParmSysOrgDict.class);
    }

    @Override
    @Transactional(rollbackFor = Exception.class,propagation = Propagation.REQUIRED)
    //@ShardingSphereTransactionType(TransactionType.XA)
    protected boolean updateDb(ParmModificationRecord parmModificationRecord) {
        ParmSysDictDTO parmSysDictDTO = JSON.parseObject(parmModificationRecord.getParmBody(), ParmSysDictDTO.class);
        String typeId = parmSysDictDTO.getTypeId();
        String organizationNumber = parmSysDictDTO.getOrganizationNumber();
        // 方式一、先删除，待考虑
//        parmSysDictSelfMapper.deleteByTypeId(typeId);
        // 方式二、原有所有节点
        List<ParmSysOrgDict> allTypelist = parmSysOrgDictSelfMapper.selectListByTypeId(typeId, organizationNumber);
        List<String> allIdList = allTypelist.stream().filter(e -> Long.parseLong(e.getPid()) > 0).map(k -> k.getId()).distinct().collect(Collectors.toList());
        if (null != allIdList && !allIdList.isEmpty()) {
            List<String> idList = new ArrayList<>(16);
            findElementIdList(parmSysDictDTO.getChildren(), idList);
            allIdList.removeAll(idList);
            //批量删除allIdList
            if (!allIdList.isEmpty() && allIdList.size() >= 1) {
                parmSysOrgDictSelfMapper.deleteByIdList(allIdList, organizationNumber);
            }
        }
        long pid = StringUtils.isEmpty(parmSysDictDTO.getPid()) ? 0L : Long.parseLong(parmSysDictDTO.getPid());
        ParmSysOrgDict firstDict = null;
        if (pid == 0) {
            // 查询该类型是否存在
            firstDict = parmSysOrgDictSelfMapper.getOneByTypeIdAndCodeIdAndPid(typeId, "", parmSysDictDTO.getPid(), organizationNumber);
        } else {
            // 查询该类型、该pid和该codeId是否存在
            String codeId = parmSysDictDTO.getCodeId();
            if (StringUtils.isEmpty(codeId)) {
                return false;
            }
            firstDict = parmSysOrgDictSelfMapper.getOneByTypeIdAndCodeIdAndPid(typeId, parmSysDictDTO.getCodeId(), parmSysDictDTO.getPid(), organizationNumber);
        }
        //创建该次添加的第一级
        if (null != firstDict) {
            firstDict.setCodeId(parmSysDictDTO.getCodeId());
            firstDict.setCodeName(parmSysDictDTO.getCodeName());
            firstDict.setCodeDesc(parmSysDictDTO.getCodeDesc());
            firstDict.setEnableStatus("1");
            parmSysOrgDictMapper.updateByPrimaryKey(firstDict);
        } else {
            firstDict = BeanMapping.copy(parmSysDictDTO, ParmSysOrgDict.class);
            firstDict.setId(numberIdGenerator.generateId(TenantUtils.getTenantId())+"");
            firstDict.setTypeId(typeId);
            firstDict.setCreateTime(LocalDateTime.now());
            firstDict.setPid(pid+"");
            firstDict.setOrganizationNumber(organizationNumber);
            parmSysOrgDictMapper.insert(firstDict);
        }
        List<ParmSysDictDTO> childList = parmSysDictDTO.getChildren();
        if (null != childList && !childList.isEmpty() && Long.parseLong(firstDict.getId()) >= 1) {
            updateChildDict(typeId, childList, firstDict.getId(), organizationNumber);
        }
        return true;
    }

    @Override
    @Transactional(rollbackFor = Exception.class,propagation = Propagation.REQUIRED)
    //@ShardingSphereTransactionType(TransactionType.XA)
    protected boolean insertDb(ParmModificationRecord parmModificationRecord) {
        ParmSysDictDTO parmSysDictDTO = JSON.parseObject(parmModificationRecord.getParmBody(), ParmSysDictDTO.class);
        String typeId = parmSysDictDTO.getTypeId();
        String organizationNumber = parmSysDictDTO.getOrganizationNumber();
        long pid = StringUtils.isEmpty(parmSysDictDTO.getPid()) ? 0L : Long.parseLong(parmSysDictDTO.getPid());
        ParmSysOrgDict firstDict = null;
        if (pid == 0) {
            // 查询该类型是否存在
            firstDict = parmSysOrgDictSelfMapper.getOneByTypeIdAndCodeIdAndPid(typeId, "", parmSysDictDTO.getPid(), organizationNumber);
            if (null != firstDict) {
                return false;
            }
        } else {
            // 查询该类型、该pid和该codeId是否存在
            String codeId = parmSysDictDTO.getCodeId();
            if (StringUtils.isEmpty(codeId)) {
                return false;
            }
            firstDict = parmSysOrgDictSelfMapper.getOneByTypeIdAndCodeIdAndPid(typeId, parmSysDictDTO.getCodeId(), parmSysDictDTO.getPid(), organizationNumber);
        }
        //创建该次添加的第一级
        if (null == firstDict) {
            firstDict = BeanMapping.copy(parmSysDictDTO, ParmSysOrgDict.class);
            firstDict.setId(numberIdGenerator.generateId(TenantUtils.getTenantId())+"");
            firstDict.setTypeId(typeId);
            firstDict.setCreateTime(LocalDateTime.now());
            firstDict.setPid(pid+"");
            firstDict.setEnableStatus(parmSysDictDTO.getEnableStatus());
            firstDict.setOrganizationNumber(organizationNumber);
            parmSysOrgDictMapper.insert(firstDict);
        }
        List<ParmSysDictDTO> childList = parmSysDictDTO.getChildren();
        if (null != childList && !childList.isEmpty() && Long.parseLong(firstDict.getId()) >= 1) {
            insertChildDict(typeId, childList, firstDict.getId(), organizationNumber);
        }
        return true;
    }

    @Override
    @Transactional(rollbackFor = Exception.class,propagation = Propagation.REQUIRED)
    //@ShardingSphereTransactionType(TransactionType.XA)
    protected boolean deleteDb(ParmModificationRecord parmModificationRecord) {
        ParmSysOrgDict parmSysOrgDict = JSON.parseObject(parmModificationRecord.getParmBody(), ParmSysOrgDict.class);
        int i = parmSysOrgDictSelfMapper.deleteByTypeId(parmSysOrgDict.getTypeId(), parmSysOrgDict.getOrganizationNumber());
        return i > 0;
    }
}
