package com.anytech.anytxn.parameter.account.service;

import com.alibaba.fastjson.JSON;
import com.github.pagehelper.Page;
import com.github.pagehelper.PageHelper;
import com.anytech.anytxn.common.core.annotation.db.DeleteParameterAnnotation;
import com.anytech.anytxn.common.core.annotation.db.InsertParameterAnnotation;
import com.anytech.anytxn.common.core.annotation.db.UpdateParameterAnnotation;
import com.anytech.anytxn.business.base.audit.AbstractParameterService;
import com.anytech.anytxn.common.core.base.ParameterCompare;
import com.anytech.anytxn.business.base.audit.ParmModificationRecord;
import com.anytech.anytxn.common.core.utils.OrgNumberUtils;
import com.anytech.anytxn.common.core.base.PageResultDTO;
import com.anytech.anytxn.common.core.utils.BeanMapping;
import com.anytech.anytxn.common.sequence.utils.Number16IdGen;
import com.anytech.anytxn.common.core.utils.TenantUtils;
import com.anytech.anytxn.parameter.base.account.domain.dto.MinimumPaymentPercentReqDTO;
import com.anytech.anytxn.parameter.base.account.domain.dto.MinimumPaymentPercentResDTO;
import com.anytech.anytxn.parameter.base.account.service.IMinimumPaymentPercentService;
import com.anytech.anytxn.parameter.base.common.enums.AnyTxnParameterRespCodeEnum;
import com.anytech.anytxn.parameter.base.common.enums.ParameterRepDetailEnum;
import com.anytech.anytxn.parameter.base.common.exception.AnyTxnParameterException;
import com.anytech.anytxn.parameter.account.mapper.ParmMinimumPaymentPercentMapper;
import com.anytech.anytxn.parameter.account.mapper.ParmMinimumPaymentPercentSelfMapper;
import com.anytech.anytxn.parameter.base.account.domain.model.ParmMinimumPaymentPercent;
//import org.apache.shardingsphere.transaction.annotation.ShardingSphereTransactionType;
//import org.apache.shardingsphere.transaction.core.TransactionType;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.StringUtils;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 最低还款比例参数 业务接口实现
 * <AUTHOR> tingting
 * @date 2018/9/20
 */
@Service(value = "parm_minimum_payment_percent_serviceImpl")
public class MinimumPaymentPercentServiceImpl extends AbstractParameterService implements IMinimumPaymentPercentService {

    @Autowired
    private ParmMinimumPaymentPercentMapper parmMinimumPaymentPercentMapper;
    @Autowired
    private ParmMinimumPaymentPercentSelfMapper parmMinimumPaymentPercentSelfMapper;

    @Autowired
    private Number16IdGen numberIdGenerator;

    /**
     * 通过机构号,tableId查询最低还款比例参数信息
     *
     * @param organizationNumber 机构号
     * @param tableId            表Id
     * @return MinimumPaymentPercentRes
     * @throws AnyTxnParameterException
     */
    @Override
    //@PreGetProcess(args = {"#0","#1"})
    public MinimumPaymentPercentResDTO findByOrgAndTableId(String organizationNumber, String tableId){
        ParmMinimumPaymentPercent minimumPaymentPercent = parmMinimumPaymentPercentSelfMapper.selectByOrgAndTableId(organizationNumber, tableId);
        if (minimumPaymentPercent == null) {

            throw new AnyTxnParameterException(AnyTxnParameterRespCodeEnum.D_QUERY_MINIMUM_PAYMENT_PERCENT_FAULT);
        }
        return BeanMapping.copy(minimumPaymentPercent, MinimumPaymentPercentResDTO.class);
    }

    /**
     * 添加最低还款比例参数
     *
     * @param minimumPaymentPercentReq
     * @return 最低还款比例响应参数
     * @throws AnyTxnParameterException
     */
    @Override
    @InsertParameterAnnotation(tableName = "parm_minimum_payment_percent", tableDesc = "Minimum Repayment")
    public ParameterCompare addParmMinimumPaymentPercent(MinimumPaymentPercentReqDTO minimumPaymentPercentReq){
        ParmMinimumPaymentPercent isExsit = parmMinimumPaymentPercentSelfMapper.selectByOrgAndTableId(OrgNumberUtils.getOrg(minimumPaymentPercentReq.getOrganizationNumber()), minimumPaymentPercentReq.getTableId());
        if (isExsit != null) {

            throw new AnyTxnParameterException(AnyTxnParameterRespCodeEnum.D_EXIST_MINIMUM_PAYMENT_PERCENT_FAULT);
        }
        ParmMinimumPaymentPercent parmMinimumPaymentPercent = BeanMapping.copy(minimumPaymentPercentReq, ParmMinimumPaymentPercent.class);

        parmMinimumPaymentPercent.setId(String.valueOf(numberIdGenerator.generateId(TenantUtils.getTenantId())));

        return ParameterCompare.getBuilder().withAfter(parmMinimumPaymentPercent).build(ParmMinimumPaymentPercent.class);
    }

    /**
     * 更新最低还款比例参数
     *
     * @param minimumPaymentPercentReq 最低还款比例入参对象
     * @return 最低还款比例响应参数
     * @throws AnyTxnParameterException
     */
    @Override
    @UpdateParameterAnnotation(tableName = "parm_minimum_payment_percent", tableDesc = "Minimum Repayment")
    public ParameterCompare modifyParmMinimumPaymentPercent(MinimumPaymentPercentReqDTO minimumPaymentPercentReq){
        // 查询此记录是否存在
        ParmMinimumPaymentPercent parmMinimumPaymentPercent = parmMinimumPaymentPercentMapper.selectByPrimaryKey(minimumPaymentPercentReq.getId());
        if (null == parmMinimumPaymentPercent) {

            throw new AnyTxnParameterException(AnyTxnParameterRespCodeEnum.D_QUERY_MINIMUM_PAYMENT_PERCENT_BY_ID_FAULT);
        }

        if (!minimumPaymentPercentReq.getOrganizationNumber().equals(parmMinimumPaymentPercent.getOrganizationNumber())
                || !minimumPaymentPercentReq.getTableId().equals(parmMinimumPaymentPercent.getTableId())) {
            ParmMinimumPaymentPercent isExsit = parmMinimumPaymentPercentSelfMapper.selectByOrgAndTableId(minimumPaymentPercentReq.getOrganizationNumber(), minimumPaymentPercentReq.getTableId());
            if (isExsit != null) {

                throw new AnyTxnParameterException(AnyTxnParameterRespCodeEnum.D_EXIST_MINIMUM_PAYMENT_PERCENT_FAULT);
            }
        }
        ParmMinimumPaymentPercent minimumPaymentPercent = BeanMapping.copy(minimumPaymentPercentReq, ParmMinimumPaymentPercent.class);
        minimumPaymentPercent.setVersionNumber(parmMinimumPaymentPercent.getVersionNumber());

        return ParameterCompare.getBuilder().withAfter(minimumPaymentPercent).withBefore(parmMinimumPaymentPercent).build(ParmMinimumPaymentPercent.class);
    }

    /**
     * 通过Id主键删除最低还款比例参数
     *
     * @param id 主键
     * @return Boolean
     * @throws AnyTxnParameterException
     */
    @Override
    @DeleteParameterAnnotation(tableName = "parm_minimum_payment_percent", tableDesc = "Minimum Repayment")
    public ParameterCompare removeParmMinimumPaymentPercent(String id){
        // 如果id不存在，则查询该记录
        if (null == id) {

            throw new AnyTxnParameterException(AnyTxnParameterRespCodeEnum.P_PARAMETER_EMPTY_FAULT);
        }
        ParmMinimumPaymentPercent parmMinimumPaymentPercent = parmMinimumPaymentPercentMapper.selectByPrimaryKey(id);
        // 如果该记录不存在，抛出异常，否则就删除该记录
        if (null == parmMinimumPaymentPercent) {

            throw new AnyTxnParameterException(AnyTxnParameterRespCodeEnum.D_QUERY_MINIMUM_PAYMENT_PERCENT_BY_ID_FAULT);
        }
        return ParameterCompare.getBuilder().withBefore(parmMinimumPaymentPercent).build(ParmMinimumPaymentPercent.class);
    }

    /**
     * 查询所有最低还款比例参数
     *
     * @param pageNum
     * @param pageSize
     * @return 最低还款比例响应参数
     * @throws AnyTxnParameterException
     */
    @Override
    public PageResultDTO<MinimumPaymentPercentResDTO> findAll(Integer pageNum, Integer pageSize,String tableId,String description,String billEvenDollars,String curMinPaymentPercentage,String preMinPaymentPercentage, String organizationNumber) {
        BigDecimal curMinPaymentPercentageDeci = null;
        BigDecimal preMinPaymentPercentageDeci = null;
        if(!StringUtils.isEmpty(curMinPaymentPercentage)){
            try {
                curMinPaymentPercentageDeci = new BigDecimal(curMinPaymentPercentage);
            }catch (NumberFormatException e){
                throw new AnyTxnParameterException(AnyTxnParameterRespCodeEnum.PARAMETER_EXCEPTION, ParameterRepDetailEnum.NUMBER_ERROR);
            }
        }
        if(!StringUtils.isEmpty(preMinPaymentPercentage)){
            try {
                preMinPaymentPercentageDeci = new BigDecimal(preMinPaymentPercentage);
            }catch (NumberFormatException e){
                throw new AnyTxnParameterException(AnyTxnParameterRespCodeEnum.PARAMETER_EXCEPTION,ParameterRepDetailEnum.NUMBER_ERROR);
            }
        }
        Map<String,Object> map = new HashMap<>(8);
        map.put("tableId",tableId);
        map.put("description",description);
        map.put("billEvenDollars",billEvenDollars);
        map.put("curMinPaymentPercentage",curMinPaymentPercentage);
        map.put("preMinPaymentPercentage",preMinPaymentPercentage);
        map.put("curMinPaymentPercentageDeci",curMinPaymentPercentageDeci);
        map.put("preMinPaymentPercentageDeci",preMinPaymentPercentageDeci);
        map.put("organizationNumber", StringUtils.isEmpty(organizationNumber) ? OrgNumberUtils.getOrg() : organizationNumber);
        Page<ParmMinimumPaymentPercent> page = PageHelper.startPage(pageNum, pageSize);
        List<ParmMinimumPaymentPercent> minimumPaymentPercentList = parmMinimumPaymentPercentSelfMapper.selectByCondition(map);
        if (minimumPaymentPercentList.isEmpty()) {
            minimumPaymentPercentList = new ArrayList<>();
           // throw new AnyTxnParameterException(AnyTxnParameterRespCodeEnum.D_PAGE_QUERY_MINIMUM_PAYMENT_PERCENT_FAULT);
        }
        List<MinimumPaymentPercentResDTO> res = BeanMapping.copyList(minimumPaymentPercentList, MinimumPaymentPercentResDTO.class);
        return new PageResultDTO<>(pageNum,pageSize,page.getTotal(), page.getPages(), res);
    }

    /**
     * 通过id查询最低还款比例参数信息
     *
     * @param id
     * @return MinimumPaymentPercentRes
     * @throws AnyTxnParameterException
     */
    @Override
    //@PreGetProcess(byPrimaryKey = true)
    public MinimumPaymentPercentResDTO findById(String id){
        if (id == null) {

            throw new AnyTxnParameterException(AnyTxnParameterRespCodeEnum.P_PARAMETER_EMPTY_FAULT);
        }
        ParmMinimumPaymentPercent parmMinimumPaymentPercent = parmMinimumPaymentPercentMapper.selectByPrimaryKey(id);
        if (parmMinimumPaymentPercent == null) {

            throw new AnyTxnParameterException(AnyTxnParameterRespCodeEnum.D_QUERY_MINIMUM_PAYMENT_PERCENT_BY_ID_FAULT);
        }
        return BeanMapping.copy(parmMinimumPaymentPercent, MinimumPaymentPercentResDTO.class);
    }

    /**
     * 通过机构号查询最低还款比例参数信息
     *
     * @param organizationNumber 机构号
     * @return 最低还款比例参数响应参数
     * @throws AnyTxnParameterException 异常
     */
    @Override
    public List<MinimumPaymentPercentResDTO> findAllByOrgNumber(String organizationNumber){
        List<MinimumPaymentPercentResDTO> minimumPaymentPercentResList = null;
        if (!StringUtils.isEmpty(organizationNumber)) {
            List<ParmMinimumPaymentPercent> autoPaymentList = parmMinimumPaymentPercentSelfMapper.findAllByOrgNumber(organizationNumber);
            minimumPaymentPercentResList = BeanMapping.copyList(autoPaymentList, MinimumPaymentPercentResDTO.class);
        }

        return minimumPaymentPercentResList;
    }

    @Override
    @Transactional(rollbackFor = Exception.class,propagation = Propagation.REQUIRED)
    //@ShardingSphereTransactionType(TransactionType.XA)
    protected boolean updateDb(ParmModificationRecord parmModificationRecord) {
        ParmMinimumPaymentPercent parmMinimumPaymentPercent = JSON.parseObject(parmModificationRecord.getParmBody(), ParmMinimumPaymentPercent.class);
        parmMinimumPaymentPercent.initUpdateDateTime();
        parmMinimumPaymentPercent.setUpdateBy(parmModificationRecord.getApplicationBy());
        int i = parmMinimumPaymentPercentMapper.updateByPrimaryKeySelective(parmMinimumPaymentPercent);
        return i > 0;
    }

    @Override
    @Transactional(rollbackFor = Exception.class,propagation = Propagation.REQUIRED)
    //@ShardingSphereTransactionType(TransactionType.XA)
    protected boolean insertDb(ParmModificationRecord parmModificationRecord) {
        ParmMinimumPaymentPercent parmMinimumPaymentPercent = JSON.parseObject(parmModificationRecord.getParmBody(), ParmMinimumPaymentPercent.class);
        parmMinimumPaymentPercent.initCreateDateTime();
        parmMinimumPaymentPercent.setVersionNumber(1L);
        int i = parmMinimumPaymentPercentMapper.insertSelective(parmMinimumPaymentPercent);
        return i > 0;
    }

    @Override
    @Transactional(rollbackFor = Exception.class,propagation = Propagation.REQUIRED)
    //@ShardingSphereTransactionType(TransactionType.XA)
    protected boolean deleteDb(ParmModificationRecord parmModificationRecord) {
        ParmMinimumPaymentPercent parmMinimumPaymentPercent = JSON.parseObject(parmModificationRecord.getParmBody(), ParmMinimumPaymentPercent.class);
        int i = parmMinimumPaymentPercentMapper.deleteByPrimaryKey(parmMinimumPaymentPercent.getId());
        return i > 0;
    }
}
