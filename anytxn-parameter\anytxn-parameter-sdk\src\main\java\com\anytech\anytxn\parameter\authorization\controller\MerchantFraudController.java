package com.anytech.anytxn.parameter.authorization.controller;

import io.swagger.v3.oas.annotations.tags.Tag;
import io.swagger.v3.oas.annotations.Operation;
import com.anytech.anytxn.common.core.base.PageResultDTO;
import com.anytech.anytxn.common.core.base.AnyTxnHttpResponse;
import com.anytech.anytxn.common.core.base.BizBaseController;
import com.anytech.anytxn.parameter.base.authorization.domain.dto.MerchantFraudDTO;
import com.anytech.anytxn.parameter.base.authorization.service.IMerchantFraudService;
import com.anytech.anytxn.parameter.base.common.enums.ParameterRepDetailEnum;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import jakarta.validation.Valid;

/**
 * <AUTHOR>
 * @version 1.0
 * @description
 * @date 2020-07-07 13:54
 **/
@RestController
@Tag(name = "欺诈商户表")
public class MerchantFraudController extends BizBaseController {

    @Autowired
    private IMerchantFraudService merchantFraudService;

    @Operation(summary = "新增欺诈商户表")
    @PostMapping("/param/merchantFraud")
    public AnyTxnHttpResponse create(@Valid @RequestBody MerchantFraudDTO merchantFraudDTO) {
        int add = merchantFraudService.add(merchantFraudDTO);
        return AnyTxnHttpResponse.success(add, ParameterRepDetailEnum.CREATE.message());
    }

    @Operation(summary = "修改欺诈商户表")
    @PutMapping("/param/merchantFraud")
    public AnyTxnHttpResponse modify(@Valid @RequestBody MerchantFraudDTO merchantFraudDTO) {
        int modify = merchantFraudService.modify(merchantFraudDTO);
        return AnyTxnHttpResponse.success(modify, ParameterRepDetailEnum.UPDATE.message());
    }

    @Operation(summary = "删除欺诈商户表")
    @DeleteMapping("/param/merchantFraud/{id}")
    public AnyTxnHttpResponse delete(@PathVariable Long id) {
        int delete = merchantFraudService.delete(id);
        return AnyTxnHttpResponse.success(delete,ParameterRepDetailEnum.DEL.message());
    }

    @Operation(summary = "根据id查询欺诈商户表")
    @GetMapping("/param/merchantFraud/{id}")
    public AnyTxnHttpResponse<MerchantFraudDTO> getMccCode(@PathVariable Long id) {
        MerchantFraudDTO merchantFraud = merchantFraudService.selectById(id);
        return AnyTxnHttpResponse.success(merchantFraud);
    }

    @Operation(summary = "分页查询欺诈商户表")
    @GetMapping("/param/merchantFraud/{pageNum}/{pageSize}")
    public AnyTxnHttpResponse<PageResultDTO<MerchantFraudDTO>> getPage(@PathVariable(value = "pageNum")int pageNum,
                                                                       @PathVariable(value = "pageSize")int pageSize) {
        PageResultDTO<MerchantFraudDTO> result = merchantFraudService.getPage(pageNum, pageSize);
        return AnyTxnHttpResponse.success(result);
    }
}
