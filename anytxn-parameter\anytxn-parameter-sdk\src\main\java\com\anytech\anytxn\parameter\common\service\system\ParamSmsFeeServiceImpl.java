package com.anytech.anytxn.parameter.common.service.system;

import com.alibaba.fastjson.JSON;
import com.github.pagehelper.Page;
import com.github.pagehelper.PageHelper;
import com.anytech.anytxn.common.core.annotation.db.DeleteParameterAnnotation;
import com.anytech.anytxn.common.core.annotation.db.InsertParameterAnnotation;
import com.anytech.anytxn.common.core.annotation.db.UpdateParameterAnnotation;
import com.anytech.anytxn.business.base.audit.AbstractParameterService;
import com.anytech.anytxn.common.core.base.ParameterCompare;
import com.anytech.anytxn.business.base.audit.ParmModificationRecord;
import com.anytech.anytxn.common.core.base.PageResultDTO;
import com.anytech.anytxn.common.core.utils.BeanMapping;
import com.anytech.anytxn.common.core.base.AnyTxnHttpResponse;
import com.anytech.anytxn.common.sequence.utils.Number16IdGen;
import com.anytech.anytxn.common.core.utils.TenantUtils;
import com.anytech.anytxn.parameter.base.common.constants.Constants;
import com.anytech.anytxn.parameter.base.common.enums.AnyTxnParameterRespCodeEnum;
import com.anytech.anytxn.parameter.base.common.exception.AnyTxnParameterException;
import com.anytech.anytxn.parameter.common.mapper.broadcast.system.ParamSmsFeeMapper;
import com.anytech.anytxn.parameter.base.common.domain.model.system.ParamSmsFee;
import com.anytech.anytxn.parameter.base.common.domain.dto.ParamSmsFeeDTO;
import com.anytech.anytxn.parameter.base.common.service.IParamSmsFeeService;
//import org.apache.shardingsphere.transaction.annotation.ShardingSphereTransactionType;
//import org.apache.shardingsphere.transaction.core.TransactionType;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;
import org.springframework.util.ObjectUtils;
import org.springframework.util.StringUtils;

import jakarta.annotation.Resource;
import java.math.BigDecimal;
import java.util.Collections;
import java.util.List;
import java.util.Objects;

/**
 * @Author: sukang
 * @Date: 2021/3/18 14:13
 */
@Service("parm_sms_fee_serviceImpl")
public class ParamSmsFeeServiceImpl extends AbstractParameterService implements IParamSmsFeeService {

    @Resource
    ParamSmsFeeMapper paramSmsFeeMapper;

    @Autowired
    private Number16IdGen numberIdGenerator;

    @Override
    public PageResultDTO<ParamSmsFeeDTO> findAllByCondition(Integer pageNum, Integer pageSize,
                                                 String tableId, String description,
                                                 String organizationNumber) {

        Page<Object> pageInfo = PageHelper.startPage(pageNum, pageSize);

        List<ParamSmsFee> paramSmsFees = paramSmsFeeMapper.selectByCondition(organizationNumber, tableId, description);

        List<ParamSmsFeeDTO> paramSmsFeeDTOList;
        if (!CollectionUtils.isEmpty(paramSmsFees)){
            paramSmsFeeDTOList = BeanMapping.copyList(paramSmsFees, ParamSmsFeeDTO.class);
        }else {
            paramSmsFeeDTOList = Collections.emptyList();
        }
        return new PageResultDTO<>(pageNum,pageSize,pageInfo.getTotal(),pageInfo.getPages(),paramSmsFeeDTOList);

    }

    @Override
    @InsertParameterAnnotation(tableName = "parm_sms_fee",tableDesc = "Message notification charge parameters")
    public ParameterCompare addParamSmsFeeInfo(ParamSmsFeeDTO paramSmsFeeDto) {

        //唯一约束查询
        ParamSmsFee paramSmsFee = paramSmsFeeMapper.findInfoByUnionKey(paramSmsFeeDto.getOrganizationNumber(),
                paramSmsFeeDto.getChannelIdentification(),paramSmsFeeDto.getTableId());

        if (paramSmsFee != null){
            throwsException(AnyTxnParameterRespCodeEnum.D_DATA_ALREADY_EXISTS_FAULT);
        }
        //如果收取服务费，则校验服务费相关字段必须有值
        if (Objects.equals(Constants.ENABLED,paramSmsFeeDto.getEnablePeriodFee())){
            checkForNull(paramSmsFeeDto);
        }
        paramSmsFee = BeanMapping.copy(paramSmsFeeDto, ParamSmsFee.class);
        paramSmsFee.setId(String.valueOf(numberIdGenerator.generateId(TenantUtils.getTenantId())));
        return ParameterCompare
                .getBuilder()
                .withAfter(paramSmsFee)
                .build(ParamSmsFee.class);
    }

    private void checkForNull(ParamSmsFeeDTO paramSmsFeeDto) {

        if (paramSmsFeeDto.getPeriodChargeDay() == null || paramSmsFeeDto.getPeriodChargeDay() <= 0 ||
                paramSmsFeeDto.getPeriodChargeDay() > 28 ){
            throwsException(AnyTxnParameterRespCodeEnum.P_PARAMETER_EMPTY_FAULT);
        }

        if (org.apache.commons.lang3.StringUtils.isBlank(paramSmsFeeDto.getPeriodChargeTransactionCode()) ||
                org.apache.commons.lang3.StringUtils.isBlank(paramSmsFeeDto.getPeriodRefundTransactionCode())){
            throwsException(AnyTxnParameterRespCodeEnum.P_PARAMETER_EMPTY_FAULT);
        }

        if (illegalAmount(paramSmsFeeDto.getPeriodChargeAmount(),paramSmsFeeDto.getPeriodRefundAmount(),
                paramSmsFeeDto.getPeriodRefundTargetAmount())){
            throwsException(AnyTxnParameterRespCodeEnum.P_PARAMETER_EMPTY_FAULT);
        }



    }


    private Boolean illegalAmount(BigDecimal... amounts){

        for (BigDecimal amount : amounts) {
            if (amount == null || amount.compareTo(BigDecimal.ZERO) <= 0 || String.valueOf(amount).length() > 20){
                return true;
            }
        }
        return false;
    }






    @Override
    @DeleteParameterAnnotation(tableName = "parm_sms_fee",tableDesc = "Message notification charge parameters")
    public ParameterCompare deleteParamSmsFeeInfo(String id) {
        if (StringUtils.isEmpty(id)){
            throwsException(AnyTxnParameterRespCodeEnum.P_PARAMETER_EMPTY_FAULT);
        }
        ParamSmsFee paramSmsFee = paramSmsFeeMapper.selectByPrimaryKey(id);
        if (ObjectUtils.isEmpty(paramSmsFee)){
            throwsException(AnyTxnParameterRespCodeEnum.P_PARAMETER_DATA_NOT_EXIST);
        }
        return ParameterCompare
                .getBuilder()
                .withBefore(paramSmsFee)
                .build(ParamSmsFee.class);
    }

    @Override
    @UpdateParameterAnnotation(tableName = "parm_sms_fee",tableDesc = "Message notification charge parameters")
    public ParameterCompare updateParamSmsFeeInfo(ParamSmsFeeDTO paramSmsFeeDto) {
        if (StringUtils.isEmpty(paramSmsFeeDto.getId())){
            throwsException(AnyTxnParameterRespCodeEnum.P_PARAMETER_EMPTY_FAULT);
        }
        ParamSmsFee paramSmsFee = paramSmsFeeMapper.selectByPrimaryKey(paramSmsFeeDto.getId());
        if (paramSmsFee == null){
            throwsException(AnyTxnParameterRespCodeEnum.P_PARAMETER_DATA_NOT_EXIST);
        }
        ParamSmsFee modify = BeanMapping.copy(paramSmsFeeDto, ParamSmsFee.class);
        return ParameterCompare
                .getBuilder()
                .withBefore(paramSmsFee)
                .withAfter(modify)
                .build(ParamSmsFee.class);
    }


    @Override
    public AnyTxnHttpResponse<ParamSmsFeeDTO> getParamSmsFeeInfo(String id) {
        if (StringUtils.isEmpty(id)){
            throwsException(AnyTxnParameterRespCodeEnum.P_PARAMETER_EMPTY_FAULT);
        }

        ParamSmsFee paramSmsFee = paramSmsFeeMapper.selectByPrimaryKey(id);
        if (paramSmsFee == null){
            return AnyTxnHttpResponse.success(null);
        }
        return AnyTxnHttpResponse.success(BeanMapping.copy(paramSmsFee,ParamSmsFeeDTO.class));
    }



    public void throwsException(AnyTxnParameterRespCodeEnum anyTxnParameterRespCode){
        throw new AnyTxnParameterException(anyTxnParameterRespCode);
    }

    @Override
    @Transactional(rollbackFor = Exception.class,propagation = Propagation.REQUIRED)
    //@ShardingSphereTransactionType(TransactionType.XA)
    protected boolean updateDb(ParmModificationRecord parmModificationRecord) {
        ParamSmsFee paramSmsFee = JSON.parseObject(parmModificationRecord.getParmBody(), ParamSmsFee.class);
        paramSmsFee.initUpdateDateTime();
        int res = paramSmsFeeMapper.updateByPrimaryKeySelective(paramSmsFee);
        return res > 0;
    }

    @Override
    @Transactional(rollbackFor = Exception.class,propagation = Propagation.REQUIRED)
    //@ShardingSphereTransactionType(TransactionType.XA)
    protected boolean insertDb(ParmModificationRecord parmModificationRecord) {
        ParamSmsFee paramSmsFee = JSON.parseObject(parmModificationRecord.getParmBody(), ParamSmsFee.class);
        paramSmsFee.initUpdateDateTime();
        paramSmsFee.initCreateDateTime();
        int res = paramSmsFeeMapper.insertSelective(paramSmsFee);
        return res > 0;
    }

    @Override
    @Transactional(rollbackFor = Exception.class,propagation = Propagation.REQUIRED)
    //@ShardingSphereTransactionType(TransactionType.XA)
    protected boolean deleteDb(ParmModificationRecord parmModificationRecord) {
        ParamSmsFee paramSmsFee = JSON.parseObject(parmModificationRecord.getParmBody(), ParamSmsFee.class);
        int res = paramSmsFeeMapper.deleteByPrimaryKey(paramSmsFee.getId());
        return res > 0;
    }
}
