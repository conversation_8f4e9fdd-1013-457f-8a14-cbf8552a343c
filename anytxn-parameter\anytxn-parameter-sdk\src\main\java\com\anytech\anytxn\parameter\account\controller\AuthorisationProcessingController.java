package com.anytech.anytxn.parameter.account.controller;

import io.swagger.v3.oas.annotations.tags.Tag;
import io.swagger.v3.oas.annotations.Operation;
import com.anytech.anytxn.common.core.base.ParameterCompare;
import com.anytech.anytxn.common.core.base.PageResultDTO;
import com.anytech.anytxn.common.core.base.BizBaseController;
import com.anytech.anytxn.common.core.base.AnyTxnHttpResponse;
import com.anytech.anytxn.parameter.base.account.domain.dto.AuthorisationProcessingReqDTO;
import com.anytech.anytxn.parameter.base.account.domain.dto.AuthorisationProcessingResDTO;
import com.anytech.anytxn.parameter.base.account.service.IAuthorisationProcessingService;
import com.anytech.anytxn.parameter.base.common.enums.ParameterRepDetailEnum;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import jakarta.validation.Valid;

/**
 * 授权处理参数接口
 * <AUTHOR>
 * @date 2018-11-26 11:22
 **/
@Tag(name = "授权处理参数")
@RestController
public class AuthorisationProcessingController extends BizBaseController {

    @Autowired
    private IAuthorisationProcessingService authorisationProcessingService;

    /**
     * @param organizationNumber 机构号
     * @param tableId 参数表id
     * @return CurrencyRateRes 授权处理参数表返回参数
     *
     */
    @Operation(summary = "通过机构号,参数表id查询授权处理参数", description = "通过机构号,参数表id查询授权处理参数")
    @GetMapping(value = "/param/AuthorisationProcessing/organizationNumber/{organizationNumber}/tableId/{tableId}")
    public AnyTxnHttpResponse<AuthorisationProcessingResDTO> getByOrgAndTableId(@PathVariable(value = "organizationNumber") String organizationNumber
            , @PathVariable(value = "tableId") String tableId) {
        AuthorisationProcessingResDTO res;
        res = authorisationProcessingService.findByOrgAndTableId(organizationNumber,tableId);
        return AnyTxnHttpResponse.success(res);

    }

    @Operation(summary = "新增授权处理参数", description = "新增授权处理参数")
    @PostMapping(value = "/param/AuthorisationProcessing")
    public AnyTxnHttpResponse<Object> create(@Valid @RequestBody AuthorisationProcessingReqDTO authorisationProcessingReqDTO) {
        ParameterCompare res = authorisationProcessingService.add(authorisationProcessingReqDTO);
        return AnyTxnHttpResponse.success(res, ParameterRepDetailEnum.CREATE.message());
    }

    @Operation(summary = "删除授权处理参数", description = "删除授权处理参数")
    @DeleteMapping(value = "/param/AuthorisationProcessing/id/{id}")
    public AnyTxnHttpResponse<Object> remove(@PathVariable(value = "id") String id) {
        ParameterCompare deleted = authorisationProcessingService.remove(id);
        return AnyTxnHttpResponse.success(deleted,ParameterRepDetailEnum.DEL.message());

    }

    @Operation(summary = "修改授权处理参数", description = "修改授权处理参数")
    @PutMapping(value = "/param/AuthorisationProcessing")
    public AnyTxnHttpResponse<Object> modify(@Valid @RequestBody AuthorisationProcessingReqDTO authorisationProcessingReqDTO) {
        ParameterCompare authorisationProcessingResDTO = authorisationProcessingService.modify(authorisationProcessingReqDTO);
        return AnyTxnHttpResponse.success(authorisationProcessingResDTO,ParameterRepDetailEnum.UPDATE.message());

    }

    @Operation(summary = "分页查询授权处理参数", description = "分页查询授权处理参数")
    @GetMapping(value = "/param/AuthorisationProcessing/pageNum/{pageNum}/pageSize/{pageSize}")
    public AnyTxnHttpResponse<PageResultDTO<AuthorisationProcessingResDTO>> getPage(@PathVariable(value = "pageNum")Integer pageNum,
                                                                                 @PathVariable(value = "pageSize")Integer pageSize,
                                                                                @RequestParam(value = "tableId",required = false) String tableId,
                                                                                @RequestParam(value = "description",required = false) String description,
                                                                                @RequestParam(value = "authorisationRemainDays",required = false) String authorisationRemainDays,
                                                                                @RequestParam(value = "authorisationMatchFlag",required = false) String authorisationMatchFlag,
                                                                                @RequestParam(value = "authorisationMatchAmount",required = false) String authorisationMatchAmount,
                                                                                @RequestParam(value = "authorisationMatchPer",required = false) String authorisationMatchPer,
                                                                                    @RequestParam(required = false) String organizationNumber) {
        PageResultDTO<AuthorisationProcessingResDTO> pageResultDto = authorisationProcessingService.findPage(pageNum, pageSize,tableId,description,authorisationRemainDays,authorisationMatchFlag,authorisationMatchAmount,authorisationMatchPer, organizationNumber);
        return AnyTxnHttpResponse.success(pageResultDto);

    }

    @Operation(summary = "根据id查询授权处理参数", description = "根据id查询授权处理参数")
    @GetMapping(value = "/param/AuthorisationProcessing/id/{id}")
    public AnyTxnHttpResponse<AuthorisationProcessingResDTO> getById(@PathVariable(value = "id") String id) {
        AuthorisationProcessingResDTO authorisationProcessingResDTO = authorisationProcessingService.findOne(id);
        return AnyTxnHttpResponse.success(authorisationProcessingResDTO);

    }
}
