package com.anytech.anytxn.parameter.common.service;

import com.anytech.anytxn.parameter.base.common.domain.dto.CurrencyConversionFeeDTO;
import com.anytech.anytxn.parameter.base.common.service.ICurrencyConversionFeeService;
import com.anytech.anytxn.parameter.base.common.constants.Constants;
import com.anytech.anytxn.parameter.base.common.enums.TransFeeEnum;
import com.anytech.anytxn.parameter.authorization.mapper.ParmTransFeeSelfMapper;
import com.anytech.anytxn.parameter.common.mapper.broadcast.system.ParmCurrencyRateSelfMapper;
import com.anytech.anytxn.parameter.common.mapper.broadcast.system.ParmOrganizationInfoSelfMapper;
import com.anytech.anytxn.parameter.common.mapper.unicast.system.ParmSysClassSelfMapper;
import com.anytech.anytxn.parameter.base.common.domain.model.unicast.ParmSysClass;
import com.anytech.anytxn.parameter.base.authorization.domain.model.ParmTransFee;
import com.anytech.anytxn.parameter.base.common.domain.model.system.ParmCurrencyRate;
import com.anytech.anytxn.parameter.base.common.domain.model.system.ParmOrganizationInfo;
import com.anytech.anytxn.common.core.enums.RuleTypeEnum;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.util.ObjectUtils;
import org.springframework.util.StringUtils;

import jakarta.annotation.Resource;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.HashSet;
import java.util.Map;
import java.util.Objects;
import java.util.Set;
import java.util.concurrent.ConcurrentHashMap;

/**
 * <pre>
 * Description: 货币转换费公用处理逻辑
 * Company:	    江融信
 * Version:	    1.0
 * Create at:   2022/12/7
 * <p>
 * 修改历史:
 * 日期    作者    版本  修改描述
 * ------------------------------------------------------------------
 * <AUTHOR>
 * </pre>
 */
@Service
@Slf4j
public class CurrencyConversionFeeServiceImpl implements ICurrencyConversionFeeService {
    @Resource
    private ParmCurrencyRateSelfMapper currencyRateSelfMapper;
    @Resource
    private ParmOrganizationInfoSelfMapper organizationInfoSelfMapper;
    @Resource
    private ParmSysClassSelfMapper sysClassSelfMapper;
    @Resource
    private ParmTransFeeSelfMapper parmTransFeeSelfMapper;


    /**
     * MC除还款/还款还原交易外其他所有借贷记交易都收markUpFee以及DCC费
     * UPI/DCI只有借记交易收markUpFee
     *
     * @param currencyConversionFeeDTO CurrencyConversionFeeDTO
     * @return CurrencyConversionFeeDTO
     */
    @Override
    public CurrencyConversionFeeDTO currencyConversionFeeProcess(
            CurrencyConversionFeeDTO currencyConversionFeeDTO) {
        String transactionCurrency = currencyConversionFeeDTO.getTransactionCurrency();
        String billingCurrency = currencyConversionFeeDTO.getBillingCurrency();
        String organizationNumber = currencyConversionFeeDTO.getOrganizationNumber();
        BigDecimal transactionAmount = currencyConversionFeeDTO.getTransactionAmount();
        String groupType = currencyConversionFeeDTO.getGroupType();
        String debitCreditIndicator = currencyConversionFeeDTO.getDebitCreditIndicator();
        String markUpFeeTableId = currencyConversionFeeDTO.getMarkUpFeeTableId();
        String dccFeeTableId = currencyConversionFeeDTO.getDccFeeTableId();
        String transactionSource = currencyConversionFeeDTO.getTransactionSource();
        String countryCode = currencyConversionFeeDTO.getCountryCode();
        String transactionAttribute = currencyConversionFeeDTO.getTransactionAttribute();
        BigDecimal labelMarkupFeeRate = currencyConversionFeeDTO.getMarkupFeeRate();
        BigDecimal labelDccFeeRate = currencyConversionFeeDTO.getDccFeeRate();
        currencyConversionFeeDTO.setDccFeeAmount(BigDecimal.ZERO);
        BigDecimal markupFeeAmount = BigDecimal.ZERO;
        BigDecimal billingAmount;
        //MC还款及还款还原交易不收markUpFee/DCC Fee
        if ("M".equals(transactionSource) && "7".equals(transactionAttribute)) {
            currencyConversionFeeDTO.setMarkupFeeAmount(BigDecimal.ZERO);
            currencyConversionFeeDTO.setDccFeeAmount(BigDecimal.ZERO);
            return currencyConversionFeeDTO;
        }
        //交易币种入账币种一致
        if (transactionCurrency.equals(billingCurrency)) {
            Set<String> txnSourceSet = new HashSet<>();
            txnSourceSet.add("M");txnSourceSet.add("V");
            if (!StringUtils.isEmpty(transactionSource) && txnSourceSet.contains(transactionSource)) {
                currencyConversionFeeDTO = dccFeeProcessor(currencyConversionFeeDTO, countryCode,
                        transactionCurrency, transactionAmount, dccFeeTableId, labelDccFeeRate);
            }

            currencyConversionFeeDTO.setBillingAmount(transactionAmount);
            currencyConversionFeeDTO.setMarkupFeeAmount(BigDecimal.ZERO);
            return currencyConversionFeeDTO;
        }
        String markUpFeeMergeFlag ="1";
        String markUpFeeTxnCode = null;
        if (Objects.nonNull(labelMarkupFeeRate) && labelMarkupFeeRate.compareTo(BigDecimal.ZERO) > 0){
            log.info("当前使用标签定价获取markupFee");
            billingAmount = currencyConversionFeeDTO.getBillingAmount();
            markupFeeAmount = billingAmount.multiply(labelMarkupFeeRate);
        } else {
            //交易币种和入账币种不一致
            //交易来源为UPI 直接拿入参中的入账金额
            boolean countryCodeFlag = !"702".equals(countryCode) && !"SGP".equals(countryCode);
            if (!StringUtils.isEmpty(transactionSource) && ("UPI".equals(transactionSource)
                    || ("M".equals(transactionSource) && (countryCodeFlag || currencyConversionFeeDTO.getCurrencyFlag()))||( "V".equals(transactionSource)&& countryCodeFlag))) {
                billingAmount = currencyConversionFeeDTO.getBillingAmount();
            } else {
                //否则进行汇率转换 系统汇率拿取的统一逻辑
                ParmCurrencyRate parmCurrencyRate = currencyRateSelfMapper.selectByOrgAndCurrencyAndRateType(
                        organizationNumber, transactionCurrency, billingCurrency, "0");
                if (parmCurrencyRate == null) {
                    log.error("交易币种:{},入账币种:{} 未在汇率参数表配置", transactionCurrency, billingCurrency);
                    return null;
                }
                //交易币种兑入账币种汇率
                // 拿到汇率的小数位的10次幂
                BigDecimal divisor = BigDecimal.valueOf(Math.pow(10, parmCurrencyRate.getExponent()));
                BigDecimal exchangeRateMid = new BigDecimal(parmCurrencyRate.getRateValue());
                // 汇率保留 parmCurrencyRate.getExponent()小数
                BigDecimal exchangeRate = exchangeRateMid.divide(divisor, parmCurrencyRate.getExponent(), RoundingMode.HALF_UP);
                //入账金额
                billingAmount = transactionAmount.multiply(exchangeRate);
            }
            String markupFeeInd = "";
            BigDecimal markupFeeRate = BigDecimal.ZERO;
            boolean b = "D".equals(debitCreditIndicator) ||
                    ("C".equals(debitCreditIndicator) && "M".equals(transactionSource));
            //如果入参货币转换费表ID为空 以机构参数表相关货币转换字段兜底
            if (StringUtils.isEmpty(markUpFeeTableId)) {
                ParmOrganizationInfo organizationInfo =
                        organizationInfoSelfMapper.selectByOrganizationNumber(organizationNumber);
                if (organizationInfo == null) {
                    log.info("该机构:{}在系统中不存在", organizationNumber);
                    return null;
                }
                if (b) {
                    //系统员工参数
                    ParmSysClass sysClass = sysClassSelfMapper.selectBySysClass(groupType);
                    if (sysClass == null) {
                        log.error("客群:{} 未在系统员工参数配置", groupType);
                        return null;
                    }
                    String sysClassStaffInd = sysClass.getSysClassStaffInd() == null ? ""
                            : sysClass.getSysClassStaffInd();
                    if ("Y".equals(sysClassStaffInd)) {
                        markupFeeInd = organizationInfo.getMcMarkupFeeInd();
                        markupFeeRate = organizationInfo.getMcMarkupFeeRate();
                    } else {
                        markupFeeInd = organizationInfo.getMarkupFeeInd();
                        markupFeeRate = organizationInfo.getMarkupFeeRate();
                    }
                }
            } else {
                ParmTransFee markUpFee = parmTransFeeSelfMapper.selectByOrgAndCode(organizationNumber,markUpFeeTableId, TransFeeEnum.getFeeType(RuleTypeEnum.MARK_UP_FEE_RULE.getRuleType()));
                if (markUpFee == null) {
                    log.error("货币兑换表ID{}查询货币兑换参数 数据不存在", markUpFeeTableId);
                    return null;
                }
                if (b) {
                    markupFeeInd = markUpFee.getChargeFlag();
                    markupFeeRate = markUpFee.getFeeRate();
                    markUpFeeMergeFlag = markUpFee.getFeeMergeFlag();
                    markUpFeeTxnCode = markUpFee.getTransactionCode();

                }
            }
            if (Objects.nonNull(labelMarkupFeeRate) && labelMarkupFeeRate.compareTo(BigDecimal.ZERO) > 0) {
                markupFeeRate = labelMarkupFeeRate;
            }
            if ("1".equals(markupFeeInd) && markupFeeRate.compareTo(BigDecimal.ZERO) > 0) {
                markupFeeAmount = billingAmount.multiply(markupFeeRate);
            }
        }
        //无论收不收mkupfee，只要做了货币转换就都在这赋值后返回
        currencyConversionFeeDTO.setBillingAmount(billingAmount.setScale(2,
                RoundingMode.HALF_UP));
        currencyConversionFeeDTO.setMarkupFeeAmount(markupFeeAmount.setScale(2,
                RoundingMode.HALF_UP));
        currencyConversionFeeDTO.setMarkUpFeeMergeFlag(markUpFeeMergeFlag);
        currencyConversionFeeDTO.setMarkUpFeeTxnCode(markUpFeeTxnCode);
        return currencyConversionFeeDTO;
    }

    private CurrencyConversionFeeDTO dccFeeProcessor(CurrencyConversionFeeDTO currencyConversionFeeDTO,
                                                     String countryCode, String transactionCurrency,
                                                     BigDecimal transactionAmount, String dccFeeTableId,
                                                     BigDecimal labelDccFeeRate) {
        if ("702".equals(transactionCurrency)) {
            Map<String, String> currencyMap = new ConcurrentHashMap<>();
            currencyMap.put("SGP", "702");
            currencyMap.put("SG", "702");
            if (!currencyMap.containsKey(countryCode) && !"702".equals(countryCode)) {
                dccFeeCalculation(currencyConversionFeeDTO,countryCode,transactionCurrency,transactionAmount,dccFeeTableId,labelDccFeeRate);
            }
        }
        if ("840".equals(transactionCurrency)) {
            dccFeeCalculation(currencyConversionFeeDTO,countryCode,transactionCurrency,transactionAmount,dccFeeTableId,labelDccFeeRate);
        }
        return currencyConversionFeeDTO;
    }
    private CurrencyConversionFeeDTO dccFeeCalculation(CurrencyConversionFeeDTO currencyConversionFeeDTO,
                                             String countryCode, String transactionCurrency,
                                             BigDecimal transactionAmount, String dccFeeTableId,
                                             BigDecimal labelDccFeeRate){
        if (!StringUtils.isEmpty(dccFeeTableId)) {
            ParmTransFee parmDccFeeInfo = parmTransFeeSelfMapper.selectByOrgAndCode(currencyConversionFeeDTO.getOrganizationNumber(),dccFeeTableId,TransFeeEnum.getFeeType(TransFeeEnum.getFeeType(RuleTypeEnum.DCC_FEE_RULE.getRuleType())));
            BigDecimal dccRate = ObjectUtils.isEmpty(parmDccFeeInfo) ?
                    BigDecimal.ONE : parmDccFeeInfo.getFeeRate();
            if (Objects.nonNull(labelDccFeeRate)
                    && labelDccFeeRate.compareTo(BigDecimal.ZERO) > 0) {
                dccRate = labelDccFeeRate;
            }
            BigDecimal dccFee = transactionAmount.multiply(dccRate).add(Objects.isNull(parmDccFeeInfo.getSurcharge()) ? BigDecimal.ZERO : parmDccFeeInfo.getSurcharge()).setScale(2,
                    RoundingMode.HALF_UP);
            String dccTxnCode = parmDccFeeInfo.getTransactionCode();
            currencyConversionFeeDTO.setDccFeeAmount(dccFee);
            currencyConversionFeeDTO.setDccTxnCode(dccTxnCode);
            currencyConversionFeeDTO.setDccFeeMergeFlag(parmDccFeeInfo.getFeeMergeFlag());
            return currencyConversionFeeDTO;
        } else {
            log.error("mc清算交易，交易币种：{},国家码：{} 的dcc费匹配不到dcc费率规则，请检查配置参数",
                    transactionCurrency,
                    countryCode);
            return null;
        }
    }

    @Override
    public CurrencyConversionFeeDTO paymentFeeProcess(CurrencyConversionFeeDTO currencyConversionFeeDTO) {
        String organizationNumber = currencyConversionFeeDTO.getOrganizationNumber();
        String groupType = currencyConversionFeeDTO.getGroupType();
        String markUpFeeTableId = currencyConversionFeeDTO.getMarkUpFeeTableId();
        BigDecimal labelMarkupFeeRate = currencyConversionFeeDTO.getMarkupFeeRate();
        BigDecimal billingAmount = currencyConversionFeeDTO.getBillingAmount();
        String markupFeeInd;
        BigDecimal markupFeeRate;
        //是否合并 1-合并
        String markUpFeeMergeFlag = Constants.ENABLED;
        String markUpFeeTxnCode = null;

        //如果入参货币转换费表ID为空 以机构参数表相关货币转换字段兜底
        if (StringUtils.isEmpty(markUpFeeTableId)) {
            ParmOrganizationInfo organizationInfo = organizationInfoSelfMapper.selectByOrganizationNumber(organizationNumber);
            if (organizationInfo == null) {
                return null;
            }

            //系统员工参数
            ParmSysClass sysClass = sysClassSelfMapper.selectBySysClass(groupType);
            if (sysClass == null) {
                log.error("客群:{} 未在系统员工参数配置", groupType);
                return null;
            }

            String sysClassStaffInd = sysClass.getSysClassStaffInd() == null ? "" : sysClass.getSysClassStaffInd();

            if ("Y".equals(sysClassStaffInd)) {
                markupFeeInd = organizationInfo.getMcMarkupFeeInd();
                markupFeeRate = organizationInfo.getMcMarkupFeeRate();
            } else {
                markupFeeInd = organizationInfo.getMarkupFeeInd();
                markupFeeRate = organizationInfo.getMarkupFeeRate();
            }

        } else {

            ParmTransFee markUpFee = parmTransFeeSelfMapper.selectByOrgAndCode(organizationNumber, markUpFeeTableId,TransFeeEnum.getFeeType(RuleTypeEnum.MARK_UP_FEE_RULE.getRuleType()));
            if (markUpFee == null) {
                log.error("根据机构号货币兑换表ID{}查询货币兑换参数 数据不存在", markUpFeeTableId);
                return null;
            }

            markupFeeInd = markUpFee.getChargeFlag();
            markupFeeRate = markUpFee.getFeeRate();
            markUpFeeMergeFlag = markUpFee.getFeeMergeFlag();
            markUpFeeTxnCode = markUpFee.getTransactionCode();

        }

        if (Objects.nonNull(labelMarkupFeeRate) && labelMarkupFeeRate.compareTo(BigDecimal.ZERO) > 0) {
            markupFeeRate = labelMarkupFeeRate;
        }

        BigDecimal markupFeeAmount = BigDecimal.ZERO;
        if ("1".equals(markupFeeInd) && markupFeeRate.compareTo(BigDecimal.ZERO) > 0) {
            markupFeeAmount = billingAmount.multiply(markupFeeRate);
        }

        currencyConversionFeeDTO.setBillingAmount(billingAmount.setScale(2, RoundingMode.HALF_UP));
        currencyConversionFeeDTO.setMarkupFeeAmount(markupFeeAmount.setScale(2, RoundingMode.HALF_UP));
        currencyConversionFeeDTO.setMarkupFeeRate(markupFeeRate);
        currencyConversionFeeDTO.setMarkUpFeeMergeFlag(markUpFeeMergeFlag);
        currencyConversionFeeDTO.setMarkUpFeeTxnCode(markUpFeeTxnCode);
        return currencyConversionFeeDTO;
    }

}
