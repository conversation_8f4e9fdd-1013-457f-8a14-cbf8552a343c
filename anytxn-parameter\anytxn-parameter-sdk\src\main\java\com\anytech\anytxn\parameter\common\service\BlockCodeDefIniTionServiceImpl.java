package com.anytech.anytxn.parameter.common.service;

import com.alibaba.fastjson.JSON;
import com.anytech.anytxn.parameter.base.common.domain.dto.BlockCodePlasticReqDTO;
import com.anytech.anytxn.parameter.base.common.domain.dto.BlockCodePlasticResDTO;
import com.anytech.anytxn.parameter.base.common.service.IBlockCodeDefIniTionService;
import com.anytech.anytxn.parameter.base.account.domain.dto.BlockCodeAccountReqDTO;
import com.anytech.anytxn.parameter.base.account.domain.dto.BlockCodeAccountResDTO;
import com.anytech.anytxn.parameter.base.card.domain.dto.BlockCodeDefinitionReqDTO;
import com.anytech.anytxn.parameter.base.card.domain.dto.BlockCodeDefinitionResDTO;
import com.anytech.anytxn.parameter.base.common.constants.Constants;
import com.anytech.anytxn.parameter.base.common.constants.ParamConstant;
import com.anytech.anytxn.parameter.base.common.enums.AnyTxnParameterRespCodeEnum;
import com.anytech.anytxn.parameter.base.common.exception.AnyTxnParameterException;
import com.anytech.anytxn.parameter.common.mapper.broadcast.ParmBlockCodeDefinitionMapper;
import com.anytech.anytxn.parameter.common.mapper.broadcast.ParmBlockCodeDefinitionSelfMapper;
import com.anytech.anytxn.parameter.common.mapper.broadcast.ParmBlockCodePlasticMapper;
import com.anytech.anytxn.parameter.common.mapper.broadcast.ParmBlockCodePlasticSelfMapper;
import com.anytech.anytxn.parameter.account.mapper.ParmBlockCodeAccountMapper;
import com.anytech.anytxn.parameter.account.mapper.ParmBlockCodeAccountSelfMapper;
import com.anytech.anytxn.parameter.base.account.domain.model.ParmBlockCodeAccount;
import com.anytech.anytxn.parameter.base.account.domain.model.ParmBlockCodeDefinition;
import com.anytech.anytxn.parameter.base.account.domain.model.ParmBlockCodePlastic;
import com.anytech.anytxn.parameter.base.common.domain.dto.BlockCodeCustomerReqDTO;
import com.github.pagehelper.Page;
import com.github.pagehelper.PageHelper;
import com.anytech.anytxn.common.core.annotation.db.DeleteParameterAnnotation;
import com.anytech.anytxn.common.core.annotation.db.InsertParameterAnnotation;
import com.anytech.anytxn.common.core.annotation.db.UpdateParameterAnnotation;
import com.anytech.anytxn.business.base.audit.AbstractParameterService;
import com.anytech.anytxn.common.core.base.ParameterCompare;
import com.anytech.anytxn.business.base.audit.ParmModificationRecord;
import com.anytech.anytxn.common.core.base.PageResultDTO;
import com.anytech.anytxn.common.core.utils.BeanMapping;
import com.anytech.anytxn.common.core.utils.OrgNumberUtils;
import com.anytech.anytxn.common.core.utils.TenantUtils;
import com.anytech.anytxn.common.sequence.utils.Number16IdGen;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.HashSet;
import java.util.List;
import java.util.Set;


/**
 * Description   封锁码 业务接口
 * Copyright:	Copyright (c) 2018
 * Company:		江融信
 * Author:		lichao
 * Version:		1.0
 * Create at:	2018/9/27 下午5:57
 * <p>
 * 修改历史:
 * 日期    作者    版本  修改描述
 * ------------------------------------------------------------------
 **/
@Service(value = "parm_block_code_definition_serviceImpl")
public class BlockCodeDefIniTionServiceImpl extends AbstractParameterService implements IBlockCodeDefIniTionService {
    private final Logger logger = LoggerFactory.getLogger(BlockCodeDefIniTionServiceImpl.class);

    @Autowired
    private ParmBlockCodeDefinitionMapper blockCodeDefinitionMapper;
    @Autowired
    private ParmBlockCodeDefinitionSelfMapper blockCodeDefinitionSelfMapper;
    @Autowired
    private ParmBlockCodeAccountMapper blockCodeAccountMapper;
    @Autowired
    private ParmBlockCodeAccountSelfMapper blockCodeAccountSelfMapper;
    @Autowired
    private ParmBlockCodePlasticMapper blockCodePlasticMapper;
    @Autowired
    private ParmBlockCodePlasticSelfMapper blockCodePlasticSelfMapper;

    @Autowired
    private Number16IdGen numberIdGenerator;


    /**
     * 添加封锁码
     *
     * @param req 各个层级封锁码入参对象
     * @return 各个层级封锁码详情
     * @throws AnyTxnParameterException
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    @InsertParameterAnnotation(tableName = "parm_block_code_definition", tableDesc = "Block Code", isJoinTable = true)
    public ParameterCompare add(BlockCodeDefinitionReqDTO req) {
        boolean isExists = blockCodeDefinitionSelfMapper.isExists(req.getOrganizationNumber(), req.getTableId(), req.getCapIndicator()) > 0;
        if (isExists) {
            logger.warn("参数表ID和封锁码已存在, orgNumber={}, tableId={}, capIndicator={}",
                    req.getOrganizationNumber(), req.getTableId(), req.getCapIndicator());
            throw new AnyTxnParameterException(AnyTxnParameterRespCodeEnum.P_EXIST_BLOCK_CODE_AND_ID_FAULT);
        }
        //验证是否存在重复的封锁码
        checkBlockCode(req);
        return ParameterCompare.getBuilder()
                .withMainParmId(req.getTableId())
                .withAfter(req)
                .build(BlockCodeDefinitionReqDTO.class);
    }

    /**
     * @param req 请求参数
     * @throws AnyTxnParameterException 异常
     */
    private void checkBlockCode(BlockCodeDefinitionReqDTO req) {
        if (ParamConstant.BlockLevel.BLOCK_LEVEL_A.equals(req.getCapIndicator())) {
            List<BlockCodeAccountReqDTO> blockCodeAccountReqDtos = req.getBlockCodeAccountReqList();
            Set<String> accountSet = new HashSet<>();
            int size = CollectionUtils.isEmpty(blockCodeAccountReqDtos) ? 0 : blockCodeAccountReqDtos.size();
            for (BlockCodeAccountReqDTO blockCodeAccountReqDTO : blockCodeAccountReqDtos) {
                if (!StringUtils.isEmpty(blockCodeAccountReqDTO.getBlockCode())) {
                    accountSet.add(blockCodeAccountReqDTO.getBlockCode());
                } else {
                    size = size - 1;
                }
            }
            if (size > accountSet.size()) {
                throw new AnyTxnParameterException(AnyTxnParameterRespCodeEnum.D_REPEAT_PARM_BLOCK_CODE_CUSTOMER_FAULT);
            }
        } else {
            List<BlockCodePlasticReqDTO> blockCodePlasticReqs = req.getBlockCodePlasticReqList();
            Set<String> plasticSet = new HashSet<>();
            for (BlockCodePlasticReqDTO blockCodePlasticReq : blockCodePlasticReqs) {
                plasticSet.add(blockCodePlasticReq.getPriority() + "");
            }
            if (blockCodePlasticReqs.size() > plasticSet.size()) {
                throw new AnyTxnParameterException(AnyTxnParameterRespCodeEnum.D_REPEAT_PARM_BLOCK_CODE_CUSTOMER_FAULT);

            }
        }
    }


    /**
     * @Description: 保存各个层级封锁码信息
     * @Param:
     * @return:
     * @Date: 2018/9/28
     **/
    @Transactional(rollbackFor = Exception.class)
    public void saveBlockCodeLevel(ParmBlockCodeDefinition blockCodeDefinition, BlockCodeDefinitionReqDTO req) {
        try {
            if (ParamConstant.BlockLevel.BLOCK_LEVEL_A.equals(req.getCapIndicator())) {
                req.getBlockCodeAccountReqList().stream().forEach(blockCodeAccountReq -> {
                    ParmBlockCodeAccount account = BeanMapping.copy(blockCodeAccountReq, ParmBlockCodeAccount.class);
                    account.setTableId(blockCodeDefinition.getTableId());
                    account.setOrganizationNumber(blockCodeDefinition.getOrganizationNumber());
                    account.setCreateTime(LocalDateTime.now());
                    account.setUpdateTime(LocalDateTime.now());
                    account.setUpdateBy(Constants.DEFAULT_USER);
                    account.setVersionNumber(1L);
                    account.setStatus(Constants.ENABLED);
                    account.setRepaymentIndicator(blockCodeAccountReq.getRepaymentIndicator());
                    account.setId(numberIdGenerator.generateId(TenantUtils.getTenantId()));
                    blockCodeAccountMapper.insertSelective(account);
                });
            } else {//保存卡层级
                req.getBlockCodePlasticReqList().stream().forEach(blockCodePlasticReq -> {
                    ParmBlockCodePlastic plastic = BeanMapping.copy(blockCodePlasticReq, ParmBlockCodePlastic.class);
                    plastic.setTableId(blockCodeDefinition.getTableId());
                    plastic.setOrganizationNumber(blockCodeDefinition.getOrganizationNumber());
                    plastic.setCreateTime(LocalDateTime.now());
                    plastic.setUpdateTime(LocalDateTime.now());
                    plastic.setUpdateBy(Constants.DEFAULT_USER);
                    plastic.setVersionNumber(1);
                    plastic.setStatus(Constants.ENABLED);
                    plastic.setId(numberIdGenerator.generateId(TenantUtils.getTenantId()));
                    blockCodePlasticMapper.insertSelective(plastic);
                });
            }
        } catch (Exception e) {
            logger.error("Cause:{}\nMessage:{}", e.getCause(), e.getMessage());
            throw new AnyTxnParameterException(AnyTxnParameterRespCodeEnum.S_SAVE_BLOCK_CODE_PLASTIC_FAULT);


        }
    }

    /**
     * 修改各層級封锁码信息
     *
     * @param req 層級封锁码入参对象
     * @return 介质层封锁码详情
     * @throws AnyTxnParameterException
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    @UpdateParameterAnnotation(tableName = "parm_block_code_definition", tableDesc = "Block Code", isJoinTable = true)
    public ParameterCompare modify(BlockCodeDefinitionReqDTO req) {
        if (req.getId() == null) {
            throw new AnyTxnParameterException(AnyTxnParameterRespCodeEnum.P_BLOCK_CODE_DEFINITION_FAULT);
        }

        BlockCodeDefinitionResDTO blockCodeDefinitionResDTO = find(req.getId());
        if (blockCodeDefinitionResDTO == null) {
            logger.error("修改{}封锁码, 通过主键id({})未找到数据", req.getCapIndicator(), req.getId());
            throw new AnyTxnParameterException(AnyTxnParameterRespCodeEnum.P_QUERY_CODE_DEFINITION_FAULT);
        }
        BlockCodeDefinitionReqDTO codeDefinitionReqDTO = BeanMapping.copy(blockCodeDefinitionResDTO, BlockCodeDefinitionReqDTO.class);
        codeDefinitionReqDTO.setBlockCodePlasticReqList(BeanMapping.copyList(blockCodeDefinitionResDTO.getBlockCodePlasticResList(), BlockCodePlasticReqDTO.class));
        codeDefinitionReqDTO.setBlockCodeCustomerReqList(BeanMapping.copyList(blockCodeDefinitionResDTO.getBlockCodeCustomerResList(), BlockCodeCustomerReqDTO.class));
        codeDefinitionReqDTO.setBlockCodeAccountReqList(BeanMapping.copyList(blockCodeDefinitionResDTO.getBlockCodeAccountResList(), BlockCodeAccountReqDTO.class));
        //验证是否存在重复的封锁码
        checkBlockCode(req);

        return ParameterCompare.getBuilder()
                .withMainParmId(req.getTableId())
                .withAfter(req)
                .withBefore(codeDefinitionReqDTO)
                .build(BlockCodeDefinitionReqDTO.class);
    }


    /**
     * 分页查询各层級封锁码，通过blockCodeLevel層級
     *
     * @param pageNum        页号
     * @param pageSize       每页大小
     * @param blockCodeLevel 封锁码层级
     * @return 分页的各层級封锁码詳情
     * @throws AnyTxnParameterException
     */
    @Override
    public PageResultDTO<BlockCodeDefinitionResDTO> findPageByBlockCodeLevel(Integer pageNum, Integer pageSize, String blockCodeLevel, String tableId, String description, String organizationNumber) {
        logger.info("分页查询介质层封锁码, pageNum={}, pageSize={}, blockCodeLevel={}", pageNum, pageSize, blockCodeLevel);

        Page page = PageHelper.startPage(pageNum, pageSize);
        organizationNumber = StringUtils.isEmpty(organizationNumber) ? OrgNumberUtils.getOrg() : organizationNumber;
        List<ParmBlockCodeDefinition> dataList = blockCodeDefinitionSelfMapper.selectPageByBlockCodeLevel(blockCodeLevel, tableId, description, false, organizationNumber);
        List<BlockCodeDefinitionResDTO> listData = BeanMapping.copyList(dataList, BlockCodeDefinitionResDTO.class);

        return new PageResultDTO(pageNum, pageSize, page.getTotal(), page.getPages(), listData);
    }


    /**
     * 获取层封锁码详情，通过id
     *
     * @param id 层封锁码ID
     * @return 层封锁码详情
     * @throws AnyTxnParameterException
     */
    @Override
    public BlockCodeDefinitionResDTO find(String id) {
        if (id == null) {
            throw new AnyTxnParameterException(AnyTxnParameterRespCodeEnum.P_PARAMETER_EMPTY_FAULT);
        }
        BlockCodeDefinitionResDTO res = new BlockCodeDefinitionResDTO();
        ParmBlockCodeDefinition blockCodeDefinition = blockCodeDefinitionMapper.selectByPrimaryKey(id);
        if (blockCodeDefinition == null) {
            logger.error("获取客户层封锁码详情, 通过主键id({})未找到数据", id);
            throw new AnyTxnParameterException(AnyTxnParameterRespCodeEnum.P_BLOCK_CODE_CUSTOMER_BY_ID_FAULT);
        }
        BeanMapping.copy(blockCodeDefinition, res);
        List<BlockCodeAccountResDTO> blockCodeAccountRes = new ArrayList<>();
        List<BlockCodePlasticResDTO> blockCodePlasticRes = new ArrayList<>();
        if (ParamConstant.BlockLevel.BLOCK_LEVEL_A.equals(blockCodeDefinition.getCapIndicator())) {
            List<ParmBlockCodeAccount> blockCodeAccountList = blockCodeAccountSelfMapper.selectByOrgNumAndTableId(blockCodeDefinition.getOrganizationNumber(), blockCodeDefinition.getTableId());
            if (!blockCodeAccountList.isEmpty()) {
                blockCodeAccountRes = BeanMapping.copyList(blockCodeAccountList, BlockCodeAccountResDTO.class);
            }
        } else if (ParamConstant.BlockLevel.BLOCK_LEVEL_P.equals(blockCodeDefinition.getCapIndicator())) {
            List<ParmBlockCodePlastic> blockCodePlasticList = blockCodePlasticSelfMapper.selectByOrgNumAndTableId(blockCodeDefinition.getOrganizationNumber(), blockCodeDefinition.getTableId());
            if (!blockCodePlasticList.isEmpty()) {
                blockCodePlasticRes = BeanMapping.copyList(blockCodePlasticList, BlockCodePlasticResDTO.class);
            }
        }
        res.setBlockCodeAccountResList(blockCodeAccountRes);
        res.setBlockCodePlasticResList(blockCodePlasticRes);
        return res;
    }

    @Override
    public BlockCodeDefinitionResDTO findByOrgAndTableId(String organizationNumber, String tableId) {
        if (StringUtils.isEmpty(organizationNumber) || StringUtils.isEmpty(tableId)) {
            throw new AnyTxnParameterException(AnyTxnParameterRespCodeEnum.P_PARAMETER_EMPTY_FAULT);
        }
        BlockCodeDefinitionResDTO res = new BlockCodeDefinitionResDTO();
        ParmBlockCodeDefinition blockCodeDefinition = blockCodeDefinitionSelfMapper.selectByOrgAndTableId(organizationNumber, tableId);
        if (blockCodeDefinition == null) {
            logger.error("获取客户层封锁码详情, 通过机构号：({})，参数表id：{}未找到数据", organizationNumber, tableId);
            throw new AnyTxnParameterException(AnyTxnParameterRespCodeEnum.P_BLOCK_CODE_CUSTOMER_BY_ID_FAULT);
        }
        BeanMapping.copy(blockCodeDefinition, res);
        List<BlockCodeAccountResDTO> blockCodeAccountRes = new ArrayList<>();
        List<BlockCodePlasticResDTO> blockCodePlasticRes = new ArrayList<>();
        if (ParamConstant.BlockLevel.BLOCK_LEVEL_A.equals(blockCodeDefinition.getCapIndicator())) {
            List<ParmBlockCodeAccount> blockCodeAccountList = blockCodeAccountSelfMapper.selectByOrgNumAndTableId(blockCodeDefinition.getOrganizationNumber(), blockCodeDefinition.getTableId());
            if (!blockCodeAccountList.isEmpty()) {
                blockCodeAccountRes = BeanMapping.copyList(blockCodeAccountList, BlockCodeAccountResDTO.class);
            }
        } else if (ParamConstant.BlockLevel.BLOCK_LEVEL_P.equals(blockCodeDefinition.getCapIndicator())) {
            List<ParmBlockCodePlastic> blockCodePlasticList = blockCodePlasticSelfMapper.selectByOrgNumAndTableId(blockCodeDefinition.getOrganizationNumber(), blockCodeDefinition.getTableId());
            if (!blockCodePlasticList.isEmpty()) {
                blockCodePlasticRes = BeanMapping.copyList(blockCodePlasticList, BlockCodePlasticResDTO.class);
            }
        }
        res.setBlockCodeAccountResList(blockCodeAccountRes);
        res.setBlockCodePlasticResList(blockCodePlasticRes);
        return res;
    }


    @Override
    public BlockCodeDefinitionResDTO findByOrgAndTableIdAndBlockCodeLevel(String organizationNumber, String tableId,String balckCodeLevel) {
        if (StringUtils.isEmpty(organizationNumber) || StringUtils.isEmpty(tableId)) {
            throw new AnyTxnParameterException(AnyTxnParameterRespCodeEnum.P_PARAMETER_EMPTY_FAULT);
        }
        BlockCodeDefinitionResDTO res = new BlockCodeDefinitionResDTO();
        ParmBlockCodeDefinition blockCodeDefinition = blockCodeDefinitionSelfMapper.selectByOrgAndTableIdAndBlockCodeLevel(organizationNumber, tableId,balckCodeLevel);
        if (blockCodeDefinition == null) {
            logger.error("获取客户层封锁码详情, 通过机构号：({})，参数表id：{}未找到数据", organizationNumber, tableId);
            throw new AnyTxnParameterException(AnyTxnParameterRespCodeEnum.P_BLOCK_CODE_CUSTOMER_BY_ID_FAULT);
        }
        BeanMapping.copy(blockCodeDefinition, res);
        List<BlockCodeAccountResDTO> blockCodeAccountRes = new ArrayList<>();
        List<BlockCodePlasticResDTO> blockCodePlasticRes = new ArrayList<>();
        if (ParamConstant.BlockLevel.BLOCK_LEVEL_A.equals(blockCodeDefinition.getCapIndicator())) {
            List<ParmBlockCodeAccount> blockCodeAccountList = blockCodeAccountSelfMapper.selectByOrgNumAndTableId(blockCodeDefinition.getOrganizationNumber(), blockCodeDefinition.getTableId());
            if (!blockCodeAccountList.isEmpty()) {
                blockCodeAccountRes = BeanMapping.copyList(blockCodeAccountList, BlockCodeAccountResDTO.class);
            }
        } else if (ParamConstant.BlockLevel.BLOCK_LEVEL_P.equals(blockCodeDefinition.getCapIndicator())) {
            List<ParmBlockCodePlastic> blockCodePlasticList = blockCodePlasticSelfMapper.selectByOrgNumAndTableId(blockCodeDefinition.getOrganizationNumber(), blockCodeDefinition.getTableId());
            if (!blockCodePlasticList.isEmpty()) {
                blockCodePlasticRes = BeanMapping.copyList(blockCodePlasticList, BlockCodePlasticResDTO.class);
            }
        }
        res.setBlockCodeAccountResList(blockCodeAccountRes);
        res.setBlockCodePlasticResList(blockCodePlasticRes);
        return res;
    }

    /**
     * 删除各层级封锁码，通过id先删除定义表信息
     *
     * @param id 定义表封锁码ID
     * @return true:成功|false:失败
     * @throws AnyTxnParameterException
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    @DeleteParameterAnnotation(tableName = "parm_block_code_definition", tableDesc = "Block Code", isJoinTable = true)
    public ParameterCompare remove(String id) {
        if (id == null) {
            throw new AnyTxnParameterException(AnyTxnParameterRespCodeEnum.P_PARAMETER_EMPTY_FAULT);
        }
        ParmBlockCodeDefinition blockCodeDefinition = blockCodeDefinitionMapper.selectByPrimaryKey(id);
        if (blockCodeDefinition == null) {
            logger.error("删除封锁码定义表信息, 通过主键id({})未找到数据", id);
            throw new AnyTxnParameterException(AnyTxnParameterRespCodeEnum.D_DELETE_BLOCK_CODE_DEFINITION_FAULT);
        }

        return ParameterCompare.getBuilder()
                .withMainParmId(blockCodeDefinition.getTableId())
                .withBefore(blockCodeDefinition)
                .build(ParmBlockCodeDefinition.class);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean removeByOrgAndTableId(String organizationNumber, String tableId) {
        if (StringUtils.isEmpty(organizationNumber) || StringUtils.isEmpty(tableId)) {
            throw new AnyTxnParameterException(AnyTxnParameterRespCodeEnum.P_PARAMETER_EMPTY_FAULT);
        }
        int deleteRow;
        ParmBlockCodeDefinition blockCodeDefinition = blockCodeDefinitionSelfMapper.selectByOrgAndTableId(organizationNumber, tableId);
        if (blockCodeDefinition == null) {
            logger.error("删除封锁码定义表信息, 通过机构号({}),参数表id:{}未找到数据", organizationNumber, tableId);
            throw new AnyTxnParameterException(AnyTxnParameterRespCodeEnum.D_DELETE_BLOCK_CODE_DEFINITION_FAULT);
        }
        try {
            //先删除各个层级的标信息根据tableId和机构编号
            deleteBlockLvelInfo(blockCodeDefinition);
            //删除主表定义表信息
            logger.warn("通过id删除账户层封锁码, {}", blockCodeDefinition);
            deleteRow = blockCodeDefinitionMapper.deleteByPrimaryKey(blockCodeDefinition.getId());
        } catch (Exception e) {
            logger.error("通过id删除各层级封锁码失败, {}", e.getMessage());
            throw new AnyTxnParameterException(AnyTxnParameterRespCodeEnum.D_DELETE_LEVEL_BLOCK_CODE_DEFINITION_FAULT);
        }

        return deleteRow > 0;
    }


    /**
     * 删除各层级封锁码，通过id先删除定义表信息
     *
     * @param blockCodeDefinition 定义表封锁码blockCodeDefinition
     * @return true:成功|false:失败
     * @throws AnyTxnParameterException
     */
    @Transactional(rollbackFor = Exception.class)
    public void deleteBlockLvelInfo(ParmBlockCodeDefinition blockCodeDefinition) {
        try {
            if (ParamConstant.BlockLevel.BLOCK_LEVEL_A.equals(blockCodeDefinition.getCapIndicator())) {
                blockCodeAccountSelfMapper.deleteByOrgNoAndTableId(blockCodeDefinition.getTableId(), blockCodeDefinition.getOrganizationNumber());
            } else {
                blockCodePlasticSelfMapper.deleteByOrgNoAndTableId(blockCodeDefinition.getTableId(), blockCodeDefinition.getOrganizationNumber());
            }
        } catch (Exception e) {
            throw new AnyTxnParameterException(AnyTxnParameterRespCodeEnum.D_DELETE_LEVEL_BLOCK_CODE_DEFINITION_BY_ID_FAULT);

        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class, propagation = Propagation.REQUIRED)
    //@ShardingSphereTransactionType(TransactionType.XA)
    protected boolean updateDb(ParmModificationRecord parmModificationRecord) {
        BlockCodeDefinitionReqDTO req = JSON.parseObject(parmModificationRecord.getParmBody(), BlockCodeDefinitionReqDTO.class);
        ParmBlockCodeDefinition blockCodeDefinition = blockCodeDefinitionMapper.selectByPrimaryKey(req.getId());
        // 拷贝修改的数据并更新
        BeanMapping.copy(req, blockCodeDefinition);
        blockCodeDefinition.initUpdateDateTime();
        blockCodeDefinition.setUpdateBy(parmModificationRecord.getApplicationBy());
        blockCodeDefinitionMapper.updateByPrimaryKeySelective(blockCodeDefinition);
        //先删除层级
        deleteBlockLvelInfo(blockCodeDefinition);
        //保存账户层级
        saveBlockCodeLevel(blockCodeDefinition, req);
        return true;
    }

    @Override
    @Transactional(rollbackFor = Exception.class, propagation = Propagation.REQUIRED)
    //@ShardingSphereTransactionType(TransactionType.XA)
    protected boolean insertDb(ParmModificationRecord parmModificationRecord) {
        BlockCodeDefinitionReqDTO req = JSON.parseObject(parmModificationRecord.getParmBody(), BlockCodeDefinitionReqDTO.class);
        ParmBlockCodeDefinition blockCodeDefinition = BeanMapping.copy(req, ParmBlockCodeDefinition.class);
        blockCodeDefinition.setId(String.valueOf(numberIdGenerator.generateId(TenantUtils.getTenantId())));
        blockCodeDefinition.initUpdateDateTime();
        blockCodeDefinition.initUpdateDateTime();
        blockCodeDefinition.setUpdateBy(parmModificationRecord.getApplicationBy());
        blockCodeDefinition.setVersionNumber(1L);
        blockCodeDefinitionMapper.insertSelective(blockCodeDefinition);
        //保存账户层级
        saveBlockCodeLevel(blockCodeDefinition, req);
        return true;
    }

    @Override
    @Transactional(rollbackFor = Exception.class, propagation = Propagation.REQUIRED)
    //@ShardingSphereTransactionType(TransactionType.XA)
    protected boolean deleteDb(ParmModificationRecord parmModificationRecord) {
        ParmBlockCodeDefinition blockCodeDefinition = JSON.parseObject(parmModificationRecord.getParmBody(), ParmBlockCodeDefinition.class);
        try {
            //先删除各个层级的标信息根据tableId和机构编号
            deleteBlockLvelInfo(blockCodeDefinition);
            //删除主表定义表信息
            logger.warn("通过id删除账户层封锁码, {}", blockCodeDefinition);
            blockCodeDefinitionMapper.deleteByPrimaryKey(blockCodeDefinition.getId());
        } catch (Exception e) {
            logger.error("通过id删除各层级封锁码失败, {}", e.getMessage());
            throw new AnyTxnParameterException(AnyTxnParameterRespCodeEnum.D_DELETE_LEVEL_BLOCK_CODE_DEFINITION_FAULT);
        }
        return true;
    }
}
