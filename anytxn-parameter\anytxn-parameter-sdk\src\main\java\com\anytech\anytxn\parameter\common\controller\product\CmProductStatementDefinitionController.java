package com.anytech.anytxn.parameter.common.controller.product;
import jakarta.annotation.Resource;
import com.anytech.anytxn.common.core.base.PageResultDTO;
import com.anytech.anytxn.common.core.base.AnyTxnHttpResponse;
import com.anytech.anytxn.parameter.base.common.domain.dto.product.CmProductStatementDefinitionDTO;
import com.anytech.anytxn.parameter.base.common.domain.dto.product.CmProductStatementDefinitionPageRequest;
import com.anytech.anytxn.parameter.base.common.service.product.ICmProductStatementDefinitionService;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import java.util.List;
/**
 *
 * <p>Title: CmProductStatementDefinitionController </p>
 * <p>Description: (CmProductStatementDefinition)前端控制器 </p>
 *
 * <AUTHOR>
 * @version v1.0.0
 * @classname CmProductStatementDefinitionController
 * @date 2022/09/13
 */
@Tag(name ="产品参数定义表")
@RestController
@RequestMapping("/cmproductstatementdefinition")
public class CmProductStatementDefinitionController {

    @Resource
    private ICmProductStatementDefinitionService cmProductStatementDefinitionService;

    /**
     * 保存
     */
    @PostMapping(value = "add")
    public AnyTxnHttpResponse<Boolean> save(@RequestBody @Validated CmProductStatementDefinitionDTO request) {
        Boolean resp = cmProductStatementDefinitionService.save(request);
        return AnyTxnHttpResponse.success(resp);
    }

    /**
     * 删除
     */
    @PostMapping(value = "delete")
    public AnyTxnHttpResponse<Boolean> delete(@RequestBody @Validated CmProductStatementDefinitionDTO request) {
        Boolean resp = cmProductStatementDefinitionService.delete(request);
        return AnyTxnHttpResponse.success(resp);
    }

    /**
     * 更新
     */
    @PostMapping(value = "modify")
    public AnyTxnHttpResponse<Boolean> update(@RequestBody @Validated CmProductStatementDefinitionDTO request) {
        Boolean resp = cmProductStatementDefinitionService.update(request);
        return AnyTxnHttpResponse.success(resp);
    }

    /**
     * 详情
     */
    @PostMapping(value = "info")
    public AnyTxnHttpResponse<CmProductStatementDefinitionDTO> info(@RequestBody @Validated CmProductStatementDefinitionDTO request) {
        CmProductStatementDefinitionDTO resp = cmProductStatementDefinitionService.info(request);
        return AnyTxnHttpResponse.success(resp);
    }

    /**
     * 列表
     */
    @PostMapping(value = "list")
    public AnyTxnHttpResponse<List<CmProductStatementDefinitionDTO>> list(@RequestBody @Validated CmProductStatementDefinitionDTO request) {
        List<CmProductStatementDefinitionDTO> resp = cmProductStatementDefinitionService.list(request);
        return AnyTxnHttpResponse.success(resp);
    }

    /**
     * 分页列表
     */
    @PostMapping(value = "page")
    public AnyTxnHttpResponse<PageResultDTO<CmProductStatementDefinitionDTO>> page (@RequestBody @Validated CmProductStatementDefinitionPageRequest request) {
        PageResultDTO<CmProductStatementDefinitionDTO> resp = cmProductStatementDefinitionService.page(request);
        return AnyTxnHttpResponse.success(resp);
    }

}
