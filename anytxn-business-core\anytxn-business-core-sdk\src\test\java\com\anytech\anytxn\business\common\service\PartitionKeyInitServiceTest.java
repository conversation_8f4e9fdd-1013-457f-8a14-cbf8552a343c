package com.anytech.anytxn.business.common.service;

import com.anytech.anytxn.business.base.card.domain.dto.CardAuthorizationDTO;
import com.anytech.anytxn.business.dao.account.model.AccountManagementInfo;
import com.anytech.anytxn.business.dao.customer.mapper.CorporateDownTopReferenceSelfMapper;
import com.anytech.anytxn.common.core.exception.AnyTxnCommonException;
import com.anytech.anytxn.common.core.enums.AnyTxnCommonRespCodeEnum;
import com.anytech.anytxn.common.core.utils.OrgNumberUtils;
import com.anytech.anytxn.common.core.utils.PartitionKeyUtils;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockedStatic;
import org.mockito.junit.jupiter.MockitoExtension;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.Mockito.*;

/**
 * PartitionKeyInitService 单元测试类
 * 
 * <AUTHOR> Generator
 */
@ExtendWith(MockitoExtension.class)
class PartitionKeyInitServiceTest {

    @Mock
    private CorporateDownTopReferenceSelfMapper downTopReferenceSelfMapper;

    @InjectMocks
    private PartitionKeyInitService partitionKeyInitService;

    @Test
    void shouldGeneratePartitionKey_whenCardAuthorizationWithPersonalLiability() {
        try (MockedStatic<OrgNumberUtils> orgNumberUtils = mockStatic(OrgNumberUtils.class);
             MockedStatic<PartitionKeyUtils> partitionKeyUtils = mockStatic(PartitionKeyUtils.class)) {
            
            // Given
            orgNumberUtils.when(OrgNumberUtils::getOrg).thenReturn("ORG001");
            
            CardAuthorizationDTO cardAuthorizationDTO = new CardAuthorizationDTO();
            cardAuthorizationDTO.setLiability("P"); // Personal liability
            cardAuthorizationDTO.setPrimaryCustomerId("CUST001");
            cardAuthorizationDTO.setOrganizationNumber("ORG001");

            partitionKeyUtils.when(() -> PartitionKeyUtils.IntPartitionKey("CUST001"))
                    .thenReturn(5);

            // When
            int result = partitionKeyInitService.partitionKeyGenerator(cardAuthorizationDTO, null);

            // Then
            assertEquals(5, result);
            verify(downTopReferenceSelfMapper, never()).selectHighestLevelCorpCusId(anyString(), anyString());
        }
    }

    @Test
    void shouldGeneratePartitionKey_whenCardAuthorizationWithCorporateLiability() {
        try (MockedStatic<OrgNumberUtils> orgNumberUtils = mockStatic(OrgNumberUtils.class);
             MockedStatic<PartitionKeyUtils> partitionKeyUtils = mockStatic(PartitionKeyUtils.class)) {
            
            // Given
            orgNumberUtils.when(OrgNumberUtils::getOrg).thenReturn("ORG001");
            
            CardAuthorizationDTO cardAuthorizationDTO = new CardAuthorizationDTO();
            cardAuthorizationDTO.setLiability("C"); // Corporate liability
            cardAuthorizationDTO.setPrimaryCustomerId("CUST001");
            cardAuthorizationDTO.setOrganizationNumber("ORG001");
            cardAuthorizationDTO.setCorporateCustomerId("CORP001");

            when(downTopReferenceSelfMapper.selectHighestLevelCorpCusId("ORG001", "CORP001"))
                    .thenReturn("HIGHEST_CORP_CUST");

            partitionKeyUtils.when(() -> PartitionKeyUtils.IntPartitionKey("HIGHEST_CORP_CUST"))
                    .thenReturn(8);

            // When
            int result = partitionKeyInitService.partitionKeyGenerator(cardAuthorizationDTO, null);

            // Then
            assertEquals(8, result);
            verify(downTopReferenceSelfMapper).selectHighestLevelCorpCusId("ORG001", "CORP001");
        }
    }

    @Test
    void shouldUseOriginalCustomerId_whenCorporateLiabilityButNoHighestLevelFound() {
        try (MockedStatic<OrgNumberUtils> orgNumberUtils = mockStatic(OrgNumberUtils.class);
             MockedStatic<PartitionKeyUtils> partitionKeyUtils = mockStatic(PartitionKeyUtils.class)) {
            
            // Given
            orgNumberUtils.when(OrgNumberUtils::getOrg).thenReturn("ORG001");
            
            CardAuthorizationDTO cardAuthorizationDTO = new CardAuthorizationDTO();
            cardAuthorizationDTO.setLiability("C"); // Corporate liability
            cardAuthorizationDTO.setPrimaryCustomerId("CUST001");
            cardAuthorizationDTO.setOrganizationNumber("ORG001");
            cardAuthorizationDTO.setCorporateCustomerId("CORP001");

            when(downTopReferenceSelfMapper.selectHighestLevelCorpCusId("ORG001", "CORP001"))
                    .thenReturn(null); // No highest level found

            partitionKeyUtils.when(() -> PartitionKeyUtils.IntPartitionKey("CUST001"))
                    .thenReturn(3);

            // When
            int result = partitionKeyInitService.partitionKeyGenerator(cardAuthorizationDTO, null);

            // Then
            assertEquals(3, result);
            verify(downTopReferenceSelfMapper).selectHighestLevelCorpCusId("ORG001", "CORP001");
        }
    }

    @Test
    void shouldGeneratePartitionKey_whenAccountManagementWithPersonalLiability() {
        try (MockedStatic<OrgNumberUtils> orgNumberUtils = mockStatic(OrgNumberUtils.class);
             MockedStatic<PartitionKeyUtils> partitionKeyUtils = mockStatic(PartitionKeyUtils.class)) {
            
            // Given
            orgNumberUtils.when(OrgNumberUtils::getOrg).thenReturn("ORG002");
            
            AccountManagementInfo managementInfo = new AccountManagementInfo();
            managementInfo.setLiability("P"); // Personal liability
            managementInfo.setCustomerId("CUST002");
            managementInfo.setOrganizationNumber("ORG002");

            partitionKeyUtils.when(() -> PartitionKeyUtils.IntPartitionKey("CUST002"))
                    .thenReturn(6);

            // When
            int result = partitionKeyInitService.partitionKeyGenerator(null, managementInfo);

            // Then
            assertEquals(6, result);
            verify(downTopReferenceSelfMapper, never()).selectHighestLevelCorpCusId(anyString(), anyString());
        }
    }

    @Test
    void shouldGeneratePartitionKey_whenAccountManagementWithCorporateLiability() {
        try (MockedStatic<OrgNumberUtils> orgNumberUtils = mockStatic(OrgNumberUtils.class);
             MockedStatic<PartitionKeyUtils> partitionKeyUtils = mockStatic(PartitionKeyUtils.class)) {
            
            // Given
            orgNumberUtils.when(OrgNumberUtils::getOrg).thenReturn("ORG002");
            
            AccountManagementInfo managementInfo = new AccountManagementInfo();
            managementInfo.setLiability("C"); // Corporate liability
            managementInfo.setCustomerId("CUST002");
            managementInfo.setOrganizationNumber("ORG002");
            managementInfo.setCorporateCustomerId("CORP002");

            when(downTopReferenceSelfMapper.selectHighestLevelCorpCusId("ORG002", "CORP002"))
                    .thenReturn("HIGHEST_CORP_CUST2");

            partitionKeyUtils.when(() -> PartitionKeyUtils.IntPartitionKey("HIGHEST_CORP_CUST2"))
                    .thenReturn(9);

            // When
            int result = partitionKeyInitService.partitionKeyGenerator(null, managementInfo);

            // Then
            assertEquals(9, result);
            verify(downTopReferenceSelfMapper).selectHighestLevelCorpCusId("ORG002", "CORP002");
        }
    }

    @Test
    void shouldReturnDefaultPartitionKey_whenBothParametersAreNull() {
        // When
        int result = partitionKeyInitService.partitionKeyGenerator(null, null);

        // Then
        assertEquals(1, result);
        verify(downTopReferenceSelfMapper, never()).selectHighestLevelCorpCusId(anyString(), anyString());
    }

    @Test
    void shouldReturnDefaultPartitionKey_whenPartitionKeyUtilsThrowsException() {
        try (MockedStatic<OrgNumberUtils> orgNumberUtils = mockStatic(OrgNumberUtils.class);
             MockedStatic<PartitionKeyUtils> partitionKeyUtils = mockStatic(PartitionKeyUtils.class)) {
            
            // Given
            orgNumberUtils.when(OrgNumberUtils::getOrg).thenReturn("ORG001");
            
            CardAuthorizationDTO cardAuthorizationDTO = new CardAuthorizationDTO();
            cardAuthorizationDTO.setLiability("P");
            cardAuthorizationDTO.setPrimaryCustomerId("CUST001");

            partitionKeyUtils.when(() -> PartitionKeyUtils.IntPartitionKey("CUST001"))
                    .thenThrow(new AnyTxnCommonException(AnyTxnCommonRespCodeEnum.UNKONWN_ERR));

            // When
            int result = partitionKeyInitService.partitionKeyGenerator(cardAuthorizationDTO, null);

            // Then
            assertEquals(1, result); // Default value when exception occurs
        }
    }

    @Test
    void shouldUseOriginalCustomerId_whenCorporateLiabilityButEmptyHighestLevelFound() {
        try (MockedStatic<OrgNumberUtils> orgNumberUtils = mockStatic(OrgNumberUtils.class);
             MockedStatic<PartitionKeyUtils> partitionKeyUtils = mockStatic(PartitionKeyUtils.class)) {
            
            // Given
            orgNumberUtils.when(OrgNumberUtils::getOrg).thenReturn("ORG001");
            
            CardAuthorizationDTO cardAuthorizationDTO = new CardAuthorizationDTO();
            cardAuthorizationDTO.setLiability("C");
            cardAuthorizationDTO.setPrimaryCustomerId("CUST001");
            cardAuthorizationDTO.setOrganizationNumber("ORG001");
            cardAuthorizationDTO.setCorporateCustomerId("CORP001");

            when(downTopReferenceSelfMapper.selectHighestLevelCorpCusId("ORG001", "CORP001"))
                    .thenReturn(""); // Empty string

            partitionKeyUtils.when(() -> PartitionKeyUtils.IntPartitionKey("CUST001"))
                    .thenReturn(4);

            // When
            int result = partitionKeyInitService.partitionKeyGenerator(cardAuthorizationDTO, null);

            // Then
            assertEquals(4, result);
            verify(downTopReferenceSelfMapper).selectHighestLevelCorpCusId("ORG001", "CORP001");
        }
    }

    @Test
    void shouldPrioritizeCardAuthorization_whenBothParametersProvided() {
        try (MockedStatic<OrgNumberUtils> orgNumberUtils = mockStatic(OrgNumberUtils.class);
             MockedStatic<PartitionKeyUtils> partitionKeyUtils = mockStatic(PartitionKeyUtils.class)) {
            
            // Given
            orgNumberUtils.when(OrgNumberUtils::getOrg).thenReturn("ORG001");
            
            CardAuthorizationDTO cardAuthorizationDTO = new CardAuthorizationDTO();
            cardAuthorizationDTO.setLiability("P");
            cardAuthorizationDTO.setPrimaryCustomerId("CARD_CUST");

            AccountManagementInfo managementInfo = new AccountManagementInfo();
            managementInfo.setLiability("P");
            managementInfo.setCustomerId("MGMT_CUST");

            partitionKeyUtils.when(() -> PartitionKeyUtils.IntPartitionKey("CARD_CUST"))
                    .thenReturn(7);

            // When
            int result = partitionKeyInitService.partitionKeyGenerator(cardAuthorizationDTO, managementInfo);

            // Then
            assertEquals(7, result); // Should use card authorization, not management info
        }
    }
} 