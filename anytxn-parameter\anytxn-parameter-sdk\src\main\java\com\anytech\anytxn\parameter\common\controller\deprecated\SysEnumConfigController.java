package com.anytech.anytxn.parameter.common.controller.deprecated;

import io.swagger.v3.oas.annotations.tags.Tag;
import com.anytech.anytxn.common.core.base.PageResultDTO;
import com.anytech.anytxn.common.core.base.AnyTxnHttpResponse;
import com.anytech.anytxn.common.core.base.BizBaseController;
import com.anytech.anytxn.parameter.base.common.domain.dto.system.SysEnumConfigReqDTO;
import com.anytech.anytxn.parameter.base.common.domain.dto.system.SysEnumConfigResDTO;
import com.anytech.anytxn.parameter.base.common.domain.dto.system.SysEnumDataResDTO;
import com.anytech.anytxn.parameter.base.common.service.system.ISysEnumConfigService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import jakarta.validation.Valid;
import java.util.ArrayList;

/**
 * 枚举Key与sql对应关系 api
 * <AUTHOR>
 * @date 2018-09-13
 **/
@Deprecated
@Tag(name = "枚举配置信息api")
@RestController
public class SysEnumConfigController extends BizBaseController {

    @Autowired
    private ISysEnumConfigService sysEnumConfigService;

    /**
     * 创建枚举配置信息
     * @param req 请求数据
     * @return
     */
    @PostMapping(value = "/param/sys_enum_config")
    public AnyTxnHttpResponse<SysEnumConfigResDTO> create(@Valid @RequestParam SysEnumConfigReqDTO req) {
        SysEnumConfigResDTO configRes = sysEnumConfigService.add(req);
        return AnyTxnHttpResponse.success(configRes);
    }

    /**
     * 修改枚举配置信息
     * @param req 请求数据
     * @return
     */
    @PutMapping(value = "/param/sys_enum_config")
    public AnyTxnHttpResponse<SysEnumConfigResDTO> modify(@Valid @RequestParam SysEnumConfigReqDTO req) {
        SysEnumConfigResDTO configRes = sysEnumConfigService.modify(req);
        return AnyTxnHttpResponse.success(configRes);
    }

    /**
     * 枚举配置信息详情
     * @param enumKey 枚举key
     * @return
     */
    @GetMapping(value = "/param/sys_enum_config")
    public AnyTxnHttpResponse<SysEnumConfigResDTO> get(@RequestParam String enumKey) {
        SysEnumConfigResDTO configRes = sysEnumConfigService.find(enumKey);
        return AnyTxnHttpResponse.success(configRes);
    }

    /**
     * 分页查询枚举配置信息列表
     * @param pageNum 页号
     * @param pageSize 页码
     * @return
     */
    @GetMapping(value = "/param/sys_enum_config/pageNum/{pageNum}/pageSize/{pageSize}")
    public AnyTxnHttpResponse<PageResultDTO<SysEnumConfigResDTO>> search(@PathVariable(value = "pageNum") Integer pageNum,
                                                                      @PathVariable(value = "pageSize") Integer pageSize) {
        PageResultDTO<SysEnumConfigResDTO> page = sysEnumConfigService.findPage(pageNum, pageSize);
        return AnyTxnHttpResponse.success(page);
    }

    /**
     * 获取枚举key对应的枚举列表
     * @param enumKey 枚举key
     * @return
     * @throws
     */
    @GetMapping(value = "/param/sys_enum_config/enums")
    public AnyTxnHttpResponse<ArrayList<SysEnumDataResDTO>> getEnumList(@RequestParam String enumKey) {
        ArrayList<SysEnumDataResDTO>  enumList = sysEnumConfigService.findEnumList(enumKey);
        return AnyTxnHttpResponse.success(enumList);
    }


}
