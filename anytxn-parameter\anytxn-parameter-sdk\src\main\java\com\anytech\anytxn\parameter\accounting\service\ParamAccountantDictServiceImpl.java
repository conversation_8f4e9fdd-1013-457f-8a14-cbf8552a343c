package com.anytech.anytxn.parameter.accounting.service;

import com.alibaba.fastjson.JSONObject;
import com.anytech.anytxn.business.base.audit.AbstractParameterService;
import com.anytech.anytxn.business.base.audit.ParmModificationRecord;
import com.anytech.anytxn.common.core.annotation.db.DeleteParameterAnnotation;
import com.anytech.anytxn.common.core.annotation.db.InsertParameterAnnotation;
import com.anytech.anytxn.common.core.annotation.db.UpdateParameterAnnotation;
import com.anytech.anytxn.common.core.base.ParameterCompare;
import com.anytech.anytxn.common.core.base.PageResultDTO;
import com.anytech.anytxn.common.core.utils.BeanMapping;
import com.anytech.anytxn.common.core.utils.OrgNumberUtils;
import com.anytech.anytxn.parameter.base.accounting.domain.dto.ParamAccountantDicMappingReqDTO;
import com.anytech.anytxn.parameter.base.accounting.domain.dto.ParamAccountantDictMappingDTO;
import com.anytech.anytxn.parameter.base.accounting.service.IParamAccountantDictService;
import com.anytech.anytxn.parameter.base.common.enums.AnyTxnParameterRespCodeEnum;
import com.anytech.anytxn.parameter.base.common.exception.AnyTxnParameterException;
import com.anytech.anytxn.parameter.accounting.mapper.ParmAccountantDictMappingMapper;
import com.anytech.anytxn.parameter.base.accounting.domain.model.ParamAccountantDictMapping;
import com.github.pagehelper.Page;
import com.github.pagehelper.PageHelper;
import com.anytech.anytxn.common.core.utils.TenantUtils;
import com.anytech.anytxn.common.sequence.utils.Number16IdGen;
import jakarta.annotation.Resource;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
//import org.apache.shardingsphere.transaction.annotation.ShardingSphereTransactionType;
//import org.apache.shardingsphere.transaction.core.TransactionType;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import jakarta.annotation.ManagedBean;
import java.util.Comparator;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * @Author: sukang
 * @Date: 2022/12/5 20:48
 */
@ManagedBean("parm_accountant_dict_mapping_serviceImpl")
public class ParamAccountantDictServiceImpl extends AbstractParameterService implements IParamAccountantDictService {

    @Resource
    private ParmAccountantDictMappingMapper parmAccountantDictMappingMapper;

    @Autowired
    private Number16IdGen numberIdGenerator;

    private final String ACCOUT_PRODUCT_TYPE = "ACCOUT_PRODUCT_TYPE";


    @Override
    public PageResultDTO<ParamAccountantDicMappingReqDTO> getByPage(
            Integer pageNum,
            Integer pageSize,
            ParamAccountantDicMappingReqDTO paramAccountantDicMappingReqDTO) {

        if (paramAccountantDicMappingReqDTO == null){
            paramAccountantDicMappingReqDTO = new ParamAccountantDicMappingReqDTO();
        }

        ParamAccountantDictMapping conditionDictMapping = BeanMapping.copy(paramAccountantDicMappingReqDTO, ParamAccountantDictMapping.class);

        if (StringUtils.isNotBlank(paramAccountantDicMappingReqDTO.getCodeValue())){
            ParamAccountantDictMapping paramAccountantDictMapping = parmAccountantDictMappingMapper.selectByCodeValue(conditionDictMapping.getCodeValue());

            if (paramAccountantDictMapping != null){
                conditionDictMapping.setId(Objects.isNull(paramAccountantDictMapping.getParentId()) ?
                        paramAccountantDictMapping.getId() : paramAccountantDictMapping.getParentId() );
            }else {
                conditionDictMapping.setId("-1");
            }
        }



        Page<ParamAccountantDicMappingReqDTO> page = PageHelper.startPage(pageNum, pageSize);



        List<ParamAccountantDictMapping> accountantDictMappings = parmAccountantDictMappingMapper.selectByCondition(conditionDictMapping);

        List<ParamAccountantDicMappingReqDTO> dictMappings = BeanMapping.copyList(accountantDictMappings, ParamAccountantDicMappingReqDTO.class);

        return new PageResultDTO<>(pageNum, pageSize, page.getTotal(), page.getPages(), dictMappings);
    }


    @Override
    @InsertParameterAnnotation(tableName = "parm_accountant_dict_mapping",tableDesc = "Accountant dict mapping ",isJoinTable = true)
    public ParameterCompare addParam(ParamAccountantDicMappingReqDTO paramAccountantDicMappingReqDTO){
        checkRequiredInputs(paramAccountantDicMappingReqDTO);

        paramAccountantDicMappingReqDTO.setVersionNumber(1L);
        return ParameterCompare.getBuilder()
                .withAfter(paramAccountantDicMappingReqDTO)
                .build(paramAccountantDicMappingReqDTO.getClass());
    }


    private void checkRequiredInputs(ParamAccountantDicMappingReqDTO paramAccountantDicMappingReqDTO) {

    }


    @Override
    @UpdateParameterAnnotation(tableName = "parm_accountant_dict_mapping", tableDesc = "Accountant dict mapping ",isJoinTable = true)
    public ParameterCompare updateById(ParamAccountantDicMappingReqDTO paramAccountantDicMappingReqDTO){
        checkRequiredInputs(paramAccountantDicMappingReqDTO);

        ParamAccountantDicMappingReqDTO dicMappingReqDTO = getDetails(paramAccountantDicMappingReqDTO.getId());

        return ParameterCompare.getBuilder()
                .withBefore(dicMappingReqDTO)
                .withAfter(paramAccountantDicMappingReqDTO)
                .build(paramAccountantDicMappingReqDTO.getClass());
    }

    @Override
    @DeleteParameterAnnotation(tableName = "parm_accountant_dict_mapping", tableDesc = "Accountant dict mapping ",isJoinTable = true)
    public Object deleteById(String id) {

        ParamAccountantDictMapping paramAccountantDictMapping = parmAccountantDictMappingMapper.selectById(id);

        if (paramAccountantDictMapping == null){
            throw new AnyTxnParameterException(AnyTxnParameterRespCodeEnum.P_PARAMETER_DATA_NOT_EXIST);
        }

        ParamAccountantDicMappingReqDTO details = getDetails(id);

        return ParameterCompare.getBuilder()
                .withBefore(details)
                .build(ParamAccountantDicMappingReqDTO.class);
    }


    @Override
    public ParamAccountantDicMappingReqDTO getDetails(String id){

        ParamAccountantDictMapping accountantDictMapping  =  parmAccountantDictMappingMapper.selectById(id);

        ParamAccountantDicMappingReqDTO paramAccountantDicMappingReqDTO = BeanMapping.copy(accountantDictMapping, ParamAccountantDicMappingReqDTO.class);

        List<ParamAccountantDictMapping> paramAccountantDictMappingList = parmAccountantDictMappingMapper.selectByParentId(accountantDictMapping.getId());


        paramAccountantDictMappingList.sort(Comparator.comparing(ParamAccountantDictMapping::getCodeValue));

        paramAccountantDicMappingReqDTO.setAccountantDictMappingDtoS(BeanMapping.copyList(paramAccountantDictMappingList, ParamAccountantDictMappingDTO.class));
        return paramAccountantDicMappingReqDTO;
    }








    @Override
    @Transactional(rollbackFor = Exception.class,propagation = Propagation.REQUIRED)
    //@ShardingSphereTransactionType(TransactionType.XA)
    protected boolean updateDb(ParmModificationRecord parmModificationRecord) throws Exception {

        ParamAccountantDicMappingReqDTO paramAccountantDicMappingReqDTO = JSONObject.parseObject(parmModificationRecord.getParmBody(), ParamAccountantDicMappingReqDTO.class);

        ParamAccountantDictMapping accountantDictMapping = BeanMapping.copy(paramAccountantDicMappingReqDTO, ParamAccountantDictMapping.class);

        accountantDictMapping.initUpdateDateTime();
        accountantDictMapping.setUpdateBy(parmModificationRecord.getApplicationBy());
        int updateSelective = parmAccountantDictMappingMapper.updateSelective(accountantDictMapping);

        if (updateSelective != 1){
            return false;
        }

        parmAccountantDictMappingMapper.deleteAllByParentId(accountantDictMapping.getId());


        List<ParamAccountantDictMapping> dictMappingList = paramAccountantDicMappingReqDTO.getAccountantDictMappingDtoS().stream().map(e -> {
            ParamAccountantDictMapping dictMapping = BeanMapping.copy(e, ParamAccountantDictMapping.class);
            dictMapping.setParentId(accountantDictMapping.getId());
            dictMapping.setUpdateBy(parmModificationRecord.getApplicationBy());
            dictMapping.setMappingType(accountantDictMapping.getMappingType());
            dictMapping.initUpdateDateTime();
            dictMapping.initCreateDateTime();
            dictMapping.setId(String.valueOf(numberIdGenerator.generateId(TenantUtils.getTenantId())));

            if (Objects.equals(accountantDictMapping.getMappingType(),ACCOUT_PRODUCT_TYPE)){
                dictMapping.setMappingValue(accountantDictMapping.getMappingValue());
            }

            return dictMapping;

        }).collect(Collectors.toList());

        if (CollectionUtils.isNotEmpty(dictMappingList)){
            parmAccountantDictMappingMapper.batchInsert(dictMappingList);
        }
        return true;
    }



    @Override
    @Transactional(rollbackFor = Exception.class,propagation = Propagation.REQUIRED)
    //@ShardingSphereTransactionType(TransactionType.XA)
    protected boolean insertDb(ParmModificationRecord parmModificationRecord) throws Exception {

        ParamAccountantDicMappingReqDTO paramAccountantDicMappingReqDTO = JSONObject.parseObject(parmModificationRecord.getParmBody(), ParamAccountantDicMappingReqDTO.class);


        ParamAccountantDictMapping accountantDictMapping = BeanMapping.copy(paramAccountantDicMappingReqDTO, ParamAccountantDictMapping.class);

        accountantDictMapping.setId(String.valueOf(numberIdGenerator.generateId(TenantUtils.getTenantId())));
        accountantDictMapping.initUpdateDateTime();
        accountantDictMapping.initCreateDateTime();
        accountantDictMapping.setUpdateBy(parmModificationRecord.getApplicationBy());
        accountantDictMapping.setCodeValue(accountantDictMapping.getMappingType());

        parmAccountantDictMappingMapper.insert(accountantDictMapping);

        List<ParamAccountantDictMapping>  paramAccountantDictMappingList =  paramAccountantDicMappingReqDTO
                .getAccountantDictMappingDtoS().stream().map(e -> {
            ParamAccountantDictMapping dictMapping = BeanMapping.copy(e, ParamAccountantDictMapping.class);
            dictMapping.setParentId(accountantDictMapping.getId());
            dictMapping.setMappingType(accountantDictMapping.getMappingType());
            dictMapping.setId(String.valueOf(numberIdGenerator.generateId(TenantUtils.getTenantId())));
            dictMapping.initCreateDateTime();
            dictMapping.initUpdateDateTime();
            dictMapping.setOrganizationNumber(OrgNumberUtils.getOrg());
            dictMapping.setUpdateBy(parmModificationRecord.getApplicationBy());

            if (Objects.equals(accountantDictMapping.getMappingType(),ACCOUT_PRODUCT_TYPE)){
                dictMapping.setMappingValue(accountantDictMapping.getMappingValue());
            }

            return dictMapping;
        }).collect(Collectors.toList());

        if (CollectionUtils.isNotEmpty(paramAccountantDictMappingList)){
            parmAccountantDictMappingMapper.batchInsert(paramAccountantDictMappingList);
        }

        return true;
    }



    @Override
    @Transactional(rollbackFor = Exception.class,propagation = Propagation.REQUIRED)
    //@ShardingSphereTransactionType(TransactionType.XA)
    protected boolean deleteDb(ParmModificationRecord parmModificationRecord) throws Exception {

        ParamAccountantDicMappingReqDTO paramAccountantDicMappingReqDTO = JSONObject.parseObject(parmModificationRecord.getParmBody(), ParamAccountantDicMappingReqDTO.class);

        parmAccountantDictMappingMapper.deleteBytId(paramAccountantDicMappingReqDTO.getId());

        parmAccountantDictMappingMapper.deleteAllByParentId(paramAccountantDicMappingReqDTO.getId());

        return true;
    }



}
