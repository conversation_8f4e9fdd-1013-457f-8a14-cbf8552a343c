package com.anytech.anytxn.parameter.authorization.service;

import com.alibaba.fastjson.JSON;
import com.github.pagehelper.Page;
import com.github.pagehelper.PageHelper;
import com.anytech.anytxn.common.core.base.PageResultDTO;
import com.anytech.anytxn.common.core.utils.BeanMapping;
import com.anytech.anytxn.common.core.utils.OrgNumberUtils;
import com.anytech.anytxn.common.sequence.utils.Number16IdGen;
import com.anytech.anytxn.common.core.utils.TenantUtils;
import com.anytech.anytxn.parameter.base.authorization.domain.dto.MccGroupDefinitionDTO;
import com.anytech.anytxn.parameter.base.authorization.domain.dto.MccGroupDetailDTO;
import com.anytech.anytxn.parameter.base.authorization.service.IMccGroupDefinitionService;
import com.anytech.anytxn.parameter.base.authorization.service.IMccGroupDetailService;
import com.anytech.anytxn.parameter.base.common.enums.AnyTxnParameterRespCodeEnum;
import com.anytech.anytxn.parameter.base.common.exception.AnyTxnParameterException;
import com.anytech.anytxn.parameter.common.mapper.deprecated.MccGroupDefinitionMapper;
import com.anytech.anytxn.parameter.common.mapper.deprecated.MccGroupDefinitionSelfMapper;
import com.anytech.anytxn.parameter.base.authorization.domain.model.ParmMccGroupDefinition;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.cglib.beans.BeanCopier;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * @ClassName MccGroupDefinitionServiceImpl
 * @Description 商户类别群具体实现类
 * <AUTHOR>
 * @Date 2019/4/4 3:44 PM
 * Version 1.0
 **/
@Service
public class MccGroupDefinitionServiceImpl implements IMccGroupDefinitionService {

    private Logger logger = LoggerFactory.getLogger(MccGroupDefinitionServiceImpl.class);

    @Autowired
    private MccGroupDefinitionMapper mccGroupDefinitionMapper;
    @Autowired
    private MccGroupDefinitionSelfMapper mccGroupDefinitionSelfMapper;

    @Autowired
    private IMccGroupDetailService mccGroupDetailService;
    @Autowired
    private Number16IdGen numberIdGenerator;


    @Override
    public PageResultDTO<MccGroupDefinitionDTO> findListMccGroupDefinition(Integer page, Integer rows, String mccGroup,String description, String organizationNumber) {
        logger.info("分页查询商户类别群，当前页：{}，每页展示条数：{}", page, rows);
        Page pageInfo =  PageHelper.startPage(page, rows);
        List<MccGroupDefinitionDTO> mccGroupDefinitionDTOList = null;
        try {
            organizationNumber = StringUtils.isEmpty(organizationNumber) ? OrgNumberUtils.getOrg() : organizationNumber;
            List<ParmMccGroupDefinition> mccGroupDefinitionList = mccGroupDefinitionSelfMapper.selectByCondition(organizationNumber,mccGroup,description);
            if (!CollectionUtils.isEmpty(mccGroupDefinitionList)) {
                mccGroupDefinitionDTOList = new ArrayList<>();
                BeanCopier beanCopier = BeanCopier.create(ParmMccGroupDefinition.class, MccGroupDefinitionDTO.class, false);
                for (ParmMccGroupDefinition mccGroupDefinition : mccGroupDefinitionList) {
                    MccGroupDefinitionDTO mccGroupDefinitionDTO = new MccGroupDefinitionDTO();
                    beanCopier.copy(mccGroupDefinition, mccGroupDefinitionDTO, null);
                    mccGroupDefinitionDTOList.add(mccGroupDefinitionDTO);
                }
            }
            return new PageResultDTO<>(page, rows, pageInfo.getTotal(), pageInfo.getPages(),mccGroupDefinitionDTOList);
        } catch (Exception e) {
            logger.error("分页查询商户类别群信息失败，当前页：{}，每页展示条数：{},错误信息：{}", page, rows, e.getMessage());
           /* throw new AnyTXNBusRuntimeException(TransactionEnum.DATABASE_ERROR.getCode(),
                    TransactionEnum.DATABASE_ERROR.getReason());*/
            throw new AnyTxnParameterException(AnyTxnParameterRespCodeEnum.D_PAGE_MCC_GROUP_DEFINITION_FAULT);
        }
    }

    @Override
    public MccGroupDefinitionDTO findMccGroupDefinition(Long id) {
        logger.info("根据主键:{},获取商户类别码群信息",id);
        MccGroupDefinitionDTO mccGroupDefinitionDTO = null;
        try{
            ParmMccGroupDefinition mccGroupDefinition = mccGroupDefinitionMapper.selectByPrimaryKey(id);
            if (mccGroupDefinition != null) {
                mccGroupDefinitionDTO = new MccGroupDefinitionDTO();
                BeanCopier beanCopier = BeanCopier.create(ParmMccGroupDefinition.class, MccGroupDefinitionDTO.class, false);
                beanCopier.copy(mccGroupDefinition, mccGroupDefinitionDTO, null);
            }
        }
        catch(Exception e){
            logger.error("根据主键:{},获取商户类别群信息失败,错误信息:{}",id,e.getMessage());
            /*throw new AnyTXNBusRuntimeException(TransactionEnum.DATABASE_ERROR.getCode(),
                    TransactionEnum.DATABASE_ERROR.getReason());*/
            throw new AnyTxnParameterException(AnyTxnParameterRespCodeEnum.D_QUERY_MCC_GROUP_DEFINITION_BY_ID_FAULT);
        }
        if (mccGroupDefinitionDTO == null) {
            logger.error("根据主键:{},获取商户类别群信息失败", id);

            throw new AnyTxnParameterException(AnyTxnParameterRespCodeEnum.D_QUERY_MCC_GROUP_DEFINITION_NULL_BY_ID_FAULT);
        }
        return mccGroupDefinitionDTO;
    }

    @Override
    public Boolean modifyMccGroupDefinition(MccGroupDefinitionDTO mccGroupDefinitionDTO) {
        logger.info("修改商户类别群信息,商户类别群:{} 商户类别群状态:{}", mccGroupDefinitionDTO.getMccGroup(), mccGroupDefinitionDTO.getStatus());
        try {
            ParmMccGroupDefinition mccGroupDefinition= new ParmMccGroupDefinition();
            BeanCopier beanCopier = BeanCopier.create(MccGroupDefinitionDTO.class, ParmMccGroupDefinition.class, false);
            beanCopier.copy(mccGroupDefinitionDTO, mccGroupDefinition, null);
            mccGroupDefinition.setUpdateTime(LocalDateTime.now());
            return mccGroupDefinitionMapper.updateByPrimaryKeySelective(mccGroupDefinition) > 0;
        } catch (Exception e) {
            logger.error("调用[{}]更新商户类别群定义表[{}]失败,错误信息[{}]",
                    "updateByPrimaryKeySelective", "MCC_GROUP_DEFINITION", e);

            throw new AnyTxnParameterException(AnyTxnParameterRespCodeEnum.D_UPDATE_MCC_GROUP_DEFINITION_FAULT);
        }
    }

    @Override
    public int removeMccGroupDefinition(Long id) {
        ParmMccGroupDefinition mccGroupDefinition = mccGroupDefinitionMapper.selectByPrimaryKey(id);
        logger.info("查询商户类别群信息 id:{}", id);
        if (mccGroupDefinition == null) {
            logger.error("待删除商户类别群信息不存在。 id:{}", id);

            throw new AnyTxnParameterException(AnyTxnParameterRespCodeEnum.D_QUERY_MCC_GROUP_DEFINITION_NULL_BY_ID_FAULT);
        }
        try {
            return mccGroupDefinitionMapper.deleteByPrimaryKey(id);
        } catch (Exception e) {
            /*throw new AnyTXNBusRuntimeException(TransactionEnum.DATABASE_ERROR.getCode(),
                    TransactionEnum.DATABASE_ERROR.getReason());*/
            throw new AnyTxnParameterException(AnyTxnParameterRespCodeEnum.D_DELETE_MCC_GROUP_DEFINITION_FAULT);
        }
    }

    @Override
    public Boolean addMccGroupDefinition(MccGroupDefinitionDTO mccGroupDefinitionDTO) {
        ParmMccGroupDefinition mccGroupDefinition;
        //转换
        if (mccGroupDefinitionDTO == null) {
            throw new AnyTxnParameterException(AnyTxnParameterRespCodeEnum.P_PARAMETER_EMPTY_FAULT);
        }
        mccGroupDefinition = BeanMapping.copy(mccGroupDefinitionDTO, ParmMccGroupDefinition.class);
        int isExists = mccGroupDefinitionSelfMapper.isExists(mccGroupDefinition.getMccGroup(),mccGroupDefinitionDTO.getOrganizationNumber());
        if (isExists > 0) {
            logger.error("商户类别群已存在!");
            throw new AnyTxnParameterException(AnyTxnParameterRespCodeEnum.D_EXIST_MCC_GROUP_DEFINITION_FAULT);
        }
        mccGroupDefinition.setId(numberIdGenerator.generateId(TenantUtils.getTenantId()));
        mccGroupDefinition.setCreateTime(LocalDateTime.now());
        mccGroupDefinition.setUpdateTime(LocalDateTime.now());
        mccGroupDefinition.setUpdateBy("admin");
        try {
            return mccGroupDefinitionMapper.insertSelective(mccGroupDefinition) > 0 ;
        } catch (Exception e) {
            logger.error("exception:{}",e);
            throw new AnyTxnParameterException(AnyTxnParameterRespCodeEnum.D_INSERT_MCC_GROUP_DEFINITION_FAULT);
        }
    }

    @Override
    public MccGroupDefinitionDTO findMccGroupDefinitionAndDetil(Long id) {
        logger.info("根据主键:{},获取商户类别码群信息以及详情", id);
        MccGroupDefinitionDTO mccGroupDefinitionDTO = null;
        try {
            ParmMccGroupDefinition mccGroupDefinition = mccGroupDefinitionMapper.selectByPrimaryKey(id);
            logger.info("查询群信息：{}", JSON.toJSON(mccGroupDefinition));
            if (mccGroupDefinition != null) {
                mccGroupDefinitionDTO = new MccGroupDefinitionDTO();
                BeanCopier beanCopier = BeanCopier.create(ParmMccGroupDefinition.class, MccGroupDefinitionDTO.class, false);
                beanCopier.copy(mccGroupDefinition, mccGroupDefinitionDTO, null);
                List<MccGroupDetailDTO> listByMccGroup = mccGroupDetailService.getListByMccGroup(mccGroupDefinition.getMccGroup(),mccGroupDefinition.getOrganizationNumber());
                //通过mccGroup查询到的detils
                mccGroupDefinitionDTO.setMccGroupDetailDTOList(listByMccGroup);
            }
        }   catch(Exception e){
            logger.error("根据主键:{},获取商户类别码群信息以及详情失败,错误信息:{}",id,e.getMessage());
            /*throw new AnyTXNBusRuntimeException(TransactionEnum.DATABASE_ERROR.getCode(),
                    TransactionEnum.DATABASE_ERROR.getReason());*/
            throw new AnyTxnParameterException(AnyTxnParameterRespCodeEnum.D_QUERY_MCC_GROUP_DEFINITION_BY_ID_FAULT);
        }
        if (mccGroupDefinitionDTO == null) {
            logger.error("根据主键:{},获取商户类别码群信息以及详情以及失败", id);

            throw new AnyTxnParameterException(AnyTxnParameterRespCodeEnum.D_QUERY_MCC_GROUP_DEFINITION_NULL_BY_ID_FAULT);
        }
        return mccGroupDefinitionDTO;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean modifyMccGroupDefinitionAndUtils(MccGroupDefinitionDTO mccGroupDefinitionDTO) {
        logger.info("修改商户类别群信息,商户类别群:{} 商户类别群状态:{}", mccGroupDefinitionDTO.getMccGroup(), mccGroupDefinitionDTO.getStatus());
        try {
            //取出传入的
            String mccGroup = mccGroupDefinitionDTO.getMccGroup();
            String description = mccGroupDefinitionDTO.getDescription();
            List<MccGroupDetailDTO> mccGroupDetailDTOList = mccGroupDefinitionDTO.getMccGroupDetailDTOList();
            String organizationNumber = mccGroupDefinitionDTO.getOrganizationNumber();
            String status = mccGroupDefinitionDTO.getStatus();

            //商户群处理
            if (mccGroupDefinitionDTO == null) {
                logger.error("mccGroupDefinitionDTO is Null!");
                throw new AnyTxnParameterException(AnyTxnParameterRespCodeEnum.P_PARAMETER_EMPTY_FAULT);
            }
            ParmMccGroupDefinition mccGroupDefinition = new ParmMccGroupDefinition();
            mccGroupDefinition.setMccGroup(mccGroup);
            mccGroupDefinition.setDescription(description);
            mccGroupDefinition.setCreateTime(LocalDateTime.now());
            mccGroupDefinition.setUpdateTime(LocalDateTime.now());
            mccGroupDefinition.setUpdateBy("admin");
            mccGroupDefinition.setVersionNumber(1L);
            mccGroupDefinition.setStatus(status);
            mccGroupDefinition.setOrganizationNumber(organizationNumber);

            //商户群详情处理
            mccGroupDetailService.deleteByMccGroup(mccGroup);
            Long versionNumber = mccGroupDefinition.getVersionNumber();
            for (MccGroupDetailDTO bean : mccGroupDetailDTOList) {
                bean.setId(numberIdGenerator.generateId(TenantUtils.getTenantId()));
                bean.setMccGroup(mccGroup);
                bean.setStatus(status);
                bean.setCreateTime(LocalDateTime.now());
                bean.setUpdateTime(LocalDateTime.now());
                bean.setVersionNumber(versionNumber);
                bean.setUpdateBy("admin");
                bean.setOrganizationNumber(organizationNumber);
                mccGroupDetailService.addMccGroupDetail(bean);
            }
            return mccGroupDefinitionSelfMapper.updateByMccGroup(mccGroupDefinition) > 0;
        } catch (Exception e) {
            logger.error("调用[{}]更新商户类别群定义表[{}]失败,错误信息[{}]",
                    "updateByPrimaryKeySelective", "MCC_GROUP_DEFINITION", e);
            throw new AnyTxnParameterException(AnyTxnParameterRespCodeEnum.D_UPDATE_MCC_GROUP_DEFINITION_FAULT);
        }
    }

    @Override
    public Boolean addMccGroupDefinitionAndDtails(MccGroupDefinitionDTO mccGroupDefinitionDTO) {

        //判断传入的不能为空
        if (mccGroupDefinitionDTO == null) {
            logger.error("mccGroupDefinitionDTO is Null!");
            throw new AnyTxnParameterException(AnyTxnParameterRespCodeEnum.P_PARAMETER_EMPTY_FAULT);
        }
        logger.info("新增MCC类别组信息,MCC类别:{}", mccGroupDefinitionDTO.getMccGroup());

        //取出传入的
        String mccGroup = mccGroupDefinitionDTO.getMccGroup();
        String description = mccGroupDefinitionDTO.getDescription();
        List<MccGroupDetailDTO> mccGroupDetailDTOList = mccGroupDefinitionDTO.getMccGroupDetailDTOList();
        String organizationNumber = mccGroupDefinitionDTO.getOrganizationNumber();
        String status = mccGroupDefinitionDTO.getStatus();

        //商户群处理
        int isExists = mccGroupDefinitionSelfMapper.isExists(mccGroup,organizationNumber);
        if (isExists > 0) {
            logger.error("商户类别群已存在!");
            throw new AnyTxnParameterException(AnyTxnParameterRespCodeEnum.D_EXIST_MCC_GROUP_DEFINITION_FAULT);
        }
        ParmMccGroupDefinition mccGroupDefinition = BeanMapping.copy(mccGroupDefinitionDTO, ParmMccGroupDefinition.class);
        mccGroupDefinition.setId(numberIdGenerator.generateId(TenantUtils.getTenantId()));
        mccGroupDefinition.setDescription(description);
        mccGroupDefinition.setCreateTime(LocalDateTime.now());
        mccGroupDefinition.setUpdateTime(LocalDateTime.now());
        mccGroupDefinition.setUpdateBy("admin");
        mccGroupDefinition.setVersionNumber(1L);

        //商户群详情处理
        try {
            //插入详情
            String updateBy = mccGroupDefinition.getUpdateBy();
            Long versionNumber = mccGroupDefinition.getVersionNumber();
            for (MccGroupDetailDTO bean : mccGroupDetailDTOList) {
                bean.setId(numberIdGenerator.generateId(TenantUtils.getTenantId()));
                bean.setMccGroup(mccGroup);
                bean.setStatus(status);
                bean.setCreateTime(LocalDateTime.now());
                bean.setUpdateTime(LocalDateTime.now());
                bean.setVersionNumber(versionNumber);
                bean.setUpdateBy(updateBy);
                bean.setOrganizationNumber(organizationNumber);
                mccGroupDetailService.addMccGroupDetail(bean);
            }
            return mccGroupDefinitionMapper.insertSelective(mccGroupDefinition) > 0;
        } catch (Exception e) {
            logger.error("exception:{}",e);
            throw new AnyTxnParameterException(AnyTxnParameterRespCodeEnum.D_INSERT_MCC_GROUP_DEFINITION_FAULT);
        }
    }

    @Override
    public Boolean removeMccGroupDefinitionAndDtails(Long id) {
        ParmMccGroupDefinition mccGroupDefinition = mccGroupDefinitionMapper.selectByPrimaryKey(id);
        logger.info("查询商户类别群信息 id:{}", id);
        if (mccGroupDefinition == null) {
            logger.error("待删除商户类别群信息不存在。 id:{}", id);
            throw new AnyTxnParameterException(AnyTxnParameterRespCodeEnum.D_QUERY_MCC_GROUP_DEFINITION_NULL_BY_ID_FAULT);
        }
        try {
            List<MccGroupDetailDTO> listByMccGroup = mccGroupDetailService.getListByMccGroup(mccGroupDefinition.getMccGroup(), mccGroupDefinition.getOrganizationNumber());
            for (MccGroupDetailDTO dto : listByMccGroup) {
                mccGroupDetailService.removeMccGroupDetail(dto.getId());
            }
            return mccGroupDefinitionMapper.deleteByPrimaryKey(id) > 0;
        } catch (Exception e) {
            throw new AnyTxnParameterException(AnyTxnParameterRespCodeEnum.D_DELETE_MCC_GROUP_DEFINITION_FAULT);
        }
    }

    @Override
    public List<Map<String,String>> getMccGroups() {
        logger.info("获取所有商户类别群");
        List<ParmMccGroupDefinition> mccGroupDefinitionList = mccGroupDefinitionSelfMapper.selectAll(true,OrgNumberUtils.getOrg());
        List<Map<String,String>> mapList = new ArrayList<>();
        mccGroupDefinitionList.forEach(mccGroupDefinition -> {
            Map<String,String> map = new HashMap<>(4);
            map.put("value",mccGroupDefinition.getMccGroup());
            map.put("label",mccGroupDefinition.getDescription());
            mapList.add(map);
        });
        return mapList;
    }
}
