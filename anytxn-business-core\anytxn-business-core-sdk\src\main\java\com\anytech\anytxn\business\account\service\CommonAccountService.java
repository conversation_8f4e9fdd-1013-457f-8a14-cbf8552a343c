package com.anytech.anytxn.business.account.service;

import com.anytech.anytxn.business.base.account.domain.dto.CommonAccountDTO;
import com.anytech.anytxn.business.dao.account.mapper.AccountManagementInfoSelfMapper;
import com.anytech.anytxn.business.dao.account.model.AccountManagementInfo;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import java.util.*;
import java.util.stream.Collectors;

/**
 * <pre>
 * Description: 账户共用处理类
 * Company:	    江融信
 * Version:	    1.0
 * Create at:   2023/9/5
 * <p>
 * 修改历史:
 * 日期    作者    版本  修改描述
 * ------------------------------------------------------------------
 * <AUTHOR>
 * </pre>
 */
@Service
@Slf4j
public class CommonAccountService {

    @Resource
    private AccountManagementInfoSelfMapper managementInfoSelfMapper;

    /**
     * 根据机构号+客户号查询有效账户信息
     *
     * @param commonAccountDTO CommonAccountDTO
     * @return List<AccountManagementInfo>
     */
    public List<AccountManagementInfo> selectByCustomerIdAndOrg(CommonAccountDTO commonAccountDTO) {
        if (StringUtils.isEmpty(commonAccountDTO.getCustomerId())
                || StringUtils.isEmpty(commonAccountDTO.getOrganizationNumber())) {
            log.error("The entry field is empty error.");
            return Collections.emptyList();
        }
        List<AccountManagementInfo> accountManagementInfos = managementInfoSelfMapper.
                selectByCustomerId(commonAccountDTO.getOrganizationNumber(),
                        commonAccountDTO.getCustomerId());
        if (!CollectionUtils.isEmpty(accountManagementInfos)) {
            List<AccountManagementInfo> managementInfoList = new ArrayList<>(16);
            Map<String, List<AccountManagementInfo>> acctInfos = accountManagementInfos.stream()
                    .collect(Collectors.groupingBy(m -> m.getProductNumber() + "_" + m.getCurrency()));
            for (Map.Entry<String, List<AccountManagementInfo>> map : acctInfos.entrySet()) {
                List<AccountManagementInfo> managementInfos = map.getValue();
                if (!CollectionUtils.isEmpty(managementInfos)) {
                    AccountManagementInfo validAcctInfo = getValidAcctInfo(managementInfos);
                    if (Objects.nonNull(validAcctInfo)){
                        managementInfoList.add(validAcctInfo);
                    }
                }
            }
            return managementInfoList;
        } else {
            log.error("Get Acct Info Error. ");
            return Collections.emptyList();
        }
    }

    /**
     * 根据机构号+客户号+账产品+币种查询有效账户信息
     *
     * @param commonAccountDTO CommonAccountDTO
     * @return CommonAccountDTO
     */
    public AccountManagementInfo selectByCusIdProNumAndCurr(CommonAccountDTO commonAccountDTO) {
        if (StringUtils.isEmpty(commonAccountDTO.getCustomerId())
                || StringUtils.isEmpty(commonAccountDTO.getOrganizationNumber())
                || StringUtils.isEmpty(commonAccountDTO.getCurrency())
                || StringUtils.isEmpty(commonAccountDTO.getAcctProductNumber())) {
            log.error("The entry field is empty error.");
            return null;
        }
        List<AccountManagementInfo> managementInfos = managementInfoSelfMapper.
                selectByCustomerId(commonAccountDTO.getOrganizationNumber(), commonAccountDTO.getCustomerId());

        if (!CollectionUtils.isEmpty(managementInfos)) {

            managementInfos = managementInfos.stream()
                    .filter(e -> Objects.equals(e.getProductNumber(), commonAccountDTO.getAcctProductNumber()) &&
                            Objects.equals(e.getCurrency(), commonAccountDTO.getCurrency()))
                    .collect(Collectors.toList());

            return getValidAcctInfo(managementInfos);
        } else {
            log.error("Get Acct Info Error. ");
            return null;
        }
    }

    /**
     * 获取有效账户信息
     *
     * @param managementInfos List<AccountManagementInfo>
     * @return AccountManagementInfo
     */
    private AccountManagementInfo getValidAcctInfo(List<AccountManagementInfo> managementInfos) {
        AccountManagementInfo managementInfo = managementInfos.stream().
                filter(m -> !"8".equals(m.getAccountStatus())).findFirst().orElse(null);
        if (Objects.isNull(managementInfo)) {
            managementInfo = managementInfos.stream()
                    .max(Comparator.comparing(AccountManagementInfo::getCreateTime)).orElse(null);
        }
        return managementInfo;
    }
}
