package com.anytech.anytxn.parameter.batch.job.service;

import com.alibaba.fastjson.JSON;
import com.anytech.anytxn.parameter.base.common.constants.ParamConstant;
import com.anytech.anytxn.parameter.base.common.enums.ProcessFlagEnum;
import com.anytech.anytxn.parameter.base.common.enums.AnyTxnParameterRespCodeEnum;
import com.anytech.anytxn.parameter.base.common.exception.AnyTxnParameterException;
import com.anytech.anytxn.parameter.common.mapper.broadcast.system.ParmHolidayListSelfMapper;
import com.anytech.anytxn.parameter.common.mapper.broadcast.system.ParmOrganizationInfoMapper;
import com.anytech.anytxn.parameter.common.mapper.broadcast.system.ParmOrganizationInfoSelfMapper;
import com.anytech.anytxn.parameter.common.mapper.broadcast.system.ParmWeekdayTableSelfMapper;
import com.anytech.anytxn.parameter.base.common.domain.model.system.ParmHolidayList;
import com.anytech.anytxn.parameter.base.common.domain.model.system.ParmOrganizationInfo;
import com.anytech.anytxn.parameter.base.common.domain.model.system.ParmWeekdayTable;
import com.anytech.anytxn.parameter.base.common.domain.bo.system.ParmOrganizationInfoBO;
import com.anytech.anytxn.parameter.base.common.domain.bo.system.ParmWeekdayTableBO;
import com.anytech.anytxn.common.core.utils.BeanMapping;
import com.anytech.anytxn.common.core.utils.OrgNumberUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.temporal.TemporalAdjusters;

/**
 * @description: 日切逻辑。由于仅由日切批量使用，从原sdk处迁移而来。
 * @author: zhangnan
 * @create: 2021-01-21
 **/
@Slf4j
@Service
public class OrganizationDataCutOverService {
    @Autowired
    private ParmOrganizationInfoSelfMapper parmOrganizationInfoSelfMapper;
    @Autowired
    private ParmHolidayListSelfMapper parmHolidayListSelfMapper;
    @Autowired
    private ParmWeekdayTableSelfMapper parmWeekdayTableSelfMapper;
    @Autowired
    private ParmOrganizationInfoMapper parmOrganizationInfoMapper;

    /**
     * 日切主逻辑。
     * 对当前机构进行日切
     */
    public void cutOver() {
        log.info("获取到的机构号："+ OrgNumberUtils.getOrg());
        ParmOrganizationInfo parmOrganizationInfo = parmOrganizationInfoSelfMapper.selectByOrganizationNumber(OrgNumberUtils.getOrg());
        if (ProcessFlagEnum.PROCESS_TODAY_FLAG_DO.getCode().equals(parmOrganizationInfo.getProcessTodayFlag())) {
            ParmOrganizationInfoBO parmOrganizationInfoBO = BeanMapping.copy(parmOrganizationInfo, ParmOrganizationInfoBO.class);
            doCutOverForEach(parmOrganizationInfoBO);
        } else {
            //do nothing
        }
    }

    /**
     * 机构日切(单个)
     *
     * @param parmOrganizationInfoBO 机构参数BO
     */
    private void doCutOverForEach(ParmOrganizationInfoBO parmOrganizationInfoBO) {
//        ParmSystemTableBO systemTableBO = parmOrganizationInfoBO.getParmSystemTableBO();

        ParmWeekdayTable parmWeekdayTable = parmWeekdayTableSelfMapper.selectByWeekdayId(parmOrganizationInfoBO.getWeekdayId(), OrgNumberUtils.getOrg());
        if (null == parmWeekdayTable) {
            throw new AnyTxnParameterException(AnyTxnParameterRespCodeEnum.D_QUERY_ORGANIZATION_INFO_FAULT);
        }
        parmOrganizationInfoBO.setParmWeekdayTableBO(BeanMapping.copy(parmWeekdayTable, ParmWeekdayTableBO.class));

        if (ProcessFlagEnum.PROCESS_TODAY_FLAG_UNDO.getCode().equals(parmOrganizationInfoBO.getProcessTodayFlag())
                || !parmOrganizationInfoBO.checkIsNeedToProcess()) {
            log.info("机构处理标记为不处理或工作日表处理标识全为1, return");
            parmOrganizationInfoBO.setProcessTodayFlag(ProcessFlagEnum.PROCESS_TODAY_FLAG_UNDO.getCode());
            parmOrganizationInfoMapper.updateByPrimaryKeySelective(BeanMapping.copy(parmOrganizationInfoBO, ParmOrganizationInfo.class));
            return;
        }

        // 工作日表下一处理不处理，return
        LocalDate nextProcessingDay = parmOrganizationInfoBO.getNextProcessingDay();
        int n = nextProcessingDay.getDayOfWeek().getValue();
        if (!parmOrganizationInfoBO.checkIsNeedToProcess(n)) {
            log.info("n:{},对应的工作日表中的处理标识为1,不处理, return", n);
            parmOrganizationInfoBO.setProcessTodayFlag(ProcessFlagEnum.PROCESS_TODAY_FLAG_UNDO.getCode());
            parmOrganizationInfoMapper.updateByPrimaryKeySelective(BeanMapping.copy(parmOrganizationInfoBO, ParmOrganizationInfo.class));
            return;
        }

        //TODO 不知道有什么特殊含义，去掉
//        // 机构参数表的下一系统处理日（next_processing_day）> 系统下一处理日（中间变量） 不处理, return
//        LocalDate accruedThruDay = systemTableBO.getAccruedThruDay();
//        if (nextProcessingDay.isAfter(accruedThruDay)) {
//            logger.info("下一处理日（next_processing_day）> 系统下一处理日,不处理, return");
//            parmOrganizationInfoBO.setProcessTodayFlag(ProcessFlag.PROCESS_TODAY_FLAG_UNDO.getCode());
//            parmOrganizationInfoMapper.updateByPrimaryKeySelective(BeanMapping.copy(parmOrganizationInfoBO, ParmOrganizationInfo.class));
//            return;
//        }

        if (StringUtils.isNotBlank(parmOrganizationInfoBO.getParmWeekdayTableBO().getHolidayListId())) {
            ParmHolidayList holiday = new ParmHolidayList();
            parmOrganizationInfoBO.setJumpBatchDays(0L);
            while (holiday != null) {
                n = parmOrganizationInfoBO.cutting(n);
                // 根据假日列表ID（holiday_list_id）+ 假日（holiday_day） （此处假日holiday_day使用&下一处理日）
//                LocalDate nextDay = Date.from(parmOrganizationInfoBO.getNextProcessingDay().atStartOfDay(ZoneId.systemDefault()).toInstant());
                holiday = parmHolidayListSelfMapper.selectByHolidayAndId(parmOrganizationInfoBO.getParmWeekdayTableBO().getHolidayListId(), parmOrganizationInfoBO.getNextProcessingDay(), parmOrganizationInfoBO.getOrganizationNumber());
            }
        } else {
            parmOrganizationInfoBO.cutting(n);
        }

        // 上次累计日期
        parmOrganizationInfoBO.setLastAccruedThruDay(parmOrganizationInfoBO.getAccruedThruDay());
        // accrued_thru_day &下一处理日 - 1
        parmOrganizationInfoBO.setAccruedThruDay(parmOrganizationInfoBO.getNextProcessingDay().minusDays(1));
        //更新前的t当前系统处理日（today）
        parmOrganizationInfoBO.setLastProcessingDay(parmOrganizationInfoBO.getToday());
        // today 更新前的下一系统处理日（next_processing_day）
        parmOrganizationInfoBO.setToday(nextProcessingDay);

        String eoyEomFla = getEoyEomFla(parmOrganizationInfoBO.getAccruedThruDay(), parmOrganizationInfoBO.getLastAccruedThruDay().plusDays(1));

        parmOrganizationInfoBO.setProcessTodayFlag(ProcessFlagEnum.PROCESS_TODAY_FLAG_DO.getCode());
        parmOrganizationInfoBO.setEoyEomFla(eoyEomFla);
        parmOrganizationInfoBO.setBatchMode(ParamConstant.BATCH_MODE_START);
        parmOrganizationInfoBO.setLastUpdateTime(parmOrganizationInfoBO.getUpdateTime());
        parmOrganizationInfoBO.setUpdateTime(LocalDateTime.now());
        parmOrganizationInfoBO.setUpdateBy("admin");
//        parmOrganizationInfoMapper.updateByPrimaryKeySelective(BeanMapping.copy(parmOrganizationInfoBO, ParmOrganizationInfo.class));
        ParmOrganizationInfo b = BeanMapping.copy(parmOrganizationInfoBO, ParmOrganizationInfo.class);
        log.info("日切后信息:{}", JSON.toJSONString(b));
        parmOrganizationInfoMapper.updateByPrimaryKeySelective(b);
    }

    /**
     * 判断是否累积日期之间是否跨月（年）
     *
     * @param accruedThruDay     当前累计日期
     * @param lastAccruedThruDay 上次一累计日期
     * @return eoyEomFla
     * 0- 正常
     * 1- 月末
     * 2- 年末
     */
    private String getEoyEomFla(LocalDate accruedThruDay, LocalDate lastAccruedThruDay) {
        String eoyEomFla = ParamConstant.EOY_EOM_FLA_NORMAL;
        while (accruedThruDay.isAfter(lastAccruedThruDay)) {
            // 月末
            if (lastAccruedThruDay.isEqual(lastAccruedThruDay.with(TemporalAdjusters.lastDayOfMonth()))) {
                eoyEomFla = ParamConstant.EOY_EOM_FLA_MONTH;
                if (lastAccruedThruDay.getMonth().getValue() == 12) {
                    eoyEomFla = ParamConstant.EOY_EOM_FLA_YEAR;
                }
            }
            lastAccruedThruDay = lastAccruedThruDay.plusDays(1);
        }
        return eoyEomFla;
    }

    /**
     * 日切结束
     * 仅仅更新本机构batch_mode字段为0
     */
    public void cutOverEnd() {
        ParmOrganizationInfo parmOrganizationInfo = parmOrganizationInfoSelfMapper.selectByOrganizationNumber(OrgNumberUtils.getOrg());
        parmOrganizationInfo.setBatchMode(ParamConstant.BATCH_MODE_END);
        parmOrganizationInfoMapper.updateByPrimaryKey(parmOrganizationInfo);
    }
}
