package com.anytech.anytxn.business.common.service;

import com.anytech.anytxn.common.core.exception.AnyTxnCommonException;
import com.anytech.anytxn.common.core.utils.PartitionKeyUtils;
import com.anytech.anytxn.business.dao.account.model.AccountManagementInfo;
import com.anytech.anytxn.business.base.card.domain.dto.CardAuthorizationDTO;
import com.anytech.anytxn.business.dao.customer.mapper.CorporateDownTopReferenceSelfMapper;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;

import java.util.Objects;

/**
 * <pre>
 * Description: 分区键共用生成逻辑
 * Company:	    江融信
 * Version:	    1.0
 * Create at:   2023/8/7
 * <p>
 * 修改历史:
 * 日期    作者    版本  修改描述
 * ------------------------------------------------------------------
 * <AUTHOR>
 * </pre>
 */
@Service
@Slf4j
public class PartitionKeyInitService {

    @Resource
    private CorporateDownTopReferenceSelfMapper downTopReferenceSelfMapper;

    public int partitionKeyGenerator(CardAuthorizationDTO cardAuthorizationDTO,
                                     AccountManagementInfo managementInfo) {
        try {
            String cusId;
            if (!Objects.isNull(cardAuthorizationDTO)) {
                String liability = cardAuthorizationDTO.getLiability();
                cusId = cardAuthorizationDTO.getPrimaryCustomerId();
                if ("C".equals(liability)) {
                    String customerId = downTopReferenceSelfMapper.selectHighestLevelCorpCusId(
                            cardAuthorizationDTO.getOrganizationNumber(),
                            cardAuthorizationDTO.getCorporateCustomerId());
                    if (!StringUtils.isEmpty(customerId)) {
                        cusId = customerId;
                    }
                }
            } else if (!Objects.isNull(managementInfo)) {
                String liability = managementInfo.getLiability();
                cusId = managementInfo.getCustomerId();
                if ("C".equals(liability)) {
                    String customerId = downTopReferenceSelfMapper.selectHighestLevelCorpCusId(
                            managementInfo.getOrganizationNumber(),
                            managementInfo.getCorporateCustomerId());
                    if (!StringUtils.isEmpty(customerId)) {
                        cusId = customerId;
                    }
                }
            } else {
                return 1;
            }
            return PartitionKeyUtils.IntPartitionKey(cusId);
        } catch (AnyTxnCommonException e) {
           //ignore
            log.error("calculate Partitionkey by customerId error ", e);
        }

        return 1;
    }
}
