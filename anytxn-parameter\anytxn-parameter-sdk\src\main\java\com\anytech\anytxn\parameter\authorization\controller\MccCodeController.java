package com.anytech.anytxn.parameter.authorization.controller;

import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import io.swagger.v3.oas.annotations.Operation;
import com.anytech.anytxn.common.core.base.PageResultDTO;
import com.anytech.anytxn.common.core.base.BizBaseController;
import com.anytech.anytxn.common.core.base.AnyTxnHttpResponse;
import com.anytech.anytxn.parameter.base.authorization.domain.dto.MccCodeDTO;
import com.anytech.anytxn.parameter.base.authorization.service.IMccCodeService;
import com.anytech.anytxn.parameter.base.common.enums.ParameterRepDetailEnum;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import jakarta.validation.Valid;
import java.util.List;
import java.util.Map;

/**
 * @description 商户类别码api
 * <AUTHOR>
 * @date 2019/4/4
 */

@RestController
@Tag(name = "商户类别码服务")
public class MccCodeController extends BizBaseController {

    @Autowired
    private IMccCodeService mccCodeService;

    /**
     * @description 分页查询商户类别码列表
     * <AUTHOR>
     * @date 2019/4/4
     * @param page, rows 
     * @return jrx.anytxn.biz.common.web.AnyTxnHttpResponse<jrx.anytxn.biz.common.dto.PageResultDTO<jrx.anytxn.auth.dto.MccCodeDTO>>
     */
    
    @Operation(summary = "分页查询商户类别码信息")
    @GetMapping("/param/mccCodes/{page}/{rows}")
    public AnyTxnHttpResponse<PageResultDTO<MccCodeDTO>> getMccCodeList(@Parameter(name = "page", description = "当前页", example = "1")@PathVariable("page") int page,
                                                                        @Parameter(name = "rows", description = "每页大小", example = "8")@PathVariable("rows") int rows,
                                                                        @RequestParam(value = "mccCde",required = false) String mccCde,
                                                                        @RequestParam(value = "description",required = false) String description,
                                                                        @RequestParam(required = false) String organizationNumber) {
        PageResultDTO<MccCodeDTO> result = mccCodeService.findListMccCode(page, rows, mccCde, description, organizationNumber);
        return AnyTxnHttpResponse.success(result);
    }

    
    /**
     * @description 根据主键查询商户类别码信息
     * <AUTHOR>
     * @date 2019/4/4
     * @param id 
     * @return jrx.anytxn.biz.common.web.AnyTxnHttpResponse<jrx.anytxn.auth.dto.MccCodeDTO>
     */
    
    @Operation(summary = "根据主键查询商户类别码信息")
    @GetMapping("/param/mccCode/{id}")
    @Parameter(name = "id", description = "技术主键")
    public AnyTxnHttpResponse<MccCodeDTO> getMccCode(@PathVariable Long id) {
        MccCodeDTO mccCodeDTO = mccCodeService.findMccCode(id);
        return AnyTxnHttpResponse.success(mccCodeDTO);
    }


    /**
     * @description 删除商户类别码信息  
     * <AUTHOR>
     * @date 2019/4/4
     * @param id 
     * @return jrx.anytxn.biz.common.web.AnyTxnHttpResponse
     */
    
    @Operation(summary = "删除商户类别码信息")
    @DeleteMapping("/param/mccCode/{id}")
    public AnyTxnHttpResponse cancelMccCode(@PathVariable Long id) {
        Boolean flag = mccCodeService.removeMccCode(id);
        return AnyTxnHttpResponse.success(flag, ParameterRepDetailEnum.DEL.message());
    }

    /**
     * @description 新增商户类别码信息  
     * <AUTHOR>
     * @date 2019/4/4
     * @param mccCodeDTO
     * @return jrx.anytxn.biz.common.web.AnyTxnHttpResponse
     */
    
    @Operation(summary = "新增商户类别码信息")
    @PostMapping("/param/mccCode")
    public AnyTxnHttpResponse create(@Valid @RequestBody MccCodeDTO mccCodeDTO) {
        Boolean flag = mccCodeService.addMccCode(mccCodeDTO);
        return AnyTxnHttpResponse.success(flag,ParameterRepDetailEnum.CREATE.message());
    }

    /**
     * @description 商户类别码信息修改
     * <AUTHOR>
     * @date 2019/4/4
     * @param mccCodeDTO
     * @return jrx.anytxn.biz.common.web.AnyTxnHttpResponse
     */
    
    @Operation(summary = "商户类别码信息修改")
    @PutMapping("/param/mccCode")
    public AnyTxnHttpResponse modify(@Valid @RequestBody MccCodeDTO mccCodeDTO) {
        Boolean flag = mccCodeService.modifyMccCode(mccCodeDTO);
        return AnyTxnHttpResponse.success(flag,ParameterRepDetailEnum.UPDATE.message());
    }

    /**
     * @description 查询所有商户类别码
     * <AUTHOR>
     * @date 2019/4/13
     * @return AnyTxnHttpResponse<List<String>>
     */
    @Operation(summary = "查询所有商户类别码")
    @GetMapping("/param/mccCode/mccCodes")
    public AnyTxnHttpResponse<List<Map<String,String>>> selectAllCode(String organizationNumber) {
        List<Map<String,String>> list = mccCodeService.getMccCodes(organizationNumber);
        return AnyTxnHttpResponse.success(list);
    }

}
