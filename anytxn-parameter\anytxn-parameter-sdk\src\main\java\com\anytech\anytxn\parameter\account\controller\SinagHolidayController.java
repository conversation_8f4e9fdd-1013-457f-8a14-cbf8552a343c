package com.anytech.anytxn.parameter.account.controller;

import com.anytech.anytxn.common.core.base.ParameterCompare;
import com.anytech.anytxn.common.core.base.PageResultDTO;
import com.anytech.anytxn.common.core.base.AnyTxnHttpResponse;
import com.anytech.anytxn.common.core.base.BizBaseController;
import com.anytech.anytxn.parameter.base.account.domain.dto.ParmChequePaymentTableDTO;
import com.anytech.anytxn.parameter.base.account.service.ISinagHolidayService;
import com.anytech.anytxn.parameter.base.common.enums.ParameterRepDetailEnum;
import com.anytech.anytxn.parameter.base.common.domain.dto.system.HolidayListResDTO;
import io.swagger.v3.oas.annotations.tags.Tag;
import io.swagger.v3.oas.annotations.Operation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import jakarta.validation.Valid;

@Tag(name = "新加坡假日参数定义")
@RestController
public class SinagHolidayController extends BizBaseController {


    @Autowired
    private ISinagHolidayService sinagHolidayService;


    @Operation(summary = "通过机构号和表Id查询参数信息", description = "通过机构号和表Id查询")
    @GetMapping(value = "/param/sinagHoliday/organizationNumber/{organizationNumber}/tableId/{tableId}")
    public AnyTxnHttpResponse<ParmChequePaymentTableDTO> getAutoPaymentTableInfo(@PathVariable String organizationNumber, @PathVariable String tableId) {
        ParmChequePaymentTableDTO res = sinagHolidayService.findByTableIdAndOrgNo(tableId, organizationNumber);
        return AnyTxnHttpResponse.success(res);
    }


    @PostMapping(value = "/param/sinagHoliday")
    @Operation(summary = "创建支票还款假日列表信息")
    public AnyTxnHttpResponse<Object> create(@Valid @RequestBody ParmChequePaymentTableDTO chequePaymentTableDTO) {
        ParameterCompare resp = sinagHolidayService.add(chequePaymentTableDTO);
        return AnyTxnHttpResponse.success(resp, ParameterRepDetailEnum.CREATE.message());
    }


    @PutMapping(value = "/param/sinagHoliday")
    @Operation(summary = "根据id更新假日列表信息", description = "")
    public AnyTxnHttpResponse<Object> modify(@Valid @RequestBody ParmChequePaymentTableDTO chequePaymentTableDTO) {
        ParameterCompare resp = sinagHolidayService.modify(chequePaymentTableDTO);
        return AnyTxnHttpResponse.success(resp, ParameterRepDetailEnum.UPDATE.message());
    }


    @DeleteMapping(value = "/param/sinagHoliday/id/{id}")
    @Operation(summary = "根据id删除", description = "需传入id")
    public AnyTxnHttpResponse<Object> remove(@PathVariable String id) {
        ParameterCompare bool = sinagHolidayService.remove(id);
        return AnyTxnHttpResponse.success(bool, ParameterRepDetailEnum.DEL.message());
    }


    @Operation(summary = "获取支票还款假日列表详情，通过id", description = "")
    @GetMapping(value = "/param/sinagHoliday/id/{id}")
    public AnyTxnHttpResponse<ParmChequePaymentTableDTO> getByIndex(@PathVariable String id) {
        ParmChequePaymentTableDTO chequePaymentTableDTO = sinagHolidayService.find(id);
        return AnyTxnHttpResponse.success(chequePaymentTableDTO);
    }


    @Operation(summary = "支票还款假日参数分页查询", description = "分页查询")
    @GetMapping(value = "/param/sinagHoliday/pageNum/{pageNum}/pageSize/{pageSize}")
    public AnyTxnHttpResponse<PageResultDTO<ParmChequePaymentTableDTO>> getAllPage(@PathVariable(value = "pageNum") Integer pageNum,
                                                                                   @PathVariable(value = "pageSize") Integer pageSize,
                                                                                   @RequestParam(value = "tableId", required = false) String tableId,
                                                                                   @RequestParam(value = "description", required = false) String description,
                                                                                   @RequestParam(required = false) String organizationNumber) {
        PageResultDTO<ParmChequePaymentTableDTO> response;
        response = sinagHolidayService.findAll(pageNum, pageSize, tableId, description, organizationNumber);

        return AnyTxnHttpResponse.success(response);
    }


    @Operation(summary = "根据支票还款的假日列表ID查询假日列表数据信息")
    @GetMapping(value = "/param/sinagHoliday/holidayListId/{holidayListId}")
    public AnyTxnHttpResponse<HolidayListResDTO> getByHoliayInfo(@PathVariable String holidayListId, @RequestParam String organizationNumber) {
        HolidayListResDTO res = sinagHolidayService.findByHolidayListId(holidayListId, organizationNumber);
        return AnyTxnHttpResponse.success(res);
    }


}
