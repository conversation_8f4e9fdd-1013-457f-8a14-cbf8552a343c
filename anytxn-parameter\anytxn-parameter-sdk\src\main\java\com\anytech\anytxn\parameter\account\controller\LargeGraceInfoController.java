package com.anytech.anytxn.parameter.account.controller;

import io.swagger.v3.oas.annotations.tags.Tag;
import io.swagger.v3.oas.annotations.Operation;
import com.anytech.anytxn.common.core.base.PageResultDTO;
import com.anytech.anytxn.common.core.base.AnyTxnHttpResponse;
import com.anytech.anytxn.common.core.base.BizBaseController;
import com.anytech.anytxn.parameter.base.account.domain.dto.LargeGraceInfoReqDTO;
import com.anytech.anytxn.parameter.base.account.domain.dto.LargeGraceInfoResDTO;
import com.anytech.anytxn.parameter.base.account.service.ILargeGraceInfoService;
import com.anytech.anytxn.parameter.base.common.enums.ParameterRepDetailEnum;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import jakarta.validation.Valid;

/**
 * 超长免息期
 * <AUTHOR>
 * @date 2020-02-07
 */
@Tag(name = "超长免息期参数")
@RestController
public class LargeGraceInfoController extends BizBaseController {

    @Autowired
    private ILargeGraceInfoService largeGraceInfoService;

    /**
     * 根据机构号分页查询超长免息期参数信息
     * @param pageNum 页码
     * @param pageSize 每页大小
     * @return AnyTxnHttpResponse<PageResultDTO<BinCardNumberUsedDTO>>
     */
    @GetMapping(value = "/param/largeGrace/pageNum/{pageNum}/pageSize/{pageSize}")
    @Operation(summary="分页查询超长免息期信息", description = "需传入页码以及页面大小")
    public AnyTxnHttpResponse<PageResultDTO<LargeGraceInfoResDTO>> getPageByStatus (@PathVariable(value = "pageNum") Integer pageNum,
                                                                                    @PathVariable(value = "pageSize") Integer pageSize,
                                                                                    @RequestParam(value = "tableId",required = false) String tableId,
                                                                                    @RequestParam(value = "description",required = false) String description,
                                                                                    @RequestParam(value = "delayedGraceCycle",required = false) String delayedGraceCycle,
                                                                                    @RequestParam(required = false) String organizationNumber) {
        PageResultDTO<LargeGraceInfoResDTO> txnPage = largeGraceInfoService.findAll(pageNum, pageSize, tableId,description,delayedGraceCycle, organizationNumber);
        return AnyTxnHttpResponse.success(txnPage);
    }

    /**
     * 根据id查询超长免息期信息
     * @param id 超长免息期id
     * @return AnyTxnHttpResponse<BinCardNumberUsedDTO>
     */
    @Operation(summary="根据id查询超长免息期信息", description = "")
    @GetMapping(value = "/param/largeGrace/id/{id}")
    public AnyTxnHttpResponse<LargeGraceInfoResDTO> getByIndex(@PathVariable String id){
        LargeGraceInfoResDTO largeGraceInfoResDTO = largeGraceInfoService.findById(id);
        return AnyTxnHttpResponse.success(largeGraceInfoResDTO);
    }

    /**
     * 新增超长免息期信息
     * @param record 超长免息期信息请求数据
     * @return AnyTxnHttpResponse
     */
    @PostMapping("/param/largeGrace")
    @Operation(summary = "新增超长免息期信息")
    public AnyTxnHttpResponse<Object> create(@Valid @RequestBody LargeGraceInfoReqDTO record) {
        return AnyTxnHttpResponse.success(largeGraceInfoService.add(record),ParameterRepDetailEnum.CREATE.message());
    }

    /**
     * 更新超长免息期信息
     * @param record 超长免息期信息请求数据
     * @return AnyTxnHttpResponse
     */
    @PutMapping(value = "/param/largeGrace")
    @Operation(summary="根据id更新超长免息期信息")
    public AnyTxnHttpResponse<Object> modify(@Valid @RequestBody LargeGraceInfoReqDTO record) {
        return AnyTxnHttpResponse.success(largeGraceInfoService.update(record),ParameterRepDetailEnum.UPDATE.message());
    }

    /**
     * 通过id删除超长免息期信息
     * @param id 技术id
     * @return AnyTxnHttpResponse
     */
    @DeleteMapping(value = "/param/largeGrace/id/{id}")
    @Operation(summary="根据id删除超长免息期信息", description = "需传入id")
    public AnyTxnHttpResponse<Object> remove(@PathVariable String id) {
        return AnyTxnHttpResponse.success(largeGraceInfoService.delete(id),ParameterRepDetailEnum.DEL.message());
    }
}
