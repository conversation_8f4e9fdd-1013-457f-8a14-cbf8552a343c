package com.anytech.anytxn.parameter.common.controller.system;

import io.swagger.v3.oas.annotations.tags.Tag;
import io.swagger.v3.oas.annotations.Operation;
import com.anytech.anytxn.common.core.base.PageResultDTO;
import com.anytech.anytxn.common.core.utils.OrgNumberUtils;
import com.anytech.anytxn.common.core.base.AnyTxnHttpResponse;
import com.anytech.anytxn.common.core.base.BizBaseController;
import com.anytech.anytxn.parameter.base.common.enums.ParameterRepDetailEnum;
import com.anytech.anytxn.parameter.base.common.service.system.IOrganizationCycleDayService;
import com.anytech.anytxn.parameter.base.common.domain.dto.system.OrganizationCycleDayDTO;
import com.anytech.anytxn.parameter.base.common.domain.dto.system.OrganizationInfoReqDTO;
import com.anytech.anytxn.parameter.base.common.domain.dto.system.OrganizationInfoResDTO;
import com.anytech.anytxn.parameter.base.common.service.system.IOrganizationInfoService;
import com.anytech.anytxn.parameter.base.common.enums.AnyTxnParameterRespCodeEnum;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import jakarta.validation.Valid;
import java.util.List;

/**
 * 组织机构参数
 *
 * <AUTHOR>
 * @date 2018-08-15 9:25
 **/
@RestController
@Tag(name = "机构参数")
public class OrganizationInfoController extends BizBaseController {

    private final Logger logger = LoggerFactory.getLogger(getClass());

    @Autowired
    private IOrganizationInfoService organizationInfoService;
    @Autowired
    private IOrganizationCycleDayService organizationCycleDayService;

    /**
     * 创建机构参数条目
     */

    @PostMapping(value = "/param/organizationInfo")
    @Operation(summary = "新增机构参数")
    public AnyTxnHttpResponse<Object> create(@Valid @RequestBody OrganizationInfoReqDTO organizationInfoReq){
        return AnyTxnHttpResponse.success(organizationInfoService.add(organizationInfoReq), ParameterRepDetailEnum.CREATE.message());
    }

    /**
     * 更新机构参数信息
     */
    @PutMapping(value = "/param/organizationInfo")
    @Operation(summary="更新机构参数")
    public AnyTxnHttpResponse<Object> modify(@Valid @RequestBody OrganizationInfoReqDTO organizationInfoReq) {
        return AnyTxnHttpResponse.success(organizationInfoService.modify(organizationInfoReq),ParameterRepDetailEnum.UPDATE.message());

    }

    /**
     * 删除机构参数条目，通过id
     * @param id 技术id
     * @return AnyTxnHttpResponse<Boolean>
     */
    @DeleteMapping(value = "/param/organizationInfo/id/{id}")
    @Operation(summary="根据id删除", description = "需传入id")
    public AnyTxnHttpResponse<Object> remove(@PathVariable String id) {
        return AnyTxnHttpResponse.success(organizationInfoService.remove(id),ParameterRepDetailEnum.DEL.message());

    }

    /**
     * 根据根据id获取详情
     */

    @GetMapping(value = "/param/organizationInfo/id/{id}")
    @Operation(summary="根据id查询信息")
    public AnyTxnHttpResponse<OrganizationInfoResDTO> getById(@PathVariable(value = "id")String id) {
        return AnyTxnHttpResponse.success(organizationInfoService.find(id));
    }

    /**
     * 分页查询,获取机构参数信息
     */

    @GetMapping(value = "/param/organizationInfo/pageNum/{pageNum}/pageSize/{pageSize}")
    @Operation(summary="获取列表,分页", description = "需传入页码以及页面大小")
    public AnyTxnHttpResponse<PageResultDTO<OrganizationInfoResDTO>> getPageList(@PathVariable(value = "pageNum") Integer pageNum,
                                                                                 @PathVariable(value = "pageSize") Integer pageSize,
                                                                                 @RequestBody(required = false) OrganizationInfoReqDTO organizationInfoReqDTO) {
        String organizationNumber = organizationInfoReqDTO == null || StringUtils.isEmpty(organizationInfoReqDTO.getOrganizationNumber()) ? OrgNumberUtils.getOrg() : organizationInfoReqDTO.getOrganizationNumber();
        String description = organizationInfoReqDTO == null || StringUtils.isEmpty(organizationInfoReqDTO.getDescription()) ? null : organizationInfoReqDTO.getDescription();
        return AnyTxnHttpResponse.success(organizationInfoService.findListByPage(pageNum, pageSize, organizationNumber,description));
    }

    /**
     * business需要的接口
     * 获得机构参数表
     * @param organizationNumber 机构号
     * @return AnyTxnHttpResponse<OrganizationInfoRes>
     *
     */
    @GetMapping(value = "/param/organizationInfo/organizationNumber/{organizationNumber}")
    @Operation(summary = "获得机构参数")
    public AnyTxnHttpResponse<OrganizationInfoResDTO> getOrganizationInfo(@PathVariable String organizationNumber) {
        OrganizationInfoResDTO organizationInfoRes = organizationInfoService.findOrganizationInfo(organizationNumber);
        if (logger.isDebugEnabled()) {
            logger.debug("request [/param/organizationInfo/organizationNumber/{}], return [{}]", organizationNumber, organizationInfoRes);
        }
        return AnyTxnHttpResponse.success(organizationInfoRes);
    }
    /**
     * 仅限制前段获取使用
     * 获得机构参数表全部数据
     */
    @GetMapping(value = "/param/organizationInfo/all")
    @Operation(summary = "获得机构参数")
    public AnyTxnHttpResponse< List<OrganizationInfoResDTO>> getOrganizationInfoAll() {
        List<OrganizationInfoResDTO> result= organizationInfoService.findOrganizationInfoAll(true);
        return AnyTxnHttpResponse.success(result);

    }

    /**
     * 按当日处理标志获取信息
     * @param pageNum 页号
     * @param pageSize 每页大小
     * @param processTodayFlag 当日处理标志
     * @return
     */
    @GetMapping(value = "/param/organizationInfo/pageNum/{pageNum}/pageSize/{pageSize}/processTodayFlag/{processTodayFlag}")
    @Operation(summary="获取列表,分页", description = "需传入页码以及页面大小")
    public AnyTxnHttpResponse<PageResultDTO<OrganizationInfoResDTO>> getPageByStatus (@PathVariable(value = "pageNum") Integer pageNum,
                                                                             @PathVariable(value = "pageSize") Integer pageSize,
                                                                             @PathVariable(value = "processTodayFlag") String processTodayFlag) {
        PageResultDTO<OrganizationInfoResDTO> txnPage = organizationInfoService.findByProcessTodayFlagPage(pageNum, pageSize,processTodayFlag);
        if (logger.isDebugEnabled()) {
            logger.debug("request [/param/organizationInfo/pageNum/{}/pageSize/{}/processTodayFlag/{}], return [{}]", pageNum, pageSize, processTodayFlag,txnPage);
        }
        return AnyTxnHttpResponse.success(txnPage);

    }

    /**
     * 保存机构账单日参数
     * @param orgCycleDay 机构账单日参数请求数据
     * @return AnyTxnHttpResponse<OrganizationInfoRes>
     */
    @PostMapping(value = "/param/organizationInfo/cycleDay")
    @Operation(summary = "新增或修改机构账单参数")
    public AnyTxnHttpResponse<Object> create(@RequestBody OrganizationCycleDayDTO orgCycleDay){
        if (StringUtils.isEmpty(orgCycleDay.getOrganizationNumber())) {
            return AnyTxnHttpResponse.fail(AnyTxnParameterRespCodeEnum.P_PARAMETER_EMPTY_FAULT.getCode(),ParameterRepDetailEnum.ORG.message());
        }
        if (orgCycleDay.getOrganizationNumber() != null && orgCycleDay.getCycleDay().isEmpty()) {
            return AnyTxnHttpResponse.fail(AnyTxnParameterRespCodeEnum.P_PARAMETER_EMPTY_FAULT.getCode(),ParameterRepDetailEnum.CYCLE_DAY.message());
        }
        return AnyTxnHttpResponse.success(organizationCycleDayService.saveOrgCycleDay(orgCycleDay),ParameterRepDetailEnum.SET.message());
    }


    /**
     * 查询机构账单日参数
     * @param organizationNumber 机构
     * @return AnyTxnHttpResponse<OrganizationInfoRes>
     */
    @GetMapping(value = "/param/organizationInfo/cycleDay/organizationNumber/{organizationNumber}")
    @Operation(summary = "查询")
    public AnyTxnHttpResponse<OrganizationCycleDayDTO> create(@PathVariable("organizationNumber")String organizationNumber){
        if (StringUtils.isEmpty(organizationNumber)) {
            return AnyTxnHttpResponse.fail(AnyTxnParameterRespCodeEnum.P_PARAMETER_EMPTY_FAULT.getCode(),ParameterRepDetailEnum.ORG.message());
        }
        return AnyTxnHttpResponse.success(organizationCycleDayService.findOrgCycleDay(organizationNumber),ParameterRepDetailEnum.QUERY.message());
    }

}
