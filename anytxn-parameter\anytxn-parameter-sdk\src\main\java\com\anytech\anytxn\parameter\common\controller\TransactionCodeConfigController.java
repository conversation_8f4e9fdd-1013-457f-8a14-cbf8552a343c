package com.anytech.anytxn.parameter.common.controller;

import io.swagger.v3.oas.annotations.tags.Tag;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import com.anytech.anytxn.common.core.base.PageResultDTO;
import com.anytech.anytxn.common.core.base.AnyTxnHttpResponse;
import com.anytech.anytxn.common.core.base.BizBaseController;
import com.anytech.anytxn.parameter.base.common.domain.dto.TransactionCodeConfigReqDTO;
import com.anytech.anytxn.parameter.base.common.domain.dto.TransactionCodeConfigResDTO;
import com.anytech.anytxn.parameter.base.common.service.ITransactionCodeConfigService;
import com.anytech.anytxn.parameter.base.common.enums.ParameterRepDetailEnum;
import com.anytech.anytxn.parameter.base.common.utils.AnyTxnHttpResponseHelper;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import jakarta.validation.Valid;

/**
 * 内部交易码配置参数
 * <AUTHOR> tingting
 * @date 2018/10/19
 */
@Tag(name = "内部交易码配置参数")
@RestController
public class TransactionCodeConfigController extends BizBaseController {

    @Autowired
    private ITransactionCodeConfigService transactionCodeConfigService;

    /**
     * 根据机构号与类型查询
     * @param organizationNumber 机构号
     * @param type 类型
     * @return TransactionCodeConfigRes 内部交易码配置表返回参数
     *
     */
    @Operation(summary = "通过机构号,type查询内部交易码配置参数", description = "通过机构号,type查询内部交易码配置参数")
    @GetMapping(value = "/param/transactionCodeConfig/organizationNumber/{organizationNumber}/type/{type}")
    public AnyTxnHttpResponse<TransactionCodeConfigResDTO> getByOrgAndType(@PathVariable String organizationNumber, @PathVariable String type) {
        TransactionCodeConfigResDTO res;
        res = transactionCodeConfigService.findByOrgAndType(organizationNumber,type);
        return AnyTxnHttpResponse.success(res);

    }


    /**
     * @description 分页查询内部交易码配置参数
     * <AUTHOR>
     * @date 2020/06/22
     * @param page, rows
     * @return jrx.anytxn.biz.common.web.AnyTxnHttpResponse<jrx.anytxn.biz.common.dto.PageResultDTO<jrx.anytxn.auth.dto.VelocitySetDefinitionDTO>>
     */
    @Operation(summary = "分页查询内部交易码配置参数")
    @GetMapping("/param/transactionCodeConfig/{page}/{rows}")
    public AnyTxnHttpResponse<PageResultDTO<TransactionCodeConfigResDTO>> getVelocitySetDefinitionList(@Parameter(name = "page", description = "当前页", example = "1")@PathVariable("page") int page,
                                                                                                       @Parameter(name = "rows", description = "每页大小", example = "8")@PathVariable("rows") int rows,
                                                                                                       TransactionCodeConfigReqDTO velocitySetDTO) {
        PageResultDTO<TransactionCodeConfigResDTO> result = transactionCodeConfigService.findByPage(page, rows,velocitySetDTO);
        return AnyTxnHttpResponse.success(result);
    }

    /**
     * 根据cardProductCode查询内部交易码配置参数
     * @param
     * @return HttpApiResponse<VelocityControlDTO>
     */
    @Operation(summary="根据id查询内部交易码配置参数", description = "需传入id")
    @GetMapping(value = "/param/transactionCodeConfig/id/{id}")
    public AnyTxnHttpResponse<TransactionCodeConfigResDTO> getByCardProductCode(@PathVariable String id){
        TransactionCodeConfigResDTO velocityControlDTO = transactionCodeConfigService.findById(id);
        return AnyTxnHttpResponse.success(velocityControlDTO);
    }

    /**
     * 新建内部交易码配置参数
     * @param record
     * @return HttpApiResponse
     */
    @PostMapping("/param/transactionCodeConfig")
    @Operation(summary = "新增内部交易码配置参数")
    public AnyTxnHttpResponse create(@Valid @RequestBody TransactionCodeConfigReqDTO record) {
        return AnyTxnHttpResponse.success(transactionCodeConfigService.add(record),ParameterRepDetailEnum.CREATE.message());
    }

    /**
     * 更新内部交易码配置参数
     * @param record
     * @return HttpApiResponse
     */
    @PutMapping(value = "/param/transactionCodeConfig")
    @Operation(summary="根据id更新内部交易码配置参数")
    public AnyTxnHttpResponse modify(@Valid @RequestBody TransactionCodeConfigReqDTO record) {
        transactionCodeConfigService.modify(record);
        return AnyTxnHttpResponseHelper.succ(ParameterRepDetailEnum.UPDATE.message());
    }

    /**
     * 通过id删除流量规则信息
     * @param id 技术id
     * @return HttpApiResponse
     */
    @DeleteMapping(value = "/param/transactionCodeConfig/id/{id}")
    @Operation(summary="根据id删除内部交易码配置参数", description = "需传入id")
    public AnyTxnHttpResponse remove(@PathVariable String id) {
        transactionCodeConfigService.remove(id);
        return AnyTxnHttpResponseHelper.succ(ParameterRepDetailEnum.DEL.message());
    }


}
