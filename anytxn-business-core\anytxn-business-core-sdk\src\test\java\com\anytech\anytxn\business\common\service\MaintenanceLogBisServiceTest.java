package com.anytech.anytxn.business.common.service;

import com.anytech.anytxn.business.base.common.constants.MaintenanceConstant;
import com.anytech.anytxn.business.base.common.domain.dto.AnytxnLogDTO;
import com.anytech.anytxn.business.base.common.domain.dto.MaintenanceLogDTO;
import com.anytech.anytxn.business.base.common.service.IMaintenanceLogBisService;
import com.anytech.anytxn.business.base.customer.domain.dto.CustomerAddressInfoDTO;
import com.anytech.anytxn.business.base.customer.enums.DeleteIndicatorEnum;
import com.anytech.anytxn.business.dao.customer.mapper.MaintenanceLogMapper;
import com.anytech.anytxn.business.dao.customer.mapper1.ParmSysCodeTypeBisMapper;
import com.anytech.anytxn.business.dao.customer.model.CustomerAddressInfo;
import com.anytech.anytxn.business.dao.customer.model.MaintenanceLog;
import com.anytech.anytxn.business.dao.customer.model.ParmSysCode;
import com.anytech.anytxn.common.core.utils.LoginUserUtils;
import com.anytech.anytxn.common.core.utils.OrgNumberUtils;
import com.anytech.anytxn.common.core.utils.TenantUtils;
import com.anytech.anytxn.common.sequence.utils.SequenceIdGen;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.*;
import org.mockito.junit.jupiter.MockitoExtension;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.List;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.*;

/**
 * MaintenanceLogBisServiceImpl 测试类
 * 
 * <AUTHOR> Generator
 * @date 2025-01-25
 */
@ExtendWith(MockitoExtension.class)
class MaintenanceLogBisServiceTest {

    @Mock
    private MaintenanceLogMapper maintenanceLogMapper;

    @Mock
    private ParmSysCodeTypeBisMapper parmSysCodeTypeMapper;

    @Mock
    private SequenceIdGen sequenceIdGen;

    @InjectMocks
    private MaintenanceLogBisServiceImpl maintenanceLogBisService;

    @Captor
    private ArgumentCaptor<MaintenanceLog> maintenanceLogCaptor;

    private MaintenanceLogDTO maintenanceLogDTO;
    private TestObject oldTestObject;
    private TestObject newTestObject;
    private CustomerAddressInfo oldAddressInfo;
    private CustomerAddressInfo newAddressInfo;
    private CustomerAddressInfoDTO oldAddressDTO;
    private CustomerAddressInfoDTO newAddressDTO;

    @BeforeEach
    void setUp() {
        try (MockedStatic<OrgNumberUtils> mockOrgUtils = mockStatic(OrgNumberUtils.class)) {
            mockOrgUtils.when(OrgNumberUtils::getOrg).thenReturn("ORG001");
            
            // 初始化测试数据
            setupTestData();
        }
    }

    private void setupTestData() {
        // 初始化MaintenanceLogDTO
        maintenanceLogDTO = new MaintenanceLogDTO();
        maintenanceLogDTO.setOperatorId("testuser");
        maintenanceLogDTO.setOperationType(MaintenanceConstant.OPERATION_U);
        maintenanceLogDTO.setTransactionDataType(MaintenanceConstant.DATA_P);
        maintenanceLogDTO.setPrimaryKeyValue("CARD001");
        maintenanceLogDTO.setOperationTimestamp(LocalDateTime.now());

        // 初始化测试对象
        oldTestObject = new TestObject();
        oldTestObject.setName("OldName");
        oldTestObject.setValue("OldValue");
        oldTestObject.setAmount(100);

        newTestObject = new TestObject();
        newTestObject.setName("NewName");
        newTestObject.setValue("NewValue");
        newTestObject.setAmount(200);

        // 初始化地址信息
        oldAddressInfo = new CustomerAddressInfo();
        oldAddressInfo.setType("HOME");
        oldAddressInfo.setAddress("Old Address");

        newAddressInfo = new CustomerAddressInfo();
        newAddressInfo.setType("HOME");
        newAddressInfo.setAddress("New Address");

        // 初始化地址DTO
        oldAddressDTO = new CustomerAddressInfoDTO();
        oldAddressDTO.setType("WORK");
        oldAddressDTO.setAddress("Old Work Address");

        newAddressDTO = new CustomerAddressInfoDTO();
        newAddressDTO.setType("WORK");
        newAddressDTO.setAddress("New Work Address");
    }

    @Test
    void shouldInsertMaintenanceLog_whenValidParametersProvided() {
        // Given
        LocalDate nextProcessingDay = LocalDate.now().plusDays(1);
        String cardNumber = "CARD001";
        String tableName = "CARD_BASIC_INFO";

        try (MockedStatic<LoginUserUtils> mockLoginUtils = mockStatic(LoginUserUtils.class);
             MockedStatic<TenantUtils> mockTenantUtils = mockStatic(TenantUtils.class);
             MockedStatic<OrgNumberUtils> mockOrgUtils = mockStatic(OrgNumberUtils.class)) {
            
            mockLoginUtils.when(LoginUserUtils::getLoginUserName).thenReturn("testuser");
            mockTenantUtils.when(TenantUtils::getTenantId).thenReturn("tenant001");
            mockOrgUtils.when(OrgNumberUtils::getOrg).thenReturn("ORG001");
            
            when(sequenceIdGen.generateId("tenant001")).thenReturn("123456789");
            
            // 创建ParmSysCode对象
            ParmSysCode parmSysCode = new ParmSysCode();
            parmSysCode.setTypeId("Field Name");
            parmSysCode.setCodeDesc("Field Description");
            when(parmSysCodeTypeMapper.selectByTypeIdAndTypeName(eq(tableName), anyString()))
                .thenReturn(parmSysCode);
            when(maintenanceLogMapper.insert(any(MaintenanceLog.class))).thenReturn(1);

            // When
            maintenanceLogBisService.insertMaintenanceLog(newTestObject, oldTestObject, 
                nextProcessingDay, cardNumber, tableName);

            // Then
            verify(maintenanceLogMapper, atLeastOnce()).insert(maintenanceLogCaptor.capture());
            MaintenanceLog capturedLog = maintenanceLogCaptor.getValue();
            
            assertNotNull(capturedLog);
            assertEquals("testuser", capturedLog.getOperatorId());
            assertEquals(MaintenanceConstant.OPERATION_U, capturedLog.getOperationType());
            assertEquals(MaintenanceConstant.DATA_P, capturedLog.getTransactionDataType());
            assertEquals(cardNumber, capturedLog.getPrimaryKeyValue());
            assertEquals(tableName, capturedLog.getMemo());
            assertEquals(DeleteIndicatorEnum.NOT_DELETE.getCode(), capturedLog.getDeleteIndicator());
            assertEquals(1L, capturedLog.getVersionNumber());
        }
    }

    @Test
    void shouldNotInsertMaintenanceLog_whenObjectsAreNull() {
        // Given
        LocalDate nextProcessingDay = LocalDate.now().plusDays(1);
        String cardNumber = "CARD001";
        String tableName = "CARD_BASIC_INFO";

        // When
        maintenanceLogBisService.insertMaintenanceLog(null, oldTestObject, 
            nextProcessingDay, cardNumber, tableName);

        // Then
        verify(maintenanceLogMapper, never()).insert(any(MaintenanceLog.class));
    }

    @Test
    void shouldAddMaintenanceLog_whenValidParametersProvided() {
        // Given
        String tableName = "TEST_TABLE";

        try (MockedStatic<LoginUserUtils> mockLoginUtils = mockStatic(LoginUserUtils.class);
             MockedStatic<TenantUtils> mockTenantUtils = mockStatic(TenantUtils.class);
             MockedStatic<OrgNumberUtils> mockOrgUtils = mockStatic(OrgNumberUtils.class)) {
            
            mockLoginUtils.when(LoginUserUtils::getLoginUserName).thenReturn("testuser");
            mockTenantUtils.when(TenantUtils::getTenantId).thenReturn("tenant001");
            mockOrgUtils.when(OrgNumberUtils::getOrg).thenReturn("ORG001");
            
            when(sequenceIdGen.generateId("tenant001")).thenReturn("123456789");
            
            // 创建ParmSysCode对象
            ParmSysCode parmSysCode = new ParmSysCode();
            parmSysCode.setTypeId("NAME");
            parmSysCode.setCodeDesc("Name Field");
            when(parmSysCodeTypeMapper.selectByTypeIdAndTypeName(eq(tableName), anyString()))
                .thenReturn(parmSysCode);
            when(maintenanceLogMapper.insert(any(MaintenanceLog.class))).thenReturn(1);

            // When
            maintenanceLogBisService.add(maintenanceLogDTO, newTestObject, oldTestObject, tableName);

            // Then
            verify(maintenanceLogMapper, atLeastOnce()).insert(maintenanceLogCaptor.capture());
            MaintenanceLog capturedLog = maintenanceLogCaptor.getValue();
            
            assertNotNull(capturedLog);
            assertEquals("testuser", capturedLog.getOperatorId());
            assertEquals(MaintenanceConstant.OPERATION_U, capturedLog.getOperationType());
            assertEquals(MaintenanceConstant.DATA_P, capturedLog.getTransactionDataType());
            assertEquals("CARD001", capturedLog.getPrimaryKeyValue());
            assertEquals(tableName, capturedLog.getMemo());
            assertEquals("Name Field", capturedLog.getColumnName());
            assertNotNull(capturedLog.getOriginalValue());
            assertNotNull(capturedLog.getUpdatedValue());
        }
    }

    @Test
    void shouldReturnEarly_whenMaintenanceLogDTOIsNull() {
        // Given
        String tableName = "TEST_TABLE";

        // When
        maintenanceLogBisService.add(null, newTestObject, oldTestObject, tableName);

        // Then
        verify(maintenanceLogMapper, never()).insert(any(MaintenanceLog.class));
        verify(sequenceIdGen, never()).generateId(anyString());
    }

    @Test
    void shouldReturnEarly_whenMaintenanceLogDTOHasId() {
        // Given
        maintenanceLogDTO.setId(123L);
        String tableName = "TEST_TABLE";

        // When
        maintenanceLogBisService.add(maintenanceLogDTO, newTestObject, oldTestObject, tableName);

        // Then
        verify(maintenanceLogMapper, never()).insert(any(MaintenanceLog.class));
        verify(sequenceIdGen, never()).generateId(anyString());
    }

    @Test
    void shouldHandleCustomerAddressInfo_whenObjectIsCustomerAddressInfo() {
        // Given
        String tableName = "CUSTOMER_ADDRESS_INFO";

        try (MockedStatic<LoginUserUtils> mockLoginUtils = mockStatic(LoginUserUtils.class);
             MockedStatic<TenantUtils> mockTenantUtils = mockStatic(TenantUtils.class);
             MockedStatic<OrgNumberUtils> mockOrgUtils = mockStatic(OrgNumberUtils.class)) {
            
            mockLoginUtils.when(LoginUserUtils::getLoginUserName).thenReturn("testuser");
            mockTenantUtils.when(TenantUtils::getTenantId).thenReturn("tenant001");
            mockOrgUtils.when(OrgNumberUtils::getOrg).thenReturn("ORG001");
            
            when(sequenceIdGen.generateId("tenant001")).thenReturn("123456789");
            
            // 创建ParmSysCode对象
            ParmSysCode parmSysCode = new ParmSysCode();
            parmSysCode.setTypeId("ADDRESS1");
            parmSysCode.setCodeDesc("Address Line 1");
            when(parmSysCodeTypeMapper.selectByTypeIdAndTypeName(eq(tableName), anyString()))
                .thenReturn(parmSysCode);
            when(maintenanceLogMapper.insert(any(MaintenanceLog.class))).thenReturn(1);

            // When
            maintenanceLogBisService.add(maintenanceLogDTO, newAddressInfo, oldAddressInfo, tableName);

            // Then
            verify(maintenanceLogMapper, atLeastOnce()).insert(maintenanceLogCaptor.capture());
            MaintenanceLog capturedLog = maintenanceLogCaptor.getValue();
            
            assertNotNull(capturedLog);
            assertEquals("HOME", capturedLog.getSourceId()); // 应该设置地址类型
        }
    }

    @Test
    void shouldHandleCustomerAddressInfoDTO_whenObjectIsCustomerAddressInfoDTO() {
        // Given
        String tableName = "CUSTOMER_ADDRESS_INFO";

        try (MockedStatic<LoginUserUtils> mockLoginUtils = mockStatic(LoginUserUtils.class);
             MockedStatic<TenantUtils> mockTenantUtils = mockStatic(TenantUtils.class);
             MockedStatic<OrgNumberUtils> mockOrgUtils = mockStatic(OrgNumberUtils.class)) {
            
            mockLoginUtils.when(LoginUserUtils::getLoginUserName).thenReturn("testuser");
            mockTenantUtils.when(TenantUtils::getTenantId).thenReturn("tenant001");
            mockOrgUtils.when(OrgNumberUtils::getOrg).thenReturn("ORG001");
            
            when(sequenceIdGen.generateId("tenant001")).thenReturn("123456789");
            
            // 创建ParmSysCode对象
            ParmSysCode parmSysCode = new ParmSysCode();
            parmSysCode.setTypeId("ADDRESS1");
            parmSysCode.setCodeDesc("Address Line 1");
            when(parmSysCodeTypeMapper.selectByTypeIdAndTypeName(eq(tableName), anyString()))
                .thenReturn(parmSysCode);
            when(maintenanceLogMapper.insert(any(MaintenanceLog.class))).thenReturn(1);

            // When
            maintenanceLogBisService.add(maintenanceLogDTO, newAddressDTO, oldAddressDTO, tableName);

            // Then
            verify(maintenanceLogMapper, atLeastOnce()).insert(maintenanceLogCaptor.capture());
            MaintenanceLog capturedLog = maintenanceLogCaptor.getValue();
            
            assertNotNull(capturedLog);
            assertEquals("WORK", capturedLog.getSourceId()); // 应该设置地址类型
        }
    }

    @Test
    void shouldUseFieldNameWhenParmSysCodeNotFound() {
        // Given
        String tableName = "TEST_TABLE";

        try (MockedStatic<LoginUserUtils> mockLoginUtils = mockStatic(LoginUserUtils.class);
             MockedStatic<TenantUtils> mockTenantUtils = mockStatic(TenantUtils.class);
             MockedStatic<OrgNumberUtils> mockOrgUtils = mockStatic(OrgNumberUtils.class)) {
            
            mockLoginUtils.when(LoginUserUtils::getLoginUserName).thenReturn("testuser");
            mockTenantUtils.when(TenantUtils::getTenantId).thenReturn("tenant001");
            mockOrgUtils.when(OrgNumberUtils::getOrg).thenReturn("ORG001");
            
            when(sequenceIdGen.generateId("tenant001")).thenReturn("123456789");
            when(parmSysCodeTypeMapper.selectByTypeIdAndTypeName(eq(tableName), anyString()))
                .thenReturn(null); // 返回null表示未找到
            when(maintenanceLogMapper.insert(any(MaintenanceLog.class))).thenReturn(1);

            // When
            maintenanceLogBisService.add(maintenanceLogDTO, newTestObject, oldTestObject, tableName);

            // Then
            verify(maintenanceLogMapper, atLeastOnce()).insert(maintenanceLogCaptor.capture());
            MaintenanceLog capturedLog = maintenanceLogCaptor.getValue();
            
            assertNotNull(capturedLog);
            // 应该使用字段名的大写下划线格式
            assertTrue(capturedLog.getColumnName().contains("NAME") || 
                      capturedLog.getColumnName().contains("VALUE") || 
                      capturedLog.getColumnName().contains("AMOUNT"));
        }
    }

    @Test
    void shouldHandleInsertException_whenDatabaseError() {
        // Given
        String tableName = "TEST_TABLE";

        try (MockedStatic<LoginUserUtils> mockLoginUtils = mockStatic(LoginUserUtils.class);
             MockedStatic<TenantUtils> mockTenantUtils = mockStatic(TenantUtils.class);
             MockedStatic<OrgNumberUtils> mockOrgUtils = mockStatic(OrgNumberUtils.class)) {
            
            mockLoginUtils.when(LoginUserUtils::getLoginUserName).thenReturn("testuser");
            mockTenantUtils.when(TenantUtils::getTenantId).thenReturn("tenant001");
            mockOrgUtils.when(OrgNumberUtils::getOrg).thenReturn("ORG001");
            
            when(sequenceIdGen.generateId("tenant001")).thenReturn("123456789");
            
            // 创建ParmSysCode对象
            ParmSysCode parmSysCode = new ParmSysCode();
            parmSysCode.setTypeId("NAME");
            parmSysCode.setCodeDesc("Name Field");
            when(parmSysCodeTypeMapper.selectByTypeIdAndTypeName(eq(tableName), anyString()))
                .thenReturn(parmSysCode);
            when(maintenanceLogMapper.insert(any(MaintenanceLog.class)))
                .thenThrow(new RuntimeException("Database error"));

            // When & Then
            assertDoesNotThrow(() -> {
                maintenanceLogBisService.add(maintenanceLogDTO, newTestObject, oldTestObject, tableName);
            });
            
            verify(maintenanceLogMapper, atLeastOnce()).insert(any(MaintenanceLog.class));
        }
    }

    @Test
    void shouldHandleNullAddressType_whenAddressInfoTypeIsBlank() {
        // Given
        String tableName = "CUSTOMER_ADDRESS_INFO";

        try (MockedStatic<LoginUserUtils> mockLoginUtils = mockStatic(LoginUserUtils.class);
             MockedStatic<TenantUtils> mockTenantUtils = mockStatic(TenantUtils.class);
             MockedStatic<OrgNumberUtils> mockOrgUtils = mockStatic(OrgNumberUtils.class)) {
            
            mockLoginUtils.when(LoginUserUtils::getLoginUserName).thenReturn("testuser");
            mockTenantUtils.when(TenantUtils::getTenantId).thenReturn("tenant001");
            mockOrgUtils.when(OrgNumberUtils::getOrg).thenReturn("ORG001");
            
            CustomerAddressInfo newAddr = new CustomerAddressInfo();
            newAddr.setType(""); // 空类型
            newAddr.setAddress("New Address");
            
            CustomerAddressInfo oldAddr = new CustomerAddressInfo();
            oldAddr.setType("HOME"); // 有类型
            oldAddr.setAddress("Old Address");
            
            when(sequenceIdGen.generateId("tenant001")).thenReturn("123456789");
            
            // 创建ParmSysCode对象
            ParmSysCode parmSysCode = new ParmSysCode();
            parmSysCode.setTypeId("ADDRESS1");
            parmSysCode.setCodeDesc("Address Line 1");
            when(parmSysCodeTypeMapper.selectByTypeIdAndTypeName(eq(tableName), anyString()))
                .thenReturn(parmSysCode);
            when(maintenanceLogMapper.insert(any(MaintenanceLog.class))).thenReturn(1);

            // When
            maintenanceLogBisService.add(maintenanceLogDTO, newAddr, oldAddr, tableName);

            // Then
            verify(maintenanceLogMapper, atLeastOnce()).insert(maintenanceLogCaptor.capture());
            MaintenanceLog capturedLog = maintenanceLogCaptor.getValue();
            
            assertNotNull(capturedLog);
            assertEquals("HOME", capturedLog.getSourceId()); // 应该使用旧地址的类型
        }
    }

    @Test
    void shouldNotCreateLogForIgnoredFields() {
        // Given
        String tableName = "TEST_TABLE";
        TestObject oldObj = new TestObject();
        oldObj.setName("SameName");
        oldObj.setValue("SameValue");
        oldObj.setAmount(100);

        TestObject newObj = new TestObject();
        newObj.setName("SameName");
        newObj.setValue("SameValue");
        newObj.setAmount(100);

        try (MockedStatic<LoginUserUtils> mockLoginUtils = mockStatic(LoginUserUtils.class);
             MockedStatic<TenantUtils> mockTenantUtils = mockStatic(TenantUtils.class);
             MockedStatic<OrgNumberUtils> mockOrgUtils = mockStatic(OrgNumberUtils.class)) {
            
            mockLoginUtils.when(LoginUserUtils::getLoginUserName).thenReturn("testuser");
            mockTenantUtils.when(TenantUtils::getTenantId).thenReturn("tenant001");
            mockOrgUtils.when(OrgNumberUtils::getOrg).thenReturn("ORG001");

            // When
            maintenanceLogBisService.add(maintenanceLogDTO, newObj, oldObj, tableName);

            // Then
            verify(maintenanceLogMapper, never()).insert(any(MaintenanceLog.class));
        }
    }

    @Test
    void shouldInsertMaintenanceLog_whenOnlyNewObjectIsNull() {
        // Given
        LocalDate nextProcessingDay = LocalDate.now().plusDays(1);
        String cardNumber = "CARD001";
        String tableName = "CARD_BASIC_INFO";

        // When
        maintenanceLogBisService.insertMaintenanceLog(newTestObject, null, 
            nextProcessingDay, cardNumber, tableName);

        // Then
        verify(maintenanceLogMapper, never()).insert(any(MaintenanceLog.class));
    }

    @Test
    void shouldInsertMaintenanceLog_whenOnlyOldObjectIsNull() {
        // Given
        LocalDate nextProcessingDay = LocalDate.now().plusDays(1);
        String cardNumber = "CARD001";
        String tableName = "CARD_BASIC_INFO";

        // When
        maintenanceLogBisService.insertMaintenanceLog(null, null, 
            nextProcessingDay, cardNumber, tableName);

        // Then
        verify(maintenanceLogMapper, never()).insert(any(MaintenanceLog.class));
    }

    @Test
    void shouldHandleCompareException_whenReflectionFails() {
        // Given
        String tableName = "TEST_TABLE";
        Object problematicObject = new Object(); // 无字段的对象

        try (MockedStatic<LoginUserUtils> mockLoginUtils = mockStatic(LoginUserUtils.class);
             MockedStatic<TenantUtils> mockTenantUtils = mockStatic(TenantUtils.class);
             MockedStatic<OrgNumberUtils> mockOrgUtils = mockStatic(OrgNumberUtils.class)) {
            
            mockLoginUtils.when(LoginUserUtils::getLoginUserName).thenReturn("testuser");
            mockTenantUtils.when(TenantUtils::getTenantId).thenReturn("tenant001");
            mockOrgUtils.when(OrgNumberUtils::getOrg).thenReturn("ORG001");

            // When & Then - 应该不抛出异常
            assertDoesNotThrow(() -> {
                maintenanceLogBisService.add(maintenanceLogDTO, problematicObject, new Object(), tableName);
            });
        }
    }

    @Test
    void shouldHandleNullFieldValues_whenComparingObjects() {
        // Given
        String tableName = "TEST_TABLE";
        TestObject objWithNull = new TestObject();
        objWithNull.setName(null);
        objWithNull.setValue("SomeValue");
        objWithNull.setAmount(null);

        TestObject objWithValues = new TestObject();
        objWithValues.setName("Name");
        objWithValues.setValue("SomeValue");
        objWithValues.setAmount(100);

        try (MockedStatic<LoginUserUtils> mockLoginUtils = mockStatic(LoginUserUtils.class);
             MockedStatic<TenantUtils> mockTenantUtils = mockStatic(TenantUtils.class);
             MockedStatic<OrgNumberUtils> mockOrgUtils = mockStatic(OrgNumberUtils.class)) {
            
            mockLoginUtils.when(LoginUserUtils::getLoginUserName).thenReturn("testuser");
            mockTenantUtils.when(TenantUtils::getTenantId).thenReturn("tenant001");
            mockOrgUtils.when(OrgNumberUtils::getOrg).thenReturn("ORG001");
            
            when(sequenceIdGen.generateId("tenant001")).thenReturn("123456789");
            
            ParmSysCode parmSysCode = new ParmSysCode();
            parmSysCode.setTypeId("NAME");
            parmSysCode.setCodeDesc("Name Field");
            when(parmSysCodeTypeMapper.selectByTypeIdAndTypeName(eq(tableName), anyString()))
                .thenReturn(parmSysCode);
            when(maintenanceLogMapper.insert(any(MaintenanceLog.class))).thenReturn(1);

            // When
            maintenanceLogBisService.add(maintenanceLogDTO, objWithValues, objWithNull, tableName);

            // Then
            verify(maintenanceLogMapper, atLeastOnce()).insert(maintenanceLogCaptor.capture());
            List<MaintenanceLog> capturedLogs = maintenanceLogCaptor.getAllValues();
            
            assertTrue(capturedLogs.size() > 0);
            // 验证null值被正确处理
            assertTrue(capturedLogs.stream().anyMatch(log -> log.getOriginalValue().isEmpty()));
        }
    }

    @Test
    void shouldHandleBigDecimalComparison() {
        // Given
        String tableName = "TEST_TABLE";
        TestObjectWithBigDecimal oldObj = new TestObjectWithBigDecimal();
        oldObj.setPrice(new java.math.BigDecimal("100.00"));
        oldObj.setName("Test");

        TestObjectWithBigDecimal newObj = new TestObjectWithBigDecimal();
        newObj.setPrice(new java.math.BigDecimal("200.00"));
        newObj.setName("Test");

        try (MockedStatic<LoginUserUtils> mockLoginUtils = mockStatic(LoginUserUtils.class);
             MockedStatic<TenantUtils> mockTenantUtils = mockStatic(TenantUtils.class);
             MockedStatic<OrgNumberUtils> mockOrgUtils = mockStatic(OrgNumberUtils.class)) {
            
            mockLoginUtils.when(LoginUserUtils::getLoginUserName).thenReturn("testuser");
            mockTenantUtils.when(TenantUtils::getTenantId).thenReturn("tenant001");
            mockOrgUtils.when(OrgNumberUtils::getOrg).thenReturn("ORG001");
            
            when(sequenceIdGen.generateId("tenant001")).thenReturn("123456789");
            
            ParmSysCode parmSysCode = new ParmSysCode();
            parmSysCode.setTypeId("PRICE");
            parmSysCode.setCodeDesc("Price Field");
            when(parmSysCodeTypeMapper.selectByTypeIdAndTypeName(eq(tableName), anyString()))
                .thenReturn(parmSysCode);
            when(maintenanceLogMapper.insert(any(MaintenanceLog.class))).thenReturn(1);

            // When
            maintenanceLogBisService.add(maintenanceLogDTO, newObj, oldObj, tableName);

            // Then
            verify(maintenanceLogMapper, atLeastOnce()).insert(maintenanceLogCaptor.capture());
            MaintenanceLog capturedLog = maintenanceLogCaptor.getValue();
            
            assertNotNull(capturedLog);
            assertEquals("200.00", capturedLog.getUpdatedValue());
            assertEquals("100.00", capturedLog.getOriginalValue());
        }
    }

    @Test
    void shouldHandleEqualBigDecimalValues() {
        // Given
        String tableName = "TEST_TABLE";
        TestObjectWithBigDecimal oldObj = new TestObjectWithBigDecimal();
        oldObj.setPrice(new java.math.BigDecimal("100.00"));
        oldObj.setName("OldName");

        TestObjectWithBigDecimal newObj = new TestObjectWithBigDecimal();
        newObj.setPrice(new java.math.BigDecimal("100.0")); // 相等的BigDecimal
        newObj.setName("NewName");

        try (MockedStatic<LoginUserUtils> mockLoginUtils = mockStatic(LoginUserUtils.class);
             MockedStatic<TenantUtils> mockTenantUtils = mockStatic(TenantUtils.class);
             MockedStatic<OrgNumberUtils> mockOrgUtils = mockStatic(OrgNumberUtils.class)) {
            
            mockLoginUtils.when(LoginUserUtils::getLoginUserName).thenReturn("testuser");
            mockTenantUtils.when(TenantUtils::getTenantId).thenReturn("tenant001");
            mockOrgUtils.when(OrgNumberUtils::getOrg).thenReturn("ORG001");
            
            when(sequenceIdGen.generateId("tenant001")).thenReturn("123456789");
            
            ParmSysCode parmSysCode = new ParmSysCode();
            parmSysCode.setTypeId("NAME");
            parmSysCode.setCodeDesc("Name Field");
            when(parmSysCodeTypeMapper.selectByTypeIdAndTypeName(eq(tableName), anyString()))
                .thenReturn(parmSysCode);
            when(maintenanceLogMapper.insert(any(MaintenanceLog.class))).thenReturn(1);

            // When
            maintenanceLogBisService.add(maintenanceLogDTO, newObj, oldObj, tableName);

            // Then
            verify(maintenanceLogMapper).insert(maintenanceLogCaptor.capture());
            MaintenanceLog capturedLog = maintenanceLogCaptor.getValue();
            
            assertNotNull(capturedLog);
            // 只应该记录name字段的变化，price字段相等不记录
            assertEquals("NewName", capturedLog.getUpdatedValue());
            assertEquals("OldName", capturedLog.getOriginalValue());
        }
    }

    @Test
    void shouldHandleIgnoredFields() {
        // Given
        String tableName = "TEST_TABLE";
        TestObjectWithIgnoredFields oldObj = new TestObjectWithIgnoredFields();
        oldObj.setName("OldName");
        oldObj.setCreateTime("2023-01-01");
        oldObj.setUpdateTime("2023-01-01");
        oldObj.setUpdateBy("olduser");
        oldObj.setVersionNumber(1L);

        TestObjectWithIgnoredFields newObj = new TestObjectWithIgnoredFields();
        newObj.setName("NewName");
        newObj.setCreateTime("2023-01-02"); // 忽略字段
        newObj.setUpdateTime("2023-01-02"); // 忽略字段
        newObj.setUpdateBy("newuser"); // 忽略字段
        newObj.setVersionNumber(2L); // 忽略字段

        try (MockedStatic<LoginUserUtils> mockLoginUtils = mockStatic(LoginUserUtils.class);
             MockedStatic<TenantUtils> mockTenantUtils = mockStatic(TenantUtils.class);
             MockedStatic<OrgNumberUtils> mockOrgUtils = mockStatic(OrgNumberUtils.class)) {
            
            mockLoginUtils.when(LoginUserUtils::getLoginUserName).thenReturn("testuser");
            mockTenantUtils.when(TenantUtils::getTenantId).thenReturn("tenant001");
            mockOrgUtils.when(OrgNumberUtils::getOrg).thenReturn("ORG001");
            
            when(sequenceIdGen.generateId("tenant001")).thenReturn("123456789");
            
            ParmSysCode parmSysCode = new ParmSysCode();
            parmSysCode.setTypeId("NAME");
            parmSysCode.setCodeDesc("Name Field");
            when(parmSysCodeTypeMapper.selectByTypeIdAndTypeName(eq(tableName), anyString()))
                .thenReturn(parmSysCode);
            when(maintenanceLogMapper.insert(any(MaintenanceLog.class))).thenReturn(1);

            // When
            maintenanceLogBisService.add(maintenanceLogDTO, newObj, oldObj, tableName);

            // Then
            verify(maintenanceLogMapper).insert(maintenanceLogCaptor.capture());
            MaintenanceLog capturedLog = maintenanceLogCaptor.getValue();
            
            assertNotNull(capturedLog);
            // 只记录name字段的变化，忽略字段不记录
            assertEquals("NewName", capturedLog.getUpdatedValue());
            assertEquals("OldName", capturedLog.getOriginalValue());
        }
    }

    @Test
    void shouldHandleSequenceGenerationError() {
        // Given
        String tableName = "TEST_TABLE";

        try (MockedStatic<LoginUserUtils> mockLoginUtils = mockStatic(LoginUserUtils.class);
             MockedStatic<TenantUtils> mockTenantUtils = mockStatic(TenantUtils.class);
             MockedStatic<OrgNumberUtils> mockOrgUtils = mockStatic(OrgNumberUtils.class)) {
            
            mockLoginUtils.when(LoginUserUtils::getLoginUserName).thenReturn("testuser");
            mockTenantUtils.when(TenantUtils::getTenantId).thenReturn("tenant001");
            mockOrgUtils.when(OrgNumberUtils::getOrg).thenReturn("ORG001");
            
            when(sequenceIdGen.generateId("tenant001")).thenReturn("invalid_number");
            
            ParmSysCode parmSysCode = new ParmSysCode();
            parmSysCode.setTypeId("NAME");
            parmSysCode.setCodeDesc("Name Field");
            when(parmSysCodeTypeMapper.selectByTypeIdAndTypeName(eq(tableName), anyString()))
                .thenReturn(parmSysCode);

            // When & Then - 应该抛出NumberFormatException
            assertThrows(NumberFormatException.class, () -> {
                maintenanceLogBisService.add(maintenanceLogDTO, newTestObject, oldTestObject, tableName);
            });
        }
    }

    @Test
    void shouldHandleMultipleFieldChanges() {
        // Given
        String tableName = "TEST_TABLE";
        TestObject oldObj = new TestObject();
        oldObj.setName("OldName");
        oldObj.setValue("OldValue");
        oldObj.setAmount(100);

        TestObject newObj = new TestObject();
        newObj.setName("NewName");
        newObj.setValue("NewValue");
        newObj.setAmount(200);

        try (MockedStatic<LoginUserUtils> mockLoginUtils = mockStatic(LoginUserUtils.class);
             MockedStatic<TenantUtils> mockTenantUtils = mockStatic(TenantUtils.class);
             MockedStatic<OrgNumberUtils> mockOrgUtils = mockStatic(OrgNumberUtils.class)) {
            
            mockLoginUtils.when(LoginUserUtils::getLoginUserName).thenReturn("testuser");
            mockTenantUtils.when(TenantUtils::getTenantId).thenReturn("tenant001");
            mockOrgUtils.when(OrgNumberUtils::getOrg).thenReturn("ORG001");
            
            when(sequenceIdGen.generateId("tenant001")).thenReturn("123456789");
            
            ParmSysCode parmSysCode = new ParmSysCode();
            parmSysCode.setTypeId("FIELD");
            parmSysCode.setCodeDesc("Field Description");
            when(parmSysCodeTypeMapper.selectByTypeIdAndTypeName(eq(tableName), anyString()))
                .thenReturn(parmSysCode);
            when(maintenanceLogMapper.insert(any(MaintenanceLog.class))).thenReturn(1);

            // When
            maintenanceLogBisService.add(maintenanceLogDTO, newObj, oldObj, tableName);

            // Then
            verify(maintenanceLogMapper, times(3)).insert(maintenanceLogCaptor.capture());
            List<MaintenanceLog> capturedLogs = maintenanceLogCaptor.getAllValues();
            
            assertEquals(3, capturedLogs.size());
            // 验证所有字段都被记录
            assertTrue(capturedLogs.stream().anyMatch(log -> "NewName".equals(log.getUpdatedValue()) && "OldName".equals(log.getOriginalValue())));
            assertTrue(capturedLogs.stream().anyMatch(log -> "NewValue".equals(log.getUpdatedValue()) && "OldValue".equals(log.getOriginalValue())));
            assertTrue(capturedLogs.stream().anyMatch(log -> "200".equals(log.getUpdatedValue()) && "100".equals(log.getOriginalValue())));
        }
    }

    @Test
    void shouldHandleHumpToUnderlineConversion() {
        // Given
        String tableName = "TEST_TABLE";
        TestObjectWithCamelCase oldObj = new TestObjectWithCamelCase();
        oldObj.setFirstName("OldFirstName");
        oldObj.setLastNameValue("OldLastName");

        TestObjectWithCamelCase newObj = new TestObjectWithCamelCase();
        newObj.setFirstName("NewFirstName");
        newObj.setLastNameValue("NewLastName");

        try (MockedStatic<LoginUserUtils> mockLoginUtils = mockStatic(LoginUserUtils.class);
             MockedStatic<TenantUtils> mockTenantUtils = mockStatic(TenantUtils.class);
             MockedStatic<OrgNumberUtils> mockOrgUtils = mockStatic(OrgNumberUtils.class)) {
            
            mockLoginUtils.when(LoginUserUtils::getLoginUserName).thenReturn("testuser");
            mockTenantUtils.when(TenantUtils::getTenantId).thenReturn("tenant001");
            mockOrgUtils.when(OrgNumberUtils::getOrg).thenReturn("ORG001");
            
            when(sequenceIdGen.generateId("tenant001")).thenReturn("123456789");
            
            when(parmSysCodeTypeMapper.selectByTypeIdAndTypeName(eq(tableName), anyString()))
                .thenReturn(null); // 返回null使用字段名本身
            when(maintenanceLogMapper.insert(any(MaintenanceLog.class))).thenReturn(1);

            // When
            maintenanceLogBisService.add(maintenanceLogDTO, newObj, oldObj, tableName);

            // Then
            verify(maintenanceLogMapper, times(2)).insert(maintenanceLogCaptor.capture());
            List<MaintenanceLog> capturedLogs = maintenanceLogCaptor.getAllValues();
            
            assertEquals(2, capturedLogs.size());
            // 验证驼峰转下划线
            assertTrue(capturedLogs.stream().anyMatch(log -> log.getColumnName().contains("FIRST") && log.getColumnName().contains("NAME")));
            assertTrue(capturedLogs.stream().anyMatch(log -> log.getColumnName().contains("LAST") && log.getColumnName().contains("NAME")));
        }
    }



    @Test
    void shouldHandleCustomerAddressInfoWithNullOldObject() {
        // Given
        String tableName = "CUSTOMER_ADDRESS_INFO";

        try (MockedStatic<LoginUserUtils> mockLoginUtils = mockStatic(LoginUserUtils.class);
             MockedStatic<TenantUtils> mockTenantUtils = mockStatic(TenantUtils.class);
             MockedStatic<OrgNumberUtils> mockOrgUtils = mockStatic(OrgNumberUtils.class)) {
            
            mockLoginUtils.when(LoginUserUtils::getLoginUserName).thenReturn("testuser");
            mockTenantUtils.when(TenantUtils::getTenantId).thenReturn("tenant001");
            mockOrgUtils.when(OrgNumberUtils::getOrg).thenReturn("ORG001");
            
            CustomerAddressInfo newAddr = new CustomerAddressInfo();
            newAddr.setType("HOME");
            newAddr.setAddress("New Address");
            
            // 由于会抛出异常，不需要设置这些mock

            // When & Then - 当旧对象为null时，compare方法会抛出NullPointerException
            assertThrows(NullPointerException.class, () -> {
                maintenanceLogBisService.add(maintenanceLogDTO, newAddr, null, tableName);
            });

            // 由于异常，不应该有任何插入操作
            verify(maintenanceLogMapper, never()).insert(any(MaintenanceLog.class));
        }
    }

    @Test
    void shouldHandleCustomerAddressInfoDTOWithNullOldObject() {
        // Given
        String tableName = "CUSTOMER_ADDRESS_INFO";

        try (MockedStatic<LoginUserUtils> mockLoginUtils = mockStatic(LoginUserUtils.class);
             MockedStatic<TenantUtils> mockTenantUtils = mockStatic(TenantUtils.class);
             MockedStatic<OrgNumberUtils> mockOrgUtils = mockStatic(OrgNumberUtils.class)) {
            
            mockLoginUtils.when(LoginUserUtils::getLoginUserName).thenReturn("testuser");
            mockTenantUtils.when(TenantUtils::getTenantId).thenReturn("tenant001");
            mockOrgUtils.when(OrgNumberUtils::getOrg).thenReturn("ORG001");
            
            CustomerAddressInfoDTO newAddr = new CustomerAddressInfoDTO();
            newAddr.setType("WORK");
            newAddr.setAddress("New Work Address");
            
            // 由于会抛出异常，不需要设置这些mock

            // When & Then - 当旧对象为null时，compare方法会抛出NullPointerException
            assertThrows(NullPointerException.class, () -> {
                maintenanceLogBisService.add(maintenanceLogDTO, newAddr, null, tableName);
            });

            // 由于异常，不应该有任何插入操作
            verify(maintenanceLogMapper, never()).insert(any(MaintenanceLog.class));
        }
    }

    @Test
    void shouldHandleEmptyComparisonResult() {
        // Given
        String tableName = "TEST_TABLE";
        EmptyTestObject oldObj = new EmptyTestObject();
        EmptyTestObject newObj = new EmptyTestObject();

        try (MockedStatic<LoginUserUtils> mockLoginUtils = mockStatic(LoginUserUtils.class);
             MockedStatic<TenantUtils> mockTenantUtils = mockStatic(TenantUtils.class);
             MockedStatic<OrgNumberUtils> mockOrgUtils = mockStatic(OrgNumberUtils.class)) {
            
            mockLoginUtils.when(LoginUserUtils::getLoginUserName).thenReturn("testuser");
            mockTenantUtils.when(TenantUtils::getTenantId).thenReturn("tenant001");
            mockOrgUtils.when(OrgNumberUtils::getOrg).thenReturn("ORG001");

            // When
            maintenanceLogBisService.add(maintenanceLogDTO, newObj, oldObj, tableName);

            // Then
            verify(maintenanceLogMapper, never()).insert(any(MaintenanceLog.class));
        }
    }

    @Test
    void shouldHandleFieldsWithUnderscoreInName() {
        // Given
        String tableName = "TEST_TABLE";
        TestObjectWithUnderscore oldObj = new TestObjectWithUnderscore();
        oldObj.setField_name("OldValue");

        TestObjectWithUnderscore newObj = new TestObjectWithUnderscore();
        newObj.setField_name("NewValue");

        try (MockedStatic<LoginUserUtils> mockLoginUtils = mockStatic(LoginUserUtils.class);
             MockedStatic<TenantUtils> mockTenantUtils = mockStatic(TenantUtils.class);
             MockedStatic<OrgNumberUtils> mockOrgUtils = mockStatic(OrgNumberUtils.class)) {
            
            mockLoginUtils.when(LoginUserUtils::getLoginUserName).thenReturn("testuser");
            mockTenantUtils.when(TenantUtils::getTenantId).thenReturn("tenant001");
            mockOrgUtils.when(OrgNumberUtils::getOrg).thenReturn("ORG001");
            
            when(sequenceIdGen.generateId("tenant001")).thenReturn("123456789");
            
            when(parmSysCodeTypeMapper.selectByTypeIdAndTypeName(eq(tableName), anyString()))
                .thenReturn(null);
            when(maintenanceLogMapper.insert(any(MaintenanceLog.class))).thenReturn(1);

            // When
            maintenanceLogBisService.add(maintenanceLogDTO, newObj, oldObj, tableName);

            // Then
            verify(maintenanceLogMapper).insert(maintenanceLogCaptor.capture());
            MaintenanceLog capturedLog = maintenanceLogCaptor.getValue();
            
            assertNotNull(capturedLog);
            assertEquals("FIELD_NAME", capturedLog.getColumnName()); // 已经是下划线格式
        }
    }

    @Test
    void shouldHandleNullBigDecimalValues() {
        // Given
        String tableName = "TEST_TABLE";
        TestObjectWithBigDecimal oldObj = new TestObjectWithBigDecimal();
        oldObj.setPrice(null);
        oldObj.setName("Test");

        TestObjectWithBigDecimal newObj = new TestObjectWithBigDecimal();
        newObj.setPrice(new java.math.BigDecimal("100.00"));
        newObj.setName("Test");

        try (MockedStatic<LoginUserUtils> mockLoginUtils = mockStatic(LoginUserUtils.class);
             MockedStatic<TenantUtils> mockTenantUtils = mockStatic(TenantUtils.class);
             MockedStatic<OrgNumberUtils> mockOrgUtils = mockStatic(OrgNumberUtils.class)) {
            
            mockLoginUtils.when(LoginUserUtils::getLoginUserName).thenReturn("testuser");
            mockTenantUtils.when(TenantUtils::getTenantId).thenReturn("tenant001");
            mockOrgUtils.when(OrgNumberUtils::getOrg).thenReturn("ORG001");
            
            when(sequenceIdGen.generateId("tenant001")).thenReturn("123456789");
            
            ParmSysCode parmSysCode = new ParmSysCode();
            parmSysCode.setTypeId("PRICE");
            parmSysCode.setCodeDesc("Price Field");
            when(parmSysCodeTypeMapper.selectByTypeIdAndTypeName(eq(tableName), anyString()))
                .thenReturn(parmSysCode);
            when(maintenanceLogMapper.insert(any(MaintenanceLog.class))).thenReturn(1);

            // When
            maintenanceLogBisService.add(maintenanceLogDTO, newObj, oldObj, tableName);

            // Then
            verify(maintenanceLogMapper).insert(maintenanceLogCaptor.capture());
            MaintenanceLog capturedLog = maintenanceLogCaptor.getValue();
            
            assertNotNull(capturedLog);
            assertEquals("100.00", capturedLog.getUpdatedValue());
            assertEquals("", capturedLog.getOriginalValue()); // null转为空字符串
        }
    }

    @Test
    void shouldHandleBothNullBigDecimalValues() {
        // Given
        String tableName = "TEST_TABLE";
        TestObjectWithBigDecimal oldObj = new TestObjectWithBigDecimal();
        oldObj.setPrice(null);
        oldObj.setName("OldName");

        TestObjectWithBigDecimal newObj = new TestObjectWithBigDecimal();
        newObj.setPrice(null);
        newObj.setName("NewName");

        try (MockedStatic<LoginUserUtils> mockLoginUtils = mockStatic(LoginUserUtils.class);
             MockedStatic<TenantUtils> mockTenantUtils = mockStatic(TenantUtils.class);
             MockedStatic<OrgNumberUtils> mockOrgUtils = mockStatic(OrgNumberUtils.class)) {
            
            mockLoginUtils.when(LoginUserUtils::getLoginUserName).thenReturn("testuser");
            mockTenantUtils.when(TenantUtils::getTenantId).thenReturn("tenant001");
            mockOrgUtils.when(OrgNumberUtils::getOrg).thenReturn("ORG001");
            
            when(sequenceIdGen.generateId("tenant001")).thenReturn("123456789");
            
            ParmSysCode parmSysCode = new ParmSysCode();
            parmSysCode.setTypeId("NAME");
            parmSysCode.setCodeDesc("Name Field");
            when(parmSysCodeTypeMapper.selectByTypeIdAndTypeName(eq(tableName), anyString()))
                .thenReturn(parmSysCode);
            when(maintenanceLogMapper.insert(any(MaintenanceLog.class))).thenReturn(1);

            // When
            maintenanceLogBisService.add(maintenanceLogDTO, newObj, oldObj, tableName);

            // Then
            verify(maintenanceLogMapper).insert(maintenanceLogCaptor.capture());
            MaintenanceLog capturedLog = maintenanceLogCaptor.getValue();
            
            assertNotNull(capturedLog);
            // 只记录name字段的变化，price字段都是null不记录
            assertEquals("NewName", capturedLog.getUpdatedValue());
            assertEquals("OldName", capturedLog.getOriginalValue());
        }
    }

    @Test
    void shouldHandleSameStringRepresentationButDifferentValues() {
        // Given
        String tableName = "TEST_TABLE";
        TestObjectWithSimilarValues oldObj = new TestObjectWithSimilarValues();
        oldObj.setValue("100");
        oldObj.setNumber(100);

        TestObjectWithSimilarValues newObj = new TestObjectWithSimilarValues();
        newObj.setValue("100.0");
        newObj.setNumber(100);

        try (MockedStatic<LoginUserUtils> mockLoginUtils = mockStatic(LoginUserUtils.class);
             MockedStatic<TenantUtils> mockTenantUtils = mockStatic(TenantUtils.class);
             MockedStatic<OrgNumberUtils> mockOrgUtils = mockStatic(OrgNumberUtils.class)) {
            
            mockLoginUtils.when(LoginUserUtils::getLoginUserName).thenReturn("testuser");
            mockTenantUtils.when(TenantUtils::getTenantId).thenReturn("tenant001");
            mockOrgUtils.when(OrgNumberUtils::getOrg).thenReturn("ORG001");
            
            when(sequenceIdGen.generateId("tenant001")).thenReturn("123456789");
            
            ParmSysCode parmSysCode = new ParmSysCode();
            parmSysCode.setTypeId("VALUE");
            parmSysCode.setCodeDesc("Value Field");
            when(parmSysCodeTypeMapper.selectByTypeIdAndTypeName(eq(tableName), anyString()))
                .thenReturn(parmSysCode);
            when(maintenanceLogMapper.insert(any(MaintenanceLog.class))).thenReturn(1);

            // When
            maintenanceLogBisService.add(maintenanceLogDTO, newObj, oldObj, tableName);

            // Then
            verify(maintenanceLogMapper).insert(maintenanceLogCaptor.capture());
            MaintenanceLog capturedLog = maintenanceLogCaptor.getValue();
            
            assertNotNull(capturedLog);
            assertEquals("100.0", capturedLog.getUpdatedValue());
            assertEquals("100", capturedLog.getOriginalValue());
        }
    }

    // 测试用的简单对象类
    public static class TestObject {
        private String name;
        private String value;
        private Integer amount;
        private String createTime; // 这个字段会被忽略
        private String updateTime; // 这个字段会被忽略

        public String getName() {
            return name;
        }

        public void setName(String name) {
            this.name = name;
        }

        public String getValue() {
            return value;
        }

        public void setValue(String value) {
            this.value = value;
        }

        public Integer getAmount() {
            return amount;
        }

        public void setAmount(Integer amount) {
            this.amount = amount;
        }

        public String getCreateTime() {
            return createTime;
        }

        public void setCreateTime(String createTime) {
            this.createTime = createTime;
        }

        public String getUpdateTime() {
            return updateTime;
        }

        public void setUpdateTime(String updateTime) {
            this.updateTime = updateTime;
        }
    }

    // 带BigDecimal的测试对象
    public static class TestObjectWithBigDecimal {
        private java.math.BigDecimal price;
        private String name;

        public java.math.BigDecimal getPrice() {
            return price;
        }

        public void setPrice(java.math.BigDecimal price) {
            this.price = price;
        }

        public String getName() {
            return name;
        }

        public void setName(String name) {
            this.name = name;
        }
    }

    // 带忽略字段的测试对象
    public static class TestObjectWithIgnoredFields {
        private String name;
        private String createTime;
        private String updateTime;
        private String updateBy;
        private Long versionNumber;

        public String getName() {
            return name;
        }

        public void setName(String name) {
            this.name = name;
        }

        public String getCreateTime() {
            return createTime;
        }

        public void setCreateTime(String createTime) {
            this.createTime = createTime;
        }

        public String getUpdateTime() {
            return updateTime;
        }

        public void setUpdateTime(String updateTime) {
            this.updateTime = updateTime;
        }

        public String getUpdateBy() {
            return updateBy;
        }

        public void setUpdateBy(String updateBy) {
            this.updateBy = updateBy;
        }

        public Long getVersionNumber() {
            return versionNumber;
        }

        public void setVersionNumber(Long versionNumber) {
            this.versionNumber = versionNumber;
        }
    }

    // 驼峰命名的测试对象
    public static class TestObjectWithCamelCase {
        private String firstName;
        private String lastNameValue;

        public String getFirstName() {
            return firstName;
        }

        public void setFirstName(String firstName) {
            this.firstName = firstName;
        }

        public String getLastNameValue() {
            return lastNameValue;
        }

        public void setLastNameValue(String lastNameValue) {
            this.lastNameValue = lastNameValue;
        }
    }

    // 空的测试对象
    public static class EmptyTestObject {
        // 没有字段
    }

    // 带下划线字段名的测试对象
    public static class TestObjectWithUnderscore {
        private String field_name;

        public String getField_name() {
            return field_name;
        }

        public void setField_name(String field_name) {
            this.field_name = field_name;
        }
    }

    // 相似值的测试对象
    public static class TestObjectWithSimilarValues {
        private String value;
        private Integer number;

        public String getValue() {
            return value;
        }

        public void setValue(String value) {
            this.value = value;
        }

        public Integer getNumber() {
            return number;
        }

        public void setNumber(Integer number) {
            this.number = number;
        }
    }
} 