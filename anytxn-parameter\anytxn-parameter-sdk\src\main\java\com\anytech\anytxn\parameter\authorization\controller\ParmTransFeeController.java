package com.anytech.anytxn.parameter.authorization.controller;

import com.anytech.anytxn.parameter.base.authorization.domain.dto.ParmTransFeeDTO;
import com.anytech.anytxn.parameter.base.authorization.domain.dto.ParmTransFeeReqDTO;
import com.anytech.anytxn.parameter.base.authorization.service.IParmTransFeeService;
import com.anytech.anytxn.parameter.base.common.enums.ParameterRepDetailEnum;
import com.anytech.anytxn.parameter.base.common.utils.AnyTxnHttpResponseHelper;
import com.anytech.anytxn.common.core.base.PageResultDTO;
import com.anytech.anytxn.common.core.base.AnyTxnHttpResponse;
import com.anytech.anytxn.common.core.base.BizBaseController;

import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.Valid;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;


/**
 * 交易费规则
 */
@RestController
@Tag(name = "交易费规则服务")
public class ParmTransFeeController extends BizBaseController {

    @Autowired
    private IParmTransFeeService transFeeService;

    /**
     * 分页查询交易费规则列表
     *
     * @param reqDTO
     * @return
     */
    @Operation(summary = "分页查询交易费规则列表")
    @PostMapping("/param/transfee")
    public AnyTxnHttpResponse<PageResultDTO<ParmTransFeeDTO>> getVelocitySetDefinitionList(@RequestBody(required = false) ParmTransFeeReqDTO reqDTO) {
        PageResultDTO<ParmTransFeeDTO> result = transFeeService.findAll(reqDTO.getPage(), reqDTO.getRows(), reqDTO);
        return AnyTxnHttpResponse.success(result);
    }

    /**
     * @param safeLockDTO
     * @return jrx.anytxn.biz.common.web.AnyTxnHttpResponse
     * @description 新增交易费规则信息
     * <AUTHOR>
     * @date 2020/07/09
     */

    @Operation(summary = "新增交易费规则信息")
    @PostMapping("/param/transfee/create")
    public AnyTxnHttpResponse create(@Valid @RequestBody ParmTransFeeReqDTO safeLockDTO) {
        transFeeService.addParmFee(safeLockDTO);
        return AnyTxnHttpResponseHelper.succ(ParameterRepDetailEnum.CREATE.message());
    }


    /**
     * 交易费规则信息修改
     *
     * @param reqDTO
     * @return
     */
    @Operation(summary = "交易费规则信息修改")
    @PutMapping("/param/transfee/update")
    public AnyTxnHttpResponse modify(@Valid @RequestBody ParmTransFeeReqDTO reqDTO) {
        transFeeService.modifyParmFee(reqDTO);
        return AnyTxnHttpResponseHelper.succ(ParameterRepDetailEnum.UPDATE.message());
    }


    /**
     * 根据主键ID删除交易费规则信息
     *
     * @param id
     * @return
     */
    @Operation(summary = "根据主键ID删除交易费规则信息")
    @GetMapping("/param/transfee/{id}")
    public AnyTxnHttpResponse<ParmTransFeeDTO> findVelocitySetDefinitionById(@PathVariable String id) {
        ParmTransFeeDTO parmTransFeeDTO = transFeeService.findById(id);
        return AnyTxnHttpResponse.success(parmTransFeeDTO);
    }

    /**
     * 根据主键ID删除交易费规则信息
     *
     * @param id
     * @return
     */
    @Operation(summary = "根据主键ID删除交易费规则信息")
    @DeleteMapping("/param/transfee/{id}")
    public AnyTxnHttpResponse cancelVelocitySetDefinitionById(@PathVariable String id) {
        transFeeService.removeParmFee(id);
        return AnyTxnHttpResponseHelper.succ(ParameterRepDetailEnum.DEL.message());
    }
}
