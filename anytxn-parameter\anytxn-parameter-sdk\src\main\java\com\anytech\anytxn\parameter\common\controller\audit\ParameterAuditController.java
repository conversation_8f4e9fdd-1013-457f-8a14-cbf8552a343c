package com.anytech.anytxn.parameter.common.controller.audit;

import com.anytech.anytxn.parameter.common.service.audit.ParameterAuditServiceImpl;
import io.swagger.v3.oas.annotations.Operation;
import com.anytech.anytxn.common.core.base.PageResultDTO;
import com.anytech.anytxn.common.core.base.AnyTxnHttpResponse;
import com.anytech.anytxn.common.core.base.BizBaseController;
import com.anytech.anytxn.parameter.base.common.domain.dto.audit.CmParmModificationConditionDTO;
import com.anytech.anytxn.parameter.base.common.domain.dto.audit.CmParmModificationDTO;
import com.anytech.anytxn.parameter.base.common.domain.dto.audit.CmParmModificationRecoreDTO;
import org.springframework.web.bind.annotation.*;

import jakarta.annotation.Resource;

/**
 * @Author: sukang
 * @Date: 2021/4/20 16:41
 */
@RestController
@CrossOrigin
public class ParameterAuditController extends BizBaseController {

    @Resource
    private ParameterAuditServiceImpl parameterAuditServiceImpl;


    @Operation(summary = "分页查参数审核记录", description = "分页查参数审核记录")
    @PostMapping(value = "/param/auditInfo/pageInfo")
    public AnyTxnHttpResponse<PageResultDTO<CmParmModificationDTO>> getPage(
            @RequestBody CmParmModificationConditionDTO cmParmModificationConditionDTO){
        PageResultDTO<CmParmModificationDTO> pageResultDto = parameterAuditServiceImpl.findPage(
                cmParmModificationConditionDTO.getPageNum(),cmParmModificationConditionDTO.getPageSize(),
                cmParmModificationConditionDTO);
        return AnyTxnHttpResponse.success(pageResultDto);

    }

    @Operation(summary = "查看参数审核记录详情", description = "查看参数审核记录详情")
    @GetMapping(value = "param/auditInfo/{id}")
    public AnyTxnHttpResponse<CmParmModificationDTO> detail(@PathVariable("id") String id){

        return AnyTxnHttpResponse.success(parameterAuditServiceImpl.auditDetail(id));

    }

    @Operation(summary = "审核通过", description = "审核通过")
    @PutMapping(value = "param/auditApprove")
    public AnyTxnHttpResponse<Boolean> auditApprove(
            @RequestBody CmParmModificationConditionDTO cmParmModificationConditionDTO) throws Exception {

        return AnyTxnHttpResponse.success(parameterAuditServiceImpl.approve(
                cmParmModificationConditionDTO.getId(),cmParmModificationConditionDTO.getApproveInfo()));

    }

    @Operation(summary = "审核拒绝", description = "审核拒绝")
    @PutMapping(value = "param/auditRefuse")
    public AnyTxnHttpResponse<Boolean> auditRefuse(
            @RequestBody CmParmModificationConditionDTO cmParmModificationConditionDTO){

        return AnyTxnHttpResponse.success(parameterAuditServiceImpl.refuse(
                cmParmModificationConditionDTO.getId(), cmParmModificationConditionDTO.getRefuseInfo()));

    }





    @Operation(summary = "查看参数详情", description = "查看参数详情")
    @GetMapping(value = "param/parmDetailInfo/{id}")
    public AnyTxnHttpResponse<Object> parameterDetailInfo(@PathVariable("id") String id){

        return AnyTxnHttpResponse.success(parameterAuditServiceImpl.parameterDetailInfo(id));

    }

    @Operation(summary = "查看参数变更历史", description = "查看参数变更历史")
    @GetMapping(value = "/param/modifyRecord/pageNum/{pageNum}/pageSize/{pageSize}")
    public AnyTxnHttpResponse<PageResultDTO<CmParmModificationRecoreDTO>> getParmVersionHistory(
            @PathVariable("pageNum") Integer pageNum,
            @PathVariable("pageSize") Integer pageSize,
            @RequestParam("organizationNumber") String organizationNumber,
            @RequestParam(value = "originalId", required = false) String originalId,
            @RequestParam(value = "tableId", required = false) String tableId,
            @RequestParam(value = "tableCode",required = false) String tableCode){

        PageResultDTO<CmParmModificationRecoreDTO> parmRecordHistory = parameterAuditServiceImpl.findParmRecordHistory(
                organizationNumber, originalId, tableId, tableCode, pageNum, pageSize);

        return AnyTxnHttpResponse.success(parmRecordHistory);
    }


    @Operation(summary = "查询是否存在审核记录", description = "查询是否存在审核记录")
    @GetMapping(value = "/param/findAuditInfo")
    public AnyTxnHttpResponse<Boolean> auditInfoIsExist(
            @RequestParam("organizationNumber") String organizationNumber,
            @RequestParam(value = "id", required = false) String id,
            @RequestParam(value = "tableId", required = false) String tableId,
            @RequestParam(value = "tableCode",required = false) String tableCode){

        return AnyTxnHttpResponse.success(parameterAuditServiceImpl.auditInfoIsExist(organizationNumber,id,tableId,tableCode));
    }






}
