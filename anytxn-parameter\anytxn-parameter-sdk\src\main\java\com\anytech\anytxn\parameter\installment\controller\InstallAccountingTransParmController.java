package com.anytech.anytxn.parameter.installment.controller;

import io.swagger.v3.oas.annotations.tags.Tag;
import io.swagger.v3.oas.annotations.Operation;
import com.anytech.anytxn.common.core.base.ParameterCompare;
import com.anytech.anytxn.common.core.base.PageResultDTO;
import com.anytech.anytxn.common.core.base.AnyTxnHttpResponse;
import com.anytech.anytxn.common.core.base.BizBaseController;
import com.anytech.anytxn.parameter.base.installment.domain.dto.InstallAccountingTransParmReqDTO;
import com.anytech.anytxn.parameter.base.installment.domain.dto.InstallAccountingTransParmResDTO;
import com.anytech.anytxn.parameter.base.installment.service.IInstallAccountingTransParmService;
import com.anytech.anytxn.parameter.base.common.enums.ParameterRepDetailEnum;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import jakarta.validation.Valid;

/**
 *分期入账交易参数表  controller层
 * <AUTHOR>
 * @date 2019-05-13 17:20
 **/
@Tag(name = "分期入账交易参数表")
@RestController
public class InstallAccountingTransParmController extends BizBaseController {

    @Autowired
    private IInstallAccountingTransParmService iInstallAccountingTransParmService;

    /**
     * 新增分期入账交易参数表
     * @param installAccountingTransParmReqDTO 分期入账交易参数
     * @return AnyTxnHttpResponse<InstallAccountingTransParmResDTO>
     */
    @Operation(summary ="新建分期入账交易参数表")
    @PostMapping(value = "/param/installaccountingtrans")
    public AnyTxnHttpResponse<Object> create(@Valid @RequestBody InstallAccountingTransParmReqDTO installAccountingTransParmReqDTO)
    {
        ParameterCompare installAccountingTransParm = iInstallAccountingTransParmService.add(installAccountingTransParmReqDTO);
        return AnyTxnHttpResponse.success(installAccountingTransParm, ParameterRepDetailEnum.CREATE.message());

    }
    /**
     * 修改分期入账交易参数表
     * @param installAccountingTransParmReqDTO 分期入账交易参数
     * @return AnyTxnHttpResponse<InstallAccountingTransParmResDTO>
     */
    @Operation(summary = "修改分期入账交易参数")
    @PutMapping(value = "/param/installaccountingtrans")
    public AnyTxnHttpResponse<Object> modify(@Valid @RequestBody InstallAccountingTransParmReqDTO installAccountingTransParmReqDTO)
    {
        ParameterCompare installAccountingTransParm = iInstallAccountingTransParmService.modify(installAccountingTransParmReqDTO);
        return AnyTxnHttpResponse.success(installAccountingTransParm,ParameterRepDetailEnum.UPDATE.message());
    }
    /**
     * 删除分期入账交易参数表
     * @param id 分期入账交易参数
     * @return
     */
    @Operation(summary = "删除分期入账交易参数")
    @DeleteMapping(value = "/param/installaccountingtrans/id/{id}")
    public AnyTxnHttpResponse<Object> remove(@PathVariable String id)
    {
        ParameterCompare delete = iInstallAccountingTransParmService.remove(id);
        return AnyTxnHttpResponse.success(delete,ParameterRepDetailEnum.DEL.message());
    }
    /**
     * 根据id 查询分期入账交易参数表
     * @param id
     * @return AnyTxnHttpResponse<InstallAccountingTransParmResDTO>
     */
    @Operation(summary = "根据id查找分期入账参数")
    @GetMapping(value = "/param/installaccountingtrans/id/{id}")
    public AnyTxnHttpResponse<InstallAccountingTransParmResDTO> getById(@PathVariable String id)
    {
        InstallAccountingTransParmResDTO accountingTransParmResDTO= iInstallAccountingTransParmService.selectByPrimaryKey(id);
        return AnyTxnHttpResponse.success(accountingTransParmResDTO);
    }
    /**
     * 查询 分期入账交易参数 分页
     * @param pageNum  页码
     * @param pageSize 页面容量
     * @return PageResultDTO<InstallAccountingTransParmResDTO>
     */
    @Operation(summary = "分期入账交易参数信息",description = "分期入账交易参数信息")
    @GetMapping(value = "/param/installaccountingtrans/pageNum/{pageNum}/pageSize/{pageSize}")
    public AnyTxnHttpResponse<PageResultDTO<InstallAccountingTransParmResDTO>> getPage(@PathVariable(value = "pageNum")Integer pageNum,
                                                                                    @PathVariable(value = "pageSize")Integer pageSize,
                                                                                       InstallAccountingTransParmReqDTO installAccountingTransParmReqDTO)
    {
        PageResultDTO<InstallAccountingTransParmResDTO> pageResultDto = iInstallAccountingTransParmService.findPage(pageNum, pageSize, installAccountingTransParmReqDTO);
        return AnyTxnHttpResponse.success(pageResultDto);
    }
    /**
     * 根据id 查询分期入账交易参数表
     * @param organizationNumber tableId
     * @return InstallAccountingTransParmResDTO
     */
    @Operation(summary = "根据organizationNumber,tableId查找分期入账参数")
    @GetMapping(value = "/param/installaccountingtrans/organizationNumber/{organizationNumber}/tableId/{tableId}")
    public AnyTxnHttpResponse<InstallAccountingTransParmResDTO> getByOrgNumAndTableId(@PathVariable String organizationNumber, @PathVariable String tableId)
    {
        InstallAccountingTransParmResDTO accountingTransParmResDTO= iInstallAccountingTransParmService.selectByIndex(organizationNumber,tableId);
        return AnyTxnHttpResponse.success(accountingTransParmResDTO);
    }
}
