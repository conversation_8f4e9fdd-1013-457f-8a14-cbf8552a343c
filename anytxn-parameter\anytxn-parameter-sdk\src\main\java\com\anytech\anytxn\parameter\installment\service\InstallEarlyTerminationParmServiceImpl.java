package com.anytech.anytxn.parameter.installment.service;

import com.alibaba.fastjson.JSON;
import com.github.pagehelper.Page;
import com.github.pagehelper.PageHelper;
import com.anytech.anytxn.common.core.annotation.db.DeleteParameterAnnotation;
import com.anytech.anytxn.common.core.annotation.db.InsertParameterAnnotation;
import com.anytech.anytxn.common.core.annotation.db.UpdateParameterAnnotation;
import com.anytech.anytxn.business.base.audit.AbstractParameterService;
import com.anytech.anytxn.common.core.base.ParameterCompare;
import com.anytech.anytxn.business.base.audit.ParmModificationRecord;
import com.anytech.anytxn.common.core.utils.OrgNumberUtils;
import com.anytech.anytxn.common.core.base.PageResultDTO;
import com.anytech.anytxn.common.core.utils.BeanMapping;
import com.anytech.anytxn.common.sequence.utils.Number16IdGen;
import com.anytech.anytxn.common.core.utils.TenantUtils;
import com.anytech.anytxn.parameter.base.common.enums.AnyTxnParameterRespCodeEnum;
import com.anytech.anytxn.parameter.base.common.exception.AnyTxnParameterException;
import com.anytech.anytxn.parameter.base.installment.domain.dto.InstallEarlyTerminationParmReqDTO;
import com.anytech.anytxn.parameter.base.installment.domain.dto.InstallEarlyTerminationParmResDTO;
import com.anytech.anytxn.parameter.base.installment.service.IInstallEarlyTerminationParmService;
import com.anytech.anytxn.parameter.installment.mapper.InstallEarlyTerminationParmMapper;
import com.anytech.anytxn.parameter.installment.mapper.InstallEarlyTerminationParmSelfMapper;
import com.anytech.anytxn.parameter.base.installment.domain.model.InstallEarlyTerminationParm;
import org.apache.commons.lang3.StringUtils;
//import org.apache.shardingsphere.transaction.annotation.ShardingSphereTransactionType;
//import org.apache.shardingsphere.transaction.core.TransactionType;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

/**
 *分期提前终止参数
 * <AUTHOR>
 * @date 2019-05-14 17:26
 **/
@Service(value = "parm_install_early_termination_serviceImpl")
public class InstallEarlyTerminationParmServiceImpl extends AbstractParameterService implements IInstallEarlyTerminationParmService {

    private Logger logger = LoggerFactory.getLogger(InstallEarlyTerminationParmServiceImpl.class);

    @Autowired
    private InstallEarlyTerminationParmMapper installEarlyTerminationParmMapper;
    @Autowired
    private InstallEarlyTerminationParmSelfMapper installEarlyTerminationParmSelfMapper;

    @Autowired
    private Number16IdGen numberIdGenerator;

    /**
     * 新建分期提前终止参数
     * @param  installEarlyTerminationParmReqDTO 分期提前终止参数
     * @return HttpApiResponse<InstallEarlyTerminationParmResDTO>
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    @InsertParameterAnnotation(tableName = "parm_install_early_termination", tableDesc = "Early Payout")
    public ParameterCompare add(InstallEarlyTerminationParmReqDTO installEarlyTerminationParmReqDTO) {
        if (null != installEarlyTerminationParmReqDTO )
        {
            //检查必输项是否为空
            checkRequiredInputs(installEarlyTerminationParmReqDTO);
            int exists = installEarlyTerminationParmSelfMapper.isExists(installEarlyTerminationParmReqDTO.getOrganizationNumber(),
                    installEarlyTerminationParmReqDTO.getTableId());
            if (exists > 0)
            {
                logger.warn("分期提前终止参数已存在，organizationNumber={},tableId={}",
                        installEarlyTerminationParmReqDTO.getOrganizationNumber(),
                        installEarlyTerminationParmReqDTO.getTableId());

                throw new AnyTxnParameterException(AnyTxnParameterRespCodeEnum.D_EXIST_INSTALL_EARLY_TERMINATION_FAULT);
            }
            InstallEarlyTerminationParm installEarlyTerminationParm = BeanMapping.copy(installEarlyTerminationParmReqDTO, InstallEarlyTerminationParm.class);
            installEarlyTerminationParm.setId(String.valueOf(numberIdGenerator.generateId(TenantUtils.getTenantId())));

            return ParameterCompare.getBuilder().withAfter(installEarlyTerminationParm).build(InstallEarlyTerminationParm.class);

        } else {
            logger.warn("分期提前终止参数为空");

            throw new AnyTxnParameterException(AnyTxnParameterRespCodeEnum.D_QUERY_INSTALL_EARLY_TERMINATION_FAULT);
        }
    }
    /**            return ParameterCompare.getBuilder().withAfter(installEarlyTerminationParm).build(InstallEarlyTerminationParm.class);

     * 修改分期提前终止参数
     * @param  installEarlyTerminationParmReq 分期提前终止参数
     * @return HttpApiResponse<InstallEarlyTerminationParmResDTO>
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    @UpdateParameterAnnotation(tableName = "parm_install_early_termination", tableDesc = "Early Payout")
    public ParameterCompare modify(InstallEarlyTerminationParmReqDTO installEarlyTerminationParmReq) {
        if (null == installEarlyTerminationParmReq  || null == installEarlyTerminationParmReq.getId()) {

            throw new AnyTxnParameterException(AnyTxnParameterRespCodeEnum.P_PRIMARY_KEY_NULL_FAULT);
        }
        //检查必输项是否为空
        checkRequiredInputs(installEarlyTerminationParmReq);
        InstallEarlyTerminationParm installEarlyTerminationParm  = installEarlyTerminationParmMapper.selectByPrimaryKey(installEarlyTerminationParmReq.getId());

        if (installEarlyTerminationParm == null) {
            logger.error("修改分期提前终止参数信息，通过id:{}未查到数据", installEarlyTerminationParm);

            throw new AnyTxnParameterException(AnyTxnParameterRespCodeEnum.D_QUERY_INSTALL_EARLY_TERMINATION_BY_ID_FAULT);
        }
        InstallEarlyTerminationParm copy = BeanMapping.copy(installEarlyTerminationParmReq, InstallEarlyTerminationParm.class);

        InstallEarlyTerminationParmReqDTO copy1 = BeanMapping.copy(installEarlyTerminationParm, InstallEarlyTerminationParmReqDTO.class);

        InstallEarlyTerminationParm earlyTerminationParm = installEarlyTerminationParmSelfMapper.selectByIndex(
                installEarlyTerminationParm.getOrganizationNumber(),installEarlyTerminationParm.getTableId());

        if (earlyTerminationParm != null && !earlyTerminationParm.getId().equals(installEarlyTerminationParm.getId())) {
            logger.warn("分期提前终止参数已存在，orgNum={},accountProductNumber={}",installEarlyTerminationParm.getOrganizationNumber(),
                    installEarlyTerminationParm.getTableId());

            throw new AnyTxnParameterException(AnyTxnParameterRespCodeEnum.D_EXIST_INSTALL_EARLY_TERMINATION_FAULT);
        }
        return ParameterCompare.getBuilder().withAfter(installEarlyTerminationParmReq).withBefore(copy1).build(InstallEarlyTerminationParmReqDTO.class);

    }
    /**
     * 删除分期提前终止参数
     * @param  id 分期提前终止参数
     * @return HttpApiResponse<Boolean>
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    @DeleteParameterAnnotation(tableName = "parm_install_early_termination", tableDesc = "Early Payout")
    public ParameterCompare remove(String id) {
        if (null == id ) {

            throw new AnyTxnParameterException(AnyTxnParameterRespCodeEnum.P_PRIMARY_KEY_NULL_FAULT);
        }
        InstallEarlyTerminationParm earlyTerminationParm = installEarlyTerminationParmMapper.selectByPrimaryKey(id);
        if (earlyTerminationParm == null) {
            logger.error("删除分期提前终止参数信息，通过id:{}未查到数据", id);

            throw new AnyTxnParameterException(AnyTxnParameterRespCodeEnum.D_QUERY_INSTALL_EARLY_TERMINATION_BY_ID_FAULT);
        }
        return ParameterCompare.getBuilder().withBefore(earlyTerminationParm).build(InstallEarlyTerminationParm.class);

    }
    /**
     * 根据id 查询分期提前终止参数
     * @param  id 分期提前终止参数
     * @return HttpApiResponse<Boolean>
     */
    @Override
    public InstallEarlyTerminationParmResDTO getById(String id) {
        if ( null == id) {

            throw new AnyTxnParameterException(AnyTxnParameterRespCodeEnum.P_PRIMARY_KEY_NULL_FAULT);
        }
        InstallEarlyTerminationParm earlyTerminationParm = installEarlyTerminationParmMapper.selectByPrimaryKey(id);
        if (earlyTerminationParm == null) {
            logger.error("查询分期提前终止参数信息，通过id:{}未查到数据", id);

            throw new AnyTxnParameterException(AnyTxnParameterRespCodeEnum.D_QUERY_INSTALL_EARLY_TERMINATION_BY_ID_FAULT);
        }
        return BeanMapping.copy(earlyTerminationParm, InstallEarlyTerminationParmResDTO.class);
    }
    /***
     * @Description: 分页
     * @Date: 2019/5/15 9:45
     * @Param: [pageNum, pageSize]
     **/
    @Override
    public PageResultDTO<InstallEarlyTerminationParmResDTO> findPage(Integer pageNum, Integer pageSize, InstallEarlyTerminationParmReqDTO installEarlyTerminationParmReqDTO) {
        if(null == installEarlyTerminationParmReqDTO){
            installEarlyTerminationParmReqDTO = new InstallEarlyTerminationParmReqDTO();
        }
        installEarlyTerminationParmReqDTO.setOrganizationNumber(StringUtils.isEmpty(installEarlyTerminationParmReqDTO.getOrganizationNumber()) ? OrgNumberUtils.getOrg() : installEarlyTerminationParmReqDTO.getOrganizationNumber());
        logger.debug("分页查询分期提前终止参数");
        Page<InstallEarlyTerminationParm> page = PageHelper.startPage(pageNum, pageSize);
        List<InstallEarlyTerminationParm> earlyTerminationParms = installEarlyTerminationParmSelfMapper.selectByCondition(installEarlyTerminationParmReqDTO);

        if (earlyTerminationParms.isEmpty()) {
            logger.error("未查询到分期提前终止参数信息");

            //throw new AnyTxnParameterException(AnyTxnParameterRespCodeEnum.D_PAGE_INSTALL_EARLY_TERMINATION_FAULT);
        }
        List<InstallEarlyTerminationParmResDTO> currencyRateRes = BeanMapping.copyList(earlyTerminationParms, InstallEarlyTerminationParmResDTO.class);
        return new PageResultDTO<>(pageNum, pageSize, page.getTotal(), page.getPages(), currencyRateRes);
    }
    /**
     * 根据organizationNumber tableId查询分期提前终止参数
     * @param  organizationNumber tableId分期提前终止参数
     * @return HttpApiResponse<Boolean>
     */
    @Override
    public InstallEarlyTerminationParmResDTO findByIndex(String organizationNumber, String tableId) {
        if ( null == organizationNumber || "".equals(organizationNumber)) {

            throw new AnyTxnParameterException(AnyTxnParameterRespCodeEnum.D_INSTALL_ACCOUNTING_TRANS_PARM_ORG_FAULT);
        }
        if ( null == tableId || "".equals(tableId)) {

            throw new AnyTxnParameterException(AnyTxnParameterRespCodeEnum.D_INSTALL_EARLY_TERMINATION_TABLE_ID_FAULT);
        }
        InstallEarlyTerminationParm earlyTerminationParm = installEarlyTerminationParmSelfMapper.selectByIndex(organizationNumber,tableId);
        if (earlyTerminationParm == null) {
            logger.error("查询分期提前终止参数信息，通过organizationNumber:{},tableId{}未查到数据",organizationNumber,tableId);

            throw new AnyTxnParameterException(AnyTxnParameterRespCodeEnum.D_QUERY_INSTALL_EARLY_TERMINATION_FAULT);
        }
        return BeanMapping.copy(earlyTerminationParm, InstallEarlyTerminationParmResDTO.class);
    }

    //检查必输字段是否为空
    public void checkRequiredInputs(InstallEarlyTerminationParmReqDTO installEarlyTermination){
        if (null == installEarlyTermination.getOrganizationNumber() || "".equals(installEarlyTermination.getOrganizationNumber()))
        {

            throw new AnyTxnParameterException(AnyTxnParameterRespCodeEnum.D_INSTALL_ACCOUNTING_TRANS_PARM_ORG_FAULT);
        }
        if (null == installEarlyTermination.getTableId() ||  "".equals(installEarlyTermination.getTableId()))
        {

            throw new AnyTxnParameterException(AnyTxnParameterRespCodeEnum.D_INSTALL_EARLY_TERMINATION_TABLE_ID_FAULT);
        }
        if (null == installEarlyTermination.getStatus() || "".equals(installEarlyTermination.getStatus()))
        {

            throw new AnyTxnParameterException(AnyTxnParameterRespCodeEnum.D_INSTALL_ACCOUNTING_TRANS_PARM_STATUS_FAULT);
        }
        if (null == installEarlyTermination.getReturnFeeFlag() || "".equals(installEarlyTermination.getReturnFeeFlag()))
        {

            throw new AnyTxnParameterException(AnyTxnParameterRespCodeEnum.D_INSTALL_EARLY_TERMINATION_RETURN_FAULT);
        }
        if (null == installEarlyTermination.getPrepaymentFeeFlag() ||  "".equals(installEarlyTermination.getPrepaymentFeeFlag()))
        {

            throw new AnyTxnParameterException(AnyTxnParameterRespCodeEnum.D_INSTALL_EARLY_TERMINATION_PREPAYMENT_FAULT);
        }
        if(null == installEarlyTermination.getPenaltyFlag() ||  "".equals(installEarlyTermination.getPenaltyFlag()))
        {

            throw new AnyTxnParameterException(AnyTxnParameterRespCodeEnum.D_INSTALL_EARLY_TERMINATION_PENALTY_FLAG_FAULT);
        }
        if (null == installEarlyTermination.getPenaltyValue())
        {

            throw new AnyTxnParameterException(AnyTxnParameterRespCodeEnum.D_INSTALL_EARLY_TERMINATION_PENALTY_FAULT);
        }
        if (null == installEarlyTermination.getForcePenaltyFlag() ||  "".equals(installEarlyTermination.getForcePenaltyFlag()))
        {

            throw new AnyTxnParameterException(AnyTxnParameterRespCodeEnum.D_INSTALL_EARLY_TERMINATION_FORCE_FAULT);
        }
        if (null == installEarlyTermination.getForcePrepaymentFeeFlag() ||  "".equals(installEarlyTermination.getForcePrepaymentFeeFlag()))
        {

            throw new AnyTxnParameterException(AnyTxnParameterRespCodeEnum.D_INSTALL_EARLY_TERMINATION_FORCE_FLAG_FAULT);
        }
        if (null == installEarlyTermination.getForcePenaltyValue())
        {

            throw new AnyTxnParameterException(AnyTxnParameterRespCodeEnum.D_INSTALL_EARLY_TERMINATION_FORCE_PENALTY_FAULT);
        }
        if (null == installEarlyTermination.getDishonourFeeFlag() ||  "".equals(installEarlyTermination.getDishonourFeeFlag()))
        {

            throw new AnyTxnParameterException(AnyTxnParameterRespCodeEnum.D_INSTALL_EARLY_TERMINATION_REFUSE_FAULT);
        }
    }
    @Override
    @Transactional(rollbackFor = Exception.class,propagation = Propagation.REQUIRED)
    //@ShardingSphereTransactionType(TransactionType.XA)
    protected boolean updateDb(ParmModificationRecord parmModificationRecord) {
        InstallEarlyTerminationParm installEarlyTerminationParm = JSON.parseObject(parmModificationRecord.getParmBody(), InstallEarlyTerminationParm.class);
        installEarlyTerminationParm.initUpdateDateTime();
        int i = installEarlyTerminationParmMapper.updateByPrimaryKeySelective(installEarlyTerminationParm);
        return i > 0;    }

    @Override
    @Transactional(rollbackFor = Exception.class,propagation = Propagation.REQUIRED)
    //@ShardingSphereTransactionType(TransactionType.XA)
    protected boolean insertDb(ParmModificationRecord parmModificationRecord) {
        InstallEarlyTerminationParm installEarlyTerminationParm = JSON.parseObject(parmModificationRecord.getParmBody(), InstallEarlyTerminationParm.class);
        installEarlyTerminationParm.initCreateDateTime();
        installEarlyTerminationParm.initUpdateDateTime();
        int i = installEarlyTerminationParmMapper.insertSelective(installEarlyTerminationParm);
        return i > 0;
    }

    @Override
    @Transactional(rollbackFor = Exception.class,propagation = Propagation.REQUIRED)
    //@ShardingSphereTransactionType(TransactionType.XA)
    protected boolean deleteDb(ParmModificationRecord parmModificationRecord) {
        InstallEarlyTerminationParm installEarlyTerminationParm = JSON.parseObject(parmModificationRecord.getParmBody(), InstallEarlyTerminationParm.class);
        int i = installEarlyTerminationParmMapper.deleteByPrimaryKey(installEarlyTerminationParm.getId());
        return i > 0;
    }
}
