package com.anytech.anytxn.parameter.authorization.controller;

import io.swagger.v3.oas.annotations.tags.Tag;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import com.anytech.anytxn.common.core.base.PageResultDTO;
import com.anytech.anytxn.common.core.base.AnyTxnHttpResponse;
import com.anytech.anytxn.common.core.base.BizBaseController;
import com.anytech.anytxn.parameter.base.authorization.domain.dto.VelocityDefinitionDTO;
import com.anytech.anytxn.parameter.base.authorization.domain.dto.VelocitySetDTO;
import com.anytech.anytxn.parameter.base.authorization.domain.dto.VelocitySetDefinitionDTO;
import com.anytech.anytxn.parameter.base.authorization.service.IVelocitySetDefinitionService;
import com.anytech.anytxn.parameter.base.common.enums.ParameterRepDetailEnum;
import com.anytech.anytxn.parameter.base.common.utils.AnyTxnHttpResponseHelper;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import jakarta.validation.Valid;
import java.util.List;
import java.util.Map;

/**
 * @description 流量集合
 * <AUTHOR>
 * @date 2020/06/22
 */

@RestController
@Tag(name = "流量集合服务")
public class VelocitySetDefinitionController extends BizBaseController {

    @Autowired
    private IVelocitySetDefinitionService velocitySetDefinitionService;

    /**
     * @description 分页查询流量集合列表
     * <AUTHOR>
     * @date 2020/06/22
     * @param page, rows
     * @return jrx.anytxn.biz.common.web.AnyTxnHttpResponse<jrx.anytxn.biz.common.dto.PageResultDTO<jrx.anytxn.auth.dto.VelocitySetDefinitionDTO>>
     */
    @Operation(summary = "分页查询流量集合定义信息")
    @GetMapping("/param/velocitySetDefinitions/{page}/{rows}")
    public AnyTxnHttpResponse<PageResultDTO<VelocitySetDefinitionDTO>> getVelocitySetDefinitionList(@Parameter(name = "page", description = "当前页", example = "1")@PathVariable("page") int page ,
                                                                                                    @Parameter(name = "rows", description = "每页大小", example = "8")@PathVariable("rows") int rows,
                                                                                                    VelocitySetDTO velocitySetDTO) {
        PageResultDTO<VelocitySetDefinitionDTO> result = velocitySetDefinitionService.findListVelocitySetDefinition(page, rows,velocitySetDTO);
        return AnyTxnHttpResponse.success(result);
    }


    /**
     * @description 新增流量集合信息
     * <AUTHOR>
     * @date 2020/06/23
     * @param velocitySetDTO
     * @return jrx.anytxn.biz.common.web.AnyTxnHttpResponse
     */

    @Operation(summary = "新增流量集合信息")
    @PostMapping("/param/velocitySetDefinition")
    public AnyTxnHttpResponse create(@Valid @RequestBody VelocitySetDTO velocitySetDTO) {
        velocitySetDefinitionService.addVelocitySetDefinition(velocitySetDTO);
        return AnyTxnHttpResponseHelper.succ(ParameterRepDetailEnum.CREATE.message());
    }

    /**
     * @description 流量集合信息修改
     * <AUTHOR>
     * @date 2020/06/23
     * @param velocitySetDTO
     * @return jrx.anytxn.biz.common.web.AnyTxnHttpResponse
     */

    @Operation(summary = "流量集合信息修改")
    @PutMapping("/param/velocitySetDefinition")
    public AnyTxnHttpResponse modify(@Valid @RequestBody VelocitySetDTO velocitySetDTO) {
        velocitySetDefinitionService.modifyVelocitySetDefinition(velocitySetDTO);
        return AnyTxnHttpResponseHelper.succ(ParameterRepDetailEnum.UPDATE.message());
    }

    /**
     * @description 删除流量集合信息
     * <AUTHOR>
     * @date 2020/06/23
     * @param id
     * @return jrx.anytxn.biz.common.web.AnyTxnHttpResponse
     */

    @Operation(summary = "删除流量集合信息")
    @DeleteMapping("/param/velocitySetDefinition/{id}")
    public AnyTxnHttpResponse cancelVelocitySetDefinition(@PathVariable String id) {
        velocitySetDefinitionService.removeVelocitySetDefinition(id);
        return AnyTxnHttpResponseHelper.succ(ParameterRepDetailEnum.DEL.message());
    }

    /**
     * @description 根据主键查询流量集合信息
     * <AUTHOR>
     * @date 2020/06/23
     * @param id
     * @return jrx.anytxn.biz.common.web.AnyTxnHttpResponse<jrx.anytxn.auth.dto.VelocitySetDTO>
     */

    @Operation(summary = "根据主键查询流量集合信息")
    @GetMapping("/param/velocitySetDefinition/{id}")
    @Parameter(name = "id", description = "技术主键")
    public AnyTxnHttpResponse<VelocitySetDTO> getVelocitySetDefinition(@PathVariable String id) {
        VelocitySetDTO velocitySetDTO = velocitySetDefinitionService.findVelocitySetDefinition(id);
        return AnyTxnHttpResponse.success(velocitySetDTO);
    }

    /**
     * @description 获取所有流量检查码
     * <AUTHOR>
     * @date 2020/06/23
     * @param
     * @return jrx.anytxn.biz.common.web.AnyTxnHttpResponse<java.util.List<java.lang.String>>
     */
    @Operation(summary = "获取所有流量检查码")
    @GetMapping("/param/velocitySetDefinition/velocityCodes")
    public AnyTxnHttpResponse<List<Map<String,String>>> getVelocityCodes(String organizationNumber) {
        List<Map<String,String>> velocityCodes = velocitySetDefinitionService.getVelocityCodes(organizationNumber);
        return AnyTxnHttpResponse.success(velocityCodes);
    }

    /**
     * @description 根据机构号获取流量集合编号
     * <AUTHOR>
     * @date 2020/06/30
     * @param
     * @return jrx.anytxn.biz.common.web.AnyTxnHttpResponse<List<VelocitySetDefinitionDTO>>
     */
    @Operation(summary = "根据机构号获取流量集合编号")
    @GetMapping("/param/velocitySetDefinition/velocitySetCodes/organizationNumber/{organizationNumber}")
    public AnyTxnHttpResponse<List<VelocitySetDefinitionDTO>> getVelocitySetCodes(@PathVariable(value = "organizationNumber") String organizationNumber) {
        List<VelocitySetDefinitionDTO> velocitySetDefinitionDTO = velocitySetDefinitionService.getVelocitySetCodes(organizationNumber);
        return AnyTxnHttpResponse.success(velocitySetDefinitionDTO);
    }

    /**
     * @description 用机构号+流量集合编号读取流量集合详情表，获取流量检查编号
     * <AUTHOR>
     * @date 2020/06/30
     * @param
     * @return jrx.anytxn.biz.common.web.AnyTxnHttpResponse<List<VelocitySetDetailDTO>>
     */
    @Operation(summary = "根据机构号与流量集合编号获取流量检查编号")
    @GetMapping("/param/velocitySetDefinition/velocityCdes/organizationNumber/{organizationNumber}/velocitySetCode/{velocitySetCode}")
    public AnyTxnHttpResponse<List<VelocityDefinitionDTO>> getVelocityCdes(@PathVariable(value = "organizationNumber") String organizationNumber,@PathVariable(value = "velocitySetCode") String velocitySetCode) {
        List<VelocityDefinitionDTO> velocityDefinitionDTO = velocitySetDefinitionService.getVelocityCdes(organizationNumber,velocitySetCode);
        return AnyTxnHttpResponse.success(velocityDefinitionDTO);
    }


}
