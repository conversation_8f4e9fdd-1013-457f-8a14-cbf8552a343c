package com.anytech.anytxn.parameter.authorization.service;

import com.alibaba.fastjson.JSON;
import com.github.pagehelper.Page;
import com.github.pagehelper.PageHelper;
import com.anytech.anytxn.common.core.annotation.db.DeleteParameterAnnotation;
import com.anytech.anytxn.common.core.annotation.db.InsertParameterAnnotation;
import com.anytech.anytxn.common.core.annotation.db.UpdateParameterAnnotation;
import com.anytech.anytxn.business.base.audit.AbstractParameterService;
import com.anytech.anytxn.common.core.base.ParameterCompare;
import com.anytech.anytxn.business.base.audit.ParmModificationRecord;
import com.anytech.anytxn.common.core.base.PageResultDTO;
import com.anytech.anytxn.common.core.utils.BeanMapping;
import com.anytech.anytxn.common.core.utils.OrgNumberUtils;
import com.anytech.anytxn.common.sequence.utils.Number16IdGen;
import com.anytech.anytxn.common.core.utils.TenantUtils;
import com.anytech.anytxn.parameter.base.authorization.domain.dto.SafeLockDTO;
import com.anytech.anytxn.parameter.base.authorization.domain.dto.SafeLockDetailDTO;
import com.anytech.anytxn.parameter.base.authorization.service.ISafeLockService;
import com.anytech.anytxn.parameter.base.common.enums.AnyTxnParameterRespCodeEnum;
import com.anytech.anytxn.parameter.base.common.enums.ParameterRepDetailEnum;
import com.anytech.anytxn.parameter.base.common.exception.AnyTxnParameterException;
import com.anytech.anytxn.parameter.authorization.mapper.ParmSafetyLockMapper;
import com.anytech.anytxn.parameter.base.authorization.domain.model.ParmSafetyLock;
//import org.apache.shardingsphere.transaction.annotation.ShardingSphereTransactionType;
//import org.apache.shardingsphere.transaction.core.TransactionType;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.cglib.beans.BeanCopier;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;
import org.springframework.util.ObjectUtils;
import org.springframework.util.StringUtils;

import java.util.ArrayList;
import java.util.List;

/**
 * 安全开关规则具体实现类
 * <AUTHOR>
 * @date 2020-07-09
 */
@Service(value = "parm_safety_lock_serviceImpl")
public class SafeLockServiceImpl extends AbstractParameterService implements ISafeLockService {

    private static final Logger logger = LoggerFactory.getLogger(SafeLockServiceImpl.class);

    @Autowired
    private ParmSafetyLockMapper parmSafetyLockMapper;
    @Autowired
    private Number16IdGen numberIdGenerator;


    @Override
    public PageResultDTO<SafeLockDTO> findListSafeLock(Integer page, Integer rows, String cardProductCode, String organizationNumber) {
        logger.info("分页查询安全开关规则表，当前页：{}，每页展示条数：{}", page, rows);
        Page pageInfo =  PageHelper.startPage(page, rows);
        List<SafeLockDTO> safeLockDTOList = null;

        String orgNumber = org.apache.commons.lang3.StringUtils.isBlank(organizationNumber) ? OrgNumberUtils.getOrg() : organizationNumber;

        try {
            List<ParmSafetyLock> parmSafetyLockList = parmSafetyLockMapper.selectByCondition(cardProductCode, orgNumber);
            if (!CollectionUtils.isEmpty(parmSafetyLockList)) {
                safeLockDTOList = new ArrayList<>();
                BeanCopier beanCopier = BeanCopier.create(ParmSafetyLock.class, SafeLockDTO.class, false);
                for (ParmSafetyLock parmSafetyLock : parmSafetyLockList) {
                    SafeLockDTO safeLockDTO = new SafeLockDTO();
                    beanCopier.copy(parmSafetyLock, safeLockDTO, null);
                    safeLockDTOList.add(safeLockDTO);
                }
            }
            return new PageResultDTO<>(page, rows, pageInfo.getTotal(), pageInfo.getPages(),safeLockDTOList);
        } catch (Exception e) {
            logger.error("分页查询安全开关规则表信息失败，当前页：{}，每页展示条数：{},错误信息：{}", page, rows, e.getMessage());
            throw new AnyTxnParameterException(AnyTxnParameterRespCodeEnum.D_PAGE_SAFE_LOCK_FAULT);
        }
    }

    @Override
    @InsertParameterAnnotation(tableName = "parm_safety_lock", tableDesc = "Safety Switch Rules", isJoinTable = true)
    public ParameterCompare addSafeLock(SafeLockDTO safeLockDTO) {
        if (ObjectUtils.isEmpty(safeLockDTO)){
            logger.error("传入参数不能为空");
            throw new AnyTxnParameterException(AnyTxnParameterRespCodeEnum.P_PARAMETER_EMPTY_FAULT);
        }
        List<SafeLockDetailDTO> sfeLockDetailDTOList = safeLockDTO.getSfeLockDetailDtos();
        if (!CollectionUtils.isEmpty(sfeLockDetailDTOList)){
            for (SafeLockDetailDTO safeLockDetailDTO:sfeLockDetailDTOList) {
                ParmSafetyLock parmSafetyLock = parmSafetyLockMapper.selectByUniqueCondition(safeLockDTO.getOrganizationNumber(),
                        safeLockDTO.getCardProductCode(), safeLockDetailDTO.getAuthTxnType(),
                        safeLockDetailDTO.getAuthTxnCode(), safeLockDetailDTO.getCheckCurrency());
                if (!ObjectUtils.isEmpty(parmSafetyLock)){
                    logger.error("根据唯一业务索引查询安全开关规则表信息，数据已存在, organizationNumber:{}, cardProductCode:{}, authTxnType:{}, authTxnCode:{}, checkCurrency:{}",
                            safeLockDTO.getOrganizationNumber(), safeLockDTO.getCardProductCode(), safeLockDetailDTO.getAuthTxnType(), safeLockDetailDTO.getAuthTxnCode(), safeLockDetailDTO.getCheckCurrency());
                    throw new AnyTxnParameterException(AnyTxnParameterRespCodeEnum.D_DATA_ALREADY_EXISTS_FAULT,  ParameterRepDetailEnum.SAFE_LOCK);
                }
            }
        }
        return ParameterCompare.getBuilder()
                .withMainParmId(safeLockDTO.getCardProductCode())
                .withAfter(safeLockDTO).build(SafeLockDTO.class);
    }

    @Override
    @UpdateParameterAnnotation(tableName = "parm_safety_lock", tableDesc = "Safety Switch Rules", isJoinTable = true)
    public ParameterCompare modifySafeLock(SafeLockDTO safeLockDTO) {
        if (ObjectUtils.isEmpty(safeLockDTO)){
            logger.error("传入参数不能为空");
            throw new AnyTxnParameterException(AnyTxnParameterRespCodeEnum.P_PARAMETER_EMPTY_FAULT);
        }
//        List<ParmSafetyLock> parmSafetyLocks = parmSafetyLockMapper.
//                selectByCardProductCode(safeLockDTO.getCardProductCode(),
//                safeLockDTO.getOrganizationNumber());
//        if (CollectionUtils.isEmpty(parmSafetyLocks)){
//            logger.error("根据主键ID查询安全开关参数 数据不存在");
//            throw new AnyTxnParameterException(AnyTxnParameterRespCodeEnum.D_DATA_NOT_EXISTS_FAULT);
//        }
        SafeLockDTO safeLockByCardProNum = findSafeLockByCardProNum(safeLockDTO.getCardProductCode(),
                safeLockDTO.getOrganizationNumber());
//        safeLockDTO.setId(numberIdGenerator.generateId(TenantUtils.getTenantId())+"");
        return ParameterCompare.getBuilder()
                .withMainParmId(safeLockDTO.getCardProductCode())
                .withAfter(safeLockDTO)
                .withBefore(safeLockByCardProNum)
                .build(SafeLockDTO.class);
    }

    @Override
    @DeleteParameterAnnotation(tableName = "parm_safety_lock", tableDesc = "Safety Switch Rules", isJoinTable = true)
    public ParameterCompare removeSafeLock(String cardProductCode, String organizationNumber) {
        List<ParmSafetyLock> parmSafetyLockList = parmSafetyLockMapper.selectByCardProductCode(cardProductCode, organizationNumber);
        if (CollectionUtils.isEmpty(parmSafetyLockList)){
            logger.error("待删除安全开关规则信息不存在。 cardProductCode:{}", cardProductCode);
            throw new AnyTxnParameterException(AnyTxnParameterRespCodeEnum.D_QUERY_SAFE_LOCK_NULL_BY_ID_FAULT);
        }
        return ParameterCompare.getBuilder()
                .withMainParmId(cardProductCode)
                .withBefore(parmSafetyLockList.get(0)).build(ParmSafetyLock.class);
    }

    @Override
    public void removeSafeLockById(String id) {
        ParmSafetyLock parmSafetyLock = parmSafetyLockMapper.selectByPrimaryKey(id);
        if (ObjectUtils.isEmpty(parmSafetyLock)){
            logger.error("待删除安全开关规则信息不存在。 id:{}", id);
            throw new AnyTxnParameterException(AnyTxnParameterRespCodeEnum.D_QUERY_SAFE_LOCK_NULL_BY_ID_FAULT);
        }

        try {
            parmSafetyLockMapper.deleteByPrimaryKey(id);

        } catch (Exception e) {

            throw new AnyTxnParameterException(AnyTxnParameterRespCodeEnum.D_DELETE_SAFE_LOCK_FAULT);
        }
    }

    @Override
    public SafeLockDTO findSafeLockByCardProNum(String cardProductCode, String organizationNumber) {
        SafeLockDTO safeLockDTO = new SafeLockDTO();
        List<SafeLockDetailDTO> sfeLockDetailDTOList = new ArrayList<>();

        try {
            organizationNumber = StringUtils.isEmpty(organizationNumber) ? OrgNumberUtils.getOrg() : organizationNumber;
            List<ParmSafetyLock> parmSafetyLockList = parmSafetyLockMapper.selectByCardProductCode(cardProductCode, organizationNumber);

            for (ParmSafetyLock parmSafetyLock:parmSafetyLockList) {
                SafeLockDetailDTO safeLockDetailDTO = BeanMapping.copy(parmSafetyLock, SafeLockDetailDTO.class);
                sfeLockDetailDTOList.add(safeLockDetailDTO);
            }
            safeLockDTO.setOrganizationNumber(organizationNumber);
            safeLockDTO.setCardProductCode(cardProductCode);
            safeLockDTO.setSfeLockDetailDtos(sfeLockDetailDTOList);

            return safeLockDTO;
        } catch (Exception e) {
            logger.error("根据卡产品编号查询安全开关规则信息失败，cardProductCode：{},错误信息：{}", cardProductCode, e.getMessage());
            throw new AnyTxnParameterException(AnyTxnParameterRespCodeEnum.P_PARAMETER_DATA_NOT_EXIST, ParameterRepDetailEnum.CARD_PRODUCT_SAFE_LOCK);
        }
    }

    @Override
    public SafeLockDTO findSafeLockById(String id) {
        SafeLockDTO result = null;
        try {
            ParmSafetyLock parmSafetyLock1 = parmSafetyLockMapper.selectByPrimaryKey(id);
            List<ParmSafetyLock> parmSafetyLockList = parmSafetyLockMapper.selectByCardProductCode(parmSafetyLock1.getCardProductCode(), parmSafetyLock1.getOrganizationNumber());

            if (parmSafetyLock1 != null) {
                result = BeanMapping.copy(parmSafetyLock1, SafeLockDTO.class);
                result.setSfeLockDetailDtos(BeanMapping.copyList(parmSafetyLockList, SafeLockDetailDTO.class));
            }

            return result;
        } catch (Exception e) {
            logger.error("根据卡产品编号查询安全开关规则信息失败，id：{},错误信息：{}", id, e.getMessage());
            throw new AnyTxnParameterException(AnyTxnParameterRespCodeEnum.P_PARAMETER_DATA_NOT_EXIST, ParameterRepDetailEnum.CARD_PRODUCT_SAFE_LOCK);
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class, propagation = Propagation.REQUIRED)
    //@ShardingSphereTransactionType(TransactionType.XA)
    protected boolean updateDb(ParmModificationRecord parmModificationRecord) {
        SafeLockDTO safeLockDTO = JSON.parseObject(parmModificationRecord.getParmBody(), SafeLockDTO.class);
        List<SafeLockDetailDTO> sfeLockDetailDTOList = safeLockDTO.getSfeLockDetailDtos();
        if (!CollectionUtils.isEmpty(sfeLockDetailDTOList)){
            for (SafeLockDetailDTO safeLockDetailDTO:sfeLockDetailDTOList) {
                if(safeLockDetailDTO.getId().equals("0")){
                    ParmSafetyLock safetyLock = BeanMapping.copy(safeLockDetailDTO, ParmSafetyLock.class);
                    safetyLock.setCardProductCode(safeLockDTO.getCardProductCode());
                    safetyLock.setId(String.valueOf(numberIdGenerator.generateId(TenantUtils.getTenantId())));
                    safetyLock.initCreateDateTime();
                    safetyLock.initUpdateDateTime();
                    safetyLock.setVersionNumber(1L);
                    safetyLock.setUpdateBy(parmModificationRecord.getApplicationBy());
                    safetyLock.setStatus("1");
                    try{
                        parmSafetyLockMapper.insertSelective(safetyLock);
                    } catch (Exception e) {
                        logger.error("新增安全开关规则信息失败,错误信息{}", e);
                        throw new AnyTxnParameterException(AnyTxnParameterRespCodeEnum.D_INSERT_SAFE_LOCK_FAULT);
                    }
                }else{
                    ParmSafetyLock safetyLock = BeanMapping.copy(safeLockDetailDTO, ParmSafetyLock.class);
                    safetyLock.initUpdateDateTime();

                    safetyLock.setUpdateBy(parmModificationRecord.getApplicationBy());
                    try{
                        parmSafetyLockMapper.updateByPrimaryKeySelective(safetyLock);
                    } catch (Exception e) {
                        logger.error("修改安全开关规则信息失败,错误信息{}", e);
                        throw new AnyTxnParameterException(AnyTxnParameterRespCodeEnum.D_UPDATE_SAFE_LOCK_FAULT);
                    }
                }
            }
        }
        return true;
    }

    @Override
    @Transactional(rollbackFor = Exception.class, propagation = Propagation.REQUIRED)
    //@ShardingSphereTransactionType(TransactionType.XA)
    protected boolean insertDb(ParmModificationRecord parmModificationRecord) {
        SafeLockDTO safeLockDTO = JSON.parseObject(parmModificationRecord.getParmBody(), SafeLockDTO.class);
        List<SafeLockDetailDTO> sfeLockDetailDtos = safeLockDTO.getSfeLockDetailDtos();
        for (SafeLockDetailDTO safeLockDetailDTO : sfeLockDetailDtos){
            ParmSafetyLock safetyLock = BeanMapping.copy(safeLockDetailDTO, ParmSafetyLock.class);
            safetyLock.setCardProductCode(safeLockDTO.getCardProductCode());
            safetyLock.setId(String.valueOf(numberIdGenerator.generateId(TenantUtils.getTenantId())));
            safetyLock.initCreateDateTime();
            safetyLock.initUpdateDateTime();
            safetyLock.setVersionNumber(1L);
            safetyLock.setUpdateBy(parmModificationRecord.getApplicationBy());
            safetyLock.setStatus("1");
            try{
                 parmSafetyLockMapper.insertSelective(safetyLock);
            } catch (Exception e) {
                logger.error("新增安全开关规则信息失败,错误信息{}", e);
                throw new AnyTxnParameterException(AnyTxnParameterRespCodeEnum.D_INSERT_SAFE_LOCK_FAULT);
            }
        }
        return true;
    }

    @Override
    @Transactional(rollbackFor = Exception.class, propagation = Propagation.REQUIRED)
    //@ShardingSphereTransactionType(TransactionType.XA)
    protected boolean deleteDb(ParmModificationRecord parmModificationRecord) {
        ParmSafetyLock parmSafetyLock = JSON.parseObject(parmModificationRecord.getParmBody(), ParmSafetyLock.class);
        try {
            parmSafetyLockMapper.deleteByCardProductCode(parmSafetyLock.getCardProductCode(),
                    parmSafetyLock.getOrganizationNumber());
        } catch (Exception e) {
            throw new AnyTxnParameterException(AnyTxnParameterRespCodeEnum.D_DELETE_SAFE_LOCK_FAULT);
        }
        return true;
    }
}
