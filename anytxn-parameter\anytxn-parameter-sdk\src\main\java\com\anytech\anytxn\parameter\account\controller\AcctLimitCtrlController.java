package com.anytech.anytxn.parameter.account.controller;

import io.swagger.v3.oas.annotations.tags.Tag;
import io.swagger.v3.oas.annotations.Operation;
import com.anytech.anytxn.common.core.base.PageResultDTO;
import com.anytech.anytxn.common.core.base.AnyTxnHttpResponse;
import com.anytech.anytxn.common.core.base.BizBaseController;
import com.anytech.anytxn.parameter.base.account.domain.dto.AcctLimitCtrlReqDTO;
import com.anytech.anytxn.parameter.base.account.domain.dto.AcctLimitCtrlResDTO;
import com.anytech.anytxn.parameter.base.account.service.IAcctLimitCtrlService;
import com.anytech.anytxn.parameter.base.common.enums.ParameterRepDetailEnum;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

/**
 * <AUTHOR>
 * @date 2020-11-30
 */
@Tag(name = "账户额度控制信息")
@RestController
public class AcctLimitCtrlController extends BizBaseController {

    @Autowired
    private IAcctLimitCtrlService acctLimitCtrlService;

    /**
     * 新增账户额度控制参数
     * @param acctLimitCtrlReq AcctLimitCtrlReqDTO
     * @return AcctLimitCtrlResDTO
     *
     */
    @Operation(summary = "新增账户额度控制参数",description = "新增账户额度控制参数")
    @PostMapping(value = "/param/acctLimitCtrl")
    public AnyTxnHttpResponse<Object> create(@RequestBody AcctLimitCtrlReqDTO acctLimitCtrlReq) {
        return AnyTxnHttpResponse.success(acctLimitCtrlService.addAcctLimitCtrl(acctLimitCtrlReq),ParameterRepDetailEnum.CREATE.message());
    }

    /**
     * 查询账户额度控制参数信息
     * @return PageResultDTO<AcctLimitCtrlResDTO>
     */
    @Operation(summary = "账户额度控制参数分页查询",description = "分页查询")
    @GetMapping(value = "/param/acctLimitCtrl/pageNum/{pageNum}/pageSize/{pageSize}")
    public AnyTxnHttpResponse<PageResultDTO<AcctLimitCtrlResDTO>> getAllPage(@PathVariable(value = "pageNum") Integer pageNum,
                                                                             @PathVariable(value = "pageSize") Integer pageSize,
                                                                             AcctLimitCtrlReqDTO acctLimitCtrlReq) {
        PageResultDTO<AcctLimitCtrlResDTO> acctLimitCtrlPage = acctLimitCtrlService.findAll(pageNum,pageSize,acctLimitCtrlReq);
        return AnyTxnHttpResponse.success(acctLimitCtrlPage);

    }

    /**
     * 修改账户额度控制参数信息
     * @param acctLimitCtrlReq AcctLimitCtrlReqDTO
     * @return AcctLimitCtrlResDTO
     */
    @Operation(summary = "账户额度控制参数信息修改", description = "只更新需要更新的,其他不变")
    @PutMapping(value = "/param/acctLimitCtrl")
    public AnyTxnHttpResponse<Object> modify(@RequestBody AcctLimitCtrlReqDTO acctLimitCtrlReq) {
        return AnyTxnHttpResponse.success(acctLimitCtrlService.modifyAcctLimitCtrl(acctLimitCtrlReq),ParameterRepDetailEnum.UPDATE.message());
    }

    /**
     * 通过id删除账户额度控制参数信息
     * @param id Long
     * @return Boolean
     */
    @Operation(summary = "删除账户额度控制参数信息", description = "通过id删除账户额度控制参数信息")
    @DeleteMapping(value = "/param/acctLimitCtrl/id/{id}")
    public AnyTxnHttpResponse<Boolean> remove(@PathVariable String id) {
        Boolean flag = acctLimitCtrlService.removeAcctLimitCtrl(id);
        return AnyTxnHttpResponse.success(flag,ParameterRepDetailEnum.DEL.message());
    }

    /**
     * 通过机构号+账户额度控制id删除账户额度控制参数信息
     * @param orgNum String
     * @param acctLimitCtrlId String
     * @return Boolean
     */
    @Operation(summary = "账户额度控制id删除账户额度控制参数信息", description = "账户额度控制id删除账户额度控制参数信息")
    @DeleteMapping(value = "/param/acctLimitCtrl/orgNum/{orgNum}/acctLimitCtrlId/{acctLimitCtrlId}")
    public AnyTxnHttpResponse<Object> remove(@PathVariable String orgNum, @PathVariable String acctLimitCtrlId) {
        return AnyTxnHttpResponse.success(acctLimitCtrlService.removeAcctLimitCtrlByOrgAndAcctId(orgNum, acctLimitCtrlId),ParameterRepDetailEnum.DEL.message());
    }

    /**
     * 通过id查询账户额度控制参数信息
     * @param id Long
     * @return AcctLimitCtrlResDTO
     */
    @Operation(summary =  "通过id查询账户额度控制参数信息", description = "通过id查询账户额度控制参数信息")
    @GetMapping(value = "/param/acctLimitCtrl/id/{id}")
    public AnyTxnHttpResponse<AcctLimitCtrlResDTO> getById(@PathVariable String id) {
        AcctLimitCtrlResDTO acctLimitCtrlResDTO = acctLimitCtrlService.findById(id);
        return AnyTxnHttpResponse.success(acctLimitCtrlResDTO);

    }

    /**
     * 通过机构号+账户额度控制id查询账户额度控制参数信息
     * @param orgNum String
     * @param acctLimitCtrlId String
     * @return AcctLimitCtrlResDTO
     */
    @Operation(summary = "通过机构号+账户额度控制id查询账户额度控制参数信息",
            description = "通过机构号+账户额度控制id查询账户额度控制参数信息")
    @GetMapping(value = "/param/acctLimitCtrl/orgNum/{orgNum}/acctLimitCtrlId/{acctLimitCtrlId}")
    public AnyTxnHttpResponse<AcctLimitCtrlResDTO> getById(@PathVariable String orgNum, @PathVariable String acctLimitCtrlId) {
        AcctLimitCtrlResDTO acctLimitCtrlResDTO = acctLimitCtrlService.findByOrgNumAndAcctLimitCtrlId(orgNum, acctLimitCtrlId);
        return AnyTxnHttpResponse.success(acctLimitCtrlResDTO);

    }
}
