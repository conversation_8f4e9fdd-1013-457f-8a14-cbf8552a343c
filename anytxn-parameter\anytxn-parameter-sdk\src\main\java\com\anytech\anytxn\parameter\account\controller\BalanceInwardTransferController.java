package com.anytech.anytxn.parameter.account.controller;

import io.swagger.v3.oas.annotations.tags.Tag;
import com.anytech.anytxn.common.core.base.PageResultDTO;
import com.anytech.anytxn.common.core.utils.OrgNumberUtils;
import com.anytech.anytxn.common.core.base.AnyTxnHttpResponse;
import com.anytech.anytxn.common.core.base.BizBaseController;
import com.anytech.anytxn.parameter.base.account.domain.dto.BalanceInwardTransferDTO;
import com.anytech.anytxn.parameter.base.account.service.IBalanceInwardTransferService;
import com.anytech.anytxn.parameter.base.common.enums.ParameterRepDetailEnum;
import org.springframework.web.bind.annotation.*;

import jakarta.annotation.Resource;

/**
 * Description:余额内转参数
 * date: 2021/5/11 15:09
 *
 * <AUTHOR>
 */
@Tag(name = "余额内转参数")
@RestController
public class BalanceInwardTransferController extends BizBaseController {

    @Resource
    private IBalanceInwardTransferService balanceInwardTransferService;

    /**
     * 添加
     * @param  balanceInwardTransferDTO 余额内转参数
     * @return Object
     */
    @PostMapping(value = "/param/addBalanceInwardTransfer")
    public AnyTxnHttpResponse<Object> add(@RequestBody BalanceInwardTransferDTO balanceInwardTransferDTO){
        return AnyTxnHttpResponse.success(balanceInwardTransferService.add(balanceInwardTransferDTO), ParameterRepDetailEnum.CREATE.message());
    }

    /**
     * 修改
     * @param  balanceInwardTransferDTO 余额内转参数
     * @return Object
     */
    @PutMapping(value = "/param/modifyBalanceInwardTransfer")
    public AnyTxnHttpResponse<Object> modify(@RequestBody BalanceInwardTransferDTO balanceInwardTransferDTO){
        return AnyTxnHttpResponse.success(balanceInwardTransferService.modify(balanceInwardTransferDTO), ParameterRepDetailEnum.UPDATE.message());
    }

    /**
     * 分页查询
     * @param  pageNum pageSize  organizationNumber
     * @return BalanceInwardTransferDTO
     */
    @GetMapping(value = "/param/balanceInwardTransfer/pageNum/{pageNum}/pageSize/{pageSize}")
    public AnyTxnHttpResponse<PageResultDTO<BalanceInwardTransferDTO>> findByPage(@PathVariable(value = "pageNum") Integer pageNum,
                                                                                @PathVariable(value = "pageSize") Integer pageSize,
                                                                                @RequestParam(value = "tableId",required = false) String tableId,
                                                                                @RequestParam(value = "description",required = false) String description,
                                                                                @RequestParam(value = "organizationNumber",required = false) String organizationNumber){
        return AnyTxnHttpResponse.success(balanceInwardTransferService.findPage(pageNum,pageSize, OrgNumberUtils.getOrg(organizationNumber),tableId, description));
    }

    /**
     * 删除
     * @param  id 余额内转参数ID
     * @return Object
     */
    @DeleteMapping(value = "/param/removeBalanceInwardTransfer/id/{id}")
    public AnyTxnHttpResponse<Object> remove(@PathVariable String id){
        return AnyTxnHttpResponse.success(balanceInwardTransferService.remove(id), ParameterRepDetailEnum.DEL.message());
    }

    /**
     * 通过ID查看
     * @param  id 余额内转参数ID
     * @return BalanceInwardTransferDTO
     */
    @GetMapping(value = "/param/balanceInwardTransfer/id/{id}")
    public AnyTxnHttpResponse<BalanceInwardTransferDTO> findById(@PathVariable String id){
        return AnyTxnHttpResponse.success(balanceInwardTransferService.findById(id));
    }
}
