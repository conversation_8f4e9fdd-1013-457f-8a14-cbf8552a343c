package com.anytech.anytxn.parameter.settlement.controller;

import com.anytech.anytxn.common.core.base.PageResultDTO;
import com.anytech.anytxn.common.core.base.AnyTxnHttpResponse;
import com.anytech.anytxn.common.core.base.BizBaseController;
import com.anytech.anytxn.parameter.base.common.constants.Constants;
import com.anytech.anytxn.parameter.base.settlement.domain.dto.DinersFranchiseCodeDTO;
import com.anytech.anytxn.parameter.base.settlement.service.IDinersFranchiseCodeService;
import io.swagger.v3.oas.annotations.tags.Tag;
import io.swagger.v3.oas.annotations.Operation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;


/**
 * <AUTHOR>
 */
@RestController
@Tag(name = "DinersFranchiseCodeController")
public class DinersFranchiseCodeController extends BizBaseController {

    @Autowired
    private IDinersFranchiseCodeService dinersFranchiseCodeTableService;

    @Operation(summary = "FranchiseCodeTable添加")
    @PostMapping(value="/param/franchise/code/add")
    public AnyTxnHttpResponse addDinersCpDcfrmt(@RequestBody DinersFranchiseCodeDTO dinersFranchiseCodeDTO){
        dinersFranchiseCodeDTO.setUpdateBy(Constants.DEFAULT_USER);
        dinersFranchiseCodeTableService.add(dinersFranchiseCodeDTO);
        return AnyTxnHttpResponse.success();
    }

    @Operation(summary = "FranchiseCodeTable更新")
    @PostMapping(value="/param/franchise/code/update")
    public AnyTxnHttpResponse updateDinersCpDcfrmt(@RequestBody DinersFranchiseCodeDTO dinersFranchiseCodeDTO) {
        dinersFranchiseCodeTableService.modify(dinersFranchiseCodeDTO);
        return AnyTxnHttpResponse.success();
    }

    @Operation(summary = "FranchiseCodeTable分页搜索")
    @GetMapping(value="/param/franchise/code/select/{pageNum}/{pageSize}")
    public AnyTxnHttpResponse<PageResultDTO<DinersFranchiseCodeDTO>> selectDinersCpDcfrmt(@PathVariable(value = "pageNum") Integer pageNum,
                                                                                          @PathVariable(value = "pageSize") Integer pageSize,
                                                                                          @RequestParam String frFranchiseCd){
        DinersFranchiseCodeDTO dinersFranchiseCodeDTO = new DinersFranchiseCodeDTO();
        dinersFranchiseCodeDTO.setFrFranchiseCd(frFranchiseCd);
        PageResultDTO<DinersFranchiseCodeDTO> dinersFranchiseCodeList = dinersFranchiseCodeTableService.page(pageNum,pageSize, dinersFranchiseCodeDTO);
        return AnyTxnHttpResponse.success(dinersFranchiseCodeList);
    }

    @Operation(summary = "FranchiseCodeTable查询")
    @GetMapping(value="/param/franchise/code/enquiry")
    public AnyTxnHttpResponse<DinersFranchiseCodeDTO> enquiryDinersCpDcfrmt(@RequestParam String frFranchiseCd){
        return AnyTxnHttpResponse.success(dinersFranchiseCodeTableService.findOne(frFranchiseCd));
    }

    @Operation(summary = "FranchiseCodeTable删除")
    @DeleteMapping(value="/param/franchise/code/delete")
    public AnyTxnHttpResponse deleteDinersCpDcfrmt(@RequestParam String frFranchiseCd){
        dinersFranchiseCodeTableService.remove(frFranchiseCd);
        return AnyTxnHttpResponse.success();
    }

    @Operation(summary = "FranchiseCodeTable masterId 列表")
    @GetMapping(value="/param/franchise/code/all")
    public AnyTxnHttpResponse<List<String>> getAllCode(){
        return AnyTxnHttpResponse.success(dinersFranchiseCodeTableService.getAllCode());
    }
}
