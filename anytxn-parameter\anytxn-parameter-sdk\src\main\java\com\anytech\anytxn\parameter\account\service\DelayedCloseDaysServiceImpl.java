package com.anytech.anytxn.parameter.account.service;

import com.alibaba.fastjson.JSON;
import com.github.pagehelper.Page;
import com.github.pagehelper.PageHelper;
import com.anytech.anytxn.common.core.annotation.db.DeleteParameterAnnotation;
import com.anytech.anytxn.common.core.annotation.db.InsertParameterAnnotation;
import com.anytech.anytxn.common.core.annotation.db.UpdateParameterAnnotation;
import com.anytech.anytxn.business.base.audit.AbstractParameterService;
import com.anytech.anytxn.common.core.base.ParameterCompare;
import com.anytech.anytxn.business.base.audit.ParmModificationRecord;
import com.anytech.anytxn.common.core.utils.OrgNumberUtils;
import com.anytech.anytxn.common.core.base.PageResultDTO;
import com.anytech.anytxn.common.core.utils.BeanMapping;
import com.anytech.anytxn.common.sequence.utils.Number16IdGen;
import com.anytech.anytxn.common.core.utils.TenantUtils;
import com.anytech.anytxn.parameter.base.account.domain.dto.DelayedCloseDaysDTO;
import com.anytech.anytxn.parameter.base.account.service.IDelayedCloseDaysService;
import com.anytech.anytxn.parameter.base.common.enums.AccountManagementFeeIndicatorEnum;
import com.anytech.anytxn.parameter.base.common.enums.AnyTxnParameterRespCodeEnum;
import com.anytech.anytxn.parameter.base.common.enums.AutoTransferOutIndicatorEnum;
import com.anytech.anytxn.parameter.base.common.enums.ParameterRepDetailEnum;
import com.anytech.anytxn.parameter.base.common.exception.AnyTxnParameterException;
import com.anytech.anytxn.parameter.account.mapper.ParmDelayedCloseDaysMapper;
import com.anytech.anytxn.parameter.account.mapper.ParmDelayedCloseDaysSelfMapper;
import com.anytech.anytxn.parameter.base.account.domain.model.ParmDelayedCloseDays;
//import org.apache.shardingsphere.transaction.annotation.ShardingSphereTransactionType;
//import org.apache.shardingsphere.transaction.core.TransactionType;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.StringUtils;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;

/**
 * 延迟销户天数参数业务逻辑处理
 * <AUTHOR>
 * @date 2019-11-19
 */
@Service(value = "parm_delayed_close_days_serviceImpl")
public class DelayedCloseDaysServiceImpl extends AbstractParameterService implements IDelayedCloseDaysService {

    private final Logger log = LoggerFactory.getLogger(DelayedCloseDaysServiceImpl.class);

    @Autowired
    private ParmDelayedCloseDaysSelfMapper parmDelayedCloseDaysSelfMapper;
    @Autowired
    private ParmDelayedCloseDaysMapper parmDelayedCloseDaysMapper;

    @Autowired
    private Number16IdGen numberIdGenerator;

    /**
     * 新建延迟销户天数参数记录
     * @param delayedCloseDaysDTO 延迟销户天数参数
     */
    @Override
    @InsertParameterAnnotation(tableName = "parm_delayed_close_days", tableDesc = "Close Account")
    public ParameterCompare add(DelayedCloseDaysDTO delayedCloseDaysDTO) {
        log.debug("新建延迟销户天数参数记录");
        //必输项校验
        checkRequiredItem(delayedCloseDaysDTO);
        //检查索引
        ParmDelayedCloseDays parmDelayedCloseDays = parmDelayedCloseDaysSelfMapper.
                selectByIndex(OrgNumberUtils.getOrg(delayedCloseDaysDTO.getOrganizationNumber()), delayedCloseDaysDTO.getTableId());
        if (null != parmDelayedCloseDays){
            log.error("根据索引查询延迟销户天数参数表信息，数据已存在, orgNum:{}, tableId:{}",
                    delayedCloseDaysDTO.getOrganizationNumber(), delayedCloseDaysDTO.getTableId());

            throw new AnyTxnParameterException(AnyTxnParameterRespCodeEnum.D_EXIST_PARM_DELAYED_CLOSE_DAYS_FAULT);
        }
        ParmDelayedCloseDays delayedCloseDays = BeanMapping.copy(delayedCloseDaysDTO, ParmDelayedCloseDays.class);
        delayedCloseDays.setId(String.valueOf(numberIdGenerator.generateId(TenantUtils.getTenantId())));
        try {
            return ParameterCompare.getBuilder().withAfter(delayedCloseDays).build(ParmDelayedCloseDays.class);
        } catch (Exception e) {
            log.error("新建延迟销户天数参数 插入数据库失败");

            throw new AnyTxnParameterException(AnyTxnParameterRespCodeEnum.D_INSERT_PARM_DELAYED_CLOSE_DAYS_FAULT);
        }
    }

    /**
     * 必输项检查
     * @param delayedCloseDaysDTO 延迟销户天数参数
     */
    private void checkRequiredItem(DelayedCloseDaysDTO delayedCloseDaysDTO){
        if (null == delayedCloseDaysDTO.getTableId() || "".equals(delayedCloseDaysDTO.getTableId())){
            log.error("参数表id不能为空或空字符");
            throw new AnyTxnParameterException(AnyTxnParameterRespCodeEnum.D_PARM_DELAYED_CLOSE_DAYS_ID_NULL_FAULT);
        }
        if (null == delayedCloseDaysDTO.getDelayedCloseDays()){
            log.error("延迟销户天数不能为空或空字符");
            throw new AnyTxnParameterException(AnyTxnParameterRespCodeEnum.D_PARM_DELAYED_CLOSE_DAYS_DAYS_NULL_FAULT);
        }
        if (null == delayedCloseDaysDTO.getDescription() || "".equals(delayedCloseDaysDTO.getDescription())){
            log.error("参数表描述不能为空或空字符");
            throw new AnyTxnParameterException(AnyTxnParameterRespCodeEnum.D_EXIST_PARM_CURRENCY_RATE_FAULT);
        }
        //“AUTO_TRANSFER_OUT_INDICATOR”和“ACCOUNT_MANAGEMENT_FEE_INDICATOR”不可同时为“1”。
        if(Objects.equals(AutoTransferOutIndicatorEnum.YES.getCode(),delayedCloseDaysDTO.getAutoTransferOutIndicator())
                && Objects.equals(AccountManagementFeeIndicatorEnum.YES.getCode(),delayedCloseDaysDTO.getAccountManagementFeeIndicator())){
            log.error("到期自动转出溢缴款标志和收取账户管理费标志不能同时为1");
            throw new AnyTxnParameterException(AnyTxnParameterRespCodeEnum.PARAMETER_EXCEPTION, ParameterRepDetailEnum.AUTO);
        }
    }

    /**
     * 修改延迟销户天数参数记录
     * @param delayedCloseDaysDTO 延迟销户天数参数
     */
    @Override
    @UpdateParameterAnnotation(tableName = "parm_delayed_close_days", tableDesc = "Close Account")
    public ParameterCompare update(DelayedCloseDaysDTO delayedCloseDaysDTO) {
        log.debug("修改延迟销户天数参数记录");
        if (null == delayedCloseDaysDTO){
            log.error("修改延迟销户天数参数 传入参数不能为空");
            throw new AnyTxnParameterException(AnyTxnParameterRespCodeEnum.P_PARAMETER_EMPTY_FAULT);
        }
        //“AUTO_TRANSFER_OUT_INDICATOR”和“ACCOUNT_MANAGEMENT_FEE_INDICATOR”不可同时为“1”。
        if(Objects.equals(AutoTransferOutIndicatorEnum.YES.getCode(),delayedCloseDaysDTO.getAutoTransferOutIndicator())
                && Objects.equals(AccountManagementFeeIndicatorEnum.YES.getCode(),delayedCloseDaysDTO.getAccountManagementFeeIndicator())){
            log.error("到期自动转出溢缴款标志和收取账户管理费标志不能同时为1");
            throw new AnyTxnParameterException(AnyTxnParameterRespCodeEnum.PARAMETER_EXCEPTION, ParameterRepDetailEnum.AUTO);
        }
        ParmDelayedCloseDays parmDelayedCloseDays = parmDelayedCloseDaysMapper.selectByPrimaryKey(delayedCloseDaysDTO.getId());
        if (null == parmDelayedCloseDays){
            log.error("修改延迟销户天数参数 通过主键未查询到数据, id:{}", delayedCloseDaysDTO.getId());
            throw new AnyTxnParameterException(AnyTxnParameterRespCodeEnum.D_QUERY_PARM_DELAYED_CLOSE_DAYS_FAULT);
        }
        ParmDelayedCloseDays delayedCloseDays = BeanMapping.copy(delayedCloseDaysDTO, ParmDelayedCloseDays.class);
        try {
            return ParameterCompare.getBuilder().withAfter(delayedCloseDays).withBefore(parmDelayedCloseDays).build(ParmDelayedCloseDays.class);
        } catch (Exception e) {
            log.error("修改延迟销户天数参数失败");
            throw new AnyTxnParameterException(AnyTxnParameterRespCodeEnum.D_MODIFY_PARM_DELAYED_CLOSE_DAYS_FAULT);
        }
    }

    /**
     * 删除延迟销户天数参数记录
     * @param id 技术主键
     */
    @Override
    @DeleteParameterAnnotation(tableName = "parm_delayed_close_days", tableDesc = "Close Account")
    public ParameterCompare delete(String id) {
        log.debug("删除延迟销户天数参数记录");
        if (null == id){
            log.error("删除延迟销户天数参数记录, id不能为空, id:{}", id);
            throw new AnyTxnParameterException(AnyTxnParameterRespCodeEnum.P_PARAMETER_EMPTY_FAULT);
        }
        ParmDelayedCloseDays parmDelayedCloseDays = parmDelayedCloseDaysMapper.selectByPrimaryKey(id);
        if (null == parmDelayedCloseDays){
            log.error("删除延迟销户天数参数信息，通过主键未查到数据, id:{}", id);
            throw new AnyTxnParameterException(AnyTxnParameterRespCodeEnum.D_QUERY_PARM_DELAYED_CLOSE_DAYS_FAULT);
        }
        return ParameterCompare.getBuilder().withBefore(parmDelayedCloseDays).build(ParmDelayedCloseDays.class);
    }

    /**
     * 根据机构号分页查询延迟销户天数参数信息
     * @param pageNum            当前页数
     * @param pageSize           每页显示条数
     * @param organizationNumber 机构号
     * @return PageResultDTO<DelayedCloseDaysDTO>
     */
    @Override
    public PageResultDTO<DelayedCloseDaysDTO> findAll(Integer pageNum, Integer pageSize, String tableId,String description,String delayedCloseDays, String organizationNumber) {
        Integer delayedCloseDaysInt = null;
        if(!StringUtils.isEmpty(delayedCloseDays)){
            try {
                delayedCloseDaysInt = Integer.parseInt(delayedCloseDays);
            }catch (NumberFormatException e){
                throw new AnyTxnParameterException(AnyTxnParameterRespCodeEnum.PARAMETER_EXCEPTION, ParameterRepDetailEnum.NUMBER_ERROR);
            }
        }
        Map<String,Object> map = new HashMap<>(8);
        map.put("organizationNumber", StringUtils.isEmpty(organizationNumber) ? OrgNumberUtils.getOrg() :organizationNumber);
        map.put("tableId",tableId);
        map.put("description",description);
        map.put("delayedCloseDays",delayedCloseDays);
        map.put("delayedCloseDaysInt",delayedCloseDaysInt);

        log.debug("根据机构号分页查询延迟销户天数参数信息");
        Page<ParmDelayedCloseDays> page = PageHelper.startPage(pageNum, pageSize);
        List<ParmDelayedCloseDays> parmDelayedCloseDays = parmDelayedCloseDaysSelfMapper.selectByCondition(map);
        PageResultDTO<ParmDelayedCloseDays> pageResultDto = new PageResultDTO<>(pageNum, pageSize, page.getTotal(), page.getPages(), parmDelayedCloseDays);
        List<ParmDelayedCloseDays> delayedCloseDaysList = pageResultDto.getData();
        List<DelayedCloseDaysDTO> delayedCloseDaysDtos = BeanMapping.copyList(delayedCloseDaysList, DelayedCloseDaysDTO.class);
        return new PageResultDTO<>(pageNum, pageSize, page.getTotal(), page.getPages(), delayedCloseDaysDtos);
    }

    /**
     * 根据id查询延迟销户天数参数记录
     * @param id 技术主键
     * @return DelayedCloseDaysDTO
     */
    @Override
    public DelayedCloseDaysDTO findById(String id) {
        log.debug("根据id查询延迟销户天数参数记录");
        if (null == id){
            log.error("主键id不能为空");
            throw new AnyTxnParameterException(AnyTxnParameterRespCodeEnum.P_PARAMETER_EMPTY_FAULT);
        }
        ParmDelayedCloseDays parmDelayedCloseDays = parmDelayedCloseDaysMapper.selectByPrimaryKey(id);
        if (null == parmDelayedCloseDays){
            log.error("根据id查询延迟销户天数明细失败");
            throw new AnyTxnParameterException(AnyTxnParameterRespCodeEnum.D_QUERY_PARM_DELAYED_CLOSE_DAYS_FAULT);
        }
        return BeanMapping.copy(parmDelayedCloseDays, DelayedCloseDaysDTO.class);
    }

    /**
     * 根据索引：机构号、参数表id查询延迟销户天数参数记录
     * @param organizationNumber 机构号
     * @param tableId            参数表id
     * @return DelayedCloseDaysDTO
     */
    @Override
    public DelayedCloseDaysDTO findByTableId(String organizationNumber, String tableId) {
        if (null == tableId){
            log.error("根据索引查询延迟销户天数参数记录, 参数表id不能为空");
            throw new AnyTxnParameterException(AnyTxnParameterRespCodeEnum.P_PARAMETER_TABLE_ID_EMPTY_FAULT);
        }
        ParmDelayedCloseDays parmDelayedCloseDays = parmDelayedCloseDaysSelfMapper.selectByIndex(organizationNumber, tableId);
        if (null == parmDelayedCloseDays){
            log.error("根据索引查询延迟销户天数参数记录失败");
            throw new AnyTxnParameterException(AnyTxnParameterRespCodeEnum.D_GET_PARM_DELAYED_CLOSE_DAYS_FAULT);
        }
        return BeanMapping.copy(parmDelayedCloseDays, DelayedCloseDaysDTO.class);
    }

    @Override
    @Transactional(rollbackFor = Exception.class,propagation = Propagation.REQUIRED)
    //@ShardingSphereTransactionType(TransactionType.XA)
    protected boolean updateDb(ParmModificationRecord parmModificationRecord) {
        ParmDelayedCloseDays parmDelayedCloseDays = JSON.parseObject(parmModificationRecord.getParmBody(), ParmDelayedCloseDays.class);
        parmDelayedCloseDays.initUpdateDateTime();
        int i = parmDelayedCloseDaysMapper.updateByPrimaryKeySelective(parmDelayedCloseDays);
        return i > 0;
    }

    @Override
    @Transactional(rollbackFor = Exception.class,propagation = Propagation.REQUIRED)
    //@ShardingSphereTransactionType(TransactionType.XA)
    protected boolean insertDb(ParmModificationRecord parmModificationRecord) {
        ParmDelayedCloseDays parmDelayedCloseDays = JSON.parseObject(parmModificationRecord.getParmBody(), ParmDelayedCloseDays.class);
        parmDelayedCloseDays.initCreateDateTime();
        parmDelayedCloseDays.initUpdateDateTime();
        int i = parmDelayedCloseDaysMapper.insertSelective(parmDelayedCloseDays);
        return i > 0;
    }

    @Override
    @Transactional(rollbackFor = Exception.class,propagation = Propagation.REQUIRED)
    //@ShardingSphereTransactionType(TransactionType.XA)
    protected boolean deleteDb(ParmModificationRecord parmModificationRecord) {
        ParmDelayedCloseDays parmDelayedCloseDays = JSON.parseObject(parmModificationRecord.getParmBody(), ParmDelayedCloseDays.class);
        int i = parmDelayedCloseDaysMapper.deleteByPrimaryKey(parmDelayedCloseDays.getId());
        return i > 0;
    }
}
