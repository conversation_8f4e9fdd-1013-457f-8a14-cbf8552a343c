package com.anytech.anytxn.parameter.common.controller;

import io.swagger.v3.oas.annotations.tags.Tag;
import com.anytech.anytxn.common.core.base.AnyTxnHttpResponse;
import com.anytech.anytxn.common.core.base.BizBaseController;
import com.anytech.anytxn.parameter.base.common.enums.AnyTxnParameterRespCodeEnum;
import com.anytech.anytxn.parameter.base.common.exception.AnyTxnParameterException;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.bind.annotation.RestController;

/**
 * 默认controller
 *
 * <AUTHOR>
 * @date 2018-09-05 18:06
 **/
@Tag(name = "默认接口")
@RestController
public class IndexController extends BizBaseController {


    public static final String RUNTIME = "runtime";
    public static final String ARG = "arg";
    public static final String NOTFOUND = "notfound";

    @GetMapping("/param/exception/{type}")
    @ResponseBody
    public AnyTxnHttpResponse exception(@PathVariable String type) throws AnyTxnParameterException {
        if(RUNTIME.equals(type)){
            throw new AnyTxnParameterException(AnyTxnParameterRespCodeEnum.PARAMETER_EXCEPTION);
        }else if(ARG.equals(type)){
            throw new AnyTxnParameterException(AnyTxnParameterRespCodeEnum.PARAMETER_EXCEPTION);
        }else if(NOTFOUND.equals(type)){
            throw new AnyTxnParameterException(AnyTxnParameterRespCodeEnum.PARAMETER_EXCEPTION);
        }
        return AnyTxnHttpResponse.success("type:[runtime arg notfound]");
    }


}
