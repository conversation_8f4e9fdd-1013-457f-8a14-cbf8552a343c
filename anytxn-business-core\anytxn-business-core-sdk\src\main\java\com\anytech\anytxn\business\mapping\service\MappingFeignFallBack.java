package com.anytech.anytxn.business.mapping.service;

import com.anytech.anytxn.business.mapping.MappingFeign;
import com.anytech.anytxn.common.core.constants.RespCodePrefix;
import com.anytech.anytxn.common.core.base.AnyTxnHttpResponse;
import com.anytech.anytxn.business.base.card.domain.dto.MappingBusinessTokenDTO;
import com.anytech.anytxn.business.base.mapping.enums.IdentityTypeEnum;
import com.anytech.anytxn.business.base.mapping.domain.bo.MappingBO;
import com.anytech.anytxn.business.base.mapping.domain.dto.MappingDTO;
import org.springframework.stereotype.Component;
import org.springframework.web.bind.annotation.*;

import java.util.List;


/**
 *
 *<pre>
 * Description:
 * Company:		江融信
 * Author:		lichao
 * Version:		1.0
 * Create at:	2021/1/22 16:32
 * <p>
 * 修改历史:
 * 日期    作者    版本  修改描述
 * ------------------------------------------------------------------
 *
 *</pre>
 */
@Component
@RequestMapping({"/anytxn/v2/api"})
public class MappingFeignFallBack  implements MappingFeign {

    @Override
    public AnyTxnHttpResponse<String> getCidByOrgNumAndRouteMap(@RequestParam(value = "organizationNumber") String organizationNumber,
                                                       @RequestParam(value = "routeType") IdentityTypeEnum routeType,
                                                       @RequestParam(value = "routeValue") String routeValue){
        return AnyTxnHttpResponse.fail(RespCodePrefix.PREFIX_CID_MAPPING_S, "mapping服务不可用");
    }
    @Override
    public AnyTxnHttpResponse<Boolean> saveMapping(@RequestHeader(value = "organizationNumber") String organizationNumber,@RequestBody List<MappingBO> mappings){
        return AnyTxnHttpResponse.fail(RespCodePrefix.PREFIX_CID_MAPPING_S, "mapping服务不可用");
    }
    @Override
    public  AnyTxnHttpResponse<MappingDTO> getShardValueByOrgNumAndRouteMap(@RequestParam(value = "organizationNumber") String organizationNumber,
                                                                            @RequestParam(value = "routeType") IdentityTypeEnum routeType,
                                                                            @RequestParam(value = "routeValue") String routeValue){
        return AnyTxnHttpResponse.fail(RespCodePrefix.PREFIX_CID_MAPPING_S, "mapping服务不可用");
    }

    @Override
    public AnyTxnHttpResponse<Boolean> deleteMapping(@RequestHeader("organizationNumber") String organizationNumberHeader,@RequestParam(value="organizationNumber") String organizationNumber, @RequestParam(value="identity") String identity, @RequestParam(value= "identityTypeEnum") IdentityTypeEnum identityTypeEnum) {
        return AnyTxnHttpResponse.fail(RespCodePrefix.PREFIX_CID_MAPPING_S, "mapping服务不可用");

    }

    /**
     * 根据附属客户ID查询附属卡列表接口
     * @param cid
     * @return
     */
    @Override
    public AnyTxnHttpResponse<List<String>> getSubBankCardListBySubCid(@RequestParam(value = "organizationNumber") String organizationNumber,@RequestParam(value = "cid") String cid){
        return AnyTxnHttpResponse.fail(RespCodePrefix.PREFIX_CID_MAPPING_S, "mapping服务不可用");
    }

    @Override
    public AnyTxnHttpResponse<MappingBusinessTokenDTO> getBusinessTokenByCardNumber(String cardNumber) {
        return AnyTxnHttpResponse.fail(RespCodePrefix.PREFIX_CID_MAPPING_S, "mapping服务不可用");
    }

    @Override
    public AnyTxnHttpResponse<MappingBusinessTokenDTO> updateBusinessTokenByReplace(String oldCardNumber, String newCardNumber) {
        return AnyTxnHttpResponse.fail(RespCodePrefix.PREFIX_CID_MAPPING_S, "mapping服务不可用");
    }

    @Override
    public AnyTxnHttpResponse<MappingBusinessTokenDTO> getBusinessTokenByCardToken(String cardToken) {
        return AnyTxnHttpResponse.fail(RespCodePrefix.PREFIX_CID_MAPPING_S, "mapping服务不可用");
    }

    @Override
    public AnyTxnHttpResponse<List<MappingBusinessTokenDTO>> getBusinessTokenByCustomerId(String customerId) {
        return AnyTxnHttpResponse.fail(RespCodePrefix.PREFIX_CID_MAPPING_S, "mapping服务不可用");
    }

    @Override
    public AnyTxnHttpResponse<Boolean> updateBusinessToken(@RequestBody List<MappingBusinessTokenDTO> mappingBusinessTokenDTOList) {
        return AnyTxnHttpResponse.fail(RespCodePrefix.PREFIX_CID_MAPPING_S, "mapping服务不可用");
    }
}
