package com.anytech.anytxn.parameter.common.service;

import com.alibaba.fastjson.JSON;
import com.anytech.anytxn.common.core.annotation.db.DeleteParameterAnnotation;
import com.anytech.anytxn.common.core.annotation.db.InsertParameterAnnotation;
import com.anytech.anytxn.common.core.annotation.db.UpdateParameterAnnotation;
import com.anytech.anytxn.business.base.audit.AbstractParameterService;
import com.anytech.anytxn.common.core.base.ParameterCompare;
import com.anytech.anytxn.business.base.audit.ParmModificationRecord;
import com.anytech.anytxn.common.core.base.PageResultDTO;
import com.anytech.anytxn.common.core.utils.BeanMapping;
import com.anytech.anytxn.common.sequence.utils.Number16IdGen;
import com.anytech.anytxn.common.core.utils.TenantUtils;
import com.anytech.anytxn.parameter.base.authorization.domain.dto.CountryMccCheckDTO;
import com.anytech.anytxn.parameter.base.authorization.service.ICountryMccCheckService;
import com.anytech.anytxn.parameter.base.common.domain.dto.ParmCountryCodeDTO;
import com.anytech.anytxn.parameter.base.common.service.ICountryCodeService;
import com.anytech.anytxn.parameter.base.common.enums.AnyTxnParameterRespCodeEnum;
import com.anytech.anytxn.parameter.base.common.exception.AnyTxnParameterException;
import com.anytech.anytxn.parameter.authorization.mapper.ParmCountryMccCheckSelfMapper;
import com.anytech.anytxn.parameter.common.mapper.broadcast.ParmCountryCodeMapper;
import com.anytech.anytxn.parameter.common.mapper.broadcast.ParmCountryCodeSelfMapper;
import com.anytech.anytxn.parameter.base.common.domain.model.unicast.ParmCountryCode;
import com.github.pagehelper.Page;
import com.github.pagehelper.PageHelper;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
//import org.apache.shardingsphere.transaction.annotation.ShardingSphereTransactionType;
//import org.apache.shardingsphere.transaction.core.TransactionType;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.cglib.beans.BeanCopier;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.ObjectUtils;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.*;

/**
 * 国家码操作
 * <AUTHOR> 2021、10、19
 */
@Service("parm_country_code_serviceImpl")
@Slf4j
public class CountryCodeServiceImpl extends AbstractParameterService implements ICountryCodeService {

    private Logger logger = LoggerFactory.getLogger(CountryCodeServiceImpl.class);

    @Autowired
    private ParmCountryCodeSelfMapper parmCountryCodeSelfMapper;

    @Autowired
    private ParmCountryCodeMapper parmCountryCodeMapper;

    @Autowired
    private ParmCountryMccCheckSelfMapper parmCountryMccCheckSelfMapper;

    @Autowired
    private ICountryMccCheckService countryMccCheckService;

    @Autowired
    private Number16IdGen numberIdGenerator;

    @Override
    public PageResultDTO<ParmCountryCodeDTO> selectCountryCodeBasic(Integer page, Integer rows) {
        log.debug("分页查询国家码信息, page={}, rows={}",page, rows);
        Page<ParmCountryCode> page1= PageHelper.startPage(page, rows);
        List<ParmCountryCode> countryCodes=parmCountryCodeSelfMapper.selectPageList();
        if(CollectionUtils.isEmpty(countryCodes)){
            log.error("未查询到信息");
            return null;
        }
        List<ParmCountryCodeDTO> result=BeanMapping.copyList(countryCodes, ParmCountryCodeDTO.class);
        return new PageResultDTO<>(page, rows, page1.getTotal(), page1.getPages(), result);
    }

    @Override
    @InsertParameterAnnotation(tableName = "parm_country_code", tableDesc = "Country Code", isJoinTable = false)
    public ParameterCompare add(ParmCountryCodeDTO parmCountryCodeDTO){
        ParmCountryCode parmCountryCode=parmCountryCodeSelfMapper.selectAlpha(parmCountryCodeDTO.getAlpha2CountryCode(),parmCountryCodeDTO.getNumericCountryCode());
        if(parmCountryCode != null){
            log.warn("该条信息已存在,alpha2CountryCode={} numericCountryCode={}",parmCountryCodeDTO.getAlpha2CountryCode(),parmCountryCodeDTO.getNumericCountryCode());
            throw new AnyTxnParameterException(AnyTxnParameterRespCodeEnum.D_DATA_ALREADY_EXISTS_FAULT);
        }
        parmCountryCodeDTO.setId(numberIdGenerator.generateId(TenantUtils.getTenantId())+"");
        parmCountryCodeDTO.setOrganizationNumber("0000");
        return ParameterCompare.getBuilder()
                .withAfter(parmCountryCodeDTO)
                .withMainParmId(parmCountryCodeDTO.getId()).build(ParmCountryCodeDTO.class);

    }

    @Override
    @DeleteParameterAnnotation(tableName = "parm_country_code",tableDesc = "Country Code",isJoinTable = false)
    public ParameterCompare delete(String id) {
        if (id == null) {
            throw new AnyTxnParameterException(AnyTxnParameterRespCodeEnum.P_PARAMETER_ID_EMPTY_FAULT);
        }
        ParmCountryCode countryCode=parmCountryCodeSelfMapper.selectByPrimaryKey(id);
        if(ObjectUtils.isEmpty(countryCode)){
            log.error("数据不存在");
            return null;
        }
        countryCode.setOrganizationNumber("0000");
        return ParameterCompare
                .getBuilder()
                .withBefore(countryCode)
                .build(ParmCountryCode.class);
    }

    @Override
    @UpdateParameterAnnotation(tableName = "parm_country_code", tableDesc = "Country Code",isJoinTable = false)
    public ParameterCompare update(ParmCountryCodeDTO countryCodeDTO) {
        if(ObjectUtils.isEmpty(countryCodeDTO)){
            log.warn("参数为空");
            throw new AnyTxnParameterException(AnyTxnParameterRespCodeEnum.P_PARAMETER_EMPTY_FAULT);
        }
        ParmCountryCode countryCode=parmCountryCodeSelfMapper.selectByPrimaryKey(countryCodeDTO.getId());
        if(Objects.isNull(countryCode)){
            log.error("根据主键id:{}查询，数据不存在",countryCodeDTO.getId());
            throw new AnyTxnParameterException(AnyTxnParameterRespCodeEnum.D_DATA_NOT_EXISTS_FAULT);
        }
        countryCode.setOrganizationNumber("0000");
        return ParameterCompare.getBuilder()
                .withAfter(countryCodeDTO)
                .withBefore(countryCode)
                .build(ParmCountryCode.class);
    }

    @Override
    public ParmCountryCodeDTO selectById(String id) {
        ParmCountryCode parmCountryCode=parmCountryCodeSelfMapper.selectByPrimaryKey(id);

        if (parmCountryCode == null){
            throw new AnyTxnParameterException(AnyTxnParameterRespCodeEnum.D_DATA_NOT_EXISTS_FAULT);
        }

        return BeanMapping.copy(parmCountryCode,ParmCountryCodeDTO.class);
    }

    @Override
    public List<ParmCountryCodeDTO> selectAllCountry() {
        return parmCountryCodeSelfMapper.selectAll();
    }

    @Override
    public List<Map<String, String>> getAllCountryAndName() {
        List<ParmCountryCode> parmCountryCodeList = parmCountryCodeSelfMapper.selectAllCountryCode();
        List<Map<String,String>> mapList = new ArrayList<>();
        parmCountryCodeList.forEach(parmCountryCode -> {
            Map<String,String> map = new HashMap<>(4);
            map.put("value",parmCountryCode.getNumericCountryCode().toString());
            map.put("label",parmCountryCode.getDescription());
            mapList.add(map);
        });
        return mapList;
    }

    @Override
    public ParmCountryCodeDTO findCountryCodeAndCountryMccChecks(String id) {

        logger.info("根据主键:{},获取国家码信息以及国家码MCC检查", id);
        ParmCountryCodeDTO parmCountryCodeDTO = null;
        try {
            ParmCountryCode parmCountryCode = parmCountryCodeMapper.selectByPrimaryKey(id);
            logger.info("查询国家码信息：{}", JSON.toJSON(parmCountryCode));
            if (parmCountryCode != null) {
                parmCountryCodeDTO = new ParmCountryCodeDTO();
                BeanCopier beanCopier = BeanCopier.create(ParmCountryCode.class, ParmCountryCodeDTO.class, false);
                beanCopier.copy(parmCountryCode, parmCountryCodeDTO, null);
                List<CountryMccCheckDTO> listByCountry = countryMccCheckService.getListByCountryCode(parmCountryCode.getNumericCountryCode().toString());
                //通过parmCountryCodeDTO查询到的CountryMccCheck
                parmCountryCodeDTO.setCountryMccCheckDTOS(listByCountry);
            }
        }   catch(Exception e){
            logger.error("根据主键:{},获取国家码信息以及国家码MCC检查失败,错误信息:{}",id,e.getMessage());
            /*throw new AnyTXNBusRuntimeException(TransactionEnum.DATABASE_ERROR.getCode(),
                    TransactionEnum.DATABASE_ERROR.getReason());*/
            throw new AnyTxnParameterException(AnyTxnParameterRespCodeEnum.PARAMETER_EXCEPTION);
        }
        if (parmCountryCodeDTO == null) {
            logger.error("根据主键:{},获取国家码信息以及国家码MCC检查失败", id);

            throw new AnyTxnParameterException(AnyTxnParameterRespCodeEnum.P_PARAMETER_DATA_NOT_EXIST);
        }
        return parmCountryCodeDTO;
    }

    @Override
    public Boolean modifyCountryMccCheckAndUtils(ParmCountryCodeDTO parmCountryCodeDTO) {

        logger.info("修改国家MCC信息,国家码:{}", parmCountryCodeDTO.getNumericCountryCode());
        try {
            //取出传入的
            String countryCode = parmCountryCodeDTO.getNumericCountryCode();
            String allMcc = parmCountryCodeDTO.getAllMcc();
            BigDecimal triggerAmount = parmCountryCodeDTO.getTriggerAmount();
            List<CountryMccCheckDTO> countryMccCheckDTOS = parmCountryCodeDTO.getCountryMccCheckDTOS();

            //放进查出的
            ParmCountryCode parmCountryCode = parmCountryCodeSelfMapper.selectAllCountryCodeByCountryCode(countryCode);
            if(parmCountryCode ==null){
                logger.info("输入的国家码错误");
                throw new AnyTxnParameterException(AnyTxnParameterRespCodeEnum.P_PARAMETER_DATA_NOT_EXIST);
            }
            parmCountryCode.setAllMcc(allMcc);
            parmCountryCode.setTriggerAmount(triggerAmount);
            parmCountryCode.setUpdateTime(LocalDateTime.now());

            //取出查出的
            String countryName = parmCountryCode.getDescription();
            String alpha2CountryCode = parmCountryCode.getAlpha2CountryCode();
            String alpha3CountryCode = parmCountryCode.getAlpha3CountryCode();
            String updateBy = parmCountryCode.getUpdateBy();
            Long versionNumber = parmCountryCode.getVersionNumber();

            //清空原来的检查表
            countryMccCheckService.deleteByCountryCode(countryCode.toString());

            //新增新的检查表
            if(countryMccCheckDTOS != null){
                for (CountryMccCheckDTO bean : countryMccCheckDTOS) {
                    bean.setNumericCountryCode(countryCode);
                    bean.setAllMcc(allMcc);
                    bean.setAlpha2CountryCode(alpha2CountryCode);
                    bean.setAlpha3CountryCode(alpha3CountryCode);
                    bean.setCountryDescription(countryName);
                    bean.setTriggerAmount(triggerAmount);
                    bean.setCreateTime(LocalDateTime.now());
                    bean.setUpdateTime(LocalDateTime.now());
                    bean.setUpdateBy(updateBy);
                    bean.setVersionNumber(versionNumber);
                    bean.setId(new BigDecimal(numberIdGenerator.generateId(TenantUtils.getTenantId())));
                    countryMccCheckService.addCountryMccCheck(bean);
                }
            }

            //更新国家码表
            return parmCountryCodeMapper.updateByPrimaryKeySelective(parmCountryCode) > 0;

        } catch (Exception e) {
            logger.error("调用[{}]更新国家MCC[{}]失败,错误信息[{}]",
                    "updateByPrimaryKeySelective", "parm_country_mcc_check", e);
            throw new AnyTxnParameterException(AnyTxnParameterRespCodeEnum.D_UPDATE_AUTH_RULE_FAULT);
        }

    }

    @Override
    public Boolean addCountryMccCheckAndDtails(ParmCountryCodeDTO parmCountryCodeDTO) {

        logger.info("新增国家MCC信息,国家码:{}", parmCountryCodeDTO.getNumericCountryCode());

        try {
            //取出传入的
            String countryCode = parmCountryCodeDTO.getNumericCountryCode();
            String allMcc = parmCountryCodeDTO.getAllMcc();
            BigDecimal triggerAmount = parmCountryCodeDTO.getTriggerAmount();
            List<CountryMccCheckDTO> countryMccCheckDTOS = parmCountryCodeDTO.getCountryMccCheckDTOS();

            //放进查出的
            ParmCountryCode parmCountryCode = parmCountryCodeSelfMapper.selectAllCountryCodeByCountryCode(countryCode);
            if (parmCountryCode == null) {
                logger.info("输入的国家码错误");
                throw new AnyTxnParameterException(AnyTxnParameterRespCodeEnum.PARAMETER_EXCEPTION);
            }
            parmCountryCode.setAllMcc(allMcc);
            parmCountryCode.setTriggerAmount(triggerAmount);
            parmCountryCode.setUpdateTime(LocalDateTime.now());

            //取出查出的
            String countryName = parmCountryCode.getDescription();
            String alpha2CountryCode = parmCountryCode.getAlpha2CountryCode();
            String alpha3CountryCode = parmCountryCode.getAlpha3CountryCode();
            String updateBy = parmCountryCode.getUpdateBy();
            Long versionNumber = parmCountryCode.getVersionNumber();

            //新增新的检查表
            int flag = 0;
            if (countryMccCheckDTOS != null) {
                for (CountryMccCheckDTO bean : countryMccCheckDTOS) {
                    int exists = parmCountryMccCheckSelfMapper.isExists(countryCode, bean.getMccCde());
                    if (exists > 0) {
                        continue;
                    }
                    bean.setNumericCountryCode(countryCode);
                    bean.setAllMcc(allMcc);
                    bean.setAlpha2CountryCode(alpha2CountryCode);
                    bean.setAlpha3CountryCode(alpha3CountryCode);
                    bean.setCountryDescription(countryName);
                    bean.setTriggerAmount(triggerAmount);
                    bean.setCreateTime(LocalDateTime.now());
                    bean.setUpdateTime(LocalDateTime.now());
                    bean.setUpdateBy(updateBy);
                    bean.setVersionNumber(versionNumber);
                    bean.setId(new BigDecimal(numberIdGenerator.generateId(TenantUtils.getTenantId())));
                    countryMccCheckService.addCountryMccCheck(bean);
                    flag++;
                }
            }
            //更新国家码表
            parmCountryCodeMapper.updateByPrimaryKeySelective(parmCountryCode);
            return flag > 0;

        } catch (Exception e) {
            logger.error("exception:{}", e);
            /*throw new AnyTXNRuntimeException(TransactionEnum.DATABASE_ERROR.getCode(),
                    TransactionEnum.DATABASE_ERROR.getReason());*/
            throw new AnyTxnParameterException(AnyTxnParameterRespCodeEnum.PARAMETER_EXCEPTION);
        }
    }


    @Override
    public Boolean removeCountryMccCheckAndDtails(Long id) {

        ParmCountryCode parmCountryCode = parmCountryCodeMapper.selectByPrimaryKey(id.toString());
        logger.info("查询国家MCC信息 id:{}", id);
        if (parmCountryCode == null) {
            logger.error("待删除国家MCC信息不存在。 id:{}", id);
            throw new AnyTxnParameterException(AnyTxnParameterRespCodeEnum.D_DATA_NOT_EXISTS_FAULT);
        }
        try {
            List<CountryMccCheckDTO> countryMccCheckDTOList = countryMccCheckService.getListByCountryCode(parmCountryCode.getNumericCountryCode().toString());
            for (CountryMccCheckDTO dto : countryMccCheckDTOList) {
                countryMccCheckService.removeCountryMccCheck(dto.getId().longValue());
            }
            parmCountryCode.setAllMcc("X");
            parmCountryCode.setTriggerAmount(BigDecimal.valueOf(999999.00));
            parmCountryCodeMapper.updateByPrimaryKeySelective(parmCountryCode);
            return true;
        } catch (Exception e) {
            throw new AnyTxnParameterException(AnyTxnParameterRespCodeEnum.D_DELETE_AUTH_CHECK_DEFINITION_FAULT);
        }

    }

    @Override
    public ParmCountryCodeDTO getCountryCodeByCde(String countryCode) {

        ParmCountryCode parmCountryCode = parmCountryCodeSelfMapper.selectAllCountryCodeByCountryCode(countryCode);
        if(parmCountryCode == null){
            logger.error("parmCountryCode is Null!");
            throw new AnyTxnParameterException(AnyTxnParameterRespCodeEnum.D_DATA_NOT_EXISTS_FAULT);
        }
        ParmCountryCodeDTO parmCountryCodeDTO = new ParmCountryCodeDTO();
        BeanCopier beanCopier = BeanCopier.create(ParmCountryCode.class, ParmCountryCodeDTO.class, false);
        beanCopier.copy(parmCountryCode, parmCountryCodeDTO, null);

        return parmCountryCodeDTO;


    }

    @Override
    public ParmCountryCodeDTO getCountryCodeByName(String countryName) {

        ParmCountryCode parmCountryCode = parmCountryCodeSelfMapper.selectByDescription(countryName);
        if(parmCountryCode == null){
            logger.error("parmCountryCode is Null!");
            throw new AnyTxnParameterException(AnyTxnParameterRespCodeEnum.D_DATA_NOT_EXISTS_FAULT);
        }
        ParmCountryCodeDTO parmCountryCodeDTO = new ParmCountryCodeDTO();
        BeanCopier beanCopier = BeanCopier.create(ParmCountryCode.class, ParmCountryCodeDTO.class, false);
        beanCopier.copy(parmCountryCode, parmCountryCodeDTO, null);

        return parmCountryCodeDTO;
    }

    @Override
    public PageResultDTO<ParmCountryCodeDTO> selectCountryCodeCheck(Integer page, Integer rows) {
        log.debug("分页查询国家码信息, page={}, rows={}",page, rows);
        Page<ParmCountryCode> page1= PageHelper.startPage(page, rows);
        List<ParmCountryCode> countryCodes=parmCountryCodeSelfMapper.selectPageListCountryMccCheck();
        if(CollectionUtils.isEmpty(countryCodes)){
            log.error("未查询到信息");
            return null;
        }
        List<ParmCountryCodeDTO> result=BeanMapping.copyList(countryCodes, ParmCountryCodeDTO.class);
        return new PageResultDTO<>(page, rows, page1.getTotal(), page1.getPages(), result);
    }


    @Override
    @Transactional(rollbackFor = Exception.class,propagation = Propagation.REQUIRED)
    //@ShardingSphereTransactionType(TransactionType.XA)
    protected boolean updateDb(ParmModificationRecord parmModificationRecord) throws Exception {
        ParmCountryCodeDTO parmCountryCodeDTO= JSON.parseObject(parmModificationRecord.getParmBody(), ParmCountryCodeDTO.class);
        ParmCountryCode countryCodeDTO1=new ParmCountryCode();
        countryCodeDTO1.setAlpha3CountryCode(parmCountryCodeDTO.getAlpha3CountryCode());
        countryCodeDTO1.setAlpha2CountryCode(parmCountryCodeDTO.getAlpha2CountryCode());
        countryCodeDTO1.setNumericCountryCode(parmCountryCodeDTO.getNumericCountryCode());
        countryCodeDTO1.setDescription(parmCountryCodeDTO.getDescription());
        countryCodeDTO1.setCountryRegionCode(parmCountryCodeDTO.getCountryRegionCode());
        countryCodeDTO1.setDisplayPriorityCode(parmCountryCodeDTO.getDisplayPriorityCode());
        countryCodeDTO1.setId(parmCountryCodeDTO.getId());
        countryCodeDTO1.initUpdateDateTime();
        int update = parmCountryCodeMapper.updateByPrimaryKey(countryCodeDTO1);
        return update>0;
    }

    @Override
    @Transactional(rollbackFor = Exception.class,propagation = Propagation.REQUIRED)
    //@ShardingSphereTransactionType(TransactionType.XA)
    protected boolean insertDb(ParmModificationRecord parmModificationRecord) throws Exception {
        ParmCountryCodeDTO parmCountryCodeDTO= JSON.parseObject(parmModificationRecord.getParmBody(), ParmCountryCodeDTO.class);
        ParmCountryCodeDTO countryCodeDTO1=new ParmCountryCodeDTO();
        countryCodeDTO1.setId(numberIdGenerator.generateId(TenantUtils.getTenantId())+"");
        countryCodeDTO1.setAlpha3CountryCode(parmCountryCodeDTO.getAlpha3CountryCode());
        countryCodeDTO1.setAlpha2CountryCode(parmCountryCodeDTO.getAlpha2CountryCode());
        countryCodeDTO1.setNumericCountryCode(parmCountryCodeDTO.getNumericCountryCode());
        countryCodeDTO1.setDescription(parmCountryCodeDTO.getDescription());
        countryCodeDTO1.setCountryRegionCode(parmCountryCodeDTO.getCountryRegionCode());
        countryCodeDTO1.setDisplayPriorityCode(parmCountryCodeDTO.getDisplayPriorityCode());
        countryCodeDTO1.initCreateDateTime();
        countryCodeDTO1.setVersionNumber(1L);
        countryCodeDTO1.initUpdateDateTime();
        countryCodeDTO1.setOrganizationNumber("0000");
        countryCodeDTO1.setUpdateBy(parmModificationRecord.getApplicationBy());
        log.info("参数：alpha3CountryCode{}",countryCodeDTO1.getAlpha3CountryCode());
        int insert = parmCountryCodeSelfMapper.insert(countryCodeDTO1);
        return insert>0;
    }

    @Override
    @Transactional(rollbackFor = Exception.class,propagation = Propagation.REQUIRED)
    //@ShardingSphereTransactionType(TransactionType.XA)
    protected boolean deleteDb(ParmModificationRecord parmModificationRecord) throws Exception {
        ParmCountryCodeDTO parmCountryCodeDTO= JSON.parseObject(parmModificationRecord.getParmBody(), ParmCountryCodeDTO.class);
        int delete = parmCountryCodeSelfMapper.deleteByPrimaryKey(parmCountryCodeDTO.getId());
        return delete>0;
    }
}
