package com.anytech.anytxn.parameter.authorization.controller;

import com.anytech.anytxn.parameter.authorization.service.AccountGroupAuthControlServiceImpl;
import io.swagger.v3.oas.annotations.tags.Tag;
import io.swagger.v3.oas.annotations.Operation;
import com.anytech.anytxn.common.core.base.PageResultDTO;
import com.anytech.anytxn.common.core.base.AnyTxnHttpResponse;
import com.anytech.anytxn.common.core.base.BizBaseController;
import com.anytech.anytxn.parameter.base.authorization.domain.dto.AccountGroupAuthControlDTO;
import com.anytech.anytxn.parameter.base.authorization.domain.dto.AccountGroupAuthDefineDTO;
import com.anytech.anytxn.parameter.base.authorization.domain.dto.AccountGroupRespDTO;
import com.anytech.anytxn.parameter.base.common.enums.AnyTxnParameterRespCodeEnum;
import com.anytech.anytxn.parameter.base.common.enums.ParameterRepDetailEnum;
import com.anytech.anytxn.parameter.base.common.exception.AnyTxnParameterException;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.support.DefaultMessageSourceResolvable;
import org.springframework.util.CollectionUtils;
import org.springframework.validation.BindingResult;
import org.springframework.validation.ObjectError;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2020/8/10 11:22
 */
@Tag(name = "账产品组控制信息")
@RestController
public class AccountGroupAuthController extends BizBaseController {

    @Autowired
    private AccountGroupAuthControlServiceImpl accountGroupAuthControlService;

    @Operation(summary = "分页查询账产品组控制表记录")
    @GetMapping(value = "param/accountGroupAuthDefine/{pageNum}/{pageSize}")
    public AnyTxnHttpResponse<PageResultDTO<AccountGroupAuthDefineDTO>> findByPage(
            @PathVariable(value = "pageNum") Integer pageNum,
            @PathVariable(value = "pageSize") Integer pageSize,
            @RequestParam(value = "acctProductGroup",required = false) String acctProductGroup,
            @RequestParam(value = "description",required = false) String description,
            @RequestParam(required = false) String organizationNumber){
        PageResultDTO<AccountGroupAuthDefineDTO> authDefineDtoPageResult = accountGroupAuthControlService.findPage(pageNum, pageSize, acctProductGroup, description, organizationNumber);
        return AnyTxnHttpResponse.success(authDefineDtoPageResult);
    }


    @Operation(summary = "获取账产品组控制记录详情")
    @GetMapping(value = "param/accountGroupAuthControl/{id}")
    public AnyTxnHttpResponse<AccountGroupRespDTO> getAccountGroupControlInfo(@PathVariable(value = "id") String id){
//        return
        AccountGroupRespDTO accountGroupControlInfoById = accountGroupAuthControlService.getAccountGroupControlInfoById(id);
        return  AnyTxnHttpResponse.success(accountGroupControlInfoById);
    }


    @Operation(summary = "修改账产品组定义信息")
    @PutMapping(value = "param/accountGroupAuthControl")
    public AnyTxnHttpResponse<Object> modifyAccountGroupAuthControlInfo(
            @Validated  @RequestBody AccountGroupRespDTO accountGroupRespDto, BindingResult bindingResult){
        checkBindingResult(bindingResult);

        return AnyTxnHttpResponse.success(accountGroupAuthControlService.modifyAccountGroupAuthControlInfo(accountGroupRespDto));
    }


    @Operation(summary = "新增账产品组定义信息")
    @PostMapping(value = "param/accountGroupAuthControl")
    public AnyTxnHttpResponse<Object> addAccountGroupAuthControlInfo(
            @Validated  @RequestBody AccountGroupRespDTO accountGroupRespDto, BindingResult bindingResult){

        checkBindingResult(bindingResult);
        String productGroup = accountGroupRespDto.getAccountGroupAuthDefine().getAcctProductGroup();
        if (accountGroupAuthControlService.exitAccountGroupAuthDefine(accountGroupRespDto.getAccountGroupAuthDefine().getOrganizationNumber(),productGroup)){
            throw new AnyTxnParameterException(AnyTxnParameterRespCodeEnum.P_PARAMETER_DATA_EXIST, ParameterRepDetailEnum.ACCOUNT_GROUP);
        }
        return AnyTxnHttpResponse.success(accountGroupAuthControlService.addAccountGroupAuthControlInfo(accountGroupRespDto));
    }



    @Operation(summary = "删除账产品组基本信息记录")
    @DeleteMapping(value = "param/accountGroupAuthControl")
    public AnyTxnHttpResponse<Boolean> deleteAccountGroupControlInfo(
            @RequestBody AccountGroupAuthControlDTO accountGroupAuthControlDTO){
        return accountGroupAuthControlService.deleteAccountGroupControlInfo(accountGroupAuthControlDTO);
    }



    @Operation(summary = "删除账账产品组授权控制信息")
    @DeleteMapping(value = "param/accountGroupAuthDefine/{acctProductGroup}")
    public AnyTxnHttpResponse<Object> deleteAccountGroupControlDefine(
            @PathVariable(value = "acctProductGroup") String acctProductGroup,
            @RequestParam String organizationNumber){
        return AnyTxnHttpResponse.success(accountGroupAuthControlService.deleteAccountGroupControlDefine(acctProductGroup, organizationNumber));
    }




    public void checkBindingResult(BindingResult bindingResult){
        if (bindingResult != null){
            List<ObjectError> allErrors = bindingResult.getAllErrors();
            if (!CollectionUtils.isEmpty(allErrors)){
                String errorInfo = allErrors.stream().map(DefaultMessageSourceResolvable::getDefaultMessage).collect(Collectors.joining(";"));
                // TODO 异常提示？
                throw new AnyTxnParameterException(AnyTxnParameterRespCodeEnum.P_PARAMETER_EMPTY_FAULT);
            }
        }
    }
}
