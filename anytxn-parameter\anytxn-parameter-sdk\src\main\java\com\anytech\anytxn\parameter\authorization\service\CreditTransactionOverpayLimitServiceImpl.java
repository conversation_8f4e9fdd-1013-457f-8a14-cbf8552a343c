package com.anytech.anytxn.parameter.authorization.service;

import com.alibaba.fastjson.JSON;
import com.github.pagehelper.Page;
import com.github.pagehelper.PageHelper;
import com.anytech.anytxn.common.core.annotation.db.DeleteParameterAnnotation;
import com.anytech.anytxn.common.core.annotation.db.InsertParameterAnnotation;
import com.anytech.anytxn.common.core.annotation.db.UpdateParameterAnnotation;
import com.anytech.anytxn.business.base.audit.AbstractParameterService;
import com.anytech.anytxn.common.core.base.ParameterCompare;
import com.anytech.anytxn.business.base.audit.ParmModificationRecord;
import com.anytech.anytxn.common.core.base.PageResultDTO;
import com.anytech.anytxn.common.core.utils.BeanMapping;
import com.anytech.anytxn.common.core.utils.OrgNumberUtils;
import com.anytech.anytxn.common.sequence.utils.Number16IdGen;
import com.anytech.anytxn.common.core.utils.TenantUtils;
import com.anytech.anytxn.parameter.base.authorization.domain.dto.CreditTransactionOverpayLimitDTO;
import com.anytech.anytxn.parameter.base.authorization.service.ICreditTransactionOverpayLimitService;
import com.anytech.anytxn.parameter.base.common.enums.AnyTxnParameterRespCodeEnum;
import com.anytech.anytxn.parameter.base.common.exception.AnyTxnParameterException;
import com.anytech.anytxn.parameter.authorization.mapper.CreditTransactionOverpayLimitMapper;
import com.anytech.anytxn.parameter.authorization.mapper.CreditTransactionOverpayLimitSelfMapper;
import com.anytech.anytxn.parameter.base.authorization.domain.model.CreditTransactionOverpayLimit;
import lombok.extern.slf4j.Slf4j;
//import org.apache.shardingsphere.transaction.annotation.ShardingSphereTransactionType;
//import org.apache.shardingsphere.transaction.core.TransactionType;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.ObjectUtils;
import org.springframework.util.StringUtils;

import jakarta.annotation.Resource;
import java.util.List;
import java.util.Objects;

/**
 * Description:贷方交易类型关联溢缴款额度类型参数
 * date: 2021/5/11 17:44
 *
 * <AUTHOR>
 */
@Service("parm_credit_transaction_overpay_limit_serviceImpl")
@Slf4j
public class CreditTransactionOverpayLimitServiceImpl extends AbstractParameterService implements ICreditTransactionOverpayLimitService {

    @Resource
    private CreditTransactionOverpayLimitMapper creditTransactionOverpayLimitMapper;
    @Resource
    private CreditTransactionOverpayLimitSelfMapper overpayLimitSelfMapper;
    @Resource
    private Number16IdGen numberIdGenerator;

    @Override
    @Transactional(rollbackFor = Exception.class, propagation = Propagation.REQUIRED)
    //@ShardingSphereTransactionType(TransactionType.XA)
    protected boolean updateDb(ParmModificationRecord parmModificationRecord) throws Exception {
        CreditTransactionOverpayLimit creditTransactionOverpayLimit = JSON.parseObject(parmModificationRecord.getParmBody(), CreditTransactionOverpayLimit.class);
        creditTransactionOverpayLimit.initUpdateDateTime();
        int res = creditTransactionOverpayLimitMapper.updateByPrimaryKeySelective(creditTransactionOverpayLimit);
        return res == 1;
    }

    @Override
    @Transactional(rollbackFor = Exception.class, propagation = Propagation.REQUIRED)
    //@ShardingSphereTransactionType(TransactionType.XA)
    protected boolean insertDb(ParmModificationRecord parmModificationRecord) throws Exception {
        CreditTransactionOverpayLimit creditTransactionOverpayLimit = JSON.parseObject(parmModificationRecord.getParmBody(), CreditTransactionOverpayLimit.class);
        creditTransactionOverpayLimit.initUpdateDateTime();
        creditTransactionOverpayLimit.initCreateDateTime();
        int res = creditTransactionOverpayLimitMapper.insertSelective(creditTransactionOverpayLimit);
        return res == 1;
    }

    @Override
    @Transactional(rollbackFor = Exception.class, propagation = Propagation.REQUIRED)
    //@ShardingSphereTransactionType(TransactionType.XA)
    protected boolean deleteDb(ParmModificationRecord parmModificationRecord) throws Exception {
        CreditTransactionOverpayLimit creditTransactionOverpayLimit = JSON.parseObject(parmModificationRecord.getParmBody(), CreditTransactionOverpayLimit.class);
        int res = creditTransactionOverpayLimitMapper.deleteByPrimaryKey(creditTransactionOverpayLimit.getId());
        return res == 1;
    }

    @Override
    @InsertParameterAnnotation(tableName = "parm_credit_transaction_overpay_limit", tableDesc = "Credit transaction type correlates excess contribution amount type parameter")
    public ParameterCompare add(CreditTransactionOverpayLimitDTO dto) {
        if (ObjectUtils.isEmpty(dto)) {
            throw new AnyTxnParameterException(AnyTxnParameterRespCodeEnum.P_PARAMETER_EMPTY_FAULT);
        }
        if (StringUtils.isEmpty(dto.getTableId())) {
            throw new AnyTxnParameterException(AnyTxnParameterRespCodeEnum.P_PARAMETER_TABLE_ID_EMPTY_FAULT);
        }
        if (StringUtils.isEmpty(dto.getCurrency())) {
            throw new AnyTxnParameterException(AnyTxnParameterRespCodeEnum.P_PARAMETER_EMPTY_FAULT);
        }
        if (StringUtils.isEmpty(dto.getCreditTransactionTypeCode())) {
            throw new AnyTxnParameterException(AnyTxnParameterRespCodeEnum.P_PARAMETER_EMPTY_FAULT);
        }
        String organizationNumber = OrgNumberUtils.getOrg(dto.getOrganizationNumber());
        CreditTransactionOverpayLimit creditTransactionOverpayLimit =
                creditTransactionOverpayLimitMapper.selectByTableIdAndOrg(dto.getTableId(),
                        organizationNumber);
        if (!ObjectUtils.isEmpty(creditTransactionOverpayLimit)) {
            throw new AnyTxnParameterException(AnyTxnParameterRespCodeEnum.D_EXIST_PARM_PAYMENT_ALLOCATED_BY_ID_FAULT);
        }
        CreditTransactionOverpayLimit overpayLimit = overpayLimitSelfMapper.
                selectByOrgNumAndTransTypeCodeAndCurrency(organizationNumber,
                dto.getCreditTransactionTypeCode(), dto.getCurrency());
        if (!ObjectUtils.isEmpty(overpayLimit)) {
            throw new AnyTxnParameterException(AnyTxnParameterRespCodeEnum.D_DATA_ALREADY_EXISTS_FAULT);
        }
        CreditTransactionOverpayLimit after = BeanMapping.copy(dto, CreditTransactionOverpayLimit.class);
        after.setId(String.valueOf(numberIdGenerator.generateId(TenantUtils.getTenantId())));
        after.setOrganizationNumber(organizationNumber);
        return ParameterCompare
                .getBuilder()
                .withAfter(after)
                .build(CreditTransactionOverpayLimit.class);
    }

    @Override
    @UpdateParameterAnnotation(tableName = "parm_credit_transaction_overpay_limit", tableDesc = "Credit transaction type correlates excess contribution amount type parameter")
    public ParameterCompare modify(CreditTransactionOverpayLimitDTO dto) {
        if (ObjectUtils.isEmpty(dto)) {
            throw new AnyTxnParameterException(AnyTxnParameterRespCodeEnum.P_PARAMETER_EMPTY_FAULT);
        }
        CreditTransactionOverpayLimit before = creditTransactionOverpayLimitMapper.selectByPrimaryKey(dto.getId());
        if (ObjectUtils.isEmpty(before)) {
            throw new AnyTxnParameterException(AnyTxnParameterRespCodeEnum.P_PARAMETER_DATA_NOT_EXIST);
        }
        CreditTransactionOverpayLimit after = BeanMapping.copy(dto, CreditTransactionOverpayLimit.class);
        return ParameterCompare
                .getBuilder()
                .withAfter(after)
                .withBefore(before)
                .build(CreditTransactionOverpayLimit.class);
    }

    @Override
    @DeleteParameterAnnotation(tableName = "parm_credit_transaction_overpay_limit", tableDesc = "Credit transaction type correlates excess contribution amount type parameter")
    public ParameterCompare remove(String id) {
        if (StringUtils.isEmpty(id)) {
            throw new AnyTxnParameterException(AnyTxnParameterRespCodeEnum.P_PARAMETER_EMPTY_FAULT);
        }
        CreditTransactionOverpayLimit before = creditTransactionOverpayLimitMapper.selectByPrimaryKey(id);
        if (ObjectUtils.isEmpty(before)) {
            throw new AnyTxnParameterException(AnyTxnParameterRespCodeEnum.P_PARAMETER_DATA_NOT_EXIST);
        }
        return ParameterCompare
                .getBuilder()
                .withBefore(before)
                .build(CreditTransactionOverpayLimit.class);
    }

    @Override
    public CreditTransactionOverpayLimitDTO findById(String id) {
        if (StringUtils.isEmpty(id)) {
            throw new AnyTxnParameterException(AnyTxnParameterRespCodeEnum.P_PARAMETER_EMPTY_FAULT);
        }
        CreditTransactionOverpayLimit creditTransactionOverpayLimit = creditTransactionOverpayLimitMapper.selectByPrimaryKey(id);
        return BeanMapping.copy(creditTransactionOverpayLimit, CreditTransactionOverpayLimitDTO.class);
    }

    @Override
    public PageResultDTO<CreditTransactionOverpayLimitDTO> findPage(Integer pageNum, Integer pageSize, String organizationNumber, String tableId, String description) {
        Page<CreditTransactionOverpayLimit> page = PageHelper.startPage(pageNum, pageSize);
        List<CreditTransactionOverpayLimit> creditTransactionOverpayLimits = creditTransactionOverpayLimitMapper.selectByConditionAndPage(tableId, description, OrgNumberUtils.getOrg(organizationNumber));
        List<CreditTransactionOverpayLimitDTO> res = BeanMapping.copyList(creditTransactionOverpayLimits, CreditTransactionOverpayLimitDTO.class);
        return new PageResultDTO<>(pageNum, pageSize, page.getTotal(), page.getPages(), res);
    }

    @Override
    public CreditTransactionOverpayLimitDTO findByOrgNumAndTransTypeCodeAndCurr(String orgNum,
                                                                                String transTypeCode,
                                                                                String currency) {
        if (StringUtils.isEmpty(orgNum)) {
            throw new AnyTxnParameterException(AnyTxnParameterRespCodeEnum.P_PARAMETER_EMPTY_FAULT);
        }
        if (StringUtils.isEmpty(transTypeCode)) {
            throw new AnyTxnParameterException(AnyTxnParameterRespCodeEnum.P_PARAMETER_EMPTY_FAULT);
        }
        if (StringUtils.isEmpty(currency)) {
            throw new AnyTxnParameterException(AnyTxnParameterRespCodeEnum.P_PARAMETER_EMPTY_FAULT);
        }
        CreditTransactionOverpayLimit creditTransactionOverpayLimit = overpayLimitSelfMapper.
                selectByOrgNumAndTransTypeCodeAndCurrency(orgNum, transTypeCode, currency);
        if (Objects.nonNull(creditTransactionOverpayLimit)) {
            return BeanMapping.copy(creditTransactionOverpayLimit, CreditTransactionOverpayLimitDTO.class);
        } else {
            log.error("根据机构号:{},交易类型:{},币种:{}查询，数据不存在", orgNum, transTypeCode, currency);
            throw new AnyTxnParameterException(AnyTxnParameterRespCodeEnum.P_PARAMETER_DATA_NOT_EXIST);
        }
    }
}
