package com.anytech.anytxn.parameter.base.common.utils;

import com.anytech.anytxn.common.core.utils.DateHelper;
import com.anytech.anytxn.parameter.base.common.enums.AnyTxnParameterRespCodeEnum;
import com.anytech.anytxn.parameter.base.common.exception.AnyTxnParameterException;
import lombok.extern.slf4j.Slf4j;
import org.springframework.util.CollectionUtils;

import java.lang.reflect.Field;
import java.util.List;

@Slf4j
public class FileFieldUtils {


    /**
     * 获取属性列值
     *
     * @param model     显示属性对应的对象
     * @param fieldList 显示的属性列
     * @return 属性列值数组
     */
    public static String[] showFieldValue(Object model, List<String> fieldList) {
        if (CollectionUtils.isEmpty(fieldList))
            return null;
        String[] arr = new String[fieldList.size()];
        try {
            Class<?> clazz = model.getClass();
            for (; clazz != Object.class; clazz = clazz.getSuperclass()) {
                for (Field field : clazz.getDeclaredFields()) {
                    field.setAccessible(true);
                    if (fieldList.contains(field.getName())) {
                        arr[fieldList.indexOf(field.getName())] = String.valueOf(field.get(model));
                    }
                }
            }
        } catch (Exception e) {
            log.error("数据属性操作异常", e);
            throw new AnyTxnParameterException(AnyTxnParameterRespCodeEnum.PARAMETER_EXCEPTION);
        }
        return arr;
    }

    /**
     * 设置对象属性值
     *
     * @param model      对象
     * @param fieldName  属性列名
     * @param fieldValue 属性列值
     */
    public static void configFieldValue(Object model, List<String> fieldName, List<String> fieldValue) {
        if (CollectionUtils.isEmpty(fieldName) || CollectionUtils.isEmpty(fieldValue))
            return;
        try {
            Class<?> clazz = model.getClass();
            for (; clazz != Object.class; clazz = clazz.getSuperclass()) {
                for (Field field : clazz.getDeclaredFields()) {
                    field.setAccessible(true);
                    if (fieldName.contains(field.getName())) {
                        String filedtype = field.getGenericType().toString();
                        String filedvalue = fieldValue.get(fieldName.indexOf(field.getName()));
                        if ("class java.lang.String".equals(filedtype)) {
                            field.set(model, filedvalue);
                            continue;
                        }
                        if ("class java.lang.Long".equals(filedtype)) {
                            field.set(model, Long.parseLong(filedvalue));
                            continue;
                        }
                        if ("class java.lang.Integer".equals(filedtype)) {
                            field.set(model, Integer.parseInt(filedvalue));
                            continue;
                        }
                        if ("class java.util.Date".equals(filedtype)) {
                            field.set(model, DateHelper.toLocalDate(filedvalue));
                            continue;
                        }
                    }
                }
            }
        } catch (Exception e) {
            log.error("数据属性操作异常", e);
            throw new AnyTxnParameterException(AnyTxnParameterRespCodeEnum.PARAMETER_EXCEPTION);
        }
    }
}
