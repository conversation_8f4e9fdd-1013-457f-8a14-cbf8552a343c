package com.anytech.anytxn.parameter.card.service;

import com.alibaba.fastjson.JSON;
import com.anytech.anytxn.business.base.audit.AbstractParameterService;
import com.anytech.anytxn.business.base.audit.ParmModificationRecord;
import com.anytech.anytxn.common.core.annotation.db.DeleteParameterAnnotation;
import com.anytech.anytxn.common.core.annotation.db.InsertParameterAnnotation;
import com.anytech.anytxn.common.core.annotation.db.UpdateParameterAnnotation;
import com.anytech.anytxn.common.core.base.ParameterCompare;
import com.anytech.anytxn.common.core.base.PageResultDTO;
import com.anytech.anytxn.common.core.utils.BeanMapping;
import com.anytech.anytxn.common.core.utils.OrgNumberUtils;
import com.anytech.anytxn.common.sequence.utils.Number16IdGen;
import com.anytech.anytxn.common.core.utils.TenantUtils;
import com.anytech.anytxn.parameter.base.card.domain.dto.MarkupFeeReqDTO;
import com.anytech.anytxn.parameter.base.card.domain.dto.MarkupFeeResDTO;
import com.anytech.anytxn.parameter.base.card.service.IMarkUpFeeService;
import com.anytech.anytxn.parameter.base.common.enums.AnyTxnParameterRespCodeEnum;
import com.anytech.anytxn.parameter.base.common.exception.AnyTxnParameterException;
import com.anytech.anytxn.parameter.base.card.domain.model.ParmMarkUpFee;
import com.anytech.anytxn.parameter.card.mapper.broadcast.ParmMarkupFeeMapper;
import com.anytech.anytxn.parameter.card.mapper.broadcast.ParmMarkupFeeSelfMapper;
import com.github.pagehelper.Page;
import com.github.pagehelper.PageHelper;
import lombok.extern.slf4j.Slf4j;
//import org.apache.shardingsphere.transaction.annotation.ShardingSphereTransactionType;
//import org.apache.shardingsphere.transaction.core.TransactionType;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.StringUtils;

import jakarta.annotation.Resource;
import java.util.List;

/**
 * <pre>
 * Description: 货币转换费参数Service处理
 * Company:	    江融信
 * Version:	    1.0
 * Create at:   2023/3/15
 * <p>
 * 修改历史:
 * 日期    作者    版本  修改描述
 * ------------------------------------------------------------------
 * <AUTHOR>
 * </pre>
 */
@Service(value = "parm_mark_up_fee_serviceImpl")
@Slf4j
public class MarkupFeeServiceImpl extends AbstractParameterService implements IMarkUpFeeService {

    @Resource
    private ParmMarkupFeeMapper markUpFeeMapper;
    @Resource
    private ParmMarkupFeeSelfMapper markUpFeeSelfMapper;
    @Resource
    private Number16IdGen numberIdGenerator;

    @Override
    @Transactional(rollbackFor = Exception.class, propagation = Propagation.REQUIRED)
    //@ShardingSphereTransactionType(TransactionType.XA)
    protected boolean updateDb(ParmModificationRecord parmModificationRecord) {
        ParmMarkUpFee markUpFee = JSON.parseObject(parmModificationRecord.getParmBody(),
                ParmMarkUpFee.class);
        markUpFee.setUpdateBy(parmModificationRecord.getApplicationBy());
        return markUpFeeMapper.updateByPrimaryKeySelective(markUpFee) > 0;
    }

    @Override
    @Transactional(rollbackFor = Exception.class, propagation = Propagation.REQUIRED)
    //@ShardingSphereTransactionType(TransactionType.XA)
    protected boolean insertDb(ParmModificationRecord parmModificationRecord) {
        ParmMarkUpFee markUpFee = JSON.parseObject(parmModificationRecord.getParmBody(),
                ParmMarkUpFee.class);
        markUpFee.initCreateDateTime();
        markUpFee.setVersionNumber(1L);
        markUpFee.setUpdateBy(parmModificationRecord.getApplicationBy());
        return markUpFeeMapper.insertSelective(markUpFee) > 0;
    }

    @Override
    @Transactional(rollbackFor = Exception.class, propagation = Propagation.REQUIRED)
    //@ShardingSphereTransactionType(TransactionType.XA)
    protected boolean deleteDb(ParmModificationRecord parmModificationRecord) {
        ParmMarkUpFee markUpFee = JSON.parseObject(parmModificationRecord.getParmBody(),
                ParmMarkUpFee.class);
        return markUpFeeMapper.deleteByPrimaryKey(markUpFee.getId()) > 0;
    }

    /**
     * 根据机构号/tableId查询货币转换费参数表
     *
     * @param organizationNumber String
     * @param tableId            String
     * @return MarkUpFeeResDTO
     */
    @Override
    public MarkupFeeResDTO findByOrgAndTableId(String organizationNumber, String tableId) {
        ParmMarkUpFee markUpFee = markUpFeeSelfMapper
                .selectByOrgAndTableId(organizationNumber, tableId);
        if (markUpFee == null) {
            log.error("获取货币转换费参数s, 通过organizationNumber={}, tableId={} 未查到数据",
                    organizationNumber, tableId);
            throw new AnyTxnParameterException(AnyTxnParameterRespCodeEnum.S_GET_PARM_MARK_UP_FEE_FAULT);
        }
        return BeanMapping.copy(markUpFee, MarkupFeeResDTO.class);
    }

    /**
     * 新增货币转换费参数
     *
     * @param markUpFeeReqDTO MarkUpFeeReqDTO
     * @return ParameterCompare
     */
    @Override
    @InsertParameterAnnotation(tableName = "parm_mark_up_fee", tableDesc = "Mark Up Fee Parameter")
    public ParameterCompare add(MarkupFeeReqDTO markUpFeeReqDTO) {
        if (markUpFeeReqDTO == null) {
            log.error("传入参数不能为空 货币转换费新增接口");
            throw new AnyTxnParameterException(AnyTxnParameterRespCodeEnum.P_PARAMETER_EMPTY_FAULT);
        }
        if (markUpFeeReqDTO.getOrganizationNumber() == null) {
            markUpFeeReqDTO.setOrganizationNumber(OrgNumberUtils.getOrg());
        }
        if (markUpFeeSelfMapper.isExists(markUpFeeReqDTO.getOrganizationNumber(),
                markUpFeeReqDTO.getTableId()) > 0) {
            log.error("货币转换费参数已存在,机构号：{}, 参数表id：{}", markUpFeeReqDTO.getOrganizationNumber(),
                    markUpFeeReqDTO.getTableId());
            throw new AnyTxnParameterException(AnyTxnParameterRespCodeEnum.S_EXIST_PARM_MARK_UP_FEE_FAULT);
        }
        ParmMarkUpFee markUpFee = BeanMapping.copy(markUpFeeReqDTO, ParmMarkUpFee.class);
        markUpFee.setId(String.valueOf(numberIdGenerator.generateId(TenantUtils.getTenantId())));
        return ParameterCompare.getBuilder().withAfter(markUpFee).build(ParmMarkUpFee.class);
    }

    /**
     * 根据ID删除货币转换费记录
     *
     * @param id String
     * @return ParameterCompare
     */
    @Override
    @DeleteParameterAnnotation(tableName = "parm_mark_up_fee", tableDesc = "Mark Up Fee Parameter")
    public ParameterCompare removeById(String id) {
        if (id == null) {
            log.error("传入参数不能为空 货币转换费参数删除接口");
            throw new AnyTxnParameterException(AnyTxnParameterRespCodeEnum.P_PARAMETER_EMPTY_FAULT);
        }
        ParmMarkUpFee markUpFee = markUpFeeMapper.selectByPrimaryKey(id);
        if (markUpFee == null) {
            log.error("待删除货币转换费参数不存在，id:{}", id);
            throw new AnyTxnParameterException(AnyTxnParameterRespCodeEnum.S_GET_PARM_MARK_UP_FEE_BY_ID_FAULT);
        }
        return ParameterCompare.getBuilder().withBefore(markUpFee).build(ParmMarkUpFee.class);
    }

    /**
     * 根据机构号/tableId删除货币转换费记录
     *
     * @param organizationNumber String
     * @param tableId            String
     * @return ParameterCompare
     */
    @Override
    @DeleteParameterAnnotation(tableName = "parm_mark_up_fee", tableDesc = "Mark Up Fee Parameter")
    public ParameterCompare removeByOrgAndTableId(String organizationNumber, String tableId) {
        if (StringUtils.isEmpty(organizationNumber) || StringUtils.isEmpty(tableId)) {
            log.error("传入参数不能为空，发卡参数删除接口");
            throw new AnyTxnParameterException(AnyTxnParameterRespCodeEnum.P_PARAMETER_EMPTY_FAULT);
        }
        ParmMarkUpFee markUpFee = markUpFeeSelfMapper.selectByOrgAndTableId(organizationNumber, tableId);
        if (markUpFee == null) {
            log.error("待删除货币转换费参数不存在，org:{},tableId:{}", organizationNumber, tableId);
            throw new AnyTxnParameterException(AnyTxnParameterRespCodeEnum.S_GET_PARM_MARK_UP_FEE_BY_ID_FAULT);
        }
        return ParameterCompare.getBuilder().withBefore(markUpFee).build(ParmMarkUpFee.class);
    }

    /**
     * 更新货币转换费参数表
     *
     * @param markUpFeeReqDTO MarkUpFeeReqDTO
     * @return ParameterCompare
     */
    @Override
    @UpdateParameterAnnotation(tableName = "parm_mark_up_fee", tableDesc = "Mark Up Fee Parameter")
    public ParameterCompare modify(MarkupFeeReqDTO markUpFeeReqDTO) {
        if (markUpFeeReqDTO == null) {
            log.error("传入参数不能为空，货币转换费参数修改接口");
            throw new AnyTxnParameterException(AnyTxnParameterRespCodeEnum.P_PARAMETER_EMPTY_FAULT);
        }
        ParmMarkUpFee oldMarkUpFee = markUpFeeMapper.selectByPrimaryKey(markUpFeeReqDTO.getId());
        if (!oldMarkUpFee.getOrganizationNumber().equals(markUpFeeReqDTO.getOrganizationNumber()) ||
                !oldMarkUpFee.getTableId().equals(markUpFeeReqDTO.getTableId())) {
            boolean ex = markUpFeeSelfMapper.isExists(markUpFeeReqDTO.getOrganizationNumber(),
                    markUpFeeReqDTO.getTableId()) > 0;
            if (ex) {
                log.error("货币转换费参数已存在,机构号:{}，参数表id:{}", markUpFeeReqDTO
                        .getOrganizationNumber(), markUpFeeReqDTO.getTableId());
                throw new AnyTxnParameterException(AnyTxnParameterRespCodeEnum.S_EXIST_PARM_MARK_UP_FEE_FAULT);
            }
        }
        ParmMarkUpFee markUpFee = BeanMapping.copy(markUpFeeReqDTO, ParmMarkUpFee.class);
        return ParameterCompare.getBuilder().withAfter(markUpFee)
                .withBefore(oldMarkUpFee).build(ParmMarkUpFee.class);
    }

    /**
     * 根据ID查询货币转换费参数表
     *
     * @param id String
     * @return MarkUpFeeResDTO
     */
    @Override
    public MarkupFeeResDTO findById(String id) {
        if (id == null) {
            log.error("传入参数不能为空，货币转换费参数根据id查询接口");
            throw new AnyTxnParameterException(AnyTxnParameterRespCodeEnum.P_PARAMETER_EMPTY_FAULT);
        }
        ParmMarkUpFee markUpFee = markUpFeeMapper.selectByPrimaryKey(id);
        if (markUpFee == null) {
            log.error("货币转换费参数不存在，id:{}", id);
            throw new AnyTxnParameterException(AnyTxnParameterRespCodeEnum.S_GET_PARM_MARK_UP_FEE_BY_ID_FAULT);
        }
        return BeanMapping.copy(markUpFee, MarkupFeeResDTO.class);
    }

    /**
     * 分页查询货币转换费参数
     *
     * @param pageNum            Integer
     * @param pageSize           Integer
     * @param tableId            String
     * @param description        String
     * @param organizationNumber String
     * @return PageResultDTO<MarkUpFeeResDTO>
     */
    @Override
    public PageResultDTO<MarkupFeeResDTO> findList(Integer pageNum, Integer pageSize,
                                                   String tableId, String description,
                                                   String organizationNumber) {
        Page<ParmMarkUpFee> page = PageHelper.startPage(pageNum, pageSize);
        organizationNumber = StringUtils.isEmpty(organizationNumber) ? OrgNumberUtils.getOrg()
                : organizationNumber;
        List<ParmMarkUpFee> markUpFeeList = markUpFeeSelfMapper
                .selectByCondition(tableId, description, organizationNumber);
        List<MarkupFeeResDTO> markupFeeResDTOS = BeanMapping.copyList(markUpFeeList,
                MarkupFeeResDTO.class);
        return new PageResultDTO<>(pageNum, pageSize, page.getTotal(),
                page.getPages(), markupFeeResDTOS);
    }
}
