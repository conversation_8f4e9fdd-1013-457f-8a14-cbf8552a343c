package com.anytech.anytxn.parameter.common.controller.system.components;

import com.anytech.anytxn.common.core.base.PageResultDTO;
import com.anytech.anytxn.common.core.utils.OrgNumberUtils;
import com.anytech.anytxn.common.core.base.AnyTxnHttpResponse;
import com.anytech.anytxn.common.core.base.BizBaseController;
import com.anytech.anytxn.parameter.base.common.enums.ParameterRepDetailEnum;
import com.anytech.anytxn.parameter.base.common.domain.dto.TransactionCtrlUnitDTO;
import com.anytech.anytxn.parameter.base.common.service.ITransactionCtrlUnitService;
import org.springframework.web.bind.annotation.*;

import jakarta.annotation.Resource;
import java.util.List;

/**
 * Description: 交易管控单元参数
 * date: 2021/5/10 16:35
 *
 * <AUTHOR>
 */
@RestController
public class ParmTransactionCtrlUnitController extends BizBaseController {
    @Resource
    private ITransactionCtrlUnitService transactionCtrlUnitService;

    /**
     * 添加
     * @param  transactionCtrlUnitDTO 交易管控单元参数
     * @return Object
     */
    @PostMapping(value = "/param/addTransactionCtrlUnit")
    public AnyTxnHttpResponse<Object> add(@RequestBody TransactionCtrlUnitDTO transactionCtrlUnitDTO){
        return AnyTxnHttpResponse.success(transactionCtrlUnitService.add(transactionCtrlUnitDTO), ParameterRepDetailEnum.CREATE.message());
    }

    /**
     * 修改
     * @param  transactionCtrlUnitDTO 交易管控单元参数
     * @return Object
     */
    @PutMapping(value = "/param/modifyTransactionCtrlUnit")
    public AnyTxnHttpResponse<Object> modify(@RequestBody TransactionCtrlUnitDTO transactionCtrlUnitDTO){
        return AnyTxnHttpResponse.success(transactionCtrlUnitService.modify(transactionCtrlUnitDTO), ParameterRepDetailEnum.UPDATE.message());
    }

    /**
     * 分页查询
     * @param  pageNum pageSize  organizationNumber
     * @return transactionCtrlUnitDTO
     */
    @GetMapping(value = "/param/transactionCtrlUnit/pageNum/{pageNum}/pageSize/{pageSize}")
    public AnyTxnHttpResponse<PageResultDTO<TransactionCtrlUnitDTO>> findByPage(@PathVariable(value = "pageNum") Integer pageNum,
                                                                      @PathVariable(value = "pageSize") Integer pageSize,
                                                                      @RequestParam(value = "unitCode",required = false) String unitCode,
                                                                      @RequestParam(value = "description",required = false) String description,
                                                                      @RequestParam(value = "organizationNumber",required = false) String organizationNumber){
        return AnyTxnHttpResponse.success(transactionCtrlUnitService.findPage(pageNum,pageSize,OrgNumberUtils.getOrg(organizationNumber),unitCode, description));
    }

    /**
     * 删除
     * @param  id 交易管控单元参数ID
     * @return Object
     */
    @DeleteMapping(value = "/param/removeTransactionCtrlUnit/id/{id}")
    public AnyTxnHttpResponse<Object> remove(@PathVariable String id){
        return AnyTxnHttpResponse.success(transactionCtrlUnitService.remove(id), ParameterRepDetailEnum.DEL.message());
    }

    /**
     * 通过ID查看
     * @param  id 交易管控单元参数ID
     * @return TransactionCtrlUnitDTO
     */
    @GetMapping(value = "/param/transactionCtrlUnit/id/{id}")
    public AnyTxnHttpResponse<TransactionCtrlUnitDTO> findById(@PathVariable String id){
        return AnyTxnHttpResponse.success(transactionCtrlUnitService.findById(id));
    }

    /**
     * 查询状态为1为所有交易管控单元参数
     * @param organizationNumber 机构号
     * @return TransactionCtrlUnitDTO
     */
    @GetMapping(value = "/param/transactionCtrlUnit/all/{organizationNumber}")
    public AnyTxnHttpResponse<List<TransactionCtrlUnitDTO>> findAllAndStatus(@PathVariable String organizationNumber){
        return AnyTxnHttpResponse.success(transactionCtrlUnitService.findAllAndStatus(OrgNumberUtils.getOrg(organizationNumber)));
    }

}
