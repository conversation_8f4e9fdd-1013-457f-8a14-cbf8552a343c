package com.anytech.anytxn.business.card.service;


import com.anytech.anytxn.business.base.card.domain.dto.CardManageFeeRecordDTO;
import com.anytech.anytxn.business.dao.card.mapper.CardManageFeeRecordSelfMapper;
import com.anytech.anytxn.business.dao.card.model.CardManageFeeRecord;
import com.anytech.anytxn.business.base.card.service.ICardManageFeeRecordService;
import com.anytech.anytxn.common.core.utils.BeanMapping;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.jdbc.core.namedparam.NamedParameterJdbcTemplate;
import org.springframework.jdbc.core.namedparam.SqlParameterSource;
import org.springframework.jdbc.core.namedparam.SqlParameterSourceUtils;
import org.springframework.stereotype.Service;

import java.util.List;


/**
 * <AUTHOR>
 * @description:
 * @date 2025/2/2515:55
 */
@Slf4j
@Service
public class CardManageFeeRecordServiceImpl implements ICardManageFeeRecordService {

    @Autowired
    private CardManageFeeRecordSelfMapper cardManageFeeRecordSelfMapper;
    @Autowired
    NamedParameterJdbcTemplate bizJdbcTemplate;

    @Override
    public void insertBatch(List<CardManageFeeRecordDTO> manageFeeRecordDTOS) {
        cardManageFeeRecordSelfMapper.insertBatch(BeanMapping.copyList(manageFeeRecordDTOS, CardManageFeeRecordDTO.class));
    }



    public int[] batchInset(List<CardManageFeeRecord> records) {
        StringBuilder sql = new StringBuilder();
        sql.append("INSERT INTO card_manage_fee_record (ID, GLOBAL_FLOW_NUMBER, CARD_TYPE, TXN_BILLING_AMOUNT,")
                .append("TXN_BILLING_CURRENCY, TXN_CARD_NUMBER, TXN_COUNTRY_CODE, ")
                .append("TXN_FORCE_POST_INDICATOR, TXN_POST_METHOD, TXN_TRANSACTION_AMOUNT, ")
                .append("TXN_TRANSACTION_CODE, TXN_TRANSACTION_CURRENCY, ")
                .append("TXN_TRANSACTION_DATE, TXN_ORGANIZATION_NUMBER, TXN_POST_DATE, ")
                .append("TXN_POST_STATUS,SERVICE_TYPE, CREATE_TIME, UPDATE_TIME, UPDATE_BY) ")
                .append(" VALUES (:id,:globalFlowNumber,:cardType,:txnBillingAmount,:txnBillingCurrency," +
                        ":txnCardNumber,:txnCountryCode,:txnForcePostIndicator,:txnPostMethod,:txnTransactionAmount," +
                        ":txnTransactionCode,:txnTransactionCurrency,:txnTransactionDate,:txnOrganizationNumber," +
                        ":txnPostDate,:txnPostStatus,:serviceType,:createTime,:updateTime,:updateBy)");
        return batchInsert(sql.toString(), records);
    }

    public int[] batchInsert(String sql, List<CardManageFeeRecord> records) {
        SqlParameterSource[] beanSources = SqlParameterSourceUtils.createBatch(records.toArray());
        return bizJdbcTemplate.batchUpdate(sql, beanSources);
    }

    public int[] batchUpdateByRecordId(List<CardManageFeeRecord> records) {
        StringBuilder sql = new StringBuilder();
        sql.append("UPDATE card_manage_fee_record ")
                .append(" set GLOBAL_FLOW_NUMBER = :globalFlowNumber ,")
                .append(" CARD_TYPE = :cardType  ,")
                .append(" TXN_BILLING_AMOUNT = :txnBillingAmount  , ")
                .append(" TXN_BILLING_CURRENCY = :txnBillingCurrency , ")
                .append(" TXN_CARD_NUMBER = :txnCardNumber  ,")
                .append(" TXN_COUNTRY_CODE = :txnCountryCode  , ")
                .append(" TXN_FORCE_POST_INDICATOR = :txnForcePostIndicator , ")
                .append(" TXN_POST_METHOD=:txnPostMethod,")
                .append(" TXN_TRANSACTION_AMOUNT=:txnTransactionAmount,")
                .append(" TXN_TRANSACTION_CODE=:txnTransactionCode,")
                .append(" TXN_TRANSACTION_CURRENCY=:txnTransactionCurrency, ")
                .append(" TXN_TRANSACTION_DATE=:txnTransactionDate,")
                .append(" TXN_ORGANIZATION_NUMBER=:txnOrganizationNumber,")
                .append(" TXN_POST_DATE=:txnPostDate,")
                .append(" TXN_POST_STATUS=:txnPostStatus,")
                .append(" CREATE_TIME=:createTime,")
                .append(" UPDATE_TIME=:updateTime,")
                .append(" UPDATE_BY=:updateBy,")
                .append(" SERVICE_TYPE=:serviceType ")
                .append(" WHERE id =:id");
        return batchUpdate(sql.toString(), records);
    }

    public int[] batchUpdate(String sql, List<CardManageFeeRecord> records) {
        SqlParameterSource[] beanSources = SqlParameterSourceUtils.createBatch(records.toArray());
        return bizJdbcTemplate.batchUpdate(sql, beanSources);
    }

}
