package com.anytech.anytxn.business.card.service;

import com.anytech.anytxn.common.core.enums.AnyTxnCommonRespCodeEnum;
import com.anytech.anytxn.business.base.card.enums.CardMcAbuStatusEnum;
import com.anytech.anytxn.business.dao.card.mapper.CardMcAbuLogMapper;
import com.anytech.anytxn.business.dao.card.model.CardMcAbuLog;
import com.anytech.anytxn.business.base.card.service.ICardMcAbuLogService;
import com.anytech.anytxn.common.core.exception.AnyTxnCommonException;
import com.anytech.anytxn.common.core.utils.LoginUserUtils;
import com.anytech.anytxn.common.core.utils.TenantUtils;
import com.anytech.anytxn.common.sequence.utils.SequenceIdGen;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.List;
import java.util.Objects;

/**
 * MC卡片ABU状态变更服务类
 *
 * <AUTHOR>
 * @time 2024/7/15
 */
@Service
@Slf4j
public class CardMcAbuLogServiceImpl implements ICardMcAbuLogService {

    @Autowired
    private CardMcAbuLogMapper cardMcAbuLogMapper;

    @Autowired
    private SequenceIdGen sequenceIdGen;

    @Override
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = Exception.class)
    public int insertLog(CardMcAbuStatusEnum abuStatus, LocalDate processDate, String cardNumber, String oldExpireDate, String newExpireDate, String currency) {
        CardMcAbuLog cardMcAbuLog = new CardMcAbuLog();
        cardMcAbuLog.setId(sequenceIdGen.generateId(TenantUtils.getTenantId()));
        cardMcAbuLog.setAbuStatus(abuStatus.getCode());
        cardMcAbuLog.setAbuStatusDate(processDate);
        cardMcAbuLog.setExtractInd(0);
        cardMcAbuLog.setCurrency(currency);
        cardMcAbuLog.setCreateTime(LocalDateTime.now());
        cardMcAbuLog.setUpdateTime(LocalDateTime.now());
        cardMcAbuLog.setUpdateBy(LoginUserUtils.getLoginUserName());
        cardMcAbuLog.setVersionNumber(1L);

        switch (abuStatus.getCode()) {
            case "N":
                cardMcAbuLog.setNewCardNumber(cardNumber);
                cardMcAbuLog.setNewExpireDate(newExpireDate);
                break;
            case "E":
                cardMcAbuLog.setOldCardNumber(cardNumber);
                cardMcAbuLog.setNewCardNumber(cardNumber);
                cardMcAbuLog.setOldExpireDate(oldExpireDate);
                cardMcAbuLog.setNewExpireDate(newExpireDate);
                break;
            case "C":
                cardMcAbuLog.setOldCardNumber(cardNumber);
                cardMcAbuLog.setOldExpireDate(oldExpireDate);
                //如果是C，需要查询下当天是否有同卡号N类型的记录；如果有，则需要修改状态
                List<CardMcAbuLog> cardCreationRecord = cardMcAbuLogMapper.selectByStatusDateAndNewCardNo(processDate, cardNumber);
                cardCreationRecord.stream().filter(c -> c.getExtractInd() == 0).forEach(c -> {
                    c.setExtractInd(1);
                    c.setUpdateBy("CLOSE on the same day");
                    c.setUpdateTime(LocalDateTime.now());
                    int i = cardMcAbuLogMapper.updateByPrimaryKeySelective(c);
                    if (i == 0) {
                        log.error("ABU关闭旧卡时查询更新当天的新卡数据，更新状态失败！cardNumber:{}", cardNumber);
                        throw new AnyTxnCommonException(AnyTxnCommonRespCodeEnum.D_ERR);
                    }
                    cardMcAbuLog.setExtractInd(1);
                });
                break;
            default:
        }
        return cardMcAbuLogMapper.insertSelective(cardMcAbuLog);
    }

    @Override
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = Exception.class)
    public void processForAccountClosure(String cardNumber, LocalDate processDate, String cardExpireDate) {
        //TODO PCI 插入时使用明文卡号
        //ABU更新，如果当天已经有过销户+销户撤销，则恢复原有的状态，否则直接插入一条N的记录
        List<CardMcAbuLog> cardMcAbuLogStatusCList = cardMcAbuLogMapper.selectByStatusDateAndOldCardNo(processDate, cardNumber);
        CardMcAbuLog cardMcAbuLogStatusC = cardMcAbuLogStatusCList.stream().filter(c -> c.getExtractInd() == 1).findFirst().orElse(null);
        if (Objects.nonNull(cardMcAbuLogStatusC)) {
            //先检查当天是否有N的数据，如果有，则置为非有效状态，C状态的不用更新（C和N相互抵消）
            List<CardMcAbuLog> cardMcAbuLogStatusNList = cardMcAbuLogMapper.selectByStatusDateAndNewCardNo(processDate, cardNumber);
            CardMcAbuLog cardMcAbuLogStatusN = cardMcAbuLogStatusNList.stream().filter(c -> c.getExtractInd() == 0).findFirst().orElse(null);
            if (Objects.nonNull(cardMcAbuLogStatusN)) {
                cardMcAbuLogStatusN.setExtractInd(1);
                cardMcAbuLogStatusN.setUpdateTime(LocalDateTime.now());
                cardMcAbuLogStatusN.setUpdateBy("Account closure");
                int j = cardMcAbuLogMapper.updateByPrimaryKeySelective(cardMcAbuLogStatusN);
                if (j == 0) {
                    log.error("销户更新当天状态N的ABU的状态，更新状态失败！cardNumber:{}", cardNumber);
                    throw new AnyTxnCommonException(AnyTxnCommonRespCodeEnum.D_ERR);
                }
            } else {
                //否则需要更新C的状态为有效
                cardMcAbuLogStatusC.setExtractInd(0);
                cardMcAbuLogStatusC.setUpdateTime(LocalDateTime.now());
                cardMcAbuLogStatusC.setUpdateBy("Account closure");
                int i = cardMcAbuLogMapper.updateByPrimaryKeySelective(cardMcAbuLogStatusC);
                if (i == 0) {
                    log.error("销户时更新当天状态C的ABU的状态，更新状态失败！cardNumber:{}", cardNumber);
                    throw new AnyTxnCommonException(AnyTxnCommonRespCodeEnum.D_ERR);
                }
            }
        } else {
            int i = insertLog(CardMcAbuStatusEnum.CLOSE_CARD, processDate, cardNumber, cardExpireDate, null, "");
            if (i == 0) {
                log.error("MC销卡插入旧卡ABU信息异常！cardNumber:{}, abuStatus:{}", cardNumber, CardMcAbuStatusEnum.CLOSE_CARD.getCode());
                throw new AnyTxnCommonException(AnyTxnCommonRespCodeEnum.D_ERR);
            }
        }
    }

    @Override
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = Exception.class)
    public void processForAccountClosureCancellation(String cardNumber, LocalDate processDate, String cardExpireDate) {
        //TODO PCI 插入时使用明文卡号
        //ABU恢复，如果销户和撤销在同一天，找到关户当天状态为C的数据，并修改状态
        List<CardMcAbuLog> cardMcAbuLogStatusCList = cardMcAbuLogMapper.selectByStatusDateAndOldCardNo(processDate, cardNumber);
        CardMcAbuLog cardMcAbuLogStatusC = cardMcAbuLogStatusCList.stream().filter(c -> c.getExtractInd() == 0).findFirst().orElse(null);
        if (Objects.nonNull(cardMcAbuLogStatusC)) {
            cardMcAbuLogStatusC.setExtractInd(1);
            cardMcAbuLogStatusC.setUpdateTime(LocalDateTime.now());
            cardMcAbuLogStatusC.setUpdateBy("Account closure cancellation");
            int i = cardMcAbuLogMapper.updateByPrimaryKeySelective(cardMcAbuLogStatusC);
            if (i == 0) {
                log.error("销户撤销更新当天状态C的ABU的状态，更新状态失败！cardNumber:{}", cardNumber);
                throw new AnyTxnCommonException(AnyTxnCommonRespCodeEnum.D_ERR);
            }
        }
        //再看当天是否有状态已经为失效的N记录，如果有，恢复状态；如果没有，则新建一条
        List<CardMcAbuLog> cardMcAbuLogStatusNList = cardMcAbuLogMapper.selectByStatusDateAndNewCardNo(processDate, cardNumber);
        //如果之前已经有撤销记录了，那当天必定无状态为0的N记录，仅可能有为1的
        CardMcAbuLog cardMcAbuLogStatusN = cardMcAbuLogStatusNList.stream().filter(c -> c.getExtractInd() == 1).findFirst().orElse(null);
        if (Objects.nonNull(cardMcAbuLogStatusN)) {
            cardMcAbuLogStatusN.setExtractInd(0);
            cardMcAbuLogStatusN.setUpdateTime(LocalDateTime.now());
            cardMcAbuLogStatusN.setUpdateBy("Account closure cancellation");
            int i = cardMcAbuLogMapper.updateByPrimaryKeySelective(cardMcAbuLogStatusN);
            if (i == 0) {
                log.error("销户撤销更新当天状态N的ABU的状态，更新状态失败！cardNumber:{}", cardNumber);
                throw new AnyTxnCommonException(AnyTxnCommonRespCodeEnum.D_ERR);
            }
        } else {
            CardMcAbuLog cardMcAbuLog = new CardMcAbuLog();
            cardMcAbuLog.setId(sequenceIdGen.generateId(TenantUtils.getTenantId()));
            cardMcAbuLog.setAbuStatus(CardMcAbuStatusEnum.NEW_CARD.getCode());
            cardMcAbuLog.setAbuStatusDate(processDate);
            cardMcAbuLog.setExtractInd(0);
            cardMcAbuLog.setCurrency("");
            cardMcAbuLog.setCreateTime(LocalDateTime.now());
            cardMcAbuLog.setUpdateTime(LocalDateTime.now());
            cardMcAbuLog.setUpdateBy(LoginUserUtils.getLoginUserName());
            cardMcAbuLog.setVersionNumber(1L);
            cardMcAbuLog.setNewCardNumber(cardNumber);
            cardMcAbuLog.setNewExpireDate(cardExpireDate);
            int i = cardMcAbuLogMapper.insertSelective(cardMcAbuLog);
            if (i == 0) {
                log.error("销户撤销插入当天状态N的ABU，插入失败！cardNumber:{}", cardNumber);
                throw new AnyTxnCommonException(AnyTxnCommonRespCodeEnum.D_ERR);
            }
        }
    }

}
