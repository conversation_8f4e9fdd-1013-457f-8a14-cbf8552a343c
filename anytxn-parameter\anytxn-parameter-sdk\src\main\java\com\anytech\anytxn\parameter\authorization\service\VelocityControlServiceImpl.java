package com.anytech.anytxn.parameter.authorization.service;

import com.alibaba.fastjson.JSON;
import com.github.pagehelper.Page;
import com.github.pagehelper.PageHelper;
import com.anytech.anytxn.common.core.annotation.db.DeleteParameterAnnotation;
import com.anytech.anytxn.common.core.annotation.db.InsertParameterAnnotation;
import com.anytech.anytxn.common.core.annotation.db.UpdateParameterAnnotation;
import com.anytech.anytxn.business.base.audit.AbstractParameterService;
import com.anytech.anytxn.common.core.base.ParameterCompare;
import com.anytech.anytxn.business.base.audit.ParmModificationRecord;
import com.anytech.anytxn.common.core.base.PageResultDTO;
import com.anytech.anytxn.common.core.utils.BeanMapping;
import com.anytech.anytxn.common.core.utils.OrgNumberUtils;
import com.anytech.anytxn.common.sequence.utils.Number16IdGen;
import com.anytech.anytxn.common.core.utils.TenantUtils;
import com.anytech.anytxn.parameter.base.authorization.domain.dto.VelocityControlDTO;
import com.anytech.anytxn.parameter.base.authorization.domain.dto.VelocityControlDetailDTO;
import com.anytech.anytxn.parameter.base.authorization.domain.dto.VelocityDefinitionDTO;
import com.anytech.anytxn.parameter.base.authorization.service.IVelocityControlService;
import com.anytech.anytxn.parameter.base.common.enums.AnyTxnParameterRespCodeEnum;
import com.anytech.anytxn.parameter.base.common.enums.ParameterRepDetailEnum;
import com.anytech.anytxn.parameter.base.common.exception.AnyTxnParameterException;
import com.anytech.anytxn.parameter.authorization.mapper.ParmVelocityControlMapper;
import com.anytech.anytxn.parameter.authorization.mapper.ParmVelocityControlSelfMapper;
import com.anytech.anytxn.parameter.authorization.mapper.ParmVelocityDefinitionSelfMapper;
import com.anytech.anytxn.parameter.authorization.mapper.ParmVelocitySetDetailSelfMapper;
import com.anytech.anytxn.parameter.base.authorization.domain.model.ParmVelocityControl;
import com.anytech.anytxn.parameter.base.authorization.domain.model.ParmVelocityDefinition;
import com.anytech.anytxn.parameter.base.authorization.domain.model.ParmVelocitySetDetail;
//import org.apache.shardingsphere.transaction.annotation.ShardingSphereTransactionType;
//import org.apache.shardingsphere.transaction.core.TransactionType;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;
import org.springframework.util.ObjectUtils;
import org.springframework.util.StringUtils;

import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2020-06-22
 */

@Service(value = "parm_velocity_control_serviceImpl")
public class VelocityControlServiceImpl extends AbstractParameterService implements IVelocityControlService {

    private static final Logger log = LoggerFactory.getLogger(VelocityControlServiceImpl.class);

    @Autowired
    private ParmVelocityControlMapper parmVelocityControlMapper;
    @Autowired
    private ParmVelocityControlSelfMapper parmVelocityControlSelfMapper;
    @Autowired
    private ParmVelocitySetDetailSelfMapper velocitySetDetailSelfMapper;
    @Autowired
    private ParmVelocityDefinitionSelfMapper velocityDefinitionSelfMapper;
    @Autowired
    private Number16IdGen numberIdGenerator;

    /**
     * 分页查询流量规则表
     * @param page 当前页数
     * @param rows 每页显示条数
     * @return PageResultDTO<VelocityControlDTO>
     */
    @Override
    public PageResultDTO<VelocityControlDTO> findByPage(Integer page, Integer rows, String cardProductCode, String organizationNumber) {
        log.debug("分页查询流量规则表，当前页：{}，每页展示条数：{}", page, rows);
        Page pageInfo =  PageHelper.startPage(page, rows);
        List<VelocityControlDTO> velocityControlDTOList = new ArrayList<>();
        try {
            organizationNumber = StringUtils.isEmpty(organizationNumber) ? OrgNumberUtils.getOrg() : organizationNumber;
            List<ParmVelocityControl> parmVelocityControls = parmVelocityControlSelfMapper.selectByCondition(cardProductCode, organizationNumber);
            if (!CollectionUtils.isEmpty(parmVelocityControls)) {
                for (ParmVelocityControl velocityControl : parmVelocityControls){
                    VelocityControlDTO velocityControlDTO = BeanMapping.copy(velocityControl, VelocityControlDTO.class);
                    velocityControlDTOList.add(velocityControlDTO);
                }
            }
            return new PageResultDTO<>(page, rows, pageInfo.getTotal(), pageInfo.getPages(),velocityControlDTOList);
        } catch (Exception e) {
            log.error("分页查询流量规则表信息失败，当前页：{}，每页展示条数：{},错误信息：{}", page, rows, e.getMessage());
            throw new AnyTxnParameterException(AnyTxnParameterRespCodeEnum.P_PARAMETER_DATA_NOT_EXIST);
        }
    }

//    private VelocityControlDTO dtoToDetailDto(ParmVelocityControl record){
//        VelocityControlDTO velocityControlDTO = new VelocityControlDTO();
//        if (ObjectUtils.isEmpty(record)){
//            return velocityControlDTO;
//        }
//        if (!ObjectUtils.isEmpty(record.getCardProductCode())){
//            velocityControlDTO.setCardProductCode(record.getCardProductCode());
//        }if (!ObjectUtils.isEmpty(record.getAuthTxnType())){
//            velocityControlDTO.setAuthTxnType(record.getAuthTxnType());
//        }if (!ObjectUtils.isEmpty(record.getAuthTxnCode())){
//            velocityControlDTO.setAuthTxnCode(record.getAuthTxnCode());
//        }if (!ObjectUtils.isEmpty(record.getCheckCurrency())){
//            velocityControlDTO.setCheckCurrency(record.getCheckCurrency());
//        }if (!ObjectUtils.isEmpty(record.getDescription())){
//            velocityControlDTO.setDescription(record.getDescription());
//        }if (!ObjectUtils.isEmpty(record.getVelocitySetCode())){
//            velocityControlDTO.setVelocitySetCode(record.getVelocitySetCode());
//            buildVelocityControlDTO(velocityControlDTO);
//        }
//        return velocityControlDTO;
//    }

    private void buildVelocityControlDTO(VelocityControlDetailDTO record){
        List<ParmVelocitySetDetail> velocitySetDetails = velocitySetDetailSelfMapper.selectByOrgNumAndVsCode(record.getOrganizationNumber(), record.getVelocitySetCode());
        List<VelocityDefinitionDTO> velocityDefinitionDtos = new ArrayList<>();
        if (!CollectionUtils.isEmpty(velocitySetDetails)){
            for (ParmVelocitySetDetail velocitySetDetail : velocitySetDetails){
                VelocityDefinitionDTO velocityDefinitionDTO = new VelocityDefinitionDTO();
                velocityDefinitionDTO.setVelocityCde(velocitySetDetail.getVelocityCde());
                velocityDefinitionDTO.setOrganizationNumber(velocitySetDetail.getOrganizationNumber());
                ParmVelocityDefinition velocityDefinition = velocityDefinitionSelfMapper.selectByVelocityCode(velocitySetDetail.getOrganizationNumber(), velocitySetDetail.getVelocityCde());
                if (!ObjectUtils.isEmpty(velocityDefinition)){
                    BeanMapping.copy(velocityDefinition, velocityDefinitionDTO);
                }
                velocityDefinitionDtos.add(velocityDefinitionDTO);
                record.setVelocityDefinitionDtoS(velocityDefinitionDtos);
            }
        }
    }

    /**
     * 根据卡片编号查询流量规则表
     * @param cardProductCode 卡片编号
     * @return VelocityControlDTO
     */
    @Override
    public VelocityControlDTO findByCardProNum(String cardProductCode, String organizationNumber) {
        log.debug("根据卡片编号查询流量规则表，cardProductCode：{}", cardProductCode);
        VelocityControlDTO velocityControlDTO = new VelocityControlDTO();
        List<VelocityControlDetailDTO> velocityControlDetailDtos = new ArrayList<>();
        try {
            List<ParmVelocityControl> velocityControls = parmVelocityControlSelfMapper.selectByCardProNum(cardProductCode, organizationNumber);
            for (ParmVelocityControl velocityControl : velocityControls){
                VelocityControlDetailDTO velocityControlDetailDTO = BeanMapping.copy(velocityControl, VelocityControlDetailDTO.class);
                if (!ObjectUtils.isEmpty(velocityControlDetailDTO)){
                    buildVelocityControlDTO(velocityControlDetailDTO);
                }
                velocityControlDetailDtos.add(velocityControlDetailDTO);
            }
            velocityControlDTO.setOrganizationNumber(organizationNumber);
            velocityControlDTO.setVelocityControlDetailDtos(velocityControlDetailDtos);
            velocityControlDTO.setCardProductCode(cardProductCode);
            return velocityControlDTO;
        } catch (Exception e) {
            log.error("根据卡片编号查询流量规则表，cardProductCode：{},错误信息：{}", cardProductCode, e.getMessage());
            throw new AnyTxnParameterException(AnyTxnParameterRespCodeEnum.P_PARAMETER_DATA_NOT_EXIST, ParameterRepDetailEnum.VELOCITY_CONTROL);
        }
    }

    /**
     * 新增流量规则表
     * @param record 流量规则参数
     */
    @Override
    @InsertParameterAnnotation(tableName = "parm_velocity_control", tableDesc = "Basic Velocity rule", isJoinTable = true)
    public ParameterCompare addVelocityControl(VelocityControlDTO record) {
        if (ObjectUtils.isEmpty(record)){
            log.error("传入参数不能为空");
            throw new AnyTxnParameterException(AnyTxnParameterRespCodeEnum.P_PARAMETER_EMPTY_FAULT);
        }

        List<VelocityControlDetailDTO> velocityControlDetailDtos = record.getVelocityControlDetailDtos();
        if (!CollectionUtils.isEmpty(velocityControlDetailDtos)){
            velocityControlDetailDtos.stream().forEach(velocityControlDetailDTO -> {
                ParmVelocityControl parmVelocityControl = parmVelocityControlSelfMapper.
                        selectVelocityControlByUniqueIdx(record.getOrganizationNumber(), record.getCardProductCode(),
                                velocityControlDetailDTO.getAuthTxnType(), velocityControlDetailDTO.getAuthTxnCode(), velocityControlDetailDTO.getCheckCurrency());
                if (!ObjectUtils.isEmpty(parmVelocityControl)){
                    log.error("根据索引查询流量规则表信息，数据已存在, orgNum:{}, productCode:{}, authTxnType:{}, authTxnCode:{}, currency:{}",
                            record.getOrganizationNumber(), velocityControlDetailDTO.getCardProductCode(), velocityControlDetailDTO.getAuthTxnType(), velocityControlDetailDTO.getAuthTxnCode(), velocityControlDetailDTO.getCheckCurrency());
                    throw new AnyTxnParameterException(AnyTxnParameterRespCodeEnum.D_DATA_ALREADY_EXISTS_FAULT, ParameterRepDetailEnum.VELOCITY_EXIST);
                }
                record.setId(numberIdGenerator.generateId(TenantUtils.getTenantId())+"");
            });

        }
        return ParameterCompare.getBuilder()
                .withMainParmId(record.getCardProductCode())
                .withAfter(record).build(VelocityControlDTO.class);
    }

    /**
     * 修改流量规则表
     * @param record 流量规则参数
     */
    @Override
    @UpdateParameterAnnotation(tableName = "parm_velocity_control", tableDesc = "Basic Velocity rule", isJoinTable = true)
    public ParameterCompare updateVelocityControl(VelocityControlDTO record) {
        if (ObjectUtils.isEmpty(record)){
            log.error("传入参数不能为空");
            throw new AnyTxnParameterException(AnyTxnParameterRespCodeEnum.P_PARAMETER_EMPTY_FAULT);
        }
        if (StringUtils.isEmpty(record.getCardProductCode())){
            log.error("卡片编号不能为空");
            throw new AnyTxnParameterException(AnyTxnParameterRespCodeEnum.P_PARAMETER_EMPTY_FAULT);
        }
        List<ParmVelocityControl> parmVelocityControls = parmVelocityControlSelfMapper.selectByCardProNum(record.getCardProductCode(), record.getOrganizationNumber());
        if (CollectionUtils.isEmpty(parmVelocityControls)) {
            log.error("根据卡产品编号和机构号查询流量规则 数据不存在");
            throw new AnyTxnParameterException(AnyTxnParameterRespCodeEnum.D_DATA_NOT_EXISTS_FAULT);
        }

        record.setId(String.valueOf(numberIdGenerator.generateId(TenantUtils.getTenantId())));

        return ParameterCompare.getBuilder()
                .withMainParmId(record.getCardProductCode())
                .withAfter(record)
                .withBefore(new VelocityControlDTO())
                .build(VelocityControlDTO.class);
    }

    /**
     * 根据cardProductCode删除流量规则参数
     * @param cardProductCode 卡片编号
     */
    @Override
    @DeleteParameterAnnotation(tableName = "parm_velocity_control", tableDesc = "Basic Velocity rule", isJoinTable = true)
    public ParameterCompare deleteVelocityControl(String cardProductCode, String organizationNumber) {
        if (ObjectUtils.isEmpty(cardProductCode)){
            log.error("cardProductCode不能为空");
            throw new AnyTxnParameterException(AnyTxnParameterRespCodeEnum.P_PARAMETER_EMPTY_FAULT);
        }
        List<ParmVelocityControl> parmVelocityControls = parmVelocityControlSelfMapper.selectByCardProNum(cardProductCode, organizationNumber);
        if (CollectionUtils.isEmpty(parmVelocityControls)){
            log.error("根据cardProductCode查询流量规则表信息，数据不存在, cardProductCode:{}", cardProductCode);
            throw new AnyTxnParameterException(AnyTxnParameterRespCodeEnum.D_DATA_NOT_EXISTS_FAULT);
        }

        return ParameterCompare.getBuilder()
                .withMainParmId(cardProductCode)
                .withBefore(parmVelocityControls.get(0)).build(ParmVelocityControl.class);
    }

    /**
     * 根据唯一索引（机构号+卡产品编号+授权交易大类+授权交易细类+交易币种）查询流量规则表
     * @param orgNum 机构号
     * @param productNum 卡产品编号
     * @param authTxnType 授权交易大类
     * @param authTxnCode 授权交易细类
     * @param transCurrency 交易币种
     * @return VelocityControlDTO
     */
    @Override
    public VelocityControlDTO findByUniqueIndex(String orgNum, String productNum, String authTxnType, String authTxnCode, String transCurrency) {
        ParmVelocityControl parmVelocityControl = parmVelocityControlSelfMapper.selectVelocityControlByUniqueIdx(orgNum, productNum, authTxnType, authTxnCode, transCurrency);
        if (ObjectUtils.isEmpty(parmVelocityControl)){
            log.error("根据索引查询流量规则表信息，数据不存在, orgNum:{}, productCode:{}, authTxnType:{}, authTxnCode:{}, currency:{}",
                    orgNum, productNum, authTxnType, authTxnCode, transCurrency);
            throw new AnyTxnParameterException(AnyTxnParameterRespCodeEnum.D_DATA_NOT_EXISTS_FAULT, ParameterRepDetailEnum.VELOCITY_NOT_EXIST);
        }
        return BeanMapping.copy(parmVelocityControl, VelocityControlDTO.class);
    }

    /**
     * 必输项检查
     * @param record 页面传入流量规则参数
     */
    private void checkRequired(VelocityControlDTO record){
        if (StringUtils.isEmpty(record.getAuthTxnType())){
            log.error("授权交易大类不能为空或空字符");
            throw new AnyTxnParameterException(AnyTxnParameterRespCodeEnum.P_PARAMETER_EMPTY_FAULT, ParameterRepDetailEnum.AUTH_TYPE);
        }
        if (StringUtils.isEmpty(record.getAuthTxnCode())){
            log.error("授权交易细类不能为空或空字符");
            throw new AnyTxnParameterException(AnyTxnParameterRespCodeEnum.P_PARAMETER_EMPTY_FAULT, ParameterRepDetailEnum.AUTH_CODE);
        }
        if (StringUtils.isEmpty(record.getCheckCurrency())){
            log.error("交易币种不能为空或空字符");
            throw new AnyTxnParameterException(AnyTxnParameterRespCodeEnum.P_PARAMETER_EMPTY_FAULT, ParameterRepDetailEnum.CURRENCY);
        }
        if (StringUtils.isEmpty(record.getDescription())){
            log.error("描述不能为空或空字符");
            throw new AnyTxnParameterException(AnyTxnParameterRespCodeEnum.P_PARAMETER_EMPTY_FAULT, ParameterRepDetailEnum.DES_NULL);
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class,propagation = Propagation.REQUIRED)
    //@ShardingSphereTransactionType(TransactionType.XA)
    protected boolean updateDb(ParmModificationRecord parmModificationRecord) {
        VelocityControlDTO velocityControlDTO = JSON.parseObject(parmModificationRecord.getParmBody(), VelocityControlDTO.class);
        parmVelocityControlSelfMapper.deleteByCardProductCode(velocityControlDTO.getCardProductCode(),velocityControlDTO.getOrganizationNumber());

        VelocityControlDTO safeLockDTO = JSON.parseObject(parmModificationRecord.getParmBody(), VelocityControlDTO.class);
        List<VelocityControlDetailDTO> velocityControlDetailDtos = safeLockDTO.getVelocityControlDetailDtos();
        for (VelocityControlDetailDTO velocityControlDetailDTO : velocityControlDetailDtos){
            ParmVelocityControl velocityControl = BeanMapping.copy(velocityControlDetailDTO, ParmVelocityControl.class);
            velocityControl.setCardProductCode(safeLockDTO.getCardProductCode());
            velocityControl.setId(String.valueOf(numberIdGenerator.generateId(TenantUtils.getTenantId())));
            velocityControl.initCreateDateTime();
            velocityControl.initUpdateDateTime();
            velocityControl.setUpdateBy(parmModificationRecord.getApplicationBy());
            velocityControl.setStatus("1");
            velocityControl.setOrganizationNumber(safeLockDTO.getOrganizationNumber());
            try {
                parmVelocityControlMapper.insertSelective(velocityControl);
            } catch (Exception e) {
                throw new AnyTxnParameterException(AnyTxnParameterRespCodeEnum.D_INSERT_AUTH_CHECK_DEFINITION_FAULT);
            }
        }
        return true;
    }

    @Override
    @Transactional(rollbackFor = Exception.class,propagation = Propagation.REQUIRED)
    //@ShardingSphereTransactionType(TransactionType.XA)
    protected boolean insertDb(ParmModificationRecord parmModificationRecord) {

        VelocityControlDTO safeLockDTO = JSON.parseObject(parmModificationRecord.getParmBody(), VelocityControlDTO.class);
        List<VelocityControlDetailDTO> velocityControlDetailDtos = safeLockDTO.getVelocityControlDetailDtos();

        int res = 0;
        for (VelocityControlDetailDTO velocityControlDetailDTO : velocityControlDetailDtos){
            ParmVelocityControl velocityControl = BeanMapping.copy(velocityControlDetailDTO, ParmVelocityControl.class);
            velocityControl.setCardProductCode(safeLockDTO.getCardProductCode());
            velocityControl.setId(String.valueOf(numberIdGenerator.generateId(TenantUtils.getTenantId())));
            velocityControl.initCreateDateTime();
            velocityControl.initUpdateDateTime();
            velocityControl.setVersionNumber(1L);
            velocityControl.setUpdateBy(parmModificationRecord.getApplicationBy());
            velocityControl.setStatus("1");
            velocityControl.setOrganizationNumber(safeLockDTO.getOrganizationNumber());
            res += parmVelocityControlMapper.insertSelective(velocityControl);
        }

        if (res != velocityControlDetailDtos.size()){
            throw new AnyTxnParameterException(AnyTxnParameterRespCodeEnum.D_DATABASE_UPDATE_ERROR);
        }

        return true;
    }

    @Override
    @Transactional(rollbackFor = Exception.class,propagation = Propagation.REQUIRED)
    //@ShardingSphereTransactionType(TransactionType.XA)
    protected boolean deleteDb(ParmModificationRecord parmModificationRecord) {
        ParmVelocityControl parmVelocityControl = JSON.parseObject(parmModificationRecord.getParmBody(), ParmVelocityControl.class);

        try {
            parmVelocityControlSelfMapper.deleteByCardProductCode(parmVelocityControl.getCardProductCode(), parmVelocityControl.getOrganizationNumber());
        } catch (Exception e) {
            log.error("根据cardProductCode删除流量规则表失败");
            throw new AnyTxnParameterException(AnyTxnParameterRespCodeEnum.D_DATABASE_UPDATE_ERROR, ParameterRepDetailEnum.CARD_PRODUCT_CODE_DEL);
        }
    return true;
    }
}
