package com.anytech.anytxn.parameter.common.service.system;

import com.alibaba.fastjson.JSON;
import com.github.pagehelper.Page;
import com.github.pagehelper.PageHelper;
import com.anytech.anytxn.common.core.annotation.db.DeleteParameterAnnotation;
import com.anytech.anytxn.common.core.annotation.db.InsertParameterAnnotation;
import com.anytech.anytxn.common.core.annotation.db.UpdateParameterAnnotation;
import com.anytech.anytxn.business.base.audit.AbstractParameterService;
import com.anytech.anytxn.common.core.base.ParameterCompare;
import com.anytech.anytxn.business.base.audit.ParmModificationRecord;
import com.anytech.anytxn.common.core.base.PageResultDTO;
import com.anytech.anytxn.common.core.utils.BeanMapping;
import com.anytech.anytxn.common.core.utils.OrgNumberUtils;
import com.anytech.anytxn.common.sequence.utils.Number16IdGen;
import com.anytech.anytxn.common.core.utils.TenantUtils;
import com.anytech.anytxn.parameter.base.common.enums.AnyTxnParameterRespCodeEnum;
import com.anytech.anytxn.parameter.base.common.exception.AnyTxnParameterException;
import com.anytech.anytxn.parameter.common.mapper.broadcast.system.ParmOrganizationCycleDaySelfMapper;
import com.anytech.anytxn.parameter.common.mapper.broadcast.system.ParmOrganizationInfoMapper;
import com.anytech.anytxn.parameter.common.mapper.broadcast.system.ParmOrganizationInfoSelfMapper;
import com.anytech.anytxn.parameter.base.common.domain.model.system.ParmOrganizationCycleDay;
import com.anytech.anytxn.parameter.base.common.domain.model.system.ParmOrganizationInfo;
import com.anytech.anytxn.parameter.base.common.domain.dto.system.OrganizationInfoReqDTO;
import com.anytech.anytxn.parameter.base.common.domain.dto.system.OrganizationInfoResDTO;
import com.anytech.anytxn.parameter.base.common.service.system.IOrganizationInfoService;
//import org.apache.shardingsphere.transaction.annotation.ShardingSphereTransactionType;
//import org.apache.shardingsphere.transaction.core.TransactionType;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDate;
import java.util.*;

/**
 * 机构参数表 业务实现
 *
 * <AUTHOR>
 * @date 2018-08-16 16:02
 **/
@Service("parm_organization_info_serviceImpl")
public class OrganizationInfoServiceImpl extends AbstractParameterService implements IOrganizationInfoService {

    private Logger logger = LoggerFactory.getLogger(OrganizationInfoServiceImpl.class);

    @Autowired
    private ParmOrganizationInfoMapper parmOrganizationInfoMapper;
    @Autowired
    private ParmOrganizationInfoSelfMapper parmOrganizationInfoSelfMapper;
    @Autowired
    private ParmOrganizationCycleDaySelfMapper cycleDaySelfMapper;
    @Autowired
    private Number16IdGen numberIdGenerator;

    /**
     * 新增记录
     *
     * @param organizationInfoReq 机构参数入参对象
     * @return 机构参数详情
     * @throws AnyTxnParameterException
     */
    @Override
    @InsertParameterAnnotation(tableName = "parm_organization_info",tableDesc = "Organization Parameters")
    public ParameterCompare add(OrganizationInfoReqDTO organizationInfoReq) {
        boolean isExists = parmOrganizationInfoSelfMapper.isExists(organizationInfoReq.getOrganizationNumber()) > 0;
        if (isExists) {
            logger.warn("机构参数已存在, Organization ={}", organizationInfoReq.getOrganizationNumber());
            throw new AnyTxnParameterException(AnyTxnParameterRespCodeEnum.D_EXIST_ORGANIZATION_INFO_FAULT);
        }
        ParmOrganizationInfo parmOrganizationInfo = BeanMapping.copy(organizationInfoReq, ParmOrganizationInfo.class);
        parmOrganizationInfo.setId(numberIdGenerator.generateId(TenantUtils.getTenantId())+"");
        return ParameterCompare
                .getBuilder()
                .withAfter(parmOrganizationInfo)
                .withMainParmId(parmOrganizationInfo.getOrganizationNumber())
                .build(ParmOrganizationInfo.class);
    }

    /**
     * 通过id删除条目
     *
     * @param id 技术主键
     * @return true:删除成功|false:删除失败
     * @throws AnyTxnParameterException
     */
    @Override
    @DeleteParameterAnnotation(tableName = "parm_organization_info",tableDesc = "Organization Parameters")
    public ParameterCompare remove(String id) {
        ParmOrganizationInfo parmOrganizationInfo = parmOrganizationInfoMapper.selectByPrimaryKey(id);
        if (parmOrganizationInfo == null) {
            logger.error("删除机构参数, 通过主键id({})未找到数据", id);
            throw new AnyTxnParameterException(AnyTxnParameterRespCodeEnum.D_QUERY_ORGANIZATION_INFO_FAULT);
        }
        logger.warn("通过id删除机构参数, {}", parmOrganizationInfo);
        return ParameterCompare
                .getBuilder()
                .withMainParmId(parmOrganizationInfo.getOrganizationNumber())
                .withAfter(parmOrganizationInfo)
                .build(ParmOrganizationInfo.class);
    }

    /**
     * 通过id获取详情
     *
     * @param id 主键id
     * @return 机构参数详情
     * @throws AnyTxnParameterException
     */
    @Override
    public OrganizationInfoResDTO find(String id) {
        if (id == null) {
            throw new AnyTxnParameterException(AnyTxnParameterRespCodeEnum.P_PARAMETER_EMPTY_FAULT);
        }

        ParmOrganizationInfo parmOrganizationInfo = parmOrganizationInfoMapper.selectByPrimaryKey(id);

        if (parmOrganizationInfo == null) {
            logger.error("查询机构参数, 通过主键id({})未找到数据", id);
            throw new AnyTxnParameterException(AnyTxnParameterRespCodeEnum.D_QUERY_ORGANIZATION_INFO_FAULT);
        }

        return BeanMapping.copy(parmOrganizationInfo, OrganizationInfoResDTO.class);
    }

    /**
     * 修改机构参数
     *
     * @param organizationInfoReq 机构参数入参对象
     * @return 机构参数详情
     * @throws AnyTxnParameterException
     */
    @Override
    @UpdateParameterAnnotation(tableName = "parm_organization_info",tableDesc = "Organization Parameters")
    public ParameterCompare modify(OrganizationInfoReqDTO organizationInfoReq) {
        if (organizationInfoReq.getId() == null) {
            throw new AnyTxnParameterException(AnyTxnParameterRespCodeEnum.P_PARAMETER_ID_EMPTY_FAULT);
        }
        ParmOrganizationInfo parmOrganizationInfo = parmOrganizationInfoMapper.selectByPrimaryKey(organizationInfoReq.getId());

        if (parmOrganizationInfo == null) {
            logger.error("修改机构参数, 通过主键id({})未找到数据", organizationInfoReq.getId());
            throw new AnyTxnParameterException(AnyTxnParameterRespCodeEnum.D_QUERY_ORGANIZATION_INFO_FAULT);
        }

        //验证修改后是否有冲突
        String oldOrgNum = parmOrganizationInfo.getOrganizationNumber();
        String newOrgNum = organizationInfoReq.getOrganizationNumber();
        if (!oldOrgNum.equals(newOrgNum)) {
            if (parmOrganizationInfoSelfMapper.isExists(organizationInfoReq.getOrganizationNumber()) > 0) {
                logger.warn("机构参数已存在, Organization ={}", organizationInfoReq.getOrganizationNumber());
                throw new AnyTxnParameterException(AnyTxnParameterRespCodeEnum.D_EXIST_ORGANIZATION_INFO_FAULT);
            }
        }

        // 拷贝修改的数据并更新
        ParmOrganizationInfo modify = BeanMapping.copy(organizationInfoReq, ParmOrganizationInfo.class);
        return ParameterCompare
                .getBuilder()
                .withAfter(modify)
                .withMainParmId(modify.getOrganizationNumber())
                .withBefore(parmOrganizationInfo)
                .build(ParmOrganizationInfo.class);
    }

    @Override
    public PageResultDTO<OrganizationInfoResDTO> findListByPage(Integer pageNum, Integer pageSize, String organizationNumber,String description) {
        Page<ParmOrganizationInfo> page = PageHelper.startPage(pageNum, pageSize);
        List<ParmOrganizationInfo> organizationInfoList = parmOrganizationInfoSelfMapper.selectByCondition(OrgNumberUtils.getOrg(organizationNumber),description);
        return new PageResultDTO<>(pageNum, pageSize, page.getTotal(), page.getPages(), BeanMapping.copyList(organizationInfoList, OrganizationInfoResDTO.class));
    }

    /**
     * 按当日处理标志获取信息
     *
     * @param pageNum          页号
     * @param pageSize         每页大小
     * @param processTodayFlag 当日处理标志
     * @return 机构参数分页
     * @throws AnyTxnParameterException
     */
    @Override
    public PageResultDTO<OrganizationInfoResDTO> findByProcessTodayFlagPage(Integer pageNum, Integer pageSize,
                                                                      String processTodayFlag) {

        logger.debug("分页查询机构参数, pageNum={}, pageSize={}", pageNum, pageSize);
        Page<ParmOrganizationInfo> page = PageHelper.startPage(pageNum, pageSize);
        List<ParmOrganizationInfo> oganizationInfoList =
                parmOrganizationInfoSelfMapper.selectByProcessTodayFlagPage(OrgNumberUtils.getOrg(), processTodayFlag, false);
        PageResultDTO<ParmOrganizationInfo> txnPage = new PageResultDTO<>(pageNum, pageSize,  page.getTotal(),page.getPages(), oganizationInfoList);
        List<ParmOrganizationInfo> parmOrganizationInfoList = txnPage.getData();
        if (parmOrganizationInfoList.isEmpty()) {
            logger.error("未查询到信息");
            return null;
        }
        List<OrganizationInfoResDTO> organizationInfoResList = BeanMapping.copyList(parmOrganizationInfoList,
                OrganizationInfoResDTO.class);

        return new PageResultDTO<>(pageNum, pageSize,  page.getTotal(),page.getPages(), organizationInfoResList);
    }

    /**
     * 获得机构参数表
     *
     * @param organizationNumber
     * @return OrganizationInfoRes
     */
    @Override
    public OrganizationInfoResDTO findOrganizationInfo(String organizationNumber) {
        ParmOrganizationInfo organizationInfo =
                parmOrganizationInfoSelfMapper.selectByOrganizationNumber(organizationNumber);
        if (organizationInfo == null) {
            logger.error("获得机构参数,通过orgNumber={} 未查到数据", organizationNumber);
            throw new AnyTxnParameterException(AnyTxnParameterRespCodeEnum.D_QUERY_ORGANIZATION_INFO_BY_ORG_FAULT);
        }
        return BeanMapping.copy(organizationInfo, OrganizationInfoResDTO.class);
    }

    /**
     * 机构日切
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void cutOver() {

        //TODO 多机构改造前日切逻辑，暂时备份。新代码迁移至OrganizationDataCutOverService
//
//        ParmSystemTable parmSystemTable = parmSystemTableSelfMapper.selectBySystemId("0000");
//        // 未指定机构获取当前用户机构
//        List<ParmOrganizationInfo> parmOrganizationInfos = parmOrganizationInfoSelfMapper.selectAll(OrgNumberUtils.getOrg(), true);
//        if (ProcessFlag.PROCESS_TODAY_FLAG_DO.getCode().equals(parmSystemTable.getProcessTodayFlag())) {
//            parmOrganizationInfos.forEach(org -> {
//                ParmOrganizationInfoBO parmOrganizationInfoBO = BeanMapping.copy(org, ParmOrganizationInfoBO.class);
//                parmOrganizationInfoBO.setParmSystemTableBO(BeanMapping.copy(parmSystemTable, ParmSystemTableBO.class));
//                cutOverForEach(parmOrganizationInfoBO);
//            });
//        } else {
//            parmOrganizationInfos.forEach(org -> {
//                org.setProcessTodayFlag(ProcessFlag.PROCESS_TODAY_FLAG_DO.getCode());
//                parmOrganizationInfoMapper.updateByPrimaryKeySelective(org);
//            });
//        }
    }

    @Override
    public List<OrganizationInfoResDTO> findOrganizationInfoAll(Boolean ableAll) {
        String orgNum = ableAll ? OrgNumberUtils.ROOT_ORG_NUM : OrgNumberUtils.getOrg();
        List<ParmOrganizationInfo> parmOrganizationInfos = parmOrganizationInfoSelfMapper.selectAll(orgNum, true);
        return BeanMapping.copyList(parmOrganizationInfos, OrganizationInfoResDTO.class);
    }

    /**
     * 账单日
     * @param orgNum 机构号
     * @return Integer
     */
    @Override
    public Integer getOrgAutoCycleDay(String orgNum) {
        ParmOrganizationInfo parmOrganizationInfo = parmOrganizationInfoSelfMapper.selectByOrganizationNumber(orgNum);
        LocalDate nextDay = parmOrganizationInfo.getNextProcessingDay();
        List<ParmOrganizationCycleDay> list = cycleDaySelfMapper.selectByOrgNumber(orgNum);
        if (list != null && !list.isEmpty()) {
            return list.get(0).getCycleDay();
        }
        return nextDay.getDayOfMonth();
    }

    @Override
    @Transactional(rollbackFor = Exception.class,propagation = Propagation.REQUIRED)
    //@ShardingSphereTransactionType(TransactionType.XA)
    protected boolean updateDb(ParmModificationRecord parmModificationRecord) {
        ParmOrganizationInfo parmOrganizationInfo = JSON.parseObject(parmModificationRecord.getParmBody(),ParmOrganizationInfo.class);
        parmOrganizationInfo.initUpdateDateTime();
        int res = parmOrganizationInfoMapper.updateByPrimaryKeySelective(parmOrganizationInfo);
        return res > 0;
    }

    @Override
    @Transactional(rollbackFor = Exception.class,propagation = Propagation.REQUIRED)
    //@ShardingSphereTransactionType(TransactionType.XA)
    protected boolean insertDb(ParmModificationRecord parmModificationRecord) {
        ParmOrganizationInfo parmOrganizationInfo = JSON.parseObject(parmModificationRecord.getParmBody(),ParmOrganizationInfo.class);
        parmOrganizationInfo.initCreateDateTime();
        parmOrganizationInfo.initUpdateDateTime();
        int res = parmOrganizationInfoMapper.insertSelective(parmOrganizationInfo);
        return res > 0;
    }

    @Override
    @Transactional(rollbackFor = Exception.class,propagation = Propagation.REQUIRED)
    //@ShardingSphereTransactionType(TransactionType.XA)
    protected boolean deleteDb(ParmModificationRecord parmModificationRecord) {
        ParmOrganizationInfo parmOrganizationInfo = JSON.parseObject(parmModificationRecord.getParmBody(),ParmOrganizationInfo.class);
        int res = parmOrganizationInfoMapper.deleteByPrimaryKey(parmOrganizationInfo.getId());
        return res > 0;
    }
}
