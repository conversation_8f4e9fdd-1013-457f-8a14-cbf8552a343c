package com.anytech.anytxn.parameter.accounting.controller;

import com.anytech.anytxn.parameter.base.common.enums.AnyTxnParameterRespCodeEnum;
import com.anytech.anytxn.parameter.base.common.exception.AnyTxnParameterException;
import com.anytech.anytxn.parameter.accounting.mapper.TPmsGlamsControlMapper;
import com.anytech.anytxn.parameter.accounting.mapper.TPmsGlamsControlSelfMapper;
import com.anytech.anytxn.parameter.accounting.mapper.TPmsGlamsDefinitionMapper;
import com.anytech.anytxn.parameter.base.accounting.domain.model.TPmsGlamsControl;
import com.anytech.anytxn.parameter.base.accounting.domain.model.TPmsGlamsDefinition;
import io.swagger.v3.oas.annotations.tags.Tag;
import io.swagger.v3.oas.annotations.Operation;
import com.anytech.anytxn.common.core.base.PageResultDTO;
import com.anytech.anytxn.common.core.base.AnyTxnHttpResponse;
import com.anytech.anytxn.common.core.base.BizBaseController;
import com.anytech.anytxn.parameter.base.accounting.domain.dto.TPmsGlamsControlDTO;
import com.anytech.anytxn.parameter.base.accounting.domain.dto.TPmsGlamsDefinitionDTO;
import com.anytech.anytxn.parameter.base.accounting.service.ITPmsGlamsControlService;
import com.anytech.anytxn.parameter.base.accounting.service.ITPmsGlamsDefinitionService;
import com.anytech.anytxn.parameter.base.common.enums.ParameterRepDetailEnum;
import com.anytech.anytxn.parameter.base.common.utils.AnyTxnHttpResponseHelper;
import org.apache.commons.lang3.ObjectUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.cglib.beans.BeanCopier;
import org.springframework.util.CollectionUtils;
import org.springframework.web.bind.annotation.*;

import jakarta.annotation.Resource;
import java.util.ArrayList;
import java.util.List;

@Tag(name = "会计事件定义表")
@RestController
public class TPmsGlamsDefinitionController extends BizBaseController {
    private static final Logger logger = LoggerFactory.getLogger(TPmsGlamsDefinitionController.class);
    @Autowired
    private ITPmsGlamsDefinitionService pmsGlamsDefinitionService;
    @Autowired
    private ITPmsGlamsControlService pmsGlamsControlService;

    @Resource
    private TPmsGlamsDefinitionMapper tPmsGlamsDefinitionMapper;

    @Resource
    private TPmsGlamsControlSelfMapper tPmsGlamsControlSelfMapper;

    @Autowired
    private TPmsGlamsControlMapper tPmsGlamsControlMapper;

    @Operation(summary = "分页查询会计事件定义表表")
    @GetMapping("/param/ams/{page}/{rows}")
    public AnyTxnHttpResponse<PageResultDTO<TPmsGlamsDefinitionDTO>> page(@PathVariable("page") int page,
                                                                          @PathVariable("rows") int rows,
                                                                          @RequestParam(value = "organizationNumber",required = false) String organizationNumber,
                                                                          @RequestParam(value = "tableId",required = false) String tableId,
                                                                          @RequestParam(value = "description",required = false) String description) {
        PageResultDTO<TPmsGlamsDefinitionDTO> result = pmsGlamsDefinitionService.page(page, rows,organizationNumber,tableId,description);
        return AnyTxnHttpResponse.success(result);
    }

    @Operation(summary = "查询会计核算事件定义表明细")
    @GetMapping("/param/ams/id/{id}")
    public AnyTxnHttpResponse<TPmsGlamsDefinitionDTO> detail(@PathVariable(value = "id") String id) {
        TPmsGlamsDefinitionDTO detail = pmsGlamsDefinitionService.detail(id);
        List<TPmsGlamsControlDTO> result = pmsGlamsControlService.findByDefine(detail.getOrganizationNumber(),
                detail.getBranchid(), detail.getTableId());
        detail.setTpmsGlamsControlList(result);
        return AnyTxnHttpResponse.success(detail);
    }

    @Operation(summary = "删除会计核算事件控制表")
    @DeleteMapping("/param/ams/id/{id}")
    public AnyTxnHttpResponse remove(@PathVariable(value = "id") String id) {
        TPmsGlamsDefinition tPmsGlamsDefinition = tPmsGlamsDefinitionMapper.selectByPrimaryKey(id);
        if(ObjectUtils.isEmpty(tPmsGlamsDefinition)){
            throw new AnyTxnParameterException(AnyTxnParameterRespCodeEnum.D_DATA_NOT_EXISTS_TEMPLATE_FAULT);
        }else {
            TPmsGlamsDefinitionDTO tPmsGlamsDefinitionDTO = new TPmsGlamsDefinitionDTO();
            BeanCopier beanCopier = BeanCopier.create(TPmsGlamsDefinition.class, TPmsGlamsDefinitionDTO.class, false);
            beanCopier.copy(tPmsGlamsDefinition, tPmsGlamsDefinitionDTO, null);
            List<TPmsGlamsControl> tPmsGlamsControls = tPmsGlamsControlSelfMapper.selectByDefine(tPmsGlamsDefinitionDTO.getOrganizationNumber(),tPmsGlamsDefinitionDTO.getBranchid(),tPmsGlamsDefinitionDTO.getTableId());
            if(!CollectionUtils.isEmpty(tPmsGlamsControls)){
                tPmsGlamsControls.forEach(x->tPmsGlamsControlMapper.deleteByPrimaryKey(x.getId()));
            }
        }
        pmsGlamsDefinitionService.remove(id);
        return AnyTxnHttpResponseHelper.succ(ParameterRepDetailEnum.DEL.message());
    }

    @Operation(summary = "新增会计核算事件控制表")
    @PostMapping("/param/ams/add")
    public AnyTxnHttpResponse<Object> add(@RequestBody TPmsGlamsDefinitionDTO data) {
        if (data.getTpmsGlamsControlList() == null) {
            data.setTpmsGlamsControlList(new ArrayList<>());
        }
        return AnyTxnHttpResponse.success(pmsGlamsDefinitionService.add(data),ParameterRepDetailEnum.CREATE.message());
    }


    @Operation(summary = "更新会计核算事件控制表")
    @PostMapping("/param/ams/update")
    public AnyTxnHttpResponse<Object> update(@RequestBody TPmsGlamsDefinitionDTO data) {
        if (data.getTpmsGlamsControlList() == null) {
            data.setTpmsGlamsControlList(new ArrayList<>());
        }
        return AnyTxnHttpResponse.success(pmsGlamsDefinitionService.update(data),ParameterRepDetailEnum.UPDATE.message());
    }
}
