package com.anytech.anytxn.parameter.installment.controller;

import io.swagger.v3.oas.annotations.tags.Tag;
import io.swagger.v3.oas.annotations.Operation;
import com.anytech.anytxn.common.core.base.PageResultDTO;
import com.anytech.anytxn.common.core.utils.OrgNumberUtils;
import com.anytech.anytxn.common.core.base.BizBaseController;
import com.anytech.anytxn.common.core.base.AnyTxnHttpResponse;
import com.anytech.anytxn.parameter.base.installment.domain.dto.InstallProductInfoReqDTO;
import com.anytech.anytxn.parameter.base.installment.domain.dto.InstallProductInfoResDTO;
import com.anytech.anytxn.parameter.base.installment.domain.dto.InstallProductInfoSearchDTO;
import com.anytech.anytxn.parameter.base.installment.service.IInstallProductInfoService;
import com.anytech.anytxn.parameter.base.common.enums.ParameterRepDetailEnum;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.util.StringUtils;
import org.springframework.web.bind.annotation.*;

import jakarta.validation.Valid;
import java.util.Arrays;
import java.util.List;
import java.util.Map;

/**
 *  分期产品参数表
 * <AUTHOR>
 * @date 2019-05-15 11:39
 **/
@Tag(name = "分期产品参数表")
@RestController
public class InstallProductInfoController extends BizBaseController {

    @Autowired
    private IInstallProductInfoService installProductInfoService;

    @Operation(summary = "根据id查询分期产品参数表",description = "根据id查询分期产品参数表")
    @GetMapping(value = "/param/istallproductinfo/id/{id}")
    public AnyTxnHttpResponse<InstallProductInfoResDTO> getById(@PathVariable(value = "id") String id) {
        InstallProductInfoResDTO productInfoResDTO = installProductInfoService.getById(id);
        return AnyTxnHttpResponse.success(productInfoResDTO);
    }
    @Operation(summary = "新增分期产品参数表",description = "新增分期产品参数表")
    @PostMapping(value = "/param/istallproductinfo")
    public AnyTxnHttpResponse<Object> create(@Valid @RequestBody InstallProductInfoReqDTO productInfoReqDTO) {
        return AnyTxnHttpResponse.success(installProductInfoService.add(productInfoReqDTO), ParameterRepDetailEnum.CREATE.message());
    }

    @Operation(summary = "删除分期产品参数信息",description = "删除分期产品参数信息")
    @DeleteMapping(value = "/param/istallproductinfo/id/{id}")
    public AnyTxnHttpResponse<Object> remove(@PathVariable String id) {
        return AnyTxnHttpResponse.success(installProductInfoService.remove(id),ParameterRepDetailEnum.DEL.message());
    }

    @Operation(summary = "修改分期产品参数信息",description = "修改分期产品参数信息")
    @PutMapping(value = "/param/istallproductinfo")
    public AnyTxnHttpResponse<Object> modify(@Valid @RequestBody InstallProductInfoReqDTO productInfoReqDTO) {
        return AnyTxnHttpResponse.success(installProductInfoService.modify(productInfoReqDTO),ParameterRepDetailEnum.UPDATE.message());

    }
    @Operation(summary = "分页查询分期产品参数信息",description = "分页分期产品参数信息")
    @GetMapping(value = "/param/istallproductinfo/pageNum/{pageNum}/pageSize/{pageSize}")
    public AnyTxnHttpResponse<PageResultDTO<InstallProductInfoResDTO>> getPage(@PathVariable(value = "pageNum")Integer pageNum,
                                                                            @PathVariable(value = "pageSize")Integer pageSize,
                                                                               InstallProductInfoSearchDTO installProductInfoSearchDTO) {
        PageResultDTO<InstallProductInfoResDTO> pageResultDto = installProductInfoService.findPage(pageNum, pageSize,installProductInfoSearchDTO);
        return AnyTxnHttpResponse.success(pageResultDto);
    }

    @Operation(summary = "分页查询分期产品参数信息",description = "分页分期产品参数信息")
    @GetMapping(value = "/param/istallproductinfo/organizationNumber/{organizationNumber}")
    public AnyTxnHttpResponse<List<InstallProductInfoResDTO>> getPage(
            @PathVariable(value = "organizationNumber") String organizationNumber,
            @RequestParam(value = "installmentType" , required = false) String installmentType) {
        return AnyTxnHttpResponse.success(installProductInfoService.findAll(organizationNumber, installmentType));
    }
    @Operation(summary = "根据分期产品码表查询",description = "根据分期产品码表查询")
    @GetMapping(value = "/param/istallproductinfo/productCode/{productCode}")
    public AnyTxnHttpResponse<Map> getInstallProInfoIsExistByCode(@PathVariable(value = "productCode") String productCode, @RequestParam String organizationNumber) {
        Map productInfoRes = installProductInfoService.getInstallProInfoIsExistByCode(productCode, organizationNumber);
        return AnyTxnHttpResponse.success(productInfoRes);
    }
    @Operation(summary = "根据机构号 分期产品代码查询",description = "根据机构号 分期产品代码查询")
    @GetMapping(value = "/param/istallproductinfo/organizationNumber/{organizationNumber}/productCode/{productCode}")
    public AnyTxnHttpResponse<InstallProductInfoResDTO> getByOrgNumAndType(@PathVariable(value = "organizationNumber") String organizationNumber,
                                                                        @PathVariable(value = "productCode") String productCode) {
        InstallProductInfoResDTO productInfoResDTO = installProductInfoService.findByIndex(organizationNumber,productCode);
        return AnyTxnHttpResponse.success(productInfoResDTO);
    }
    @Operation(summary = "根据机构号,分期类型,分期期数,还款方式,手续费收取方式查询")
    @GetMapping(value = "/param/istallproductinfo/organizationNumber/{organizationNumber}/prodType/{prodType}/term/{term}/paymentWay/{paymentWay}/feeReceiveFlag/{feeReceiveFlag}")
    public AnyTxnHttpResponse<InstallProductInfoResDTO> getByOrgNumTypeTermPayWayFee(@PathVariable(value = "organizationNumber") String organizationNumber,
                                                                                  @PathVariable(value = "prodType") String prodType,
                                                                                  @PathVariable(value = "term") Integer term,
                                                                                  @PathVariable(value = "paymentWay") String paymentWay,
                                                                                  @PathVariable(value = "feeReceiveFlag") String feeReceiveFlag) {
        InstallProductInfoResDTO productInfoResDTO = installProductInfoService.findByIndexOrgNumTypeTermPayWayFee(organizationNumber,
                prodType,term,paymentWay,feeReceiveFlag,"1");
        return AnyTxnHttpResponse.success(productInfoResDTO);
    }

    @Operation(summary = "根据机构号,分期类型,分期期数查询")
    @GetMapping(value = "/param/istallproductinfo/organizationNumber/{organizationNumber}/prodType/{prodType}/term/{term}")
    public AnyTxnHttpResponse<InstallProductInfoResDTO> getByOrgNumTypeTermPayWayFee(@PathVariable(value = "organizationNumber") String organizationNumber,
                                                                                  @PathVariable(value = "prodType") String prodType,
                                                                                  @PathVariable(value = "term") Integer term) {
        InstallProductInfoResDTO productInfoResList = installProductInfoService.findProInfoByTermAndType(organizationNumber, prodType,term);
        return AnyTxnHttpResponse.success(productInfoResList);
    }

    @Operation(summary = "根据机构号,分期类型,还款方式,手续费收取方式查询")
    @GetMapping(value = "/param/istallproductinfo/organizationNumber/{organizationNumber}/prodType/{prodType}/paymentWay/{paymentWay}/feeReceiveFlag/{feeReceiveFlag}")
    public AnyTxnHttpResponse<List<InstallProductInfoResDTO>> getByOrgNumTypePayWayFee(@PathVariable(value = "organizationNumber") String organizationNumber,
                                                                                    @PathVariable(value = "prodType") String prodType,
                                                                                    @PathVariable(value = "paymentWay") String paymentWay,
                                                                                    @PathVariable(value = "feeReceiveFlag") String feeReceiveFlag) {
        List<InstallProductInfoResDTO> installProductInfoResDTOList = installProductInfoService.findByIndexOrgNumTypePayWayFee(organizationNumber,
                prodType, paymentWay, feeReceiveFlag);
        return AnyTxnHttpResponse.success(installProductInfoResDTOList);
    }


    @Operation(summary = "根据机构号,分期类型查询分期产品")
    @GetMapping(value = "/param/istallproductinfo/organizationNumber/{organizationNumber}/prodType/{prodType}")
    public AnyTxnHttpResponse<List<InstallProductInfoResDTO>> findByOrgNumAndInstallType(@PathVariable(value = "organizationNumber") String organizationNumber,
                                                                                       @PathVariable(value = "prodType") String prodType) {
        List<InstallProductInfoResDTO> installProductInfoResDTOList = installProductInfoService.findByOrgNumType(organizationNumber,prodType);
        return AnyTxnHttpResponse.success(installProductInfoResDTOList);
    }

    @Operation(summary = "根据机构号查询自动分期类型")
    @GetMapping(value = "/param/istallproductinfo/auto/orgNum/{orgNum}")
    public AnyTxnHttpResponse<List<InstallProductInfoResDTO>> findAutoInstallProduct(@PathVariable(value = "orgNum") String orgNum) {
        List<String> prodTypes = Arrays.asList("RD", "LD");
        List<InstallProductInfoResDTO> installProductInfoList = installProductInfoService.findByOrgAndTypes(StringUtils.isEmpty(orgNum) ? OrgNumberUtils.getOrg() : orgNum, prodTypes);
        return AnyTxnHttpResponse.success(installProductInfoList);
    }

    @Operation(summary = "根据机构号,分期类型查询分期期数(信用卡运营用)")
    @GetMapping(value = "/param/istallproduct/organizationNumber/{organizationNumber}/type/{type}")
    public AnyTxnHttpResponse<List<Integer>> getTermsByOrgNumType(@PathVariable(value = "organizationNumber") String organizationNumber,
                                                                                     @PathVariable(value = "type") String type) {
        List<Integer> terms = installProductInfoService.getInstallTermByOrgAndType(organizationNumber, type);
        return AnyTxnHttpResponse.success(terms);
    }

}
