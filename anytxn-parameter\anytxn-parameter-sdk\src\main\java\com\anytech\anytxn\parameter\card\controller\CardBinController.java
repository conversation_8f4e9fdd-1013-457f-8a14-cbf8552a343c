package com.anytech.anytxn.parameter.card.controller;

import io.swagger.v3.oas.annotations.tags.Tag;
import io.swagger.v3.oas.annotations.Operation;
import com.anytech.anytxn.common.core.base.ParameterCompare;
import com.anytech.anytxn.common.core.base.PageResultDTO;
import com.anytech.anytxn.common.core.base.AnyTxnHttpResponse;
import com.anytech.anytxn.common.core.base.BizBaseController;
import com.anytech.anytxn.parameter.base.card.domain.dto.CardBinDefinitionReqDTO;
import com.anytech.anytxn.parameter.base.card.domain.dto.CardBinDefinitionResDTO;
import com.anytech.anytxn.parameter.base.card.service.ICardBinDefinitionService;
import com.anytech.anytxn.parameter.base.common.enums.ParameterRepDetailEnum;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import jakarta.validation.Valid;
import java.util.ArrayList;

/**
 * bin参数 接口
 *
 * <AUTHOR>
 * @date 2018-08-15 9:51
 **/
@RestController
@Tag(name = "bin参数接口")
public class CardBinController extends BizBaseController {

    @Autowired
    private ICardBinDefinitionService cardBinDefinitionService;


    /**
     * 创建卡bin
     * @param cardBinReq 卡bin请求数据
     * @return HttpApiResponse<CardBinDefinitionRes>
     */
    @PostMapping(value = "/param/cardBin")
    @Operation(summary = "新增bin参数")
    public AnyTxnHttpResponse<Object> create(@Valid @RequestBody CardBinDefinitionReqDTO cardBinReq)  {
        if (cardBinReq.getCardBinControlList()==null) {
            cardBinReq.setCardBinControlList(new ArrayList<>());
        }
        ParameterCompare res = cardBinDefinitionService.add(cardBinReq);
        return AnyTxnHttpResponse.success(res, ParameterRepDetailEnum.CREATE.message());

    }

    /**
     * 更新卡bin信息
     * @param cardBinReq 卡bin请求数据
     * @return HttpApiResponse<CardBinDefinitionRes>
     */
    @PutMapping(value = "/param/cardBin")
    @Operation(summary="根据id更新卡bin信息", description = "")
    public AnyTxnHttpResponse<Object> modify(@Valid @RequestBody CardBinDefinitionReqDTO cardBinReq)  {
        if (cardBinReq.getCardBinControlList()==null) {
            cardBinReq.setCardBinControlList(new ArrayList<>());
        }
        ParameterCompare cardBinRes = cardBinDefinitionService.modify(cardBinReq);
        return AnyTxnHttpResponse.success(cardBinRes,ParameterRepDetailEnum.UPDATE.message());

    }

    /**
     * 根据根据id获取详情，进行编辑
     * @param id 卡bin id
     * @return HttpApiResponse<CardBinDefinitionRes>
     */
    @Operation(summary="获取bin参数详情通过id", description = "")
    @GetMapping(value = "/param/cardBin/id/{id}")
    public AnyTxnHttpResponse<CardBinDefinitionResDTO> getByIndex(@PathVariable(value = "id") String id) {
        CardBinDefinitionResDTO cardBinRes = cardBinDefinitionService.find(id);
        return AnyTxnHttpResponse.success(cardBinRes);

    }

    @Operation(summary="根据机构号和参数表id获取卡BIN参数", description = "")
    @GetMapping(value = "/param/cardBin/organizationNumber/{organizationNumber}/tableId/{tableId}")
    public AnyTxnHttpResponse<CardBinDefinitionResDTO> getByIndex(@PathVariable(value = "organizationNumber") String organizationNumber,@PathVariable(value = "tableId") String tableId) {
        CardBinDefinitionResDTO cardBinRes = cardBinDefinitionService.findByOrgAndTableId(organizationNumber,tableId);
        return AnyTxnHttpResponse.success(cardBinRes);

    }

    /**
     * 分页查询，获取卡bin信息
     * @param pageNum 页码
     * @param pageSize 每页大小
     * @return HttpApiResponse<PageResultDTO<CardBinDefinitionRes>>
     */
    @GetMapping(value = "/param/cardBin/pageNum/{pageNum}/pageSize/{pageSize}")
    @Operation(summary="获取列表,分页", description = "需传入页码以及页面大小")
    public AnyTxnHttpResponse<PageResultDTO<CardBinDefinitionResDTO>> getPageByStatus (@PathVariable(value = "pageNum") Integer pageNum,
                                                                                       @PathVariable(value = "pageSize") Integer pageSize,
                                                                                       @RequestParam(value = "tableId",required = false) String tableId,
                                                                                       @RequestParam(value = "description",required = false) String description,
                                                                                       @RequestParam(required = false)String organizationNumber) {
        PageResultDTO<CardBinDefinitionResDTO> pageResultDto = cardBinDefinitionService.findPage(pageNum, pageSize, organizationNumber,tableId,description);
        return AnyTxnHttpResponse.success(pageResultDto);

    }

    @Operation(summary = "通过id删除Bin参数定义信息")
    @DeleteMapping(value = "/param/cardBin/id/{id}")
    public AnyTxnHttpResponse<Object> remove(@PathVariable(value = "id") String id) {
        ParameterCompare deleted = cardBinDefinitionService.remove(id);
        return AnyTxnHttpResponse.success(deleted,ParameterRepDetailEnum.DEL.message());

    }

    @Operation(summary = "通过机构号和参数表ID,删除Bin参数定义信息")
    @DeleteMapping(value = "/param/cardBin/organizationNumber/{organizationNumber}/tableId/{tableId}")
    public AnyTxnHttpResponse<Boolean> removeByOrgAndTableId(@PathVariable(value = "organizationNumber") String organizationNumber,@PathVariable(value = "tableId") String tableId) {
        Boolean deleted = cardBinDefinitionService.removeByOrgAndTableId(organizationNumber,tableId);
        return AnyTxnHttpResponse.success(deleted,ParameterRepDetailEnum.DEL.message());

    }
}
