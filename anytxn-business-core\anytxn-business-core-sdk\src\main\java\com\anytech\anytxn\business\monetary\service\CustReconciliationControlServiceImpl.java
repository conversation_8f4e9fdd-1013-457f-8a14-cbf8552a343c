package com.anytech.anytxn.business.monetary.service;

import java.time.LocalDate;
import java.time.LocalDateTime;

import com.anytech.anytxn.business.base.monetary.service.ICustReconciliationControlService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import com.anytech.anytxn.common.core.utils.BeanMapping;
import com.anytech.anytxn.business.base.monetary.domain.dto.CustReconciliationControlDTO;
import com.anytech.anytxn.business.base.monetary.enums.AnyTxnCustAccountLockRespCodeEnum;
import com.anytech.anytxn.business.base.monetary.enums.AnyTxnCustAccountRespCodeEnum;
import com.anytech.anytxn.business.base.monetary.exception.AnyTxnCustAccountException;
import com.anytech.anytxn.business.base.monetary.exception.AnyTxnCustAccountLockException;
import com.anytech.anytxn.business.dao.monetary.mapper.CustReconciliationControlSelfMapper;
import com.anytech.anytxn.business.dao.monetary.model.CustReconciliationControl;

/**
 * <AUTHOR>
 * @date 2020/5/18
 */
@Service
@Transactional(propagation = Propagation.REQUIRED, rollbackFor = Exception.class)
public class CustReconciliationControlServiceImpl implements ICustReconciliationControlService {

    Logger log = LoggerFactory.getLogger(CustReconciliationControlServiceImpl.class);

    @Autowired
    private CustReconciliationControlSelfMapper custReconciliationControlSelfMapper;

    @Override
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = Exception.class)
    public void commitLock(CustReconciliationControlDTO custReconciliationControlDTO) {
        if (custReconciliationControlDTO != null) {
            // 重置批中状态
            String id = custReconciliationControlDTO.getId();
            Long version = custReconciliationControlDTO.getVersionNumber();
            Long count = custReconciliationControlDTO.getOptmisticLockCount();
            log.info("客户对账信息:{}",custReconciliationControlDTO.toString());
            int i = custReconciliationControlSelfMapper.updateLock(id,
                    version,
                    custReconciliationControlDTO.getReconciliationDate(),
                    count,
                    count + 1,
                    LocalDateTime.now());
            // 如果没有更新成功，抛出异常出发事物回滚
            if (i <= 0) {
                throw new AnyTxnCustAccountLockException(AnyTxnCustAccountLockRespCodeEnum.D_LOCK_ERROR, custReconciliationControlDTO.getCustomerId());
            } else {
                log.info("更新客户【{}】对账表信息，业务处理日：{}， 更新后版本号： {}", custReconciliationControlDTO.getCustomerId(), custReconciliationControlDTO.getReconciliationDate(), version + 1);
            }

            // 刷新乐观锁
            custReconciliationControlDTO.setOptmisticLockCount(custReconciliationControlDTO.getOptmisticLockCount() + 1);
            custReconciliationControlDTO.setVersionNumber(custReconciliationControlDTO.getVersionNumber() + 1);
        } else {
//            throw new AnyTxnCustAccountLockException(AnyTxnCustAccountLockRespCode.D_LOCK_ERROR, "对账表信息为空，无法更新！");
            log.info("未找到对账表信息，这也许是一个错误");
        }
    }

    @Override
    public void commitLockAndReStatus(CustReconciliationControlDTO custReconciliationControlDTO) {
        if (custReconciliationControlDTO != null) {
            Long oldStatus = custReconciliationControlDTO.getBatchStatus();
            custReconciliationControlDTO.setBatchStatus(0L);
            try {
                commitLock(custReconciliationControlDTO);
            }catch (AnyTxnCustAccountLockException e){
                custReconciliationControlDTO.setBatchStatus(oldStatus);
                throw e;
            }
        }
    }

    /**
     * 更新状态， 不需要事务处理（隔离外部事务）
     * @param id
     * @param oldStatus
     */
    @Override
    @Transactional(propagation = Propagation.REQUIRES_NEW, rollbackFor = Exception.class)
    public void updateStatus(String id, Long oldStatus){
        try {
            log.warn("更新提交乐观锁异常，修改批次状态，status: {}", oldStatus);
            custReconciliationControlSelfMapper.updateByStatus(id, oldStatus);
        }catch (Exception e2){
            throw e2;
        }
    }

    @Override
    public LocalDate getBillingDate(CustReconciliationControlDTO controlDTO, LocalDate accruedThruDay, LocalDate today, LocalDate nextProcessingDay) {
       if(controlDTO==null){
           return nextProcessingDay;
       }
        // 客户业务日期
        LocalDate reconciliationDate = controlDTO.getReconciliationDate();
        // 1. 业务日期 = 当前累计日期
        if (reconciliationDate.isEqual(accruedThruDay)) {
            return nextProcessingDay;
        // 2. 业务日期 < 当前累计日期
        } else if (reconciliationDate.isBefore(accruedThruDay)) {
            return today;
            // 3. 业务日期 > 当前累计日期
        } else {
            log.warn("客户业务日期大于当前累计日期，customerId: {}, org: {},accruedThruDay:{}, reconciliationDate:{}",
                    controlDTO.getCustomerId(), controlDTO.getOrganizationNumber(),accruedThruDay,reconciliationDate);
            throw new AnyTxnCustAccountException(AnyTxnCustAccountRespCodeEnum.P_DATE_ERROR, "客户业务日期大于当前累计日期!");
        }
    }

    @Override
    public CustReconciliationControlDTO getControl(String customerId, String orgNum) {
        CustReconciliationControl control = custReconciliationControlSelfMapper.selectByCustAndOrg(customerId, orgNum);

        // TODO 是否需要抛出异常
        if (control == null) {
            log.warn("客户对账控制表无该客户数据，customerId: {}, org: {}", customerId, orgNum);
        } else {
            return BeanMapping.copy(control, CustReconciliationControlDTO.class);
        }

        return null;
    }
}
