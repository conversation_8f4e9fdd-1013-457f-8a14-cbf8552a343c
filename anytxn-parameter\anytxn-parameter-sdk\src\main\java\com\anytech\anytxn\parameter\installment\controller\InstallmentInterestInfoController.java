package com.anytech.anytxn.parameter.installment.controller;

import com.anytech.anytxn.common.core.base.ParameterCompare;
import com.anytech.anytxn.common.core.base.PageResultDTO;
import com.anytech.anytxn.common.core.base.AnyTxnHttpResponse;
import com.anytech.anytxn.common.core.base.BizBaseController;
import com.anytech.anytxn.parameter.base.installment.domain.dto.InstallmentInterestInfoDTO;
import com.anytech.anytxn.parameter.base.installment.service.IInstallmentInterestInfoService;
import io.swagger.v3.oas.annotations.Operation;
import org.springframework.web.bind.annotation.*;

import jakarta.annotation.Resource;

/**
 * @Author: sukang
 * @Date: 2022/11/9 14:44
 */
@RestController
public class InstallmentInterestInfoController extends BizBaseController {


    @Resource
    private IInstallmentInterestInfoService installmentInterestInfoService;


    @Operation(summary = "分页查询分期利息参数信息",description = "分页分期利息参数信息")
    @GetMapping(value = "/param/installInterestInfo/pageNum/{pageNum}/pageSize/{pageSize}")
    public AnyTxnHttpResponse<PageResultDTO<InstallmentInterestInfoDTO>> getPage(@PathVariable(value = "pageNum")Integer pageNum,
                                                                                 @PathVariable(value = "pageSize")Integer pageSize,
                                                                                 InstallmentInterestInfoDTO installmentInterestInfoDTO) {
        PageResultDTO<InstallmentInterestInfoDTO> pageResultDto = installmentInterestInfoService.getPage(pageNum, pageSize, installmentInterestInfoDTO);

        return AnyTxnHttpResponse.success(pageResultDto);
    }



    @Operation(summary = "新增分期利息参数信息", description = "新增分期利息参数信息")
    @PostMapping(value = "/param/installinterestInfo")
    public AnyTxnHttpResponse<Object> insert(@RequestBody InstallmentInterestInfoDTO installmentInterestInfoDTO) {
        ParameterCompare installmentInfoDTO = installmentInterestInfoService.insert(installmentInterestInfoDTO);
        return AnyTxnHttpResponse.success(installmentInfoDTO);
    }



    @Operation(summary = "更新分期利息参数信息",description = "更新利息参数信息")
    @PutMapping(value = "/param/installinterestInfo")
    public AnyTxnHttpResponse<Object> updateInstallmentInterestInfo(@RequestBody InstallmentInterestInfoDTO installmentInterestInfoDTO) {

        return AnyTxnHttpResponse.success(installmentInterestInfoService.updateInstallmentInterestInfo(installmentInterestInfoDTO));
    }


    @Operation(summary = "查看分期利息参数信息",description = "查看利息参数信息")
    @GetMapping(value = "/param/installInterestInfo/id/{id}")
    public AnyTxnHttpResponse<Object> getInstallmentInterestInfo(@PathVariable("id") String id) {

        return AnyTxnHttpResponse.success(installmentInterestInfoService.getInstallmentInterestInfo(id));
    }


    @Operation(summary = "删除分期利息参数信息", description = "删除分期利息参数信息")
    @DeleteMapping(value = "/param/installinterestInfo/{id}")
    public AnyTxnHttpResponse<Object> deleteInstallmentInterestInfo(@PathVariable("id") String id) {
        return AnyTxnHttpResponse.success(installmentInterestInfoService.deleteInstallmentInterestInfo(id));
    }






}
