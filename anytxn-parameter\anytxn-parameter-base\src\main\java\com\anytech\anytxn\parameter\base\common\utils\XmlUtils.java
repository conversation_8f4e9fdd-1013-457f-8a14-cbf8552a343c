package com.anytech.anytxn.parameter.base.common.utils;

import com.anytech.anytxn.parameter.base.common.enums.AnyTxnParameterRespCodeEnum;
import com.anytech.anytxn.parameter.base.common.exception.AnyTxnParameterException;
import lombok.extern.slf4j.Slf4j;

import jakarta.xml.bind.JAXBContext;
import jakarta.xml.bind.JAXBException;
import jakarta.xml.bind.Marshaller;
import jakarta.xml.bind.Unmarshaller;
import java.io.InputStream;
import java.io.StringReader;
import java.io.StringWriter;

/**
 * JDK xml格式转换工具
 *
 * <AUTHOR>
 */
@Slf4j
public class XmlUtils {

    /**
     * 将String类型的xml转换成对象
     */
    @SuppressWarnings("unchecked")
    public static <T> T convertToObj(String xmlStr, Class<T> clazz) {
        T xmlObject = null;
        // 进行将Xml转成对象的核心接口
        try {
            JAXBContext jaxbContext = JAXBContext.newInstance(clazz);
            Unmarshaller unmarshaller = jaxbContext.createUnmarshaller();
            StringReader sr = new StringReader(xmlStr);
            xmlObject = (T) unmarshaller.unmarshal(sr);
        } catch (JAXBException e) {
            log.error(String.format("xml转对象[%s]时异常: %s", clazz.getName(), e.getMessage()), e);
        }
        return xmlObject;
    }

    @SuppressWarnings("unchecked")
    public static <T> T convertToObj(InputStream inputStream, Class<T> clazz) {
        T xmlObject = null;
        // 进行将Xml转成对象的核心接口
        try {
            JAXBContext jaxbContext = JAXBContext.newInstance(clazz);
            Unmarshaller unmarshaller = jaxbContext.createUnmarshaller();
            xmlObject = (T) unmarshaller.unmarshal(inputStream);
        } catch (JAXBException e) {
            log.error(String.format("xml转对象[%s]时异常: %s", clazz.getName(), e.getMessage()), e);
        }
        return xmlObject;
    }

    /**
     * 将对象直接转换成String类型的 XML
     */
    public static String convertToXml(Object obj) {
        // 创建输出流
        StringWriter sw = new StringWriter();
        try {
            // 利用jdk中自带的转换类实现
            JAXBContext context = JAXBContext.newInstance(obj.getClass());
            // 格式化xml输出的格式
            Marshaller marshaller = context.createMarshaller();
            marshaller.setProperty(Marshaller.JAXB_FORMATTED_OUTPUT, Boolean.TRUE);
            // 将对象转换成输出流形式的xml
            marshaller.marshal(obj, sw);
        } catch (JAXBException e) {
            log.error(String.format("对象[%s]转xml时异常: %s", obj.getClass().getName(), e.getMessage()), e);
            throw new AnyTxnParameterException(AnyTxnParameterRespCodeEnum.P_EXIST_BLOCK_CODE_AND_ID_FAULT);
        }
        return sw.toString();
    }
}