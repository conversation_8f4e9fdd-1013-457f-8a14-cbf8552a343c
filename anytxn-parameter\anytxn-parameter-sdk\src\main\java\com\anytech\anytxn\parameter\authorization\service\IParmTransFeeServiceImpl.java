package com.anytech.anytxn.parameter.authorization.service;

import com.alibaba.fastjson.JSON;
import com.anytech.anytxn.parameter.base.authorization.domain.dto.ParmTransFeeDTO;
import com.anytech.anytxn.parameter.base.authorization.domain.dto.ParmTransFeeReqDTO;
import com.anytech.anytxn.parameter.base.authorization.service.IParmTransFeeService;
import com.anytech.anytxn.parameter.base.common.constants.Constants;
import com.anytech.anytxn.parameter.base.common.enums.AnyTxnParameterRespCodeEnum;
import com.anytech.anytxn.parameter.base.common.exception.AnyTxnParameterException;
import com.anytech.anytxn.parameter.authorization.mapper.ParmTransFeeMapper;
import com.anytech.anytxn.parameter.authorization.mapper.ParmTransFeeSelfMapper;
import com.anytech.anytxn.parameter.base.authorization.domain.model.ParmTransFee;
import com.github.pagehelper.Page;
import com.github.pagehelper.PageHelper;
import com.anytech.anytxn.common.core.annotation.db.DeleteParameterAnnotation;
import com.anytech.anytxn.common.core.annotation.db.InsertParameterAnnotation;
import com.anytech.anytxn.common.core.annotation.db.UpdateParameterAnnotation;
import com.anytech.anytxn.business.base.audit.AbstractParameterService;
import com.anytech.anytxn.common.core.base.ParameterCompare;
import com.anytech.anytxn.business.base.audit.ParmModificationRecord;
import com.anytech.anytxn.common.core.base.PageResultDTO;
import com.anytech.anytxn.common.core.utils.BeanMapping;
import com.anytech.anytxn.common.core.utils.OrgNumberUtils;
import com.anytech.anytxn.common.core.utils.TenantUtils;
//import com.anytech.anytxn.common.sequence.generator.INumberIdGenerator;
import com.anytech.anytxn.common.sequence.utils.Number16IdGen;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import java.util.Collections;
import java.util.List;

/**
 * @Author: lt
 * @Date: 2025-03-13
 * @Package: com.anytech.anytxn.parameter.service.impl.authorization
 */
@Service(value = "parm_trans_fee_serviceImpl")
public class IParmTransFeeServiceImpl extends AbstractParameterService implements IParmTransFeeService {

    private static final Logger logger = LoggerFactory.getLogger(AuthorizationRuleServiceImpl.class);

    @Autowired
    private ParmTransFeeSelfMapper transFeeSelfMapper;
    @Autowired
    private ParmTransFeeMapper transFeeMapper;
//    @Autowired
//    private INumberIdGenerator numberIdGenerator;
    @Autowired
    private Number16IdGen number16IdGen;

    @Override
    public PageResultDTO<ParmTransFeeDTO> findAll(Integer pageNum, Integer pageSize, ParmTransFeeReqDTO reqDTO) {

        Page<Object> pageInfo = PageHelper.startPage(pageNum, pageSize);

        List<ParmTransFee> parmTransFeeList = transFeeSelfMapper.selectByCondition(reqDTO);
        List<ParmTransFeeDTO> parmTransFeeDTOList;
        if (!CollectionUtils.isEmpty(parmTransFeeList)) {
            parmTransFeeDTOList = BeanMapping.copyList(parmTransFeeList, ParmTransFeeDTO.class);
        } else {
            parmTransFeeDTOList = Collections.emptyList();
        }
        return new PageResultDTO<>(pageNum, pageSize, pageInfo.getTotal(), pageInfo.getPages(), parmTransFeeDTOList);
    }

    @Override
    @InsertParameterAnnotation(tableName = "parm_trans_fee", tableDesc = "交易费参数新增")
    public ParameterCompare addParmFee(ParmTransFeeReqDTO reqDTO) {
        if (StringUtils.isAnyEmpty(reqDTO.getTableId(), reqDTO.getStatus(), reqDTO.getFeeType())) {
            logger.info("参数为空：{}", JSON.toJSON(reqDTO));
            throw new AnyTxnParameterException(AnyTxnParameterRespCodeEnum.P_PARAMETER_EMPTY_FAULT);
        }
        ParmTransFee parmTransFeeOld = transFeeSelfMapper.selectByOrgAndCode(reqDTO.getOrganizationNumber(), reqDTO.getTableId(), reqDTO.getFeeType());
        if (ObjectUtils.isNotEmpty(parmTransFeeOld)) {
            logger.warn("参数表已存在, Organization ={} ProductNumber={} CurrencyCode={}", reqDTO.getOrganizationNumber(), reqDTO.getTableId(), reqDTO.getFeeType());
            throw new AnyTxnParameterException(AnyTxnParameterRespCodeEnum.P_PARAMETER_DATA_EXIST);
        }
        ParmTransFee parmTransFee = BeanMapping.copy(reqDTO, ParmTransFee.class);
        parmTransFee.setStatus(Constants.ENABLED);

        return ParameterCompare.getBuilder()
                .withAfter(parmTransFee)
                .build(ParmTransFee.class);
    }

    @Override
    @UpdateParameterAnnotation(tableName = "parm_trans_fee", tableDesc = "交易费参数更新")
    public ParameterCompare modifyParmFee(ParmTransFeeReqDTO reqDTO) {
        if (org.springframework.util.ObjectUtils.isEmpty(reqDTO)) {
            throw new AnyTxnParameterException(AnyTxnParameterRespCodeEnum.P_PARAMETER_EMPTY_FAULT);
        }
        ParmTransFee parmTransFeeOld = transFeeMapper.selectByPrimaryKey(reqDTO.getId());

        if (parmTransFeeOld == null) {
            logger.error("修改参数表信息, 通过主键id({})未找到数据", reqDTO.getId());
            throw new AnyTxnParameterException(AnyTxnParameterRespCodeEnum.P_PARAMETER_DATA_NOT_EXIST);
        }
        // 拷贝修改的数据并更新
        ParmTransFee parmTransFee = BeanMapping.copy(reqDTO, ParmTransFee.class);
        parmTransFee.setId(reqDTO.getId());

        return ParameterCompare.getBuilder()
                .withAfter(parmTransFee)
                .withBefore(parmTransFeeOld)
                .build(ParmTransFee.class);
    }

    @Override
    @DeleteParameterAnnotation(tableName = "parm_trans_fee", tableDesc = "交易费参数删除")
    public ParameterCompare removeParmFee(String id) {
        if (StringUtils.isBlank(id)) {
            logger.info("参数为空：ID:{}", id);
            throw new AnyTxnParameterException(AnyTxnParameterRespCodeEnum.P_PARAMETER_EMPTY_FAULT);
        }
        ParmTransFee parmTransFee = transFeeMapper.selectByPrimaryKey(id);
        if (parmTransFee == null) {
            logger.error("删除参数表信息, 通过主键id({})未找到数据", id);
            throw new AnyTxnParameterException(AnyTxnParameterRespCodeEnum.P_PARAMETER_DATA_NOT_EXIST);
        }
        return ParameterCompare.getBuilder()
                .withBefore(parmTransFee)
                .build(ParmTransFee.class);
    }

    @Override
    public ParmTransFeeDTO findById(String id) {
        if (StringUtils.isBlank(id)) {
            logger.info("参数为空：ID:{}", id);
            throw new AnyTxnParameterException(AnyTxnParameterRespCodeEnum.P_PARAMETER_EMPTY_FAULT);
        }
        ParmTransFee parmTransFee = transFeeMapper.selectByPrimaryKey(id);
        return BeanMapping.copy(parmTransFee, ParmTransFeeDTO.class);

    }

    @Override
    public ParmTransFeeDTO findByOrgAndTableId(String orgNum, String tableId, String feeType) {
        ParmTransFee parmTransFee = transFeeSelfMapper.selectByOrgAndCode(orgNum, tableId, feeType);
        if(null == parmTransFee){
            return null;
        }
        return BeanMapping.copy(parmTransFee, ParmTransFeeDTO.class);
    }

    @Override
    @Transactional(rollbackFor = Exception.class, propagation = Propagation.REQUIRED)
//    @ShardingTransactionType(TransactionType.XA)
    protected boolean updateDb(ParmModificationRecord parmModificationRecord) throws Exception {
        ParmTransFee parmTransFee = JSON.parseObject(parmModificationRecord.getParmBody(), ParmTransFee.class);
        parmTransFee.initUpdateDateTime();
        parmTransFee.setOrganizationNumber(OrgNumberUtils.getOrg());
        int i = transFeeMapper.updateByPrimaryKeySelective(parmTransFee);
        return i > 0;
    }

    @Override
    @Transactional(rollbackFor = Exception.class, propagation = Propagation.REQUIRED)
//    @ShardingTransactionType(TransactionType.XA)
    protected boolean insertDb(ParmModificationRecord parmModificationRecord) throws Exception {
        ParmTransFee parmTransFee = JSON.parseObject(parmModificationRecord.getParmBody(), ParmTransFee.class);
        parmTransFee.initUpdateDateTime();
        parmTransFee.initCreateDateTime();
        parmTransFee.setUpdateBy(parmModificationRecord.getApplicationBy());
        parmTransFee.setId(number16IdGen.generateId(TenantUtils.getTenantId())+"" + "");
        int i = transFeeMapper.insertSelective(parmTransFee);
        return i > 0;
    }

    @Override
    @Transactional(rollbackFor = Exception.class, propagation = Propagation.REQUIRED)
//    @ShardingTransactionType(TransactionType.XA)
    protected boolean deleteDb(ParmModificationRecord parmModificationRecord) throws Exception {
        ParmTransFee parmTransFee = JSON.parseObject(parmModificationRecord.getParmBody(), ParmTransFee.class);
        logger.warn("通过id删除参数表信息, {}", parmTransFee.toString());
        int i = transFeeMapper.deleteByPrimaryKey(parmTransFee.getId());
        return i > 0;
    }
}
