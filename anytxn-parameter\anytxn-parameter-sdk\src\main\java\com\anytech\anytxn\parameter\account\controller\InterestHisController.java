package com.anytech.anytxn.parameter.account.controller;

import io.swagger.v3.oas.annotations.tags.Tag;
import io.swagger.v3.oas.annotations.Operation;
import com.anytech.anytxn.common.core.base.PageResultDTO;
import com.anytech.anytxn.common.core.utils.OrgNumberUtils;
import com.anytech.anytxn.common.core.base.AnyTxnHttpResponse;
import com.anytech.anytxn.common.core.base.BizBaseController;
import com.anytech.anytxn.parameter.base.account.domain.dto.InterestHisResDTO;
import com.anytech.anytxn.parameter.base.account.service.IInterestHisService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * 利率历史参数
 * <AUTHOR>
 * @date 2021-04-08
 */
@Tag(name = "利率历史参数")
@RestController
@Slf4j
public class InterestHisController extends BizBaseController {

    @Autowired
    private IInterestHisService interestHisService;


    /**
     * 通过机构号,利率参数表id,查询利率历史
     * @return List<InterestHisResDTO>
     *
     */
    @Operation(summary = "通过机构号,利率参数表id,查询利率历史")
    @GetMapping(value = "/param/interestHis/organizationNumber/{organizationNumber}/tableId/{interestTableId}")
    public AnyTxnHttpResponse<List<InterestHisResDTO>> getInterest(@PathVariable String organizationNumber,
                                                       @PathVariable String interestTableId) {
        List<InterestHisResDTO> res = interestHisService.findByOrgAndInterestTableId(organizationNumber,interestTableId);
        return AnyTxnHttpResponse.success(res);

    }

    @Operation(summary = "利率历史参数分页查询", description = "分页查询")
    @GetMapping(value = "/param/interestHis/pageNum/{pageNum}/pageSize/{pageSize}")
    public AnyTxnHttpResponse<PageResultDTO<InterestHisResDTO>> getAllPage(@PathVariable(value = "pageNum") Integer pageNum,
                                                                           @PathVariable(value = "pageSize") Integer pageSize,
                                                                           @RequestParam(value = "tableId",required = false) String tableId) {
        PageResultDTO<InterestHisResDTO> response = interestHisService.findPageByOrgAndInterestTableId(pageNum,pageSize, OrgNumberUtils.getOrg(),tableId);
        return AnyTxnHttpResponse.success(response);

    }


    /**
     * 通过id查询利率参数信息
     * @param id Long
     * @return InterestHisResDTO
     *
     */
    @Operation(summary = "通过id查询利率历史", description = "通过id查询利率历史")
    @GetMapping(value = "/param/interestHis/id/{id}")
    public AnyTxnHttpResponse<InterestHisResDTO> getById(@PathVariable Long id) {
        InterestHisResDTO res = interestHisService.findById(id);
        return AnyTxnHttpResponse.success(res);

    }


}
