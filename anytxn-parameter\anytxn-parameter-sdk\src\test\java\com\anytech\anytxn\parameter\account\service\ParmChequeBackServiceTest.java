package com.anytech.anytxn.parameter.account.service;

import com.alibaba.fastjson.JSON;
import com.anytech.anytxn.business.base.audit.ParmModificationRecord;
import com.anytech.anytxn.common.core.base.PageResultDTO;
import com.anytech.anytxn.common.core.base.ParameterCompare;
import com.anytech.anytxn.common.core.utils.BeanMapping;
import com.anytech.anytxn.common.core.utils.OrgNumberUtils;
import com.anytech.anytxn.common.core.utils.TenantUtils;
import com.anytech.anytxn.common.sequence.utils.Number16IdGen;
import com.anytech.anytxn.parameter.account.mapper.ParmChequeBackMapper;
import com.anytech.anytxn.parameter.account.mapper.ParmChequeBackSelfMapper;
import com.anytech.anytxn.parameter.base.account.domain.dto.ChequeBackDTO;
import com.anytech.anytxn.parameter.base.account.domain.dto.ChequeBackResDTO;
import com.anytech.anytxn.parameter.base.account.domain.model.ParmChequeBack;
import com.anytech.anytxn.parameter.base.common.enums.AnyTxnParameterRespCodeEnum;
import com.anytech.anytxn.parameter.base.common.exception.AnyTxnParameterException;
import com.github.pagehelper.Page;
import com.github.pagehelper.PageHelper;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockedStatic;
import org.mockito.junit.jupiter.MockitoExtension;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.*;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.*;

/**
 * ParmChequeBackService 测试类
 * 测试 ChequeBackServiceImpl 中的所有public方法
 */
@ExtendWith(MockitoExtension.class)
class ParmChequeBackServiceTest {

    @Mock
    private ParmChequeBackMapper parmChequeBackMapper;

    @Mock
    private ParmChequeBackSelfMapper parmChequeBackSelfMapper;

    @Mock
    private Number16IdGen numberIdGenerator;

    @InjectMocks
    private ChequeBackServiceImpl chequeBackService;

    /**
     * 测试 findByTableIdAndOrgNo 方法 - 成功路径
     */
    @Test
    void testFindByTableIdAndOrgNo_Success() {
        // Arrange
        String tableId = "CB001";
        String organizationNumber = "001";
        
        ParmChequeBack mockEntity = createMockParmChequeBack();
        ChequeBackResDTO expectedResDTO = createMockChequeBackResDTO();

        when(parmChequeBackSelfMapper.queryByChequeBackTableIdAndOrgNo(tableId, organizationNumber))
                .thenReturn(mockEntity);

        try (MockedStatic<BeanMapping> beanMappingMock = mockStatic(BeanMapping.class)) {
            beanMappingMock.when(() -> BeanMapping.copy(mockEntity, ChequeBackResDTO.class))
                    .thenReturn(expectedResDTO);

            // Act
            ChequeBackResDTO result = chequeBackService.findByTableIdAndOrgNo(tableId, organizationNumber);

            // Assert
            assertNotNull(result);
            assertEquals(expectedResDTO.getId(), result.getId());
            assertEquals(expectedResDTO.getTableId(), result.getTableId());
            verify(parmChequeBackSelfMapper).queryByChequeBackTableIdAndOrgNo(tableId, organizationNumber);
        }
    }

    /**
     * 测试 findByTableIdAndOrgNo 方法 - 参数为null异常
     */
    @Test
    void testFindByTableIdAndOrgNo_NullTableId() {
        // Act & Assert
        AnyTxnParameterException exception = assertThrows(AnyTxnParameterException.class, () -> {
            chequeBackService.findByTableIdAndOrgNo(null, "001");
        });
        
        assertEquals(AnyTxnParameterRespCodeEnum.P_PARAMETER_EMPTY_FAULT.getCode(), exception.getErrCode());
        verifyNoInteractions(parmChequeBackSelfMapper);
    }

    /**
     * 测试 findByTableIdAndOrgNo 方法 - 机构号为空异常
     */
    @Test
    void testFindByTableIdAndOrgNo_EmptyOrganizationNumber() {
        // Act & Assert
        AnyTxnParameterException exception = assertThrows(AnyTxnParameterException.class, () -> {
            chequeBackService.findByTableIdAndOrgNo("CB001", "");
        });
        
        assertEquals(AnyTxnParameterRespCodeEnum.P_PARAMETER_EMPTY_FAULT.getCode(), exception.getErrCode());
        verifyNoInteractions(parmChequeBackSelfMapper);
    }

    /**
     * 测试 findByTableIdAndOrgNo 方法 - 数据不存在异常
     */
    @Test
    void testFindByTableIdAndOrgNo_DataNotFound() {
        // Arrange
        String tableId = "CB001";
        String organizationNumber = "001";
        
        when(parmChequeBackSelfMapper.queryByChequeBackTableIdAndOrgNo(tableId, organizationNumber))
                .thenReturn(null);

        // Act & Assert
        AnyTxnParameterException exception = assertThrows(AnyTxnParameterException.class, () -> {
            chequeBackService.findByTableIdAndOrgNo(tableId, organizationNumber);
        });
        
        assertEquals(AnyTxnParameterRespCodeEnum.D_QUERY_PARA_AUTO_PAYMENT_TABLE_FAULT.getCode(), exception.getErrCode());
        verify(parmChequeBackSelfMapper).queryByChequeBackTableIdAndOrgNo(tableId, organizationNumber);
    }

    /**
     * 测试 add 方法 - 成功路径
     */
    @Test
    void testAdd_Success() {
        // Arrange
        ChequeBackDTO inputDTO = createMockChequeBackDTO();
        ParmChequeBack mockCopiedEntity = createMockParmChequeBack();
        
        when(parmChequeBackSelfMapper.queryByChequeBackTableIdAndOrgNo(anyString(), anyString()))
                .thenReturn(null);
        when(numberIdGenerator.generateId(anyString())).thenReturn(123456789L);

        try (MockedStatic<OrgNumberUtils> orgUtilsMock = mockStatic(OrgNumberUtils.class);
             MockedStatic<BeanMapping> beanMappingMock = mockStatic(BeanMapping.class);
             MockedStatic<TenantUtils> tenantUtilsMock = mockStatic(TenantUtils.class)) {
            
            orgUtilsMock.when(() -> OrgNumberUtils.getOrg(anyString())).thenReturn("001");
            tenantUtilsMock.when(() -> TenantUtils.getTenantId()).thenReturn("1");
            beanMappingMock.when(() -> BeanMapping.copy(inputDTO, ParmChequeBack.class))
                    .thenReturn(mockCopiedEntity);

            // Act
            ParameterCompare result = chequeBackService.add(inputDTO);

            // Assert
            assertNotNull(result);
            verify(parmChequeBackSelfMapper).queryByChequeBackTableIdAndOrgNo(anyString(), anyString());
            verify(numberIdGenerator).generateId("1");
        }
    }

    /**
     * 测试 add 方法 - 数据已存在异常
     */
    @Test
    void testAdd_DataAlreadyExists() {
        // Arrange
        ChequeBackDTO inputDTO = createMockChequeBackDTO();
        ParmChequeBack existingEntity = createMockParmChequeBack();
        
        when(parmChequeBackSelfMapper.queryByChequeBackTableIdAndOrgNo(anyString(), anyString()))
                .thenReturn(existingEntity);

        try (MockedStatic<OrgNumberUtils> orgUtilsMock = mockStatic(OrgNumberUtils.class)) {
            orgUtilsMock.when(() -> OrgNumberUtils.getOrg(anyString())).thenReturn("001");
            orgUtilsMock.when(() -> OrgNumberUtils.getOrg()).thenReturn("001");

            // Act & Assert
            AnyTxnParameterException exception = assertThrows(AnyTxnParameterException.class, () -> {
                chequeBackService.add(inputDTO);
            });
            
            assertEquals(AnyTxnParameterRespCodeEnum.D_EXIST_PARA_AUTO_PAYMENT_TABLE_FAULT.getCode(), exception.getErrCode());
            verify(parmChequeBackSelfMapper).queryByChequeBackTableIdAndOrgNo(anyString(), anyString());
            verifyNoInteractions(numberIdGenerator);
        }
    }

    /**
     * 测试 modify 方法 - 成功路径
     */
    @Test
    void testModify_Success() {
        // Arrange
        ChequeBackDTO inputDTO = createMockChequeBackDTO();
        inputDTO.setId("123456789");
        ParmChequeBack existingEntity = createMockParmChequeBack();
        ParmChequeBack modifiedEntity = createMockParmChequeBack();
        
        when(parmChequeBackMapper.selectByPrimaryKey(inputDTO.getId()))
                .thenReturn(existingEntity);

        try (MockedStatic<BeanMapping> beanMappingMock = mockStatic(BeanMapping.class)) {
            beanMappingMock.when(() -> BeanMapping.copy(inputDTO, ParmChequeBack.class))
                    .thenReturn(modifiedEntity);

            // Act
            ParameterCompare result = chequeBackService.modify(inputDTO);

            // Assert
            assertNotNull(result);
            verify(parmChequeBackMapper).selectByPrimaryKey(inputDTO.getId());
        }
    }

    /**
     * 测试 modify 方法 - ID为null异常
     */
    @Test
    void testModify_NullId() {
        // Arrange
        ChequeBackDTO inputDTO = createMockChequeBackDTO();
        inputDTO.setId(null);

        // Act & Assert
        AnyTxnParameterException exception = assertThrows(AnyTxnParameterException.class, () -> {
            chequeBackService.modify(inputDTO);
        });
        
        assertEquals(AnyTxnParameterRespCodeEnum.P_PARAMETER_EMPTY_PARA_AUTO_PAYMENT_TABLE_FAULT.getCode(), exception.getErrCode());
        verifyNoInteractions(parmChequeBackMapper);
    }

    /**
     * 测试 modify 方法 - 数据不存在异常
     */
    @Test
    void testModify_DataNotFound() {
        // Arrange
        ChequeBackDTO inputDTO = createMockChequeBackDTO();
        inputDTO.setId("123456789");
        
        when(parmChequeBackMapper.selectByPrimaryKey(inputDTO.getId()))
                .thenReturn(null);

        // Act & Assert
        AnyTxnParameterException exception = assertThrows(AnyTxnParameterException.class, () -> {
            chequeBackService.modify(inputDTO);
        });
        
        assertEquals(AnyTxnParameterRespCodeEnum.D_MODIFY_PARA_AUTO_PAYMENT_TABLE_BY_ID_FAULT.getCode(), exception.getErrCode());
        verify(parmChequeBackMapper).selectByPrimaryKey(inputDTO.getId());
    }

    /**
     * 测试 remove(String id) 方法 - 成功路径
     */
    @Test
    void testRemoveById_Success() {
        // Arrange
        String id = "123456789";
        ParmChequeBack existingEntity = createMockParmChequeBack();
        
        when(parmChequeBackMapper.selectByPrimaryKey(id))
                .thenReturn(existingEntity);

        // Act
        ParameterCompare result = chequeBackService.remove(id);

        // Assert
        assertNotNull(result);
        verify(parmChequeBackMapper).selectByPrimaryKey(id);
    }

    /**
     * 测试 remove(String id) 方法 - ID为null异常
     */
    @Test
    void testRemoveById_NullId() {
        // Act & Assert
        AnyTxnParameterException exception = assertThrows(AnyTxnParameterException.class, () -> {
            chequeBackService.remove((String) null);
        });
        
        assertEquals(AnyTxnParameterRespCodeEnum.P_DELETE_EMPTY_PARA_AUTO_PAYMENT_TABLE_FAULT.getCode(), exception.getErrCode());
        verifyNoInteractions(parmChequeBackMapper);
    }

    /**
     * 测试 remove(String id) 方法 - 数据不存在异常
     */
    @Test
    void testRemoveById_DataNotFound() {
        // Arrange
        String id = "123456789";
        
        when(parmChequeBackMapper.selectByPrimaryKey(id))
                .thenReturn(null);

        // Act & Assert
        AnyTxnParameterException exception = assertThrows(AnyTxnParameterException.class, () -> {
            chequeBackService.remove(id);
        });
        
        assertEquals(AnyTxnParameterRespCodeEnum.D_DELETE_PARA_AUTO_PAYMENT_TABLE_BY_ID_FAULT.getCode(), exception.getErrCode());
        verify(parmChequeBackMapper).selectByPrimaryKey(id);
    }

    /**
     * 测试 remove(String tableId, String orgNum) 方法 - 成功路径
     */
    @Test
    void testRemoveByTableIdAndOrgNum_Success() {
        // Arrange
        String tableId = "CB001";
        String orgNum = "001";
        ParmChequeBack existingEntity = createMockParmChequeBack();
        
        when(parmChequeBackMapper.selectByTableIdAndOrgNum(tableId, orgNum))
                .thenReturn(existingEntity);

        // Act
        ParameterCompare result = chequeBackService.remove(tableId, orgNum);

        // Assert
        assertNotNull(result);
        verify(parmChequeBackMapper).selectByTableIdAndOrgNum(tableId, orgNum);
    }

    /**
     * 测试 remove(String tableId, String orgNum) 方法 - 参数为null异常
     */
    @Test
    void testRemoveByTableIdAndOrgNum_NullParameters() {
        // Act & Assert - tableId为null
        AnyTxnParameterException exception1 = assertThrows(AnyTxnParameterException.class, () -> {
            chequeBackService.remove(null, "001");
        });
        assertEquals(AnyTxnParameterRespCodeEnum.P_DELETE_EMPTY_PARA_AUTO_PAYMENT_TABLE_FAULT.getCode(), exception1.getErrCode());

        // Act & Assert - orgNum为null
        AnyTxnParameterException exception2 = assertThrows(AnyTxnParameterException.class, () -> {
            chequeBackService.remove("CB001", null);
        });
        assertEquals(AnyTxnParameterRespCodeEnum.P_DELETE_EMPTY_PARA_AUTO_PAYMENT_TABLE_FAULT.getCode(), exception2.getErrCode());
        
        verifyNoInteractions(parmChequeBackMapper);
    }

    /**
     * 测试 remove(String tableId, String orgNum) 方法 - 数据不存在异常
     */
    @Test
    void testRemoveByTableIdAndOrgNum_DataNotFound() {
        // Arrange
        String tableId = "CB001";
        String orgNum = "001";
        
        when(parmChequeBackMapper.selectByTableIdAndOrgNum(tableId, orgNum))
                .thenReturn(null);

        // Act & Assert
        AnyTxnParameterException exception = assertThrows(AnyTxnParameterException.class, () -> {
            chequeBackService.remove(tableId, orgNum);
        });
        
        assertEquals(AnyTxnParameterRespCodeEnum.D_DELETE_PARA_AUTO_PAYMENT_TABLE_BY_ID_FAULT.getCode(), exception.getErrCode());
        verify(parmChequeBackMapper).selectByTableIdAndOrgNum(tableId, orgNum);
    }

    /**
     * 测试 find(String id) 方法 - 成功路径
     */
    @Test
    void testFindById_Success() {
        // Arrange
        String id = "123456789";
        ParmChequeBack mockEntity = createMockParmChequeBack();
        ChequeBackResDTO expectedResDTO = createMockChequeBackResDTO();
        
        when(parmChequeBackMapper.selectByPrimaryKey(id))
                .thenReturn(mockEntity);

        try (MockedStatic<BeanMapping> beanMappingMock = mockStatic(BeanMapping.class)) {
            beanMappingMock.when(() -> BeanMapping.copy(mockEntity, ChequeBackResDTO.class))
                    .thenReturn(expectedResDTO);

            // Act
            ChequeBackResDTO result = chequeBackService.find(id);

            // Assert
            assertNotNull(result);
            assertEquals(expectedResDTO.getId(), result.getId());
            verify(parmChequeBackMapper).selectByPrimaryKey(id);
        }
    }

    /**
     * 测试 find(String id) 方法 - ID为null异常
     */
    @Test
    void testFindById_NullId() {
        // Act & Assert
        AnyTxnParameterException exception = assertThrows(AnyTxnParameterException.class, () -> {
            chequeBackService.find((String) null);
        });
        
        assertEquals(AnyTxnParameterRespCodeEnum.P_PRIMARY_KEY_NULL_FAULT.getCode(), exception.getErrCode());
        verifyNoInteractions(parmChequeBackMapper);
    }

    /**
     * 测试 find(String id) 方法 - 数据不存在异常
     */
    @Test
    void testFindById_DataNotFound() {
        // Arrange
        String id = "123456789";
        
        when(parmChequeBackMapper.selectByPrimaryKey(id))
                .thenReturn(null);

        // Act & Assert
        AnyTxnParameterException exception = assertThrows(AnyTxnParameterException.class, () -> {
            chequeBackService.find(id);
        });
        
        assertEquals(AnyTxnParameterRespCodeEnum.D_QUERY_PARA_AUTO_PAYMENT_TABLE_FAULT.getCode(), exception.getErrCode());
        verify(parmChequeBackMapper).selectByPrimaryKey(id);
    }

    /**
     * 测试 find(String tableId, String orgNum) 方法 - 成功路径
     */
    @Test
    void testFindByTableIdAndOrgNum_Success() {
        // Arrange
        String tableId = "CB001";
        String orgNum = "001";
        ParmChequeBack mockEntity = createMockParmChequeBack();
        ChequeBackResDTO expectedResDTO = createMockChequeBackResDTO();
        
        when(parmChequeBackMapper.selectByTableIdAndOrgNum(tableId, orgNum))
                .thenReturn(mockEntity);

        try (MockedStatic<BeanMapping> beanMappingMock = mockStatic(BeanMapping.class)) {
            beanMappingMock.when(() -> BeanMapping.copy(mockEntity, ChequeBackResDTO.class))
                    .thenReturn(expectedResDTO);

            // Act
            ChequeBackResDTO result = chequeBackService.find(tableId, orgNum);

            // Assert
            assertNotNull(result);
            assertEquals(expectedResDTO.getId(), result.getId());
            verify(parmChequeBackMapper).selectByTableIdAndOrgNum(tableId, orgNum);
        }
    }

    /**
     * 测试 find(String tableId, String orgNum) 方法 - 参数为null异常
     */
    @Test
    void testFindByTableIdAndOrgNum_NullParameters() {
        // Act & Assert - tableId为null
        AnyTxnParameterException exception1 = assertThrows(AnyTxnParameterException.class, () -> {
            chequeBackService.find(null, "001");
        });
        assertEquals(AnyTxnParameterRespCodeEnum.P_PRIMARY_KEY_NULL_FAULT.getCode(), exception1.getErrCode());

        // Act & Assert - orgNum为null
        AnyTxnParameterException exception2 = assertThrows(AnyTxnParameterException.class, () -> {
            chequeBackService.find("CB001", null);
        });
        assertEquals(AnyTxnParameterRespCodeEnum.P_PRIMARY_KEY_NULL_FAULT.getCode(), exception2.getErrCode());
        
        verifyNoInteractions(parmChequeBackMapper);
    }

    /**
     * 测试 find(String tableId, String orgNum) 方法 - 数据不存在异常
     */
    @Test
    void testFindByTableIdAndOrgNum_DataNotFound() {
        // Arrange
        String tableId = "CB001";
        String orgNum = "001";
        
        when(parmChequeBackMapper.selectByTableIdAndOrgNum(tableId, orgNum))
                .thenReturn(null);

        // Act & Assert
        AnyTxnParameterException exception = assertThrows(AnyTxnParameterException.class, () -> {
            chequeBackService.find(tableId, orgNum);
        });
        
        assertEquals(AnyTxnParameterRespCodeEnum.D_QUERY_PARA_AUTO_PAYMENT_TABLE_FAULT.getCode(), exception.getErrCode());
        verify(parmChequeBackMapper).selectByTableIdAndOrgNum(tableId, orgNum);
    }

    /**
     * 测试 findAll 方法 - 成功路径
     */
    @Test
    void testFindAll_Success() {
        // Arrange
        Integer pageNum = 1;
        Integer pageSize = 10;
        String tableId = "CB001";
        String description = "Test Description";
        String feeIndicator = "Y";
        String status = "A";
        String transactionCode = "TXN001";
        String interestIndicator = "N";
        BigDecimal fixedFee = new BigDecimal("100.00");
        String organizationNumber = "001";

        List<ParmChequeBack> mockEntityList = Arrays.asList(createMockParmChequeBack());
        List<ChequeBackResDTO> mockResDTOList = new ArrayList<>();
        Page<ParmChequeBack> mockPage = new Page<>(pageNum, pageSize);
        mockPage.setTotal(1);
        mockPage.setPages(1);

        when(parmChequeBackSelfMapper.selectAll(any(Map.class)))
                .thenReturn(mockEntityList);

        try (MockedStatic<PageHelper> pageHelperMock = mockStatic(PageHelper.class);
             MockedStatic<BeanMapping> beanMappingMock = mockStatic(BeanMapping.class);
             MockedStatic<OrgNumberUtils> orgUtilsMock = mockStatic(OrgNumberUtils.class)) {
            
            // 在mock块内部创建ResDTO
            mockResDTOList.add(createMockChequeBackResDTO());
            
            pageHelperMock.when(() -> PageHelper.startPage(pageNum, pageSize))
                    .thenReturn(mockPage);
            beanMappingMock.when(() -> BeanMapping.copyList(mockEntityList, ChequeBackResDTO.class))
                    .thenReturn(mockResDTOList);

            // Act
            PageResultDTO<ChequeBackResDTO> result = chequeBackService.findAll(
                    pageNum, pageSize, tableId, description, feeIndicator, 
                    status, transactionCode, interestIndicator, fixedFee, organizationNumber);

            // Assert
            assertNotNull(result);
            assertEquals(pageNum, result.getPage());
            assertEquals(pageSize, result.getRows());
            assertEquals(1, result.getTotal());
            assertEquals(1, result.getTotalPage());
            assertNotNull(result.getData());
            assertEquals(1, result.getData().size());
            
            verify(parmChequeBackSelfMapper).selectAll(any(Map.class));
        }
    }

    /**
     * 测试 findAll 方法 - 使用默认机构号
     */
    @Test
    void testFindAll_WithDefaultOrganization() {
        // Arrange
        Integer pageNum = 1;
        Integer pageSize = 10;
        List<ParmChequeBack> mockEntityList = Arrays.asList();
        List<ChequeBackResDTO> mockResDTOList = Arrays.asList();
        Page<ParmChequeBack> mockPage = new Page<>(pageNum, pageSize);
        mockPage.setTotal(0);
        mockPage.setPages(0);

        when(parmChequeBackSelfMapper.selectAll(any(Map.class)))
                .thenReturn(mockEntityList);

        try (MockedStatic<PageHelper> pageHelperMock = mockStatic(PageHelper.class);
             MockedStatic<BeanMapping> beanMappingMock = mockStatic(BeanMapping.class);
             MockedStatic<OrgNumberUtils> orgUtilsMock = mockStatic(OrgNumberUtils.class)) {
            
            pageHelperMock.when(() -> PageHelper.startPage(pageNum, pageSize))
                    .thenReturn(mockPage);
            beanMappingMock.when(() -> BeanMapping.copyList(mockEntityList, ChequeBackResDTO.class))
                    .thenReturn(mockResDTOList);
            orgUtilsMock.when(() -> OrgNumberUtils.getOrg()).thenReturn("001");

            // Act - 传入空的organizationNumber
            PageResultDTO<ChequeBackResDTO> result = chequeBackService.findAll(
                    pageNum, pageSize, null, null, null, 
                    null, null, null, null, "");

            // Assert
            assertNotNull(result);
            assertEquals(0, result.getData().size());
            verify(parmChequeBackSelfMapper).selectAll(any(Map.class));
        }
    }

    /**
     * 测试 findByStatus 方法 - 成功路径
     */
    @Test
    void testFindByStatus_Success() {
        // Arrange
        String status = "A";
        List<ParmChequeBack> mockEntityList = Arrays.asList(createMockParmChequeBack());
        List<ChequeBackResDTO> expectedResDTOList = new ArrayList<>();

        when(parmChequeBackSelfMapper.selectByStatus(eq(status), anyString()))
                .thenReturn(mockEntityList);

        try (MockedStatic<BeanMapping> beanMappingMock = mockStatic(BeanMapping.class);
             MockedStatic<OrgNumberUtils> orgUtilsMock = mockStatic(OrgNumberUtils.class)) {
            
            // 在mock块内部创建ResDTO
            expectedResDTOList.add(createMockChequeBackResDTO());
            
            beanMappingMock.when(() -> BeanMapping.copyList(mockEntityList, ChequeBackResDTO.class))
                    .thenReturn(expectedResDTOList);
            orgUtilsMock.when(() -> OrgNumberUtils.getOrg()).thenReturn("001");

            // Act
            List<ChequeBackResDTO> result = chequeBackService.findByStatus(status);

            // Assert
            assertNotNull(result);
            assertEquals(1, result.size());
            verify(parmChequeBackSelfMapper).selectByStatus(status, "001");
        }
    }

    /**
     * 测试 findByStatus 方法 - 状态为空
     */
    @Test
    void testFindByStatus_EmptyStatus() {
        // Act
        List<ChequeBackResDTO> result = chequeBackService.findByStatus("");

        // Assert
        assertNull(result);
        verifyNoInteractions(parmChequeBackSelfMapper);
    }

    /**
     * 测试 findByStatus 方法 - 状态为null
     */
    @Test
    void testFindByStatus_NullStatus() {
        // Act
        List<ChequeBackResDTO> result = chequeBackService.findByStatus(null);

        // Assert
        assertNull(result);
        verifyNoInteractions(parmChequeBackSelfMapper);
    }

    // ==================== 辅助方法 ====================

    /**
     * 创建测试用的 ParmChequeBack 对象
     */
    private ParmChequeBack createMockParmChequeBack() {
        ParmChequeBack entity = new ParmChequeBack();
        entity.setId("123456789");
        entity.setTableId("CB001");
        entity.setOrganizationNumber("001");
        entity.setStatus("A");
        entity.setDescription("Test Description");
        entity.setFeeIndicator("Y");
        entity.setTransactionCode("TXN001");
        entity.setFeeInterestIndicator("N");
        entity.setFixedFee(new BigDecimal("100.00"));
        entity.setCreateTime(LocalDateTime.now());
        entity.setUpdateTime(LocalDateTime.now());
        entity.setUpdateBy("testUser");
        entity.setVersionNumber(1L);
        return entity;
    }

    /**
     * 创建测试用的 ChequeBackDTO 对象
     */
    private ChequeBackDTO createMockChequeBackDTO() {
        ChequeBackDTO dto = new ChequeBackDTO();
        dto.setId("123456789");
        dto.setTableId("CB001");
        dto.setOrganizationNumber("001");
        dto.setStatus("A");
        dto.setDescription("Test Description");
        dto.setFeeIndicator("Y");
        dto.setTransactionCode("TXN001");
        dto.setFeeInterestIndicator("N");
        dto.setFixedFee(new BigDecimal("100.00"));
        dto.setCreateTime(LocalDateTime.now());
        dto.setUpdateTime(LocalDateTime.now());
        dto.setUpdateBy("testUser");
        dto.setVersionNumber(1L);
        return dto;
    }

    /**
     * 创建测试用的 ChequeBackResDTO 对象
     */
    private ChequeBackResDTO createMockChequeBackResDTO() {
        ChequeBackResDTO resDTO = new ChequeBackResDTO();
        resDTO.setId("123456789");
        resDTO.setTableId("CB001");
        resDTO.setOrganizationNumber("001");
        resDTO.setStatus("A");
        resDTO.setDescription("Test Description");
        resDTO.setFeeIndicator("Y");
        resDTO.setTransactionCode("TXN001");
        resDTO.setFeeInterestIndicator("N");
        resDTO.setFixedFee(new BigDecimal("100.00"));
        resDTO.setCreateTime(LocalDateTime.now());
        resDTO.setUpdateTime(LocalDateTime.now());
        resDTO.setUpdateBy("testUser");
        resDTO.setVersionNumber(1L);
        return resDTO;
    }
} 