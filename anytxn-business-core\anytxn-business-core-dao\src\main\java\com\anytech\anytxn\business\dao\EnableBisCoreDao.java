package com.anytech.anytxn.business.dao;

import org.mybatis.spring.annotation.MapperScan;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Import;

import java.lang.annotation.*;

/**
 * <AUTHOR>
 * @date 2020/2/21
 */
@Target(ElementType.TYPE)
@Retention(RetentionPolicy.RUNTIME)
@Documented
@Inherited
@Import(EnableBisCoreDao.BisCoreContextDaoConfigurer.class)
public @interface EnableBisCoreDao {

    @Configuration
    @EnableCmCoreDao
    @MapperScan(basePackages = {"com.anytech.anytxn.business.dao.account.mapper", "com.anytech.anytxn.business.dao.card.mapper", "com.anytech.anytxn.business.dao.customer.mapper"
            ,"com.anytech.anytxn.core.installment.mapper","com.anytech.anytxn.core.auth.mapper","com.anytech.anytxn.core.limit.mapper"
            ,"com.anytech.anytxn.core.transaction.mapper","com.anytech.anytxn.core.accountant.mapper"
            , "com.anytech.anytxn.business.dao.monetary.mapper","com.anytech.anytxn.core.companycustomer.mapper"
            ,"com.anytech.anytxn.core.poc.mapper","com.anytech.anytxn.core.settle.mapper"
            ,"com.anytech.anytxn.core.job.mapper"})
    class BisCoreContextDaoConfigurer {
    }
}
