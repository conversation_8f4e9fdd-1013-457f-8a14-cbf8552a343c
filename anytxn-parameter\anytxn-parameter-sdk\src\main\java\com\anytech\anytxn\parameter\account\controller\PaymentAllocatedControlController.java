package com.anytech.anytxn.parameter.account.controller;

import io.swagger.v3.oas.annotations.tags.Tag;
import io.swagger.v3.oas.annotations.Operation;
import com.anytech.anytxn.common.core.base.AnyTxnHttpResponse;
import com.anytech.anytxn.common.core.base.BizBaseController;
import com.anytech.anytxn.parameter.base.account.domain.dto.PaymentAllocatedControlReqDTO;
import com.anytech.anytxn.parameter.base.account.domain.dto.PaymentAllocatedControlResDTO;
import com.anytech.anytxn.parameter.base.account.service.IPaymentAllocatedControlService;
import com.anytech.anytxn.parameter.base.common.enums.ParameterRepDetailEnum;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.ArrayList;

/**
 * 还款分配控制参数
 * <AUTHOR> tingting
 * @date 2018/8/16
 */
@Tag(name = "还款分配控制参数")
@RestController
public class PaymentAllocatedControlController extends BizBaseController {
    @Autowired
    private IPaymentAllocatedControlService paymentAllocatedControlService;

    /**
     * 创建还款分配控制参数
     * @param paymentAllocatedControlReq
     * @return
     *
     */
    @Operation(summary = "创建还款分配控制参数", description = "创建还款分配控制参数")
    @PostMapping("/param/paymentAllocatedControl")
    public AnyTxnHttpResponse<PaymentAllocatedControlResDTO> create(@RequestBody PaymentAllocatedControlReqDTO paymentAllocatedControlReq) {
        PaymentAllocatedControlResDTO res;
        res = paymentAllocatedControlService.addPaymentAllocatedControl(paymentAllocatedControlReq);
        return AnyTxnHttpResponse.success(res, ParameterRepDetailEnum.CREATE.message());

    }

    /**
     * 通过id删除还款分配控制参数
     * @param id
     * @return
     *
     */
    @Operation(summary = "删除还款分配控制参数", description = "通过id删除还款分配控制参数")
    @DeleteMapping(value = "/param/paymentAllocatedControl/id/{id}")
    public AnyTxnHttpResponse<Boolean> remove(@PathVariable Long id) {
        Boolean flag;
        flag = paymentAllocatedControlService.removePaymentAllocatedControl(id);
        return AnyTxnHttpResponse.success(flag,ParameterRepDetailEnum.DEL.message());


    }

    /**
     * 通过机构号和表Id查询还款分配控制
     *
     * @param organizationNumber 机构号
     * @param tableId 表Id
     * @return
     */
    @Operation(summary = "通过机构号和表Id查询查询还款分配控制", description = "通过机构号和表Id查询")
    @GetMapping(value = "/param/paymentAllocatedControl/organizationNumber/{organizationNumber}/tableId/{tableId}")
    public AnyTxnHttpResponse<ArrayList<PaymentAllocatedControlResDTO>> getPaymentAllocatedControl(@PathVariable String organizationNumber, @PathVariable String tableId) {
        ArrayList<PaymentAllocatedControlResDTO> res;
        res = (ArrayList)paymentAllocatedControlService.findByOrgAndTableId(organizationNumber, tableId);
        return AnyTxnHttpResponse.success(res);

    }

    /**
     * 根据 根据机构号、参数表id、交易类型、已出账单标志、应计非应计标志查询
     *
     * @param transactionTypeCode 交易类型
     * @param statementFlag       已出账单标志
     * @param organizationNumber  机构号
     * @param tableId             参数表id
     * @return AnyTxnHttpResponse<PaymentAllocatedControlRes>
     */
//    @Operation(summary = "根据机构号、参数表id、交易类型、已出账单标志查询", description = "根据机构号、参数表id、交易类型、已出账单标志查询")
//    @GetMapping(value = "/param/paymentAllocatedControl/organizationNumber/{organizationNumber}/tableId/{tableId}/statementFlag/{statementFlag}/transactionTypeCode/{transactionTypeCode}")
//    public AnyTxnHttpResponse<PaymentAllocatedControlResDTO> getByMultipleConditions(@PathVariable String organizationNumber,
//                                                                                  @PathVariable String tableId,
//                                                                                  @PathVariable String statementFlag,
//                                                                                  @PathVariable String transactionTypeCode) {
//
//        PaymentAllocatedControlResDTO paymentAllocatedControlRes = null;
//        paymentAllocatedControlRes = paymentAllocatedControlService.findByMultipleConditions(organizationNumber, tableId, statementFlag, transactionTypeCode);
//        return AnyTxnHttpResponse.success(paymentAllocatedControlRes);
//    }
}
