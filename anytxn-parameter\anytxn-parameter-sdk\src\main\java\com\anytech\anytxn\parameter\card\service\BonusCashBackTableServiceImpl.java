package com.anytech.anytxn.parameter.card.service;

import com.alibaba.fastjson.JSON;
import com.anytech.anytxn.business.base.audit.AbstractParameterService;
import com.anytech.anytxn.business.base.audit.ParmModificationRecord;
import com.anytech.anytxn.parameter.base.card.domain.dto.BonusCashBackTableSearchDTO;
import com.anytech.anytxn.parameter.base.card.domain.dto.ParmBonusCashbackCharReqDTO;
import com.anytech.anytxn.parameter.base.card.domain.dto.ParmBonusCashbackCharResDTO;
import com.anytech.anytxn.parameter.base.card.service.IBonusCashBackTableService;
import com.anytech.anytxn.parameter.base.common.enums.AnyTxnParameterRespCodeEnum;
import com.anytech.anytxn.parameter.base.common.enums.ParameterRepDetailEnum;
import com.anytech.anytxn.parameter.base.common.exception.AnyTxnParameterException;
import com.anytech.anytxn.parameter.base.transaction.domain.model.ParmBonusCashbackCharge;
import com.anytech.anytxn.parameter.card.mapper.broadcast.ParmBonusCashbackChargeMapper;
import com.anytech.anytxn.parameter.card.mapper.broadcast.ParmBonusCashbackChargeSelfMapper;
import com.github.pagehelper.Page;
import com.github.pagehelper.PageHelper;
import com.anytech.anytxn.common.core.annotation.db.DeleteParameterAnnotation;
import com.anytech.anytxn.common.core.annotation.db.InsertParameterAnnotation;
import com.anytech.anytxn.common.core.annotation.db.UpdateParameterAnnotation;
import com.anytech.anytxn.common.core.base.ParameterCompare;
import com.anytech.anytxn.common.core.base.PageResultDTO;
import com.anytech.anytxn.common.core.utils.BeanMapping;
import com.anytech.anytxn.common.core.utils.OrgNumberUtils;
import com.anytech.anytxn.common.core.utils.TenantUtils;
import com.anytech.anytxn.common.sequence.utils.Number16IdGen;
import lombok.extern.slf4j.Slf4j;
//import org.apache.shardingsphere.transaction.annotation.ShardingSphereTransactionType;
//import org.apache.shardingsphere.transaction.core.TransactionType;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;
import java.util.Objects;

@Service(value = "parm_bonus_cashback_charge_serviceImpl")
@Slf4j
public class BonusCashBackTableServiceImpl extends AbstractParameterService implements IBonusCashBackTableService {

    @Autowired
    private ParmBonusCashbackChargeSelfMapper cashbackChargeSelfMapper;

    @Autowired
    private ParmBonusCashbackChargeMapper cashbackChargeMapper;

    @Autowired
    private Number16IdGen numberIdGenerator;

    @Override
    public PageResultDTO<ParmBonusCashbackCharResDTO> findAll(Integer pageNum, Integer pageSize, BonusCashBackTableSearchDTO cashBackTableSearchDTO) {

        if (null == cashBackTableSearchDTO) {
            cashBackTableSearchDTO = new BonusCashBackTableSearchDTO();
        }

        if (!Objects.nonNull(cashBackTableSearchDTO.getChargeRate())) {
            cashBackTableSearchDTO.setChargeRate(cashBackTableSearchDTO.getChargeRate());
        }

        Page<ParmBonusCashbackCharResDTO> page = PageHelper.startPage(pageNum, pageSize);
        List<ParmBonusCashbackCharge> cashbackCharges = cashbackChargeSelfMapper.selectByCondition(cashBackTableSearchDTO);
        List<ParmBonusCashbackCharResDTO> res = BeanMapping.copyList(cashbackCharges, ParmBonusCashbackCharResDTO.class);
        return new PageResultDTO<>(pageNum, pageSize, page.getTotal(), page.getPages(), res);
    }

    @Override
    @InsertParameterAnnotation(tableName = "parm_bonus_cashback_charge", tableDesc = "bonus cashback")
    public ParameterCompare addParm(ParmBonusCashbackCharReqDTO bonusCashbackCharReqDTO) {

        // 根据机构号和table_id查询该记录是否已经存在
        ParmBonusCashbackCharge cashbackCharge = cashbackChargeSelfMapper.selectByOrgAndTid(OrgNumberUtils.getOrg(bonusCashbackCharReqDTO.getOrganizationNumber()), bonusCashbackCharReqDTO.getTableId());
        if (cashbackCharge != null) {
            log.error("积分返现参数已存在,id:{}", cashbackCharge.getId());
            throw new AnyTxnParameterException(AnyTxnParameterRespCodeEnum.P_PARAMETER_TABLE_ID_EXIST);
        }

        ParmBonusCashbackCharge copy = BeanMapping.copy(bonusCashbackCharReqDTO, ParmBonusCashbackCharge.class);
        // 设置默认值
        copy.setId(String.valueOf(numberIdGenerator.generateId(TenantUtils.getTenantId())));

        return ParameterCompare.getBuilder().withAfter(copy).build(ParmBonusCashbackCharge.class);
    }

    @Override
    @UpdateParameterAnnotation(tableName = "parm_bonus_cashback_charge", tableDesc = "bonus cashback")
    public ParameterCompare modifyParm(ParmBonusCashbackCharReqDTO bonusCashbackCharReqDTO) {
        ParmBonusCashbackCharge parmBonusCashbackCharge = cashbackChargeMapper.selectByPrimaryKey(bonusCashbackCharReqDTO.getId());
        if (null == parmBonusCashbackCharge) {
            log.error("积分返现参数数据不存在");
            throw new AnyTxnParameterException(AnyTxnParameterRespCodeEnum.D_GET_IS_NULL_FAULT, ParameterRepDetailEnum.LATE_FREE);
        }
        // 判断修改后的数据在表里是否是以机构号,表id唯一确定一条数据
        if (!parmBonusCashbackCharge.getOrganizationNumber().equals(bonusCashbackCharReqDTO.getOrganizationNumber())
                || !parmBonusCashbackCharge.getTableId().equals(bonusCashbackCharReqDTO.getTableId())) {
            ParmBonusCashbackCharge cashbackCharge = cashbackChargeSelfMapper.selectByOrgAndTid(bonusCashbackCharReqDTO.getOrganizationNumber(), bonusCashbackCharReqDTO.getTableId());
            if (cashbackCharge != null) {
                log.error("积分返现参数已存在,id:{}", bonusCashbackCharReqDTO.getId());
                throw new AnyTxnParameterException(AnyTxnParameterRespCodeEnum.P_PARAMETER_TABLE_ID_EXIST);
            }
        }
        ParmBonusCashbackCharge updateBonusCashBack = BeanMapping.copy(bonusCashbackCharReqDTO, ParmBonusCashbackCharge.class);

        return ParameterCompare.getBuilder().withAfter(updateBonusCashBack).withBefore(parmBonusCashbackCharge).build(ParmBonusCashbackCharge.class);
    }

    @Override
    @DeleteParameterAnnotation(tableName = "parm_bonus_cashback_charge", tableDesc = "bonus cashback")
    @Transactional(rollbackFor = Exception.class)
    public ParameterCompare removeParm(String id) {

        ParmBonusCashbackCharge parmBonusCashbackCharge = new ParmBonusCashbackCharge();
        // 如果id不存在，则查询该记录
        if (null != id) {
            parmBonusCashbackCharge = cashbackChargeMapper.selectByPrimaryKey(id);
        }
        // 如果该记录不存在，抛出异常，否则就删除该记录
        if (null == parmBonusCashbackCharge) {
            log.error("积分返现参数数据不存在");
            throw new AnyTxnParameterException(AnyTxnParameterRespCodeEnum.D_GET_IS_NULL_FAULT, ParameterRepDetailEnum.LATE_FREE);
        }
        return ParameterCompare.getBuilder().withBefore(parmBonusCashbackCharge).build(ParmBonusCashbackCharge.class);

    }

    @Override
    public ParmBonusCashbackCharResDTO findById(String id) {
        if (id == null) {
            log.error("id不能为空");
            throw new AnyTxnParameterException(AnyTxnParameterRespCodeEnum.P_PARAMETER_ID_EMPTY_FAULT, ParameterRepDetailEnum.ID_NULL);
        }
        ParmBonusCashbackCharge parmBonusCashbackCharge = cashbackChargeMapper.selectByPrimaryKey(id);
        return BeanMapping.copy(parmBonusCashbackCharge, ParmBonusCashbackCharResDTO.class);
    }

    @Override
    public ParmBonusCashbackCharResDTO findByOrgAndTableId(String orgNum, String tableId) {
        ParmBonusCashbackCharge parmBonusCashbackCharge = cashbackChargeSelfMapper.selectByOrgAndTid(orgNum, tableId);
        if (parmBonusCashbackCharge == null) {
            log.error("积分返现参数数据不存在");
            throw new AnyTxnParameterException(AnyTxnParameterRespCodeEnum.D_GET_IS_NULL_FAULT, ParameterRepDetailEnum.LATE_FREE);
        }
        return BeanMapping.copy(parmBonusCashbackCharge, ParmBonusCashbackCharResDTO.class);
    }

    @Override
    @Transactional(rollbackFor = Exception.class,propagation = Propagation.REQUIRED)
    //@ShardingSphereTransactionType(TransactionType.XA)
    protected boolean updateDb(ParmModificationRecord parmModificationRecord) throws Exception {
        ParmBonusCashbackCharge parmBonusCashbackCharge = JSON.parseObject(parmModificationRecord.getParmBody(), ParmBonusCashbackCharge.class);
        parmBonusCashbackCharge.initUpdateDateTime();
        int i = cashbackChargeMapper.updateByPrimaryKeySelective(parmBonusCashbackCharge);
        return i > 0;
    }

    @Override
    @Transactional(rollbackFor = Exception.class,propagation = Propagation.REQUIRED)
    //@ShardingSphereTransactionType(TransactionType.XA)
    protected boolean insertDb(ParmModificationRecord parmModificationRecord) throws Exception {
        ParmBonusCashbackCharge parmBonusCashbackCharge = JSON.parseObject(parmModificationRecord.getParmBody(), ParmBonusCashbackCharge.class);
        parmBonusCashbackCharge.initCreateDateTime();
        parmBonusCashbackCharge.initUpdateDateTime();
        int i = cashbackChargeMapper.insertSelective(parmBonusCashbackCharge);
        return i > 0;
    }

    @Override
    @Transactional(rollbackFor = Exception.class,propagation = Propagation.REQUIRED)
    //@ShardingSphereTransactionType(TransactionType.XA)
    protected boolean deleteDb(ParmModificationRecord parmModificationRecord) throws Exception {
        ParmBonusCashbackCharge parmBonusCashbackCharge = JSON.parseObject(parmModificationRecord.getParmBody(), ParmBonusCashbackCharge.class);
        int i = cashbackChargeMapper.deleteByPrimaryKey(parmBonusCashbackCharge.getId());
        return i > 0;
    }
}
