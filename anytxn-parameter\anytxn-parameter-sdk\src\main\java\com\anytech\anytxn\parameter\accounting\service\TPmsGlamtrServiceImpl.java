package com.anytech.anytxn.parameter.accounting.service;

import com.alibaba.fastjson.JSON;
import com.anytech.anytxn.business.base.audit.AbstractParameterService;
import com.anytech.anytxn.business.base.audit.ParmModificationRecord;
import com.github.pagehelper.Page;
import com.github.pagehelper.PageHelper;
import com.anytech.anytxn.common.core.annotation.db.DeleteParameterAnnotation;
import com.anytech.anytxn.common.core.annotation.db.InsertParameterAnnotation;
import com.anytech.anytxn.common.core.annotation.db.UpdateParameterAnnotation;
import com.anytech.anytxn.common.core.base.ParameterCompare;
import com.anytech.anytxn.common.core.base.PageResultDTO;
import com.anytech.anytxn.common.core.utils.BeanMapping;
import com.anytech.anytxn.common.core.utils.OrgNumberUtils;
import com.anytech.anytxn.common.sequence.utils.Number16IdGen;
import com.anytech.anytxn.common.core.utils.TenantUtils;
import com.anytech.anytxn.parameter.base.accounting.domain.dto.TPmsGlamtrDTO;
import com.anytech.anytxn.parameter.base.accounting.service.ITPmsGlamtrService;
import com.anytech.anytxn.parameter.base.common.enums.AnyTxnParameterRespCodeEnum;
import com.anytech.anytxn.parameter.base.common.enums.ParameterRepDetailEnum;
import com.anytech.anytxn.parameter.base.common.exception.AnyTxnParameterException;
import com.anytech.anytxn.parameter.accounting.mapper.TPmsGlamtrMapper;
import com.anytech.anytxn.parameter.accounting.mapper.TPmsGlamtrSelfMapper;
import com.anytech.anytxn.parameter.base.accounting.domain.model.TPmsGlamtr;
import org.apache.commons.lang3.ObjectUtils;
//import org.apache.shardingsphere.transaction.annotation.ShardingSphereTransactionType;
//import org.apache.shardingsphere.transaction.core.TransactionType;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.cglib.beans.BeanCopier;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;


/**
 * @description: 税率参数
 * @author: ZXL
 * @create: 2019-10-09 14:27
 */
@Service("parm_pms_glamtr_serviceImpl")
public class TPmsGlamtrServiceImpl extends AbstractParameterService implements ITPmsGlamtrService {

    private static final Logger logger = LoggerFactory.getLogger(TPmsGlamtrServiceImpl.class);

    @Autowired
    private TPmsGlamtrSelfMapper tPmsGlamtrSelfMapper;
    @Autowired
    private TPmsGlamtrMapper tPmsGlamtrMapper;

    @Autowired
    private Number16IdGen numberIdGenerator;

    /**
     * 根据交易码,核算状态 查询 增值税率参数表
     *
     * @return TPmsGlamtrDTO
     * @throws
     */
    @Override
    public TPmsGlamtrDTO selectByTxnCodeAndFinanceStatus(String organizationNumber, String branchid, String txnCode
                                                        ) throws AnyTxnParameterException {

        TPmsGlamtr tPmsGlamtr = tPmsGlamtrSelfMapper.selectByTxnCodeAndFinanceStatus(organizationNumber, branchid,
                txnCode);
        if (tPmsGlamtr == null) {
            return null;
        }
        return BeanMapping.copy(tPmsGlamtr, TPmsGlamtrDTO.class);
    }

    @Override
    public PageResultDTO<TPmsGlamtrDTO> page(int page, int rows,String organizationNumber,String txnCode,String taxRate) {
        logger.info("分页查询增值税率参数表列表，当前页：{}，每页展示条数：{}", page, rows);
        Integer taxRateInt = null;
        if(!StringUtils.isEmpty(taxRate)){
            try {
                taxRateInt = Integer.parseInt(taxRate);
            }catch (NumberFormatException e){
                throw new AnyTxnParameterException(AnyTxnParameterRespCodeEnum.PARAMETER_EXCEPTION, ParameterRepDetailEnum.NUMBER_ERROR);
            }
        }
        Map<String,Object> map = new HashMap<>(8);
        map.put("organizationNumber", StringUtils.isEmpty(organizationNumber) ? OrgNumberUtils.getOrg() : organizationNumber);
        map.put("txnCode",txnCode);
        map.put("taxRate",taxRate);
        map.put("taxRateInt",taxRateInt);
        Page pageInfo =  PageHelper.startPage(page, rows);
        List<TPmsGlamtrDTO> tPmsGlamtrDTOList = null;
        try {
            List<TPmsGlamtr> tPmsGlamtrList = tPmsGlamtrSelfMapper.selectByCondition(map);
            if (!CollectionUtils.isEmpty(tPmsGlamtrList)) {
                tPmsGlamtrDTOList = new ArrayList<>();
                BeanCopier beanCopier = BeanCopier.create(TPmsGlamtr.class, TPmsGlamtrDTO.class, false);
                for (TPmsGlamtr tPmsGlamtr : tPmsGlamtrList) {
                    TPmsGlamtrDTO tPmsGlamtrDTO = new TPmsGlamtrDTO();
                    beanCopier.copy(tPmsGlamtr, tPmsGlamtrDTO, null);
                    tPmsGlamtrDTOList.add(tPmsGlamtrDTO);
                }
            }
            return new PageResultDTO<>(page, rows, pageInfo.getTotal(), pageInfo.getPages(),tPmsGlamtrDTOList);
        } catch (Exception e) {
            logger.error("分页查询流水拆分分录参数失败，当前页：{}，每页展示条数：{},错误信息：{}", page, rows, e.getMessage());
            /*throw new AnyTXNBusRuntimeException(TransactionEnum.DATABASE_ERROR.getCode(),
                    TransactionEnum.DATABASE_ERROR.getReason());*/
            throw new AnyTxnParameterException(AnyTxnParameterRespCodeEnum.D_PAGE_TPMS_GLAMTR_FAULT);
        }
    }

    @Override
    public TPmsGlamtrDTO detail(String id) {
        TPmsGlamtr tPmsGlamtr = tPmsGlamtrMapper.selectByPrimaryKey(id);
        if(tPmsGlamtr != null) {
            TPmsGlamtrDTO tPmsGlamtrDTO = new TPmsGlamtrDTO();
            BeanCopier beanCopier = BeanCopier.create(TPmsGlamtr.class, TPmsGlamtrDTO.class, false);
            beanCopier.copy(tPmsGlamtr, tPmsGlamtrDTO, null);
            return tPmsGlamtrDTO;
        }
        return null;
    }

    @Override
    @DeleteParameterAnnotation(tableName = "parm_pms_glamtr", tableDesc = "Vat Rate Parameters")
    public ParameterCompare remove(String id) {
        if (null == id) {
            throw new AnyTxnParameterException(AnyTxnParameterRespCodeEnum.P_PARAMETER_EMPTY_FAULT);
        }
        TPmsGlamtr tPmsGlamtr = tPmsGlamtrMapper.selectByPrimaryKey(id);
        if(ObjectUtils.isEmpty(tPmsGlamtr)){
            throw new AnyTxnParameterException(AnyTxnParameterRespCodeEnum.D_DATA_NOT_EXISTS_TEMPLATE_FAULT);
        }

        return ParameterCompare
                .getBuilder()
                .withBefore(tPmsGlamtr)
                .build(TPmsGlamtr.class);
    }

    @Override
    @InsertParameterAnnotation(tableName = "parm_pms_glamtr", tableDesc = "Vat Rate Parameters")
    public ParameterCompare add(TPmsGlamtrDTO data) {
        if(ObjectUtils.isEmpty(data)){
            throw new AnyTxnParameterException(AnyTxnParameterRespCodeEnum.P_PARAMETER_DATA_NOT_EXIST);
        }
        TPmsGlamtr tPmsGlamtr = new TPmsGlamtr();
        BeanCopier beanCopier = BeanCopier.create(TPmsGlamtrDTO.class, TPmsGlamtr.class, false);
        beanCopier.copy(data, tPmsGlamtr, null);
        tPmsGlamtr.setBranchid("DINERS");
        tPmsGlamtr.setId(numberIdGenerator.generateId(TenantUtils.getTenantId())+"");
        tPmsGlamtr.setCreateTime(LocalDateTime.now());
        tPmsGlamtr.setUpdateTime(LocalDateTime.now());


        return ParameterCompare.getBuilder().withAfter(tPmsGlamtr)
                .build(TPmsGlamtr.class);
    }

    @Override
    @UpdateParameterAnnotation(tableName = "parm_pms_glamtr", tableDesc = "Vat Rate Parameters")
    public ParameterCompare update(TPmsGlamtrDTO data) {
        if(ObjectUtils.isEmpty(data)){
            throw new AnyTxnParameterException(AnyTxnParameterRespCodeEnum.P_PARAMETER_EMPTY_FAULT);
        }
        TPmsGlamtr tPmsGlamtr1 = tPmsGlamtrMapper.selectByPrimaryKey(String.valueOf(data.getId()));

        if(ObjectUtils.isEmpty(tPmsGlamtr1)){
            throw new AnyTxnParameterException(AnyTxnParameterRespCodeEnum.D_DATA_NOT_EXISTS_TEMPLATE_FAULT);
        }
        TPmsGlamtr tPmsGlamtr = new TPmsGlamtr();
        BeanCopier beanCopier = BeanCopier.create(TPmsGlamtrDTO.class, TPmsGlamtr.class, false);
        beanCopier.copy(data, tPmsGlamtr, null);
        tPmsGlamtr.setBranchid("DINERS");
        tPmsGlamtr.setId(String.valueOf(data.getId()));

        return  ParameterCompare.getBuilder()
                .withAfter(tPmsGlamtr)
                .withBefore(tPmsGlamtr1)
                .build(TPmsGlamtr.class);
    }


    @Override
    @Transactional(rollbackFor = Exception.class,propagation = Propagation.REQUIRED)
    //@ShardingSphereTransactionType(TransactionType.XA)
    protected boolean updateDb(ParmModificationRecord parmModificationRecord) {
        TPmsGlamtr tPmsGlamtr = JSON.parseObject(parmModificationRecord.getParmBody(), TPmsGlamtr.class);
        tPmsGlamtr.initUpdateDateTime();

        try {
            tPmsGlamtrMapper.updateByPrimaryKeySelective(tPmsGlamtr);
        } catch (Exception e) {
            logger.error("更新增值税率参数表失败", e);
            throw new AnyTxnParameterException(AnyTxnParameterRespCodeEnum.D_INSERT_TPMS_GLAMS_CONTROL_FAULT);
        }
        return true;
    }


    @Override
    @Transactional(rollbackFor = Exception.class,propagation = Propagation.REQUIRED)
    //@ShardingSphereTransactionType(TransactionType.XA)
    protected boolean insertDb(ParmModificationRecord parmModificationRecord) {
        TPmsGlamtr tPmsGlamtr = JSON.parseObject(parmModificationRecord.getParmBody(), TPmsGlamtr.class);
        tPmsGlamtr.initUpdateDateTime();
        tPmsGlamtr.initCreateDateTime();
        tPmsGlamtr.setVersionNumber(1L);

        try {
            tPmsGlamtrSelfMapper.insert(tPmsGlamtr);
        } catch (Exception e) {
            logger.error("新增增值税率参数表失败", e);
            throw new AnyTxnParameterException(AnyTxnParameterRespCodeEnum.D_INSERT_TPMS_GLAMS_CONTROL_FAULT);
        }
        return true;
    }


    @Override
    @Transactional(rollbackFor = Exception.class,propagation = Propagation.REQUIRED)
    //@ShardingSphereTransactionType(TransactionType.XA)
    protected boolean deleteDb(ParmModificationRecord parmModificationRecord) {
        TPmsGlamtr tPmsGlamtr = JSON.parseObject(parmModificationRecord.getParmBody(), TPmsGlamtr.class);

        try {
            tPmsGlamtrMapper.deleteByPrimaryKey(tPmsGlamtr.getId());
        } catch (Exception e) {
            logger.error("删除增值税率参数表失败", e);
            throw new AnyTxnParameterException(AnyTxnParameterRespCodeEnum.D_INSERT_TPMS_GLAMS_CONTROL_FAULT);
        }
        return true;
    }

}
