package com.anytech.anytxn.business.base.monetary.utils;

import com.anytech.anytxn.business.base.monetary.annotation.InsertCacheAnnotation;
import com.anytech.anytxn.business.base.monetary.annotation.MapperColumnAnnotation;
import com.anytech.anytxn.business.base.monetary.annotation.UpdateCacheAnnotation;
import com.anytech.anytxn.business.base.monetary.domain.dto.SqlCacheDTO;
import com.anytech.anytxn.business.base.monetary.enums.SqlTypeEnum;
import com.anytech.anytxn.business.base.monetary.exception.AnyTxnCustAccountException;
import com.google.common.collect.Lists;
import com.google.common.collect.Sets;
import com.anytech.anytxn.business.base.monetary.annotation.MapperTableAnnotation;
import com.anytech.anytxn.business.base.monetary.enums.AnyTxnCustAccountRespCodeEnum;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.tuple.Pair;
import org.springframework.util.CollectionUtils;

import java.lang.reflect.Field;
import java.lang.reflect.Method;
import java.lang.reflect.ParameterizedType;
import java.util.*;

/**
 * <AUTHOR>
 * @date 2020/11/6
 */
@Slf4j
public class ReflectSqlUtils {

    private static final String SQL_AND = " AND ";

    public static Map<String, SqlCacheDTO> getSql(Class<?> clazz){
        Map<String, SqlCacheDTO> result = new HashMap<>(4);

        Method[] methods = clazz.getDeclaredMethods();
        for (Method method : methods) {
            SqlCacheDTO sqlCacheDTO = null;
            InsertCacheAnnotation insertCache = method.getAnnotation(InsertCacheAnnotation.class);
            if (insertCache != null) {
                try {
                    sqlCacheDTO = new SqlCacheDTO();
                    getSql(method, insertCache.columns(), SqlTypeEnum.INSERT, sqlCacheDTO);
                }catch (ClassNotFoundException e){
                    sqlCacheDTO = null;
                    log.error("类: {} 方法: {} 返回值类型无法被发现，无法进行反射sql!", clazz.getSimpleName(), method.getName());
                }
            }
            UpdateCacheAnnotation updateCache = method.getAnnotation(UpdateCacheAnnotation.class);
            if (updateCache != null) {
                try {
                    sqlCacheDTO = sqlCacheDTO == null ? new SqlCacheDTO() : sqlCacheDTO;
                    getSql(method, updateCache.columns(), SqlTypeEnum.UPDATE, sqlCacheDTO);
                }catch (ClassNotFoundException e){
                    sqlCacheDTO = null;
                    log.error("类: {} 方法: {} 返回值类型无法被发现，无法进行反射sql!", clazz.getSimpleName(), method.getName());
                }
            }
            if (sqlCacheDTO != null) {
                result.put(sqlCacheDTO.getMethodName(), sqlCacheDTO);
            }
        }

        return result;
    }

    private static void getSql(Method method, String columns, SqlTypeEnum sqlTypeEnum, SqlCacheDTO sqlCacheDTO) throws ClassNotFoundException {

        // 记录方法名称（名称禁止重复）
        sqlCacheDTO.setMethodName(method.getName());
        Class<?> returnClass = method.getReturnType();

        // 集合（继承子Collection）处理
        if (Collection.class.isAssignableFrom(returnClass)) {
            // 反射获取方法返回值中集合的属性类型
            String className = ((ParameterizedType) method.getGenericReturnType()).getActualTypeArguments()[0].getTypeName();
            returnClass = Class.forName(className);
        }

        // 根据对象类型内部注解获取表明和描述
        MapperTableAnnotation annotation = returnClass.getAnnotation(MapperTableAnnotation.class);
        // 表名
        String tableName = annotation.tableName();
        sqlCacheDTO.setTableName(tableName);
        // 表描述
        String description = annotation.description();
        sqlCacheDTO.setDescription(description);

        if (sqlTypeEnum == SqlTypeEnum.INSERT) {
            String sql = getInsertSql(returnClass, tableName, columns);
            sqlCacheDTO.setSql(sql);
        } else if (sqlTypeEnum == SqlTypeEnum.UPDATE){
            String sql = getUpdateSql(returnClass, tableName, columns);
            sqlCacheDTO.setSql(sql);
        }
    }

    /**
     * 基于注解生成插入sql语句
     */
    private static String getInsertSql(Class<?> clazz, String tableName, String columns){
        StringBuilder sql = new StringBuilder();
        sql.append("INSERT INTO ")
                .append(tableName)
                .append(" (");

        StringBuilder keys = new StringBuilder();
        StringBuilder values = new StringBuilder();
        // 自定义更新列集合
        Set<String> cs = StringUtils.isEmpty(columns) ? Sets.newHashSet() : Sets.newHashSet(Arrays.asList(columns.split(",")));
        // 遍历所有字段
        List<Field> allFieldList = new ArrayList<>();
        while(clazz != null){
            allFieldList.addAll(new ArrayList<>(Arrays.asList(clazz.getDeclaredFields())));
            clazz = clazz.getSuperclass();
        }
        Field[] fields = new Field[allFieldList.size()];
        allFieldList.toArray(fields);
        for (Field field: fields) {
            // 如果注解定义了列名称，且当前字段不包含在内则退出
            if (!CollectionUtils.isEmpty(cs) && !cs.contains(field.getName())) {
                continue;
            }

            // 支持自定义注解名称
            MapperColumnAnnotation fa = field.getAnnotation(MapperColumnAnnotation.class);
            // 默认格式化成列名，如: userName -> USER_NAME
            String columnName = getColumnName(field.getName());
            if (fa != null) {
                // 需要忽略到跳过
                if (fa.isPass()) {
                    continue;
                }
                // 如果注解含名称，使用注解上的名称
                columnName = StringUtils.isNotEmpty(fa.value()) ? fa.value() : columnName;
            }
            keys.append(columnName).append(",");
            values.append(":").append(field.getName()).append(",");
        }

        if (keys.length() == 0) {
            throw new AnyTxnCustAccountException(AnyTxnCustAccountRespCodeEnum.P_DATE_ERROR, "无可插入列！");
        }

        String ks = keys.toString();
        String vs = values.toString();
        sql.append(ks, 0, ks.length() - 1)
                .append(") VALUES (")
                .append(vs, 0, vs.length() - 1)
                .append(")");

        return sql.toString();
    }

    /**
     * 基于注解生成更新sql语句
     */
    private static String getUpdateSql(Class clazz, String tableName, String columns){
        StringBuilder sql = new StringBuilder();
        sql.append("UPDATE ")
                .append(tableName)
                .append(" set ");

        StringBuilder kvs = new StringBuilder();
        Field[] fields = clazz.getDeclaredFields();
        // 条件容器
        List<Pair<String, String>> condition = Lists.newArrayList();
        // 自定义更新列集合
        Set<String> css = StringUtils.isEmpty(columns) ? Sets.newHashSet() : Sets.newHashSet(Arrays.asList(columns.split(",")));
        // 标记是否含主键，更新操作必须有主键作为条件
        // 有的表可能存在多个主键
        boolean hadKey = false;
        boolean hadColumns = false;
        for (Field field: fields) {
            // 如果注解定义了列名称，且当前字段不包含在内则退出
            if (!CollectionUtils.isEmpty(css) && !css.contains(field.getName())) {
                continue;
            }

            // 获取注解中的标记
            MapperColumnAnnotation fa = field.getAnnotation(MapperColumnAnnotation.class);
            // 默认格式化成列名，如: userName -> USER_NAME
            String columnName = getColumnName(field.getName());
            if (fa != null) {
                // 需要忽略到跳过
                if (fa.isPass()) {
                    continue;
                }
                // 如果注解含名称，使用注解上的名称
                columnName = StringUtils.isNotEmpty(fa.value()) ? fa.value() : columnName;
                // 如果是主键，标记到条件容器不进行sql拼接
                if (fa.isKey()) {
                    condition.add(Pair.of(columnName, field.getName()));
                    hadKey = true;
                    continue;
                    // 如果是乐观锁，拼写 +1 操作并标记到条件容器
                } else if (fa.isLock()) {
                    hadColumns = true;
                    kvs.append(columnName).append(" = ").append(columnName).append(" + 1,");
                    condition.add(Pair.of(columnName, field.getName()));
                    continue;
                    // 如果是条件，标记到条件容器不进行sql拼接
                } else if (fa.isCondition()) {
                    condition.add(Pair.of(columnName, field.getName()));
                    continue;
                }
            }
            hadColumns = true;
            kvs.append(columnName).append(" = :").append(field.getName()).append(",");
        }

        // 如果没有主键抛出异常
        if (!hadKey) {
            throw new AnyTxnCustAccountException(AnyTxnCustAccountRespCodeEnum.P_DATE_ERROR, "缺少主键注解");
        }

        if (!hadColumns) {
            throw new AnyTxnCustAccountException(AnyTxnCustAccountRespCodeEnum.P_DATE_ERROR, "无可更新列！");
        }

        // 拼接sql赋值部分
        String ss = kvs.toString();
        // 去掉最后一个逗号
        sql.append(ss, 0, ss.length() - 1)
                .append(" WHERE ");

        // 拼接sql条件部分
        StringBuilder cb = new StringBuilder();
        condition.forEach(x-> cb.append(x.getLeft()).append(" = :").append(x.getRight()).append(SQL_AND));
        String cs = cb.toString();
        // 去掉最后一个SQL_AND
        sql.append(cs, 0, cs.length() - SQL_AND.length());
        return sql.toString();
    }

    /**
     * 格式化获取成列名称 如：userName -> USER_NAME
     */
    private static String getColumnName(String fieldName){
        StringBuilder name = new StringBuilder();
        char[] chars = fieldName.toCharArray();
        for (char aChar : chars) {
            if (Character.isUpperCase(aChar)) {
                name.append("_").append(aChar);
            } else {
                name.append(Character.toUpperCase(aChar));
            }
        }
        return name.toString();
    }

//    public static void main(String[] args) throws NoSuchMethodException, ClassNotFoundException {
//        Map<String, SqlCache> sql = getSql(CustomerBO.class);
//
//        for (Map.Entry<String, SqlCache> entry : sql.entrySet()) {
//            SqlCache value = entry.getValue();
//            System.out.println(entry.getKey());
//        }
//    }
}
