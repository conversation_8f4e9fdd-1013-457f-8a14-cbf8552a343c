package com.anytech.anytxn.parameter.authorization.controller;

import com.anytech.anytxn.parameter.base.authorization.domain.dto.CountryMccDTO;
import com.anytech.anytxn.parameter.base.authorization.service.ICountryMccService;
import com.anytech.anytxn.parameter.base.common.enums.ParameterRepDetailEnum;
import com.anytech.anytxn.common.core.base.AnyTxnHttpResponse;
import com.anytech.anytxn.common.core.base.BizBaseController;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.Valid;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.Map;

/**
 * 国家MCC
 * <AUTHOR>
 * @date 2021/11/8
 */

@RestController
@Tag(name = "国家MCC服务")
public class CountryMccController extends BizBaseController {

    @Autowired
    private ICountryMccService countryMccService;

//    /**
//     * @description 分页查询国家MCC列表
//     * <AUTHOR>
//     * @date 2021/11/8
//     * @param page ye
//     * @param rows rows
//     */
//
//    @Operation(summary = "分页查询国家MCC信息")
//    @GetMapping("/param/countryMccs/{page}/{rows}")
//    @ApiImplicitParams(
//            {@Parameter(name = "page", description = "当前页", example = "1"),
//             @Parameter(name = "rows", description = "每页大小", example = "8")})
//    public AnyTxnHttpResponse<PageResultDTO<CountryMccDTO>> getCountryMccList(@PathVariable("page") int page ,
//                                                                           @PathVariable("rows") int rows,
//                                                                           @RequestParam(required = false) String organizationNumber) {
//       PageResultDTO<CountryMccDTO> result = countryMccService.findListCountryMcc(page, rows, organizationNumber);
//        return AnyTxnHttpResponse.success(result);
//    }

    
    /**
     * @description 根据主键查询国家MCC信息
     * <AUTHOR>
     * @date 2021/11/8
     * @param id id
     * @return  CountryMccDTO
     */
    
    @Operation(summary = "根据主键查询国家MCC信息")
    @GetMapping("/param/countryMcc/{id}")
    @Parameter(name = "id", description = "技术主键")
    public AnyTxnHttpResponse<CountryMccDTO> getCountryMccDTO(@PathVariable Long id) {
        CountryMccDTO countryMccDTO = countryMccService.findCountryMcc(id);
        return AnyTxnHttpResponse.success(countryMccDTO);
    }


    /**
     * @description 删除国家MCC信息
     * <AUTHOR>
     * @date 2021/11/8
     * @param id id
     * @return com.anytech.anytxn.common.core.web.AnyTxnHttpResponse
     */
    
    @Operation(summary = "删除国家MCC信息")
    @DeleteMapping("/param/countryMcc/{id}")
    public AnyTxnHttpResponse cancelCountryMcc(@PathVariable Long id) {
        Boolean flag = countryMccService.removeCountryMcc(id);
        return AnyTxnHttpResponse.success(flag, ParameterRepDetailEnum.DEL.message());
    }

    /**
     * @description 新增国家MCC信息
     * <AUTHOR>
     * @date 2021/11/8
     * @param countryMccDTO c
     * @return com.anytech.anytxn.common.core.web.AnyTxnHttpResponse
     */

    @Operation(summary = "新增国家MCC信息")
    @PostMapping("/param/countryMcc")
    public AnyTxnHttpResponse create(@Valid @RequestBody CountryMccDTO countryMccDTO) {
        Boolean flag = countryMccService.addCountryMcc(countryMccDTO);
        return AnyTxnHttpResponse.success(flag,ParameterRepDetailEnum.CREATE.message());
    }

    /**
     * @description 国家MCC信息修改
     * <AUTHOR>
     * @date 2021/11/8
     * @param countryMccDTO c
     * @return com.anytech.anytxn.common.core.web.AnyTxnHttpResponse
     */

    @Operation(summary = "国家MCC信息修改")
    @PutMapping("/param/countryMcc")
    public AnyTxnHttpResponse modify(@Valid @RequestBody CountryMccDTO countryMccDTO) {
        Boolean flag = countryMccService.modifyCountryMcc(countryMccDTO);
        return AnyTxnHttpResponse.success(flag,ParameterRepDetailEnum.UPDATE.message());
    }

    /**
     * @description 查询所有countryMcc
     * <AUTHOR>
     * @date 2021/11/8
     * @return AnyTxnHttpResponse<List<String>>
     */
    @Operation(summary = "查询所有国家MCC")
    @GetMapping("/param/countryMcc/countryMccs")
    public AnyTxnHttpResponse<List<Map<String,String>>> selectAllMcc(@RequestParam(required = false) String isoGeoCodeNumeric) {
        List<Map<String,String>> list = countryMccService.getMccByCountry(isoGeoCodeNumeric);
        return AnyTxnHttpResponse.success(list);
    }

    /**
     * @description 查询所有countryMcc根据国家名称
     * <AUTHOR>
     * @date 2021/11/8
     * @return AnyTxnHttpResponse<List<String>>
     */
    @Operation(summary = "查询所有国家MCC根据国家名称")
    @GetMapping("/param/countryMcc/countryName")
    public AnyTxnHttpResponse<List<CountryMccDTO>> selectAllByCountryName(String countryName) {
        List<CountryMccDTO> countryMccByCountryName = countryMccService.findCountryMccByCountryName(countryName);
        return AnyTxnHttpResponse.success(countryMccByCountryName);
    }

    /**
     * @description 查询所有countryMcc根据mcc
     * <AUTHOR>
     * @date 2021/11/8
     * @return AnyTxnHttpResponse<List<String>>
     */
    @Operation(summary = "查询所有国家MCC根据mcc")
    @GetMapping("/param/countryMcc/mcc")
    public AnyTxnHttpResponse<List<CountryMccDTO>> selectAllByMcc(String mcc) {
        List<CountryMccDTO> countryMccBymcc = countryMccService.findCountryMccBymcc(mcc);
        return AnyTxnHttpResponse.success(countryMccBymcc);
    }

    /**
     * @description 新增一组国家MCC信息
     * <AUTHOR>
     * @date 2021/11/8
     * @param countryMccDTO c
     * @return com.anytech.anytxn.common.core.web.AnyTxnHttpResponse
     */

    @Operation(summary = "新增一组国家MCC信息")
    @PostMapping("/param/countryMccList")
    public AnyTxnHttpResponse createList(@Valid @RequestBody CountryMccDTO countryMccDTO) {
        Boolean flag = countryMccService.addCountryMccList(countryMccDTO);
        return AnyTxnHttpResponse.success(flag,ParameterRepDetailEnum.CREATE.message());
    }


}
