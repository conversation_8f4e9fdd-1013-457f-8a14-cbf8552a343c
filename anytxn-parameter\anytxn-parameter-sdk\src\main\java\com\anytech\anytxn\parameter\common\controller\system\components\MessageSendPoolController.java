package com.anytech.anytxn.parameter.common.controller.system.components;

import com.anytech.anytxn.common.core.base.PageResultDTO;
import com.anytech.anytxn.common.core.utils.OrgNumberUtils;
import com.anytech.anytxn.common.core.base.AnyTxnHttpResponse;
import com.anytech.anytxn.common.core.base.BizBaseController;
import com.anytech.anytxn.parameter.base.common.enums.ParameterRepDetailEnum;
import com.anytech.anytxn.parameter.base.common.domain.dto.MsgGatherDTO;
import com.anytech.anytxn.parameter.base.common.domain.dto.SmsTemplateDTO;
import com.anytech.anytxn.parameter.base.common.service.IParmMsgGatherService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * Description:消息发送集
 * date: 2021/3/19 10:16
 *
 * <AUTHOR>
 */
@RestController
public class MessageSendPoolController extends BizBaseController {

    @Autowired
    private IParmMsgGatherService parmMsgGatherService;

    /**
     * 添加
     * @param  msgGatherDTO
     * @return MsgGatherDTO
     * @throws
     */
    @PostMapping(value = "/param/addMsgGather")
    public AnyTxnHttpResponse<Object> add(@RequestBody MsgGatherDTO msgGatherDTO){
        return AnyTxnHttpResponse.success(parmMsgGatherService.addMsgGather(msgGatherDTO), ParameterRepDetailEnum.CREATE.message());
    }

    /**
     * 修改
     * @param  msgGatherDTO
     * @return MsgGatherDTO
     * @throws
     */
    @PutMapping(value = "/param/modifyMsgGather")
    public AnyTxnHttpResponse<Object> modify(@RequestBody MsgGatherDTO msgGatherDTO){
        return AnyTxnHttpResponse.success(parmMsgGatherService.modifyMsgGather(msgGatherDTO), ParameterRepDetailEnum.UPDATE.message());
    }

    /**
     * 分页查询
     * @param  pageNum pageSize  organizationNumber
     * @return MsgGatherDTO
     * @throws
     */
    @GetMapping(value = "/param/msgGather/pageNum/{pageNum}/pageSize/{pageSize}")
    public AnyTxnHttpResponse<PageResultDTO<MsgGatherDTO>> findByPage(@PathVariable(value = "pageNum") Integer pageNum,
                                                                      @PathVariable(value = "pageSize") Integer pageSize,
                                                                      @RequestParam(value = "gatherCode",required = false) String gatherCode,
                                                                      @RequestParam(value = "gatherDesc",required = false) String gatherDesc,
                                                                      @RequestParam(value = "organizationNumber",required = false) String organizationNumber){
        return AnyTxnHttpResponse.success(parmMsgGatherService.findByPage(pageNum,pageSize,gatherCode, gatherDesc,OrgNumberUtils.getOrg(organizationNumber)));
    }

    /**
     * 通过消息集编号查询
     * @param  id
     * @return MsgGatherDTO
     * @throws
     */
    @GetMapping(value = "/param/findMsgGather/gatherCode/{id}")
    public AnyTxnHttpResponse<MsgGatherDTO> findByGatherCode(@PathVariable String id){
        return AnyTxnHttpResponse.success(parmMsgGatherService.findByGatherCode(id));
    }

    /**
     * 通过消息集编号删除
     * @param id
     * @return Boolean
     * @throws
     */
    @DeleteMapping(value = "/param/deleteMsgGather/gatherCode/{id}")
    public AnyTxnHttpResponse<Object> delete(@PathVariable String id){
        return AnyTxnHttpResponse.success(parmMsgGatherService.removeMsgGather(id));
    }

    /**
     * 通过通道类型获取相应模板信息
     * @param  channelCode organizationNumber
     * @return  List<SmsTemplateDTO>
     * @throws
     */
    @GetMapping(value = "/param/findTemplateByChannel/channelCode/{channelCode}")
    public AnyTxnHttpResponse<List<SmsTemplateDTO>> findTemplateByChannel(@PathVariable String channelCode,
                                                                          @RequestParam("organizationNumber") String organizationNumber){
        return AnyTxnHttpResponse.success(parmMsgGatherService.findTemplateByChannel(channelCode,OrgNumberUtils.getOrg(organizationNumber)));
    }

    /**
     * 查询所有消费集
     * @param   organizationNumber
     * @return  List<SmsTemplateDTO>
     * @throws
     */
    @GetMapping(value = "/param/findAllMsg/{organizationNumber}")
    public AnyTxnHttpResponse<List<MsgGatherDTO>> findAllMsg(@PathVariable String organizationNumber){
        return AnyTxnHttpResponse.success(parmMsgGatherService.findAll(OrgNumberUtils.getOrg(organizationNumber)));
    }
}
