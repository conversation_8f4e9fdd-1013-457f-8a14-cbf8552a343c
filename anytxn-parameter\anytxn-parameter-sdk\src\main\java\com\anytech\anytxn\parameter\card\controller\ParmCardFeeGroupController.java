package com.anytech.anytxn.parameter.card.controller;

import io.swagger.v3.oas.annotations.tags.Tag;
import io.swagger.v3.oas.annotations.Operation;
import com.anytech.anytxn.common.core.base.ParameterCompare;
import com.anytech.anytxn.common.core.base.PageResultDTO;
import com.anytech.anytxn.common.core.base.AnyTxnHttpResponse;
import com.anytech.anytxn.common.core.base.BizBaseController;
import com.anytech.anytxn.parameter.base.card.domain.dto.CardFeeGroupInfoReqDTO;
import com.anytech.anytxn.parameter.base.card.domain.dto.CardFeeGroupInfoResDTO;
import com.anytech.anytxn.parameter.base.card.service.IParmCardFeeGroupService;
import com.anytech.anytxn.parameter.base.common.enums.ParameterRepDetailEnum;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import jakarta.validation.Valid;

/**
 * <pre>
 * Description  卡片费用组参数管理
 * Dept:		江融信
 * Author:		lichao
 * Version:		1.0
 * Create at:	2020-08-18 11:20
 *
 * 修改历史:
 * 日期    作者    版本  修改描述
 * ------------------------------------------------------------------
 **/
@Tag(name = "卡片费用组参数管理")
@RestController
public class ParmCardFeeGroupController extends BizBaseController {

    @Autowired
    private IParmCardFeeGroupService parmCardFeeGroupService;


    /**
     * 创建卡片费用组参数
     * @param cardFeeGroupInfoReqDto 卡片费用组参数
     * @return AnyTxnHttpResponse<List<CardFeeGroupInfoDto>>
     */
    @PostMapping(value = "/param/cardFeeGroup")
    @Operation(summary = "创建卡片费用组参数")
    public AnyTxnHttpResponse<Object> create(@Valid @RequestBody CardFeeGroupInfoReqDTO cardFeeGroupInfoReqDto) {
        ParameterCompare list = parmCardFeeGroupService.addCardFeeGroup(cardFeeGroupInfoReqDto);
        return AnyTxnHttpResponse.success(list);
    }


    /**
     * 删除卡片费用组参数，通过机构号和产品编码
     * @param organizationNumber 机构号
     * @param tableId 参数表ID
     * @return AnyTxnHttpResponse<Boolean>
     */
    @DeleteMapping(value = "/param/cardFeeGroup/orgNumber/{organizationNumber}/tableId/{tableId}")
    @Operation(summary="根据机构号和参表ID,删除卡片费用组", description = "需传入organizationNumber和tableId")
    public AnyTxnHttpResponse<Object> remove(@PathVariable String organizationNumber,
                                              @PathVariable String tableId) {
        ParameterCompare bool = parmCardFeeGroupService.removeCardFeeGroup(organizationNumber,tableId);
        return AnyTxnHttpResponse.success(bool, ParameterRepDetailEnum.DEL.message());
    }

    /**
     * 更新卡片费用组参数
     * @param cardProductCurrencyRelationReq 卡产品信息请求数据
     * @return HttpApiResponse<CardProductInfoRes>
     */
    @PutMapping(value = "/param/cardFeeGroup")
    @Operation(summary="根据id更新卡片费用组", description = "")
    public AnyTxnHttpResponse<Object> modify(@Valid @RequestBody CardFeeGroupInfoReqDTO cardProductCurrencyRelationReq) {
        ParameterCompare cardFeeGroupInfoDTOS = parmCardFeeGroupService.modifyCardFeeGroup(cardProductCurrencyRelationReq);
        return AnyTxnHttpResponse.success(cardFeeGroupInfoDTOS,ParameterRepDetailEnum.UPDATE.message());

    }

    /**
     * 根据机构号和参数表ID获取详情，进行编辑
     * @param organizationNumber 机构号
     * @param tableId 参数表ID
     * @return AnyTxnHttpResponse<CardFeeGroupInfoResDto>
     */
    @Operation(summary="获取卡片费用组详情，通过机构号和参数表ID获", description = "")
    @GetMapping(value = "/param/cardFeeGroup/orgNumber/{organizationNumber}/tableId/{tableId}")
    public AnyTxnHttpResponse<CardFeeGroupInfoResDTO> getByIndex(@PathVariable String organizationNumber,
                                                                 @PathVariable String tableId){
        CardFeeGroupInfoResDTO cardFeeGroupInfoResDto = parmCardFeeGroupService.findByOrgAndTableId(organizationNumber,tableId);
        return AnyTxnHttpResponse.success(cardFeeGroupInfoResDto);

    }


    /**
     * 分页查询卡片费用组参数
     * @param pageNum 当前页数
     * @param pageSize 每页展示条数
     * @return AnyTxnHttpResponse<PageResultDTO<CardFeeGroupInfoResDto>>
     */
    @Operation(summary = "卡片费用组参数分页查询", description = "分页查询")
    @GetMapping(value = "/param/cardFeeGroup/pageNum/{pageNum}/pageSize/{pageSize}")
    public AnyTxnHttpResponse<PageResultDTO<CardFeeGroupInfoResDTO>> getAllPage(@PathVariable(value = "pageNum") Integer pageNum,
                                                                                @PathVariable(value = "pageSize") Integer pageSize,
                                                                                @RequestParam(value = "tableId",required = false) String tableId,
                                                                                @RequestParam(value = "description",required = false) String description,
                                                                                @RequestParam(required = false) String organizationNumber) {
        PageResultDTO<CardFeeGroupInfoResDTO> response = parmCardFeeGroupService.findByPage(pageNum, pageSize,tableId,description, organizationNumber);
        return AnyTxnHttpResponse.success(response);
    }



}
