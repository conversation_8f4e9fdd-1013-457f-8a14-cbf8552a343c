package com.anytech.anytxn.parameter.card.service;

import com.alibaba.fastjson.JSON;
import com.anytech.anytxn.business.base.audit.AbstractParameterService;
import com.anytech.anytxn.business.base.audit.ParmModificationRecord;
import com.anytech.anytxn.parameter.card.mapper.broadcast.ParmAnnualFeeTableMapper;
import com.anytech.anytxn.parameter.card.mapper.broadcast.ParmAnnualFeeTableSelfMapper;
import com.github.pagehelper.Page;
import com.github.pagehelper.PageHelper;
import com.anytech.anytxn.common.core.annotation.db.DeleteParameterAnnotation;
import com.anytech.anytxn.common.core.annotation.db.InsertParameterAnnotation;
import com.anytech.anytxn.common.core.annotation.db.UpdateParameterAnnotation;
import com.anytech.anytxn.common.core.base.ParameterCompare;
import com.anytech.anytxn.common.core.base.PageResultDTO;
import com.anytech.anytxn.common.core.utils.BeanMapping;
import com.anytech.anytxn.common.core.utils.OrgNumberUtils;
import com.anytech.anytxn.common.sequence.utils.Number16IdGen;
import com.anytech.anytxn.common.core.utils.TenantUtils;
import com.anytech.anytxn.parameter.base.card.domain.dto.AnnualFeeTableReqDTO;
import com.anytech.anytxn.parameter.base.card.domain.dto.AnnualFeeTableResDTO;
import com.anytech.anytxn.parameter.base.card.service.IAnnualFeeTableService;
import com.anytech.anytxn.parameter.base.common.enums.AnyTxnParameterRespCodeEnum;
import com.anytech.anytxn.parameter.base.common.enums.ParameterRepDetailEnum;
import com.anytech.anytxn.parameter.base.common.exception.AnyTxnParameterException;
import com.anytech.anytxn.parameter.base.card.domain.model.ParmAnnualFeeTable;
//import org.apache.shardingsphere.transaction.annotation.ShardingSphereTransactionType;
//import org.apache.shardingsphere.transaction.core.TransactionType;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.StringUtils;

import java.math.BigDecimal;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 年费参数 业务接口实现
 * <AUTHOR>
 * @date 2020/3/26
 */
@Service(value = "parm_annual_fee_table_serviceImpl")
public class AnnualFeeTableServiceImpl extends AbstractParameterService implements IAnnualFeeTableService {

    private final Logger log = LoggerFactory.getLogger(AnnualFeeTableServiceImpl.class);

    @Autowired
    private ParmAnnualFeeTableMapper parmAnnualFeeTableMapper;
    @Autowired
    private ParmAnnualFeeTableSelfMapper parmAnnualFeeTableSelfMapper;
    @Autowired
    private Number16IdGen numberIdGenerator;

    /**
     * 查询年费参数
     * @return 年费参数响应参数
     * @param pageNum
     * @param pageSize
     */
    @Override
    public PageResultDTO<AnnualFeeTableResDTO> findAll(Integer pageNum, Integer pageSize,String tableId,String description,String chargeFlag,String fixedAmnt,String organizationNumber){
        BigDecimal fixedAmntDeci = null;
        if(!StringUtils.isEmpty(fixedAmnt)){
            try {
                fixedAmntDeci = new BigDecimal(fixedAmnt);
            }catch (NumberFormatException e){
                throw new AnyTxnParameterException(AnyTxnParameterRespCodeEnum.PARAMETER_EXCEPTION, ParameterRepDetailEnum.NUMBER_ERROR);
            }
        }
        Map<String,Object> map = new HashMap<>(8);
        map.put("organizationNumber", StringUtils.isEmpty(organizationNumber) ? OrgNumberUtils.getOrg() : organizationNumber);
        map.put("tableId",tableId);
        map.put("description",description);
        map.put("chargeFlag",chargeFlag);
        map.put("fixedAmnt",fixedAmnt);
        map.put("fixedAmntDeci",fixedAmntDeci);
        Page<ParmAnnualFeeTable> page = PageHelper.startPage(pageNum, pageSize);
        List<ParmAnnualFeeTable> annualFeeTableList = parmAnnualFeeTableSelfMapper.selectByCondition(map);
        List<AnnualFeeTableResDTO> res = BeanMapping.copyList(annualFeeTableList, AnnualFeeTableResDTO.class);
        return new PageResultDTO<>(pageNum,pageSize,page.getTotal(), page.getPages(),res);
    }



    /**
     * 添加年费参数
     * @param  annualFeeTableReq 年费参数入参对象
     * @return 年费参数响应参数
     */
    @Override
    @InsertParameterAnnotation(tableName = "parm_annual_fee_table", tableDesc = "Annual Fee")
    public ParameterCompare addParmFee(AnnualFeeTableReqDTO annualFeeTableReq){

        // 根据机构号和table_id查询该记录是否已经存在
        ParmAnnualFeeTable parmAnnualFeeTable = parmAnnualFeeTableSelfMapper.selectByOrgAndTid(annualFeeTableReq.getOrganizationNumber(), annualFeeTableReq.getTableId());
        if (parmAnnualFeeTable != null) {
            log.error("年费参数数据已存在");
            throw new AnyTxnParameterException(AnyTxnParameterRespCodeEnum.P_PARAMETER_DATA_EXIST, ParameterRepDetailEnum.ANNUAL_FREE_EXIST);
        }
        ParmAnnualFeeTable parmFeeTableReq = BeanMapping.copy(annualFeeTableReq, ParmAnnualFeeTable.class);

        parmFeeTableReq.setId(String.valueOf(numberIdGenerator.generateId(TenantUtils.getTenantId())));

        return ParameterCompare.getBuilder().withAfter(parmFeeTableReq).build(ParmAnnualFeeTable.class);
    }

    /**
     * 更新年费参数
     * @param  annualFeeTableReq 年费数入参对象
     * @return 年费参数响应参数
     */
    @Override
    @UpdateParameterAnnotation(tableName = "parm_annual_fee_table", tableDesc = "Annual Fee")
    public ParameterCompare modifyParmFee(AnnualFeeTableReqDTO annualFeeTableReq) {
        ParmAnnualFeeTable parmAnnualFeeTable = parmAnnualFeeTableMapper.selectByPrimaryKey(annualFeeTableReq.getId());
        if (null == parmAnnualFeeTable) {
            log.error("年费参数数据不存在");
            throw new AnyTxnParameterException(AnyTxnParameterRespCodeEnum.D_GET_IS_NULL_FAULT, ParameterRepDetailEnum.ANNUAL_FREE_NOT_EXIST);
        }
        // 判断修改后的数据在表里是否是以机构号,表id唯一确定一条数据
        if (!annualFeeTableReq.getOrganizationNumber().equals(parmAnnualFeeTable.getOrganizationNumber())
                || !annualFeeTableReq.getTableId().equals(parmAnnualFeeTable.getTableId())) {
            ParmAnnualFeeTable isExsit = parmAnnualFeeTableSelfMapper.selectByOrgAndTid(annualFeeTableReq.getOrganizationNumber(), annualFeeTableReq.getTableId());
            if (isExsit != null) {
                log.error("年费参数数据已存在，id:{}",isExsit.getId());
                throw new AnyTxnParameterException(AnyTxnParameterRespCodeEnum.P_PARAMETER_DATA_EXIST, ParameterRepDetailEnum.ANNUAL_FREE_EXIST);
            }
        }

        ParmAnnualFeeTable updateAnnualFee = BeanMapping.copy(annualFeeTableReq, ParmAnnualFeeTable.class);

        return ParameterCompare.getBuilder().withAfter(updateAnnualFee).withBefore(parmAnnualFeeTable).build(ParmAnnualFeeTable.class);

    }

    /**
     * 通过Id主键删除年费参数
     * @param  id 主键
     * @return Boolean
     */
    @Transactional(rollbackFor = Exception.class)
    @Override
    @DeleteParameterAnnotation(tableName = "parm_annual_fee_table", tableDesc = "Annual Fee Rule")
    public ParameterCompare removeParmFee(String id){
        ParmAnnualFeeTable parmAnnualFeeTable = new ParmAnnualFeeTable();
        // 如果id不存在，则查询该记录
        if (null != id) {
            parmAnnualFeeTable = parmAnnualFeeTableMapper.selectByPrimaryKey(id);
        }
        // 如果该记录不存在，抛出异常，否则就删除该记录
        if (null == parmAnnualFeeTable) {
            log.error("年费参数数据不存在");
            throw new AnyTxnParameterException(AnyTxnParameterRespCodeEnum.D_GET_IS_NULL_FAULT, ParameterRepDetailEnum.ANNUAL_FREE_NOT_EXIST);
        }
        return ParameterCompare.getBuilder().withBefore(parmAnnualFeeTable).build(ParmAnnualFeeTable.class);

    }

    /**
     * 通过Id查询年费参数信息
     * @param id 主键id
     * @return AnnualFeeTableResDTO
     */
    @Override
    public AnnualFeeTableResDTO findById(String id){
        if (id == null) {
            log.error("id不能为空");
            throw new AnyTxnParameterException(AnyTxnParameterRespCodeEnum.P_PARAMETER_ID_EMPTY_FAULT,ParameterRepDetailEnum.ID_NULL);
        }
        ParmAnnualFeeTable parmAnnualFeeTable = parmAnnualFeeTableMapper.selectByPrimaryKey(id);
        return BeanMapping.copy(parmAnnualFeeTable, AnnualFeeTableResDTO.class);
    }

    /**
     * 根据表Id查询年费参数
     *
     * @param tableId 表Id
     * @return 年费参数响应参数
     */
    @Override
    public AnnualFeeTableResDTO findByOrgAndTableId(String orgNum, String tableId){
        ParmAnnualFeeTable parmAnnualFeeTable = parmAnnualFeeTableSelfMapper.selectByOrgAndTid(orgNum, tableId);
        if (parmAnnualFeeTable == null) {
            log.error("年费参数数据不存在");
            throw new AnyTxnParameterException(AnyTxnParameterRespCodeEnum.D_GET_IS_NULL_FAULT, ParameterRepDetailEnum.ANNUAL_FREE_NOT_EXIST);
        }
        return BeanMapping.copy(parmAnnualFeeTable, AnnualFeeTableResDTO.class);
    }


    @Override
    @Transactional(rollbackFor = Exception.class,propagation = Propagation.REQUIRED)
    //@ShardingSphereTransactionType(TransactionType.XA)
    protected boolean updateDb(ParmModificationRecord parmModificationRecord) {
        ParmAnnualFeeTable parmAnnualFeeTable = JSON.parseObject(parmModificationRecord.getParmBody(), ParmAnnualFeeTable.class);
        parmAnnualFeeTable.initUpdateDateTime();
        parmAnnualFeeTableMapper.updateByPrimaryKey(parmAnnualFeeTable);
        return true;
    }

    @Override
    @Transactional(rollbackFor = Exception.class,propagation = Propagation.REQUIRED)
    //@ShardingSphereTransactionType(TransactionType.XA)
    protected boolean insertDb(ParmModificationRecord parmModificationRecord) {
        ParmAnnualFeeTable parmAnnualFeeTable = JSON.parseObject(parmModificationRecord.getParmBody(), ParmAnnualFeeTable.class);
        parmAnnualFeeTableMapper.insertSelective(parmAnnualFeeTable);
        return true;
    }

    @Override
    @Transactional(rollbackFor = Exception.class,propagation = Propagation.REQUIRED)
    //@ShardingSphereTransactionType(TransactionType.XA)
    protected boolean deleteDb(ParmModificationRecord parmModificationRecord) {
        ParmAnnualFeeTable parmAnnualFeeTable = JSON.parseObject(parmModificationRecord.getParmBody(), ParmAnnualFeeTable.class);
        parmAnnualFeeTableMapper.deleteByPrimaryKey(parmAnnualFeeTable.getId());
        return true;
    }
}
