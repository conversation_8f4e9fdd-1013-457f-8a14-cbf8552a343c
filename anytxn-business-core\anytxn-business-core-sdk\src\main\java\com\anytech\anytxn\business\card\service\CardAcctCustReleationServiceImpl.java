package com.anytech.anytxn.business.card.service;


import com.anytech.anytxn.business.base.account.domain.dto.AccountManagementInfoDTO;
import com.anytech.anytxn.business.dao.account.mapper.AccountManagementInfoSelfMapper;
import com.anytech.anytxn.business.dao.account.model.AccountManagementInfo;
import com.anytech.anytxn.business.base.card.domain.dto.CardAcctCustReleationDTO;
import com.anytech.anytxn.business.base.card.enums.AcctAttributeEnum;
import com.anytech.anytxn.business.dao.card.mapper.CardAcctCustReleationMapper;
import com.anytech.anytxn.business.dao.card.mapper.CardAcctCustReleationSelfMapper;
import com.anytech.anytxn.business.dao.card.model.CardAcctCustReleation;
import com.anytech.anytxn.business.base.card.service.ICardAcctCustReleationService;
import com.anytech.anytxn.common.core.enums.AnyTxnCommonRespCodeEnum;
import com.anytech.anytxn.common.core.enums.AccountStatusEnum;
import com.anytech.anytxn.common.core.enums.CommonRepDetailEnum;
import com.anytech.anytxn.common.core.exception.AnyTxnCommonException;
import com.anytech.anytxn.common.core.utils.BeanMapping;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @description:
 * @date 2025/2/2515:55
 */
@Slf4j
@Service
public class CardAcctCustReleationServiceImpl implements ICardAcctCustReleationService {

    @Autowired
    private CardAcctCustReleationMapper cardAcctCustReleationMapper;

    @Autowired
    private CardAcctCustReleationSelfMapper cardAcctCustReleationSelfMapper;

    @Autowired
    private AccountManagementInfoSelfMapper accountManagementInfoSelfMapper;

    @Override
    public void insert(CardAcctCustReleationDTO cardAcctCustReleationDTO) {
        CardAcctCustReleation cardAcctCustReleation = BeanMapping.copy(cardAcctCustReleationDTO, CardAcctCustReleation.class);
        int i = cardAcctCustReleationMapper.insertSelective(cardAcctCustReleation);
        if (i == 0) {
            log.error("card_acct_cust_releation add exception:The number of affected items is not 1:{}",i);
            throw new AnyTxnCommonException(AnyTxnCommonRespCodeEnum.D_ERR, CommonRepDetailEnum.DATABASE_INSERT);
        }
    }

    @Override
    public void insertBatch(List<CardAcctCustReleationDTO> cardAcctCustReleationDTOList) {
        cardAcctCustReleationSelfMapper.insertBatch(BeanMapping.copyList(cardAcctCustReleationDTOList,CardAcctCustReleation.class));
    }

    @Override
    public List<CardAcctCustReleationDTO> selectByCardNumber(String cardNumber) {
        List<CardAcctCustReleation> acctCustReleationList = cardAcctCustReleationSelfMapper.selectByCardNumber(cardNumber);
        return BeanMapping.copyList(acctCustReleationList, CardAcctCustReleationDTO.class);
    }

    public CardAcctCustReleationDTO selectByCondition(CardAcctCustReleationDTO acctCustReleationDTO) {
        CardAcctCustReleation acctCustReleation = cardAcctCustReleationSelfMapper.selectByCondition(BeanMapping.copy(acctCustReleationDTO,CardAcctCustReleation.class));
        if (Objects.isNull(acctCustReleation)) {
            return null;
        }
        return BeanMapping.copy(acctCustReleation, CardAcctCustReleationDTO.class);
    }

    /**
     * 根据卡号查询管理账户信息
     * @param organizationNumber
     * @param cardNumber
     * @return
     */
    @Override
    public List<AccountManagementInfoDTO> selectAcctByCardNumber(String organizationNumber,String cardNumber) {
        List<CardAcctCustReleation> acctCustReleationList = cardAcctCustReleationSelfMapper.selectByCardNumber(cardNumber);
        List<String> accountManagementIds = acctCustReleationList.stream().map(CardAcctCustReleation::getAccountManagementId).collect(Collectors.toList());
        List<AccountManagementInfo> accountManagementInfoList = accountManagementInfoSelfMapper.selectByOrgCusIdAndMids(organizationNumber, acctCustReleationList.get(0).getPrimaryCustomerId(), accountManagementIds);
        return BeanMapping.copyList(accountManagementInfoList,AccountManagementInfoDTO.class);
    }

    /**
     * 根据卡号、卡产品、币种查询管理账户信息
     * @param organizationNumber 机构号
     * @param cardNumber 卡号
     * @param cardProduct 卡产品
     * @param cur 币种
     * @return List<AccountManagementInfoDTO>
     */
    @Override
    public List<AccountManagementInfoDTO> selectAcctByCardPdAndCur(String organizationNumber, String cardNumber, String cardProduct, String cur) {
        List<CardAcctCustReleation> acctCustReleationList = cardAcctCustReleationSelfMapper.selectAcctByCardPdAndCur(organizationNumber, cardNumber,cardProduct,cur);
        List<String> accountManagementIds = acctCustReleationList.stream().map(CardAcctCustReleation::getAccountManagementId).collect(Collectors.toList());
        List<AccountManagementInfo> accountManagementInfoList = accountManagementInfoSelfMapper.selectByOrgCusIdAndMids(organizationNumber, acctCustReleationList.get(0).getPrimaryCustomerId(), accountManagementIds);
        return BeanMapping.copyList(accountManagementInfoList,AccountManagementInfoDTO.class);
    }

    /**
     * 根据卡号、卡产品、币种查询有效的CA管理账户信息
     * @param organizationNumber 机构号
     * @param cardNumber 卡号
     * @param cardProduct 卡产品
     * @param cur 币种
     * @return List<AccountManagementInfoDTO>
     */
    @Override
    public AccountManagementInfoDTO selectValidCaAcctByCardNumber(String organizationNumber, String cardNumber, String cardProduct, String cur) {
        List<CardAcctCustReleation> acctCustReleationList = cardAcctCustReleationSelfMapper.selectAcctByCardPdAndCur(organizationNumber, cardNumber,cardProduct,cur);
        if(CollectionUtils.isEmpty(acctCustReleationList)){
            return null;
        }
        List<String> accountManagementIds = acctCustReleationList.stream().filter(x -> !StringUtils.equalsAny(x.getAttribute(), AcctAttributeEnum.VA.getCode(),AcctAttributeEnum.TA.getCode())).map(CardAcctCustReleation::getAccountManagementId).collect(Collectors.toList());
        List<AccountManagementInfo> accountManagementInfoList = accountManagementInfoSelfMapper.selectByOrgCusIdAndMids(organizationNumber, acctCustReleationList.get(0).getPrimaryCustomerId(), accountManagementIds);
        AccountManagementInfo vaildAccountManagementInfo = accountManagementInfoList.stream().filter(x ->
                StringUtils.equalsAny(x.getAccountStatus(), AccountStatusEnum.NEW.getCode(), AccountStatusEnum.SLEEP.getCode(), AccountStatusEnum.ACTIVE.getCode()))
                .findFirst().orElse(null);
        if(null == vaildAccountManagementInfo){
            return null;
        }
        return BeanMapping.copy(vaildAccountManagementInfo,AccountManagementInfoDTO.class);
    }

    /**
     * 根据卡号、管理账户号查询
     * @param cardNumber
     * @param accountManagementId
     * @return
     */
    @Override
    public CardAcctCustReleationDTO selectByCardNumberAndMid(String cardNumber, String accountManagementId) {
        CardAcctCustReleation cardAcctCustReleation = cardAcctCustReleationSelfMapper.selectByCardNumberAndMid(cardNumber, accountManagementId);
        return BeanMapping.copy(cardAcctCustReleation, CardAcctCustReleationDTO.class);
    }

    @Override
    public List<CardAcctCustReleationDTO> selectByOrgNumberAndMid(String organizationNumber, String accountManagementId) {
        CardAcctCustReleation cardAcctCustReleation = new CardAcctCustReleation();
        cardAcctCustReleation.setOrganizationNumber(organizationNumber);
        cardAcctCustReleation.setAccountManagementId(accountManagementId);
        List<CardAcctCustReleation> acctCustReleationList = cardAcctCustReleationSelfMapper.selectListByCondition(cardAcctCustReleation);
        return BeanMapping.copyList(acctCustReleationList, CardAcctCustReleationDTO.class);
    }

    @Override
    public CardAcctCustReleationDTO selectByMidAndCardProduct(String accountManagementId, String cardProductNumber) {
        CardAcctCustReleation cardAcctCustReleation = cardAcctCustReleationSelfMapper.selectByMidAndCardProduct(accountManagementId,cardProductNumber);
        return BeanMapping.copy(cardAcctCustReleation, CardAcctCustReleationDTO.class);
    }

    @Override
    public List<CardAcctCustReleationDTO> selectByOrgAndCid(String organizationNumber, String customerId) {
        List<CardAcctCustReleation> acctCustReleationList = cardAcctCustReleationSelfMapper.selectByOrgAndCid(organizationNumber, customerId);
        return BeanMapping.copyList(acctCustReleationList, CardAcctCustReleationDTO.class);
    }

    @Override
    public List<CardAcctCustReleationDTO> selectByOrgAndCidAndCardAndCurr(String organizationNumber, String customerId, String cardNumber,String currency) {
        List<CardAcctCustReleation> acctCustReleationList = cardAcctCustReleationSelfMapper.selectByOrgAndCidAndCard(organizationNumber, customerId,cardNumber,currency);
        return BeanMapping.copyList(acctCustReleationList, CardAcctCustReleationDTO.class);
    }

    /**
     * 查询CA账户关联的VA信息
     * @param organizationNumber
     * @param accountManagementId
     * @param customerId
     * @return
     */
    @Override
    public CardAcctCustReleationDTO selectVaByCaAcctAndCustId(String organizationNumber, String accountManagementId,String customerId) {
        List<CardAcctCustReleation> acctCustReleationList = cardAcctCustReleationSelfMapper.selectByOrgAndCid(organizationNumber, customerId);
        List<String> cardNumberList = acctCustReleationList.stream().filter(x -> StringUtils.equals(x.getAccountManagementId(), accountManagementId)).map(CardAcctCustReleation::getCardNumber).collect(Collectors.toList());
        CardAcctCustReleation vaCardAcctCustReleation = acctCustReleationList.stream().filter(x -> cardNumberList.contains(x.getCardNumber()) && StringUtils.equals(x.getAttribute(), AcctAttributeEnum.VA.getCode())).findFirst().orElse(null);
        if(null == vaCardAcctCustReleation){
            return null;
        }
        return BeanMapping.copy(vaCardAcctCustReleation, CardAcctCustReleationDTO.class);
    }

    @Override
    public List<CardAcctCustReleationDTO> selectByCustIdAndAttr(String organizationNumber, String customerId, String attribute) {
        List<CardAcctCustReleation> allAcctCustReleationList = cardAcctCustReleationSelfMapper.selectByOrgAndCid(organizationNumber, customerId);
        List<CardAcctCustReleation> acctCustReleationList = allAcctCustReleationList.stream().filter(x -> StringUtils.equals(attribute, x.getAttribute())).collect(Collectors.toList());
        return BeanMapping.copyList(acctCustReleationList, CardAcctCustReleationDTO.class);
    }
}
