package com.anytech.anytxn.parameter.common.controller;

import io.swagger.v3.oas.annotations.tags.Tag;
import com.anytech.anytxn.common.core.base.BizBaseController;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

@RestController
@Tag(name = "Main类可忽略")
public class MainController extends BizBaseController {
	private static final Logger logger = LoggerFactory.getLogger(MainController.class);

	@RequestMapping(value = "/", method = RequestMethod.GET)

	public String helloOpenApi() {
		logger.info("欢迎...");
		return "welcome to anytxn-param";
	}
}
