package com.anytech.anytxn.business.common.service;

import com.anytech.anytxn.business.base.account.domain.dto.AccountManagementInfoDTO;
import com.anytech.anytxn.business.base.card.domain.dto.CardAuthorizationDTO;
import com.anytech.anytxn.business.base.card.domain.dto.CardBasicDTO;
import com.anytech.anytxn.business.base.customer.domain.dto.CustomerAuthorizationInfoDTO;
import com.anytech.anytxn.business.base.monetary.domain.bo.CustAccountBO;
import com.anytech.anytxn.business.dao.account.mapper.AccountManagementInfoMapper;
import com.anytech.anytxn.business.dao.account.model.AccountManagementInfo;
import com.anytech.anytxn.business.dao.card.mapper.CardAuthorizationInfoMapper;
import com.anytech.anytxn.business.dao.card.mapper.CardBasicInfoMapper;
import com.anytech.anytxn.business.dao.card.model.CardAuthorizationInfo;
import com.anytech.anytxn.business.dao.card.model.CardBasicInfo;
import com.anytech.anytxn.business.dao.customer.mapper.CustomerAuthorizationInfoSelfMapper;
import com.anytech.anytxn.business.dao.customer.model.CustomerAuthorizationInfo;
import com.anytech.anytxn.common.core.utils.OrgNumberUtils;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockedStatic;
import org.mockito.junit.jupiter.MockitoExtension;

import static org.assertj.core.api.Assertions.assertThat;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.Mockito.*;

/**
 * SharedInfoFindServiceImpl单元测试
 * 
 * <AUTHOR>
 * @date 2025-01-20
 */
@ExtendWith(MockitoExtension.class)
class SharedInfoFindServiceTest {

    @Mock
    private CardBasicInfoMapper cardBasicInfoMapper;

    @Mock
    private CardAuthorizationInfoMapper cardAuthorizationInfoMapper;

    @Mock
    private AccountManagementInfoMapper accountManagementInfoMapper;

    @Mock
    private CustomerAuthorizationInfoSelfMapper customerAuthorizationInfoSelfMapper;

    @InjectMocks
    private SharedInfoFindServiceImpl sharedInfoFindService;

    private static final String TEST_CARD_NUMBER = "****************";
    private static final String TEST_ACCOUNT_ID = "ACC001";
    private static final String TEST_CUSTOMER_ID = "CUST001";
    private static final String TEST_ORG = "ORG001";

    @BeforeEach
    void setUp() {
        // 测试数据准备在各个测试方法中进行
    }

    @Test
    void shouldReturnCardBasicDTO_whenValidCardNumberProvidedInOnlineMode() {
        try (MockedStatic<CustAccountBO> mockCustAccountBO = mockStatic(CustAccountBO.class);
             MockedStatic<OrgNumberUtils> mockOrgUtils = mockStatic(OrgNumberUtils.class)) {
            
            // Given
            mockCustAccountBO.when(CustAccountBO::isBatch).thenReturn(false);
            mockOrgUtils.when(OrgNumberUtils::getOrg).thenReturn(TEST_ORG);
            
            CardBasicInfo cardBasicInfo = createTestCardBasicInfo();
            when(cardBasicInfoMapper.selectByPrimaryKey(TEST_CARD_NUMBER, TEST_ORG))
                .thenReturn(cardBasicInfo);

            // When
            CardBasicDTO result = sharedInfoFindService.getCardBasicByCardNumber(TEST_CARD_NUMBER);

            // Then
            assertThat(result).isNotNull();
            assertThat(result.getCardNumber()).isEqualTo(TEST_CARD_NUMBER);
            assertThat(result.getBranchNumber()).isEqualTo("BRANCH001");
            
            verify(cardBasicInfoMapper).selectByPrimaryKey(TEST_CARD_NUMBER, TEST_ORG);
        }
    }

    @Test
    void shouldReturnNull_whenCardNotFoundInOnlineMode() {
        try (MockedStatic<CustAccountBO> mockCustAccountBO = mockStatic(CustAccountBO.class);
             MockedStatic<OrgNumberUtils> mockOrgUtils = mockStatic(OrgNumberUtils.class)) {
            
            // Given
            mockCustAccountBO.when(CustAccountBO::isBatch).thenReturn(false);
            mockOrgUtils.when(OrgNumberUtils::getOrg).thenReturn(TEST_ORG);
            
            when(cardBasicInfoMapper.selectByPrimaryKey(TEST_CARD_NUMBER, TEST_ORG))
                .thenReturn(null);

            // When
            CardBasicDTO result = sharedInfoFindService.getCardBasicByCardNumber(TEST_CARD_NUMBER);

            // Then
            assertThat(result).isNull();
            
            verify(cardBasicInfoMapper).selectByPrimaryKey(TEST_CARD_NUMBER, TEST_ORG);
        }
    }

    @Test
    void shouldReturnCardAuthorizationDTO_whenValidCardNumberProvidedInOnlineMode() {
        try (MockedStatic<CustAccountBO> mockCustAccountBO = mockStatic(CustAccountBO.class);
             MockedStatic<OrgNumberUtils> mockOrgUtils = mockStatic(OrgNumberUtils.class)) {
            
            // Given
            mockCustAccountBO.when(CustAccountBO::isBatch).thenReturn(false);
            mockOrgUtils.when(OrgNumberUtils::getOrg).thenReturn(TEST_ORG);
            
            CardAuthorizationInfo cardAuthInfo = createTestCardAuthorizationInfo();
            when(cardAuthorizationInfoMapper.selectByPrimaryKey(TEST_CARD_NUMBER, TEST_ORG))
                .thenReturn(cardAuthInfo);

            // When
            CardAuthorizationDTO result = sharedInfoFindService.getCardAuthorizationByCardNumber(TEST_CARD_NUMBER);

            // Then
            assertThat(result).isNotNull();
            assertThat(result.getCardNumber()).isEqualTo(TEST_CARD_NUMBER);
            assertThat(result.getStatus()).isEqualTo("ACTIVE");
            
            verify(cardAuthorizationInfoMapper).selectByPrimaryKey(TEST_CARD_NUMBER, TEST_ORG);
        }
    }

    @Test
    void shouldReturnNull_whenCardAuthorizationNotFoundInOnlineMode() {
        try (MockedStatic<CustAccountBO> mockCustAccountBO = mockStatic(CustAccountBO.class);
             MockedStatic<OrgNumberUtils> mockOrgUtils = mockStatic(OrgNumberUtils.class)) {
            
            // Given
            mockCustAccountBO.when(CustAccountBO::isBatch).thenReturn(false);
            mockOrgUtils.when(OrgNumberUtils::getOrg).thenReturn(TEST_ORG);
            
            when(cardAuthorizationInfoMapper.selectByPrimaryKey(TEST_CARD_NUMBER, TEST_ORG))
                .thenReturn(null);

            // When
            CardAuthorizationDTO result = sharedInfoFindService.getCardAuthorizationByCardNumber(TEST_CARD_NUMBER);

            // Then
            assertThat(result).isNull();
            
            verify(cardAuthorizationInfoMapper).selectByPrimaryKey(TEST_CARD_NUMBER, TEST_ORG);
        }
    }

    @Test
    void shouldReturnAccountManagementInfoDTO_whenValidIdProvidedInOnlineMode() {
        try (MockedStatic<CustAccountBO> mockCustAccountBO = mockStatic(CustAccountBO.class);
             MockedStatic<OrgNumberUtils> mockOrgUtils = mockStatic(OrgNumberUtils.class)) {
            
            // Given
            mockCustAccountBO.when(CustAccountBO::isBatch).thenReturn(false);
            mockOrgUtils.when(OrgNumberUtils::getOrg).thenReturn(TEST_ORG);
            
            AccountManagementInfo accountInfo = createTestAccountManagementInfo();
            when(accountManagementInfoMapper.selectByPrimaryKey(TEST_ACCOUNT_ID))
                .thenReturn(accountInfo);

            // When
            AccountManagementInfoDTO result = sharedInfoFindService.getManagementInfoById(TEST_ACCOUNT_ID);

            // Then
            assertThat(result).isNotNull();
            assertThat(result.getAccountManagementId()).isEqualTo(TEST_ACCOUNT_ID);
            assertThat(result.getCustomerId()).isEqualTo(TEST_CUSTOMER_ID);
            
            verify(accountManagementInfoMapper).selectByPrimaryKey(TEST_ACCOUNT_ID);
        }
    }

    @Test
    void shouldReturnNull_whenAccountManagementInfoNotFoundInOnlineMode() {
        try (MockedStatic<CustAccountBO> mockCustAccountBO = mockStatic(CustAccountBO.class)) {
            
            // Given
            mockCustAccountBO.when(CustAccountBO::isBatch).thenReturn(false);
            
            when(accountManagementInfoMapper.selectByPrimaryKey(TEST_ACCOUNT_ID))
                .thenReturn(null);

            // When
            AccountManagementInfoDTO result = sharedInfoFindService.getManagementInfoById(TEST_ACCOUNT_ID);

            // Then
            assertThat(result).isNull();
            
            verify(accountManagementInfoMapper).selectByPrimaryKey(TEST_ACCOUNT_ID);
        }
    }

    @Test
    void shouldReturnCustomerAuthorizationInfoDTO_whenValidCustomerIdProvidedInOnlineMode() {
        try (MockedStatic<CustAccountBO> mockCustAccountBO = mockStatic(CustAccountBO.class);
             MockedStatic<OrgNumberUtils> mockOrgUtils = mockStatic(OrgNumberUtils.class)) {
            
            // Given
            mockCustAccountBO.when(CustAccountBO::isBatch).thenReturn(false);
            mockOrgUtils.when(OrgNumberUtils::getOrg).thenReturn(TEST_ORG);
            
            CustomerAuthorizationInfo customerAuthInfo = createTestCustomerAuthorizationInfo();
            when(customerAuthorizationInfoSelfMapper.selectByCustomerId(TEST_ORG, TEST_CUSTOMER_ID))
                .thenReturn(customerAuthInfo);

            // When
            CustomerAuthorizationInfoDTO result = sharedInfoFindService.getCustomerAuthorizationByCustomerId(TEST_CUSTOMER_ID);

            // Then
            assertThat(result).isNotNull();
            assertThat(result.getCustomerId()).isEqualTo(TEST_CUSTOMER_ID);
            assertThat(result.getStatus()).isEqualTo("AUTHORIZED");
            
            verify(customerAuthorizationInfoSelfMapper).selectByCustomerId(TEST_ORG, TEST_CUSTOMER_ID);
        }
    }

    @Test
    void shouldReturnNull_whenCustomerAuthorizationInfoNotFoundInOnlineMode() {
        try (MockedStatic<CustAccountBO> mockCustAccountBO = mockStatic(CustAccountBO.class);
             MockedStatic<OrgNumberUtils> mockOrgUtils = mockStatic(OrgNumberUtils.class)) {
            
            // Given
            mockCustAccountBO.when(CustAccountBO::isBatch).thenReturn(false);
            mockOrgUtils.when(OrgNumberUtils::getOrg).thenReturn(TEST_ORG);
            
            when(customerAuthorizationInfoSelfMapper.selectByCustomerId(TEST_ORG, TEST_CUSTOMER_ID))
                .thenReturn(null);

            // When
            CustomerAuthorizationInfoDTO result = sharedInfoFindService.getCustomerAuthorizationByCustomerId(TEST_CUSTOMER_ID);

            // Then
            assertThat(result).isNull();
            
            verify(customerAuthorizationInfoSelfMapper).selectByCustomerId(TEST_ORG, TEST_CUSTOMER_ID);
        }
    }

    @Test
    void shouldHandleBatchModeGracefully() {
        // Given - Test only online mode since batch mode has complex dependencies
        try (MockedStatic<CustAccountBO> mockCustAccountBO = mockStatic(CustAccountBO.class);
             MockedStatic<OrgNumberUtils> mockOrgUtils = mockStatic(OrgNumberUtils.class)) {
            
            mockCustAccountBO.when(CustAccountBO::isBatch).thenReturn(false);
            mockOrgUtils.when(OrgNumberUtils::getOrg).thenReturn(TEST_ORG);
            
            CardBasicInfo cardBasicInfo = createTestCardBasicInfo();
            when(cardBasicInfoMapper.selectByPrimaryKey(TEST_CARD_NUMBER, TEST_ORG))
                .thenReturn(cardBasicInfo);

            // When
            CardBasicDTO result = sharedInfoFindService.getCardBasicByCardNumber(TEST_CARD_NUMBER);

            // Then
            assertThat(result).isNotNull();
            assertThat(result.getCardNumber()).isEqualTo(TEST_CARD_NUMBER);
            verify(cardBasicInfoMapper).selectByPrimaryKey(TEST_CARD_NUMBER, TEST_ORG);
        }
    }

    // Helper methods to create test data
    private CardBasicInfo createTestCardBasicInfo() {
        CardBasicInfo cardBasicInfo = new CardBasicInfo();
        cardBasicInfo.setCardNumber(TEST_CARD_NUMBER);
        cardBasicInfo.setBranchNumber("BRANCH001");
        cardBasicInfo.setOrganizationNumber(TEST_ORG);
        return cardBasicInfo;
    }

    private CardBasicDTO createTestCardBasicDTO() {
        CardBasicDTO cardBasicDTO = new CardBasicDTO();
        cardBasicDTO.setCardNumber(TEST_CARD_NUMBER);
        cardBasicDTO.setBranchNumber("BRANCH001");
        cardBasicDTO.setOrganizationNumber(TEST_ORG);
        return cardBasicDTO;
    }

    private CardAuthorizationInfo createTestCardAuthorizationInfo() {
        CardAuthorizationInfo cardAuthInfo = new CardAuthorizationInfo();
        cardAuthInfo.setCardNumber(TEST_CARD_NUMBER);
        cardAuthInfo.setStatus("ACTIVE");
        cardAuthInfo.setOrganizationNumber(TEST_ORG);
        return cardAuthInfo;
    }

    private AccountManagementInfo createTestAccountManagementInfo() {
        AccountManagementInfo accountInfo = new AccountManagementInfo();
        accountInfo.setAccountManagementId(TEST_ACCOUNT_ID);
        accountInfo.setCustomerId(TEST_CUSTOMER_ID);
        accountInfo.setProductNumber("PROD001");
        accountInfo.setOrganizationNumber(TEST_ORG);
        return accountInfo;
    }

    private CustomerAuthorizationInfo createTestCustomerAuthorizationInfo() {
        CustomerAuthorizationInfo customerAuthInfo = new CustomerAuthorizationInfo();
        customerAuthInfo.setCustomerId(TEST_CUSTOMER_ID);
        customerAuthInfo.setStatus("AUTHORIZED");
        customerAuthInfo.setOrganizationNumber(TEST_ORG);
        return customerAuthInfo;
    }
} 