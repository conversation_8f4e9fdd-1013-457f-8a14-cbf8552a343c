package com.anytech.anytxn.parameter.authorization.controller;

import io.swagger.v3.oas.annotations.tags.Tag;
import io.swagger.v3.oas.annotations.Operation;
import com.anytech.anytxn.common.core.base.AnyTxnHttpResponse;
import com.anytech.anytxn.common.core.base.BizBaseController;
import com.anytech.anytxn.parameter.base.authorization.domain.dto.AuthCheckControlResDTO;
import com.anytech.anytxn.parameter.base.authorization.service.IAuthCheckControlService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RestController;

import java.util.ArrayList;

/**
 * 授权检查控制表
 *
 * <AUTHOR>
 * @date 2018-12-07
 **/
@Tag(name = "授权检查控制表")
@RestController
public class AuthCheckControlController extends BizBaseController {

    @Autowired
    private IAuthCheckControlService authCheckControlService;

    /**
     * 根据机构号、参数表id,查询授权检查控制
     *
     * @param organizationNumber 机构号
     * @param tableId            参数表id
     * @return AnyTxnHttpResponse<ArrayList<AuthCheckControlResDTO>>
     */
    @GetMapping(value = "/param/authCheckControl/organizationNumber/{organizationNumber}/tableId/{tableId}")
    @Operation(summary = "根据机构号、参数表id,查询授权检查控制")
    public AnyTxnHttpResponse<ArrayList<AuthCheckControlResDTO>> getAuthDefinByOrgAndTableId(@PathVariable(value = "organizationNumber") String organizationNumber,
                                                                                          @PathVariable(value = "tableId") String tableId) {
        ArrayList<AuthCheckControlResDTO> authCheckControlRes = (ArrayList) authCheckControlService.findByOrgAndTableId(organizationNumber, tableId);
        return AnyTxnHttpResponse.success(authCheckControlRes);

    }
}
