package com.anytech.anytxn.parameter.authorization.service;

import com.alibaba.fastjson.JSON;
import com.github.pagehelper.Page;
import com.github.pagehelper.PageHelper;
import com.anytech.anytxn.common.core.annotation.db.DeleteParameterAnnotation;
import com.anytech.anytxn.common.core.annotation.db.InsertParameterAnnotation;
import com.anytech.anytxn.common.core.annotation.db.UpdateParameterAnnotation;
import com.anytech.anytxn.business.base.audit.AbstractParameterService;
import com.anytech.anytxn.common.core.base.ParameterCompare;
import com.anytech.anytxn.business.base.audit.ParmModificationRecord;
import com.anytech.anytxn.common.core.base.PageResultDTO;
import com.anytech.anytxn.common.core.utils.BeanMapping;
import com.anytech.anytxn.common.core.utils.OrgNumberUtils;
import com.anytech.anytxn.common.sequence.utils.Number16IdGen;
import com.anytech.anytxn.common.core.utils.TenantUtils;
import com.anytech.anytxn.parameter.base.authorization.domain.dto.AuthorizationRuleDTO;
import com.anytech.anytxn.parameter.base.authorization.domain.dto.AuthorizationRuleSearchDTO;
import com.anytech.anytxn.parameter.base.authorization.service.IAuthorizationRuleService;
import com.anytech.anytxn.parameter.base.common.enums.AnyTxnParameterRespCodeEnum;
import com.anytech.anytxn.parameter.base.common.enums.ParameterRepDetailEnum;
import com.anytech.anytxn.parameter.base.common.exception.AnyTxnParameterException;
import com.anytech.anytxn.parameter.authorization.mapper.AuthorizationRuleMapper;
import com.anytech.anytxn.parameter.authorization.mapper.AuthorizationRuleSelfMapper;
import com.anytech.anytxn.parameter.base.authorization.domain.model.AuthorizationRule;
import lombok.extern.slf4j.Slf4j;
//import org.apache.shardingsphere.transaction.annotation.ShardingSphereTransactionType;
//import org.apache.shardingsphere.transaction.core.TransactionType;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.cglib.beans.BeanCopier;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import java.util.ArrayList;
import java.util.List;

/**
 * @ClassName MccCodeServiceImpl
 * @Description 商户类别码具体实现类
 * <AUTHOR>
 * @Date 2019/4/4 3:44 PM
 * Version 1.0
 **/
@Service(value = "parm_authorization_rule_serviceImpl")
@Slf4j
public class AuthorizationRuleServiceImpl extends AbstractParameterService implements IAuthorizationRuleService {

    private static final Logger logger = LoggerFactory.getLogger(AuthorizationRuleServiceImpl.class);

    @Autowired
    private AuthorizationRuleMapper authorizationRuleMapper;
    @Autowired
    private AuthorizationRuleSelfMapper authorizationRuleSelfMapper;

    @Autowired
    private Number16IdGen numberIdGenerator;

    @Override
    public PageResultDTO<AuthorizationRuleDTO> findListAuthorizationRule(Integer page, Integer rows, AuthorizationRuleSearchDTO authorizationRuleSearchDTO) {
        if(null == authorizationRuleSearchDTO){
            authorizationRuleSearchDTO = new AuthorizationRuleSearchDTO();
        }
        Integer maxCleAprCntInt = null;
        Integer maxCleDceCntInt = null;
        Integer maxDlyAprCntInt = null;
        Integer maxDlyDceCntInt = null;
        if(!StringUtils.isEmpty(authorizationRuleSearchDTO.getMaxCleAprCnt())){
            try {
                maxCleAprCntInt = Integer.parseInt(authorizationRuleSearchDTO.getMaxCleAprCnt());
            }catch (NumberFormatException e){
                throw new AnyTxnParameterException(AnyTxnParameterRespCodeEnum.PARAMETER_EXCEPTION, ParameterRepDetailEnum.NUMBER_ERROR);
            }
            authorizationRuleSearchDTO.setMaxCleAprCntInt(maxCleAprCntInt);
        }
        if(!StringUtils.isEmpty(authorizationRuleSearchDTO.getMaxCleDceCnt())){
            try {
                maxCleDceCntInt = Integer.parseInt(authorizationRuleSearchDTO.getMaxCleDceCnt());
            }catch (NumberFormatException e){
                throw new AnyTxnParameterException(AnyTxnParameterRespCodeEnum.PARAMETER_EXCEPTION, ParameterRepDetailEnum.NUMBER_ERROR);
            }
            authorizationRuleSearchDTO.setMaxCleDceCntInt(maxCleDceCntInt);
        }
        if(!StringUtils.isEmpty(authorizationRuleSearchDTO.getMaxDlyAprCnt())){
            try {
                maxDlyAprCntInt = Integer.parseInt(authorizationRuleSearchDTO.getMaxDlyAprCnt());
            }catch (NumberFormatException e){
                throw new AnyTxnParameterException(AnyTxnParameterRespCodeEnum.PARAMETER_EXCEPTION, ParameterRepDetailEnum.NUMBER_ERROR);
            }
            authorizationRuleSearchDTO.setMaxDlyAprCntInt(maxDlyAprCntInt);
        }
        if(!StringUtils.isEmpty(authorizationRuleSearchDTO.getMaxDlyDceCnt())){
            try {
                maxDlyDceCntInt = Integer.parseInt(authorizationRuleSearchDTO.getMaxDlyDceCnt());
            }catch (NumberFormatException e){
                throw new AnyTxnParameterException(AnyTxnParameterRespCodeEnum.PARAMETER_EXCEPTION, ParameterRepDetailEnum.NUMBER_ERROR);
            }
            authorizationRuleSearchDTO.setMaxDlyDceCntInt(maxDlyDceCntInt);
        }

        logger.info("分页查询授权检查参数列表，当前页：{}，每页展示条数：{}", page, rows);
        Page pageInfo =  PageHelper.startPage(page, rows);
        List<AuthorizationRuleDTO> authorizationRuleDTOList = null;
        try {
            authorizationRuleSearchDTO.setOrganizationNumber(StringUtils.isEmpty(authorizationRuleSearchDTO.getOrganizationNumber()) ? OrgNumberUtils.getOrg() : authorizationRuleSearchDTO.getOrganizationNumber());
            List<AuthorizationRule> authorizationRuleList = authorizationRuleSelfMapper.selectByCondition(authorizationRuleSearchDTO);
            if (!CollectionUtils.isEmpty(authorizationRuleList)) {
                authorizationRuleDTOList = new ArrayList<>();
                BeanCopier beanCopier = BeanCopier.create(AuthorizationRule.class, AuthorizationRuleDTO.class, false);
                for (AuthorizationRule authorizationRule : authorizationRuleList) {
                    AuthorizationRuleDTO authorizationRuleDTO = new AuthorizationRuleDTO();
                    beanCopier.copy(authorizationRule, authorizationRuleDTO, null);
                    authorizationRuleDTOList.add(authorizationRuleDTO);
                }
            }
            return new PageResultDTO<>(page, rows, pageInfo.getTotal(), pageInfo.getPages(),authorizationRuleDTOList);
        } catch (Exception e) {
            logger.error("分页查询授权检查参数信息失败，当前页：{}，每页展示条数：{},错误信息：{}", page, rows, e.getMessage());
            /*throw new AnyTXNBusRuntimeException(TransactionEnum.DATABASE_ERROR.getCode(),
                    TransactionEnum.DATABASE_ERROR.getReason());*/
            throw new AnyTxnParameterException(AnyTxnParameterRespCodeEnum.D_PAGE_AUTH_RULE_FAULT);
        }
    }

    @Override
    public AuthorizationRuleDTO findAuthorizationRule(String id) {
        logger.info("根据主键:{},获取授权检查参数信息",id);
        AuthorizationRuleDTO authorizationRuleDTO = null;
        try{
            AuthorizationRule authorizationRule = authorizationRuleMapper.selectByPrimaryKey(id);
            if (authorizationRule != null) {
                authorizationRuleDTO = new AuthorizationRuleDTO();
                BeanCopier beanCopier = BeanCopier.create(AuthorizationRule.class, AuthorizationRuleDTO.class, false);
                beanCopier.copy(authorizationRule, authorizationRuleDTO, null);
            }
        }
        catch(Exception e){
            logger.error("根据主键:{},获取授权检查参数信息失败,错误信息:{}",id,e.getMessage());
            /*throw new AnyTXNBusRuntimeException(TransactionEnum.DATABASE_ERROR.getCode(),
                    TransactionEnum.DATABASE_ERROR.getReason());*/
            throw new AnyTxnParameterException(AnyTxnParameterRespCodeEnum.D_QUERY_AUTH_RULE_BY_ID_FAULT);

        }
        if (authorizationRuleDTO == null) {
            logger.error("根据主键:{},获取授权检查参数信息失败", id);

            throw new AnyTxnParameterException(AnyTxnParameterRespCodeEnum.D_QUERY_AUTH_RULE_NULL_BY_ID_FAULT);
        }
        return authorizationRuleDTO;
    }

    @Override
    @UpdateParameterAnnotation(tableName = "parm_authorization_rule", tableDesc = "Authorization Check Parameters")
    public ParameterCompare modifyAuthorizationRule(AuthorizationRuleDTO authorizationRuleDTO) {
        logger.info("修改授权检查参数信息,授权参数tableId:{} 授权参数状态:{}", authorizationRuleDTO.getTableId(), authorizationRuleDTO.getStatus());
        AuthorizationRule oldAuthorizationRule = authorizationRuleMapper.selectByPrimaryKey(authorizationRuleDTO.getId());
        if (null == oldAuthorizationRule) {
            log.error("授权检查参数数据不存在");
            throw new AnyTxnParameterException(AnyTxnParameterRespCodeEnum.D_GET_IS_NULL_FAULT, ParameterRepDetailEnum.ANNUAL_FREE_NOT_EXIST);
        }
        AuthorizationRule authorizationRule = BeanMapping.copy(authorizationRuleDTO, AuthorizationRule.class);
        return ParameterCompare.getBuilder().withAfter(authorizationRule).withBefore(oldAuthorizationRule).build(AuthorizationRule.class);
    }

    @Override
    @DeleteParameterAnnotation(tableName = "parm_authorization_rule", tableDesc = "Authorization Check Parameters")
    public ParameterCompare removeAuthorizationRule(String id) {
        AuthorizationRule authorizationRule = authorizationRuleMapper.selectByPrimaryKey(id);
        logger.info("查询授权检查参数信息 id:{}", id);
        if (authorizationRule == null) {
            logger.error("待删除授权检查信息不存在。 id:{}", id);
            throw new AnyTxnParameterException(AnyTxnParameterRespCodeEnum.D_QUERY_AUTH_RULE_NULL_BY_ID_FAULT);
        }
        return ParameterCompare.getBuilder().withBefore(authorizationRule).build(AuthorizationRule.class);
    }

    @Override
    @DeleteParameterAnnotation(tableName = "parm_authorization_rule", tableDesc = "Authorization Check Parameters")
    public ParameterCompare removeByOrgAndTableId(String organizationNumber,String tableId) {
        AuthorizationRule authorizationRule = authorizationRuleSelfMapper.selectByTableId(tableId,organizationNumber);
        logger.info("查询授权检查参数信息 org:{},tableId:{}", organizationNumber,tableId);
        if (authorizationRule == null) {
            logger.error("待删除授权检查信息不存在。 org:{},tableId:{}", organizationNumber,tableId);
            throw new AnyTxnParameterException(AnyTxnParameterRespCodeEnum.D_QUERY_AUTH_RULE_NULL_BY_ID_FAULT);
        }
        return ParameterCompare.getBuilder().withBefore(authorizationRule).build(AuthorizationRule.class);
    }

    @Override
    @InsertParameterAnnotation(tableName = "parm_authorization_rule", tableDesc = "Authorization Check Parameters")
    public ParameterCompare addAuthorizationRule(AuthorizationRuleDTO authorizationRuleDTO) {
        //转换
        AuthorizationRule authorizationRule ;

        checkParam(authorizationRuleDTO);

        authorizationRule = BeanMapping.copy(authorizationRuleDTO, AuthorizationRule.class);
        int isExists = authorizationRuleSelfMapper.isExists(authorizationRule.getTableId(),authorizationRuleDTO.getOrganizationNumber());
        if (isExists > 0) {
            logger.error("exist! authorizationRule!");
            throw new AnyTxnParameterException(AnyTxnParameterRespCodeEnum.D_EXIST_AUTH_RULE_FAULT);
        }
        authorizationRule.setId(String.valueOf(numberIdGenerator.generateId(TenantUtils.getTenantId())));
        return ParameterCompare.getBuilder().withAfter(authorizationRule).build(AuthorizationRule.class);
    }



    private void checkParam(AuthorizationRuleDTO authorizationRuleDTO) {
        if (authorizationRuleDTO == null) {
            logger.error("authorizationRuleDTO is null!");
            throw new AnyTxnParameterException(AnyTxnParameterRespCodeEnum.P_PARAMETER_EMPTY_FAULT);
        }

        if (authorizationRuleDTO.getRemovalLimitDays() == null){
            authorizationRuleDTO.setRemovalLimitDays(0);
        }else if (authorizationRuleDTO.getRemovalLimitDays() < 0){
            throw new AnyTxnParameterException(AnyTxnParameterRespCodeEnum.P_PARAMETER_EMPTY_FAULT);
        }

    }

    @Override
    public AuthorizationRuleDTO findAuthorizationByTableId(String tableId, String organizationNumber) {
        AuthorizationRule authorizationRule = authorizationRuleSelfMapper.selectByTableId(tableId, organizationNumber);
        AuthorizationRuleDTO authorizationRuleDTO = null;
        if(authorizationRule !=null ){
            authorizationRuleDTO = BeanMapping.copy(authorizationRule, AuthorizationRuleDTO.class);
        }
        return authorizationRuleDTO;
    }

    @Override
    public AuthorizationRuleDTO findAuthorizationById(String id) {
        AuthorizationRule authorizationRule = authorizationRuleMapper.selectByPrimaryKey(id);
        return authorizationRule !=null ? BeanMapping.copy(authorizationRule, AuthorizationRuleDTO.class) : null;
    }

    @Override
    @Transactional(rollbackFor = Exception.class,propagation = Propagation.REQUIRED)
    //@ShardingSphereTransactionType(TransactionType.XA)
    protected boolean updateDb(ParmModificationRecord parmModificationRecord) {
        AuthorizationRule authorizationRule = JSON.parseObject(parmModificationRecord.getParmBody(), AuthorizationRule.class);
        authorizationRule.initUpdateDateTime();
        authorizationRuleMapper.updateByPrimaryKeySelective(authorizationRule);
        return true;
    }

    @Override
    @Transactional(rollbackFor = Exception.class,propagation = Propagation.REQUIRED)
    //@ShardingSphereTransactionType(TransactionType.XA)
    protected boolean insertDb(ParmModificationRecord parmModificationRecord) {
        AuthorizationRule authorizationRule = JSON.parseObject(parmModificationRecord.getParmBody(), AuthorizationRule.class);
        authorizationRule.initCreateDateTime();
        authorizationRule.initUpdateDateTime();
        authorizationRuleMapper.insertSelective(authorizationRule);
        return true;
    }

    @Override
    @Transactional(rollbackFor = Exception.class,propagation = Propagation.REQUIRED)
    //@ShardingSphereTransactionType(TransactionType.XA)
    protected boolean deleteDb(ParmModificationRecord parmModificationRecord) {
        AuthorizationRule authorizationRule = JSON.parseObject(parmModificationRecord.getParmBody(), AuthorizationRule.class);
        authorizationRuleMapper.deleteByPrimaryKey(authorizationRule.getId());
        return true;
    }
}
