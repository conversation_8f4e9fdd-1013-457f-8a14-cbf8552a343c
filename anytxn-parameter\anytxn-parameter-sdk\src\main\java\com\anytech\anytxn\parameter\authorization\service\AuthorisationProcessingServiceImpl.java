package com.anytech.anytxn.parameter.authorization.service;

import com.alibaba.fastjson.JSON;
import com.github.pagehelper.Page;
import com.github.pagehelper.PageHelper;
import com.anytech.anytxn.common.core.annotation.db.DeleteParameterAnnotation;
import com.anytech.anytxn.common.core.annotation.db.InsertParameterAnnotation;
import com.anytech.anytxn.common.core.annotation.db.UpdateParameterAnnotation;
import com.anytech.anytxn.business.base.audit.AbstractParameterService;
import com.anytech.anytxn.common.core.base.ParameterCompare;
import com.anytech.anytxn.business.base.audit.ParmModificationRecord;
import com.anytech.anytxn.common.core.utils.OrgNumberUtils;
import com.anytech.anytxn.common.core.base.PageResultDTO;
import com.anytech.anytxn.common.core.utils.BeanMapping;
import com.anytech.anytxn.common.sequence.utils.Number16IdGen;
import com.anytech.anytxn.common.core.utils.TenantUtils;
import com.anytech.anytxn.parameter.base.account.domain.dto.AuthorisationProcessingReqDTO;
import com.anytech.anytxn.parameter.base.account.domain.dto.AuthorisationProcessingResDTO;
import com.anytech.anytxn.parameter.base.account.service.IAuthorisationProcessingService;
import com.anytech.anytxn.parameter.base.common.enums.AnyTxnParameterRespCodeEnum;
import com.anytech.anytxn.parameter.base.common.enums.ParameterRepDetailEnum;
import com.anytech.anytxn.parameter.base.common.exception.AnyTxnParameterException;
import com.anytech.anytxn.parameter.authorization.mapper.ParmAuthorisationProcessingMapper;
import com.anytech.anytxn.parameter.authorization.mapper.ParmAuthorisationProcessingSelfMapper;
import com.anytech.anytxn.parameter.base.authorization.domain.model.ParmAuthorisationProcessing;
//import org.apache.shardingsphere.transaction.annotation.ShardingSphereTransactionType;
//import org.apache.shardingsphere.transaction.core.TransactionType;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.StringUtils;

import java.math.BigDecimal;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 授权处理参数 业务接口实现
 *
 * <AUTHOR>
 * @date 2018-11-26 11:29
 **/
@Service(value = "parm_authorisation_processing_serviceImpl")
public class AuthorisationProcessingServiceImpl extends AbstractParameterService implements IAuthorisationProcessingService {

    private final Logger logger = LoggerFactory.getLogger(AuthorisationProcessingServiceImpl.class);

    @Autowired
    private ParmAuthorisationProcessingMapper parmAuthorisationProcessingMapper;
    @Autowired
    private ParmAuthorisationProcessingSelfMapper parmAuthorisationProcessingSelfMapper;

    @Autowired
    private Number16IdGen numberIdGenerator;

    /**
     * 根据机构号,参数表id查询
     *
     * @param orgNumber 机构号
     * @param tableId   参数表id
     * @return CurrencyRateRes 授权处理参数表返回参数
     */
    @Override
    public AuthorisationProcessingResDTO findByOrgAndTableId(String orgNumber, String tableId)  {
        ParmAuthorisationProcessing parmAuthorisationProcessing = parmAuthorisationProcessingSelfMapper.selectByOrgAndTableId(orgNumber, tableId);
        if (parmAuthorisationProcessing == null) {

            throw new AnyTxnParameterException(AnyTxnParameterRespCodeEnum.D_QUERY_AUTH_PROCESSING_FAULT);
        }

        return BeanMapping.copy(parmAuthorisationProcessing, AuthorisationProcessingResDTO.class);
    }

    /**
     * 根据id查询
     *
     * @param id id
     * @return CurrencyRateRes
     */
    @Override
    public AuthorisationProcessingResDTO findOne(String id)  {
        if (id == null) {

            throw new AnyTxnParameterException(AnyTxnParameterRespCodeEnum.P_PRIMARY_KEY_NULL_FAULT);
        }

        ParmAuthorisationProcessing parmAuthorisationProcessing = parmAuthorisationProcessingMapper.selectByPrimaryKey(id);

        if (parmAuthorisationProcessing == null) {
            logger.error("查询授权处理参数，通过id:{}未查到数据", id);

            throw new AnyTxnParameterException(AnyTxnParameterRespCodeEnum.D_QUERY_AUTH_PROCESSING_BY_ID_FAULT);
        }

        return BeanMapping.copy(parmAuthorisationProcessing, AuthorisationProcessingResDTO.class);
    }

    /**
     * 查询列表 分页
     *
     * @param pageNum  页码
     * @param pageSize 页面容量
     * @return PageResultDTO<CurrencyRateRes>
     */
    @Override
    public PageResultDTO<AuthorisationProcessingResDTO> findPage(Integer pageNum, Integer pageSize, String tableId,String description,String authorisationRemainDays,String authorisationMatchFlag,String authorisationMatchAmount,String authorisationMatchPer, String organizationNumber)  {
        Integer authorisationRemainDaysInt = null;
        BigDecimal authorisationMatchAmountDeci = null;
        BigDecimal authorisationMatchPerDeci = null;
        if(!StringUtils.isEmpty(authorisationRemainDays)){
            try {
                authorisationRemainDaysInt = Integer.parseInt(authorisationRemainDays);
            }catch (NumberFormatException e){
                throw new AnyTxnParameterException(AnyTxnParameterRespCodeEnum.PARAMETER_EXCEPTION, ParameterRepDetailEnum.NUMBER_ERROR);
            }
        }
        if(!StringUtils.isEmpty(authorisationMatchAmount)){
            try {
                authorisationMatchAmountDeci = new BigDecimal(authorisationMatchAmount);
            }catch (NumberFormatException e){
                throw new AnyTxnParameterException(AnyTxnParameterRespCodeEnum.PARAMETER_EXCEPTION, ParameterRepDetailEnum.NUMBER_ERROR);
            }
        }
        if(!StringUtils.isEmpty(authorisationMatchPer)){
            try {
                authorisationMatchPerDeci = new BigDecimal(authorisationMatchPer);
            }catch (NumberFormatException e){
                throw new AnyTxnParameterException(AnyTxnParameterRespCodeEnum.PARAMETER_EXCEPTION, ParameterRepDetailEnum.NUMBER_ERROR);
            }
        }
        Map<String,Object> map = new HashMap<>(16);
        map.put("organizationNumber", StringUtils.isEmpty(organizationNumber) ? OrgNumberUtils.getOrg() : organizationNumber);
        map.put("tableId",tableId);
        map.put("description",description);
        map.put("authorisationMatchFlag",authorisationMatchFlag);
        map.put("authorisationRemainDays",authorisationRemainDays);
        map.put("authorisationRemainDaysInt",authorisationRemainDaysInt);
        map.put("authorisationMatchAmount",authorisationMatchAmount);
        map.put("authorisationMatchAmountDeci",authorisationMatchAmountDeci);
        map.put("authorisationMatchPer",authorisationMatchPer);
        map.put("authorisationMatchPerDeci",authorisationMatchPerDeci);
        logger.debug("分页查询授权处理参数");
        Page<ParmAuthorisationProcessing> page = PageHelper.startPage(pageNum, pageSize);
        List<ParmAuthorisationProcessing> parmAuthorisationProcessings = parmAuthorisationProcessingSelfMapper.selectByCondition(map);
        List<AuthorisationProcessingResDTO> authorisationProcessingRes = BeanMapping.copyList(parmAuthorisationProcessings, AuthorisationProcessingResDTO.class);
        return new PageResultDTO<>(pageNum, pageSize, page.getTotal(),page.getPages(), authorisationProcessingRes);
    }

    /**
     * 修改
     *
     * @param authorisationProcessingReq 授权处理参数传入
     * @return int
     */
    @Override
    @UpdateParameterAnnotation(tableName = "parm_authorisation_processing", tableDesc = "Authorization Processing")
    public ParameterCompare modify(AuthorisationProcessingReqDTO authorisationProcessingReq)  {
        if (authorisationProcessingReq.getId() == null) {

            throw new AnyTxnParameterException(AnyTxnParameterRespCodeEnum.P_PARAMETER_ID_EMPTY_FAULT);
        }
        ParmAuthorisationProcessing parmAuthorisationProcessing = parmAuthorisationProcessingMapper.selectByPrimaryKey(authorisationProcessingReq.getId());
        if (parmAuthorisationProcessing == null) {
            logger.error("修改授权处理参数，通过id:{}未查到数据", authorisationProcessingReq.getId());

            throw new AnyTxnParameterException(AnyTxnParameterRespCodeEnum.D_QUERY_AUTH_PROCESSING_BY_ID_FAULT);
        }
        ParmAuthorisationProcessing authorisationProcessing = parmAuthorisationProcessingSelfMapper.selectByOrgAndTableId(
                authorisationProcessingReq.getOrganizationNumber(),authorisationProcessingReq.getTableId());

        if (authorisationProcessing != null && !authorisationProcessing.getId().equals(authorisationProcessingReq.getId())) {
            logger.warn("授权处理参数已存在，orgNum={},tableId={}",authorisationProcessing.getOrganizationNumber(),authorisationProcessing.getTableId());

            throw new AnyTxnParameterException(AnyTxnParameterRespCodeEnum.D_EXIST_AUTH_PROCESSING_FAULT);
        }
        ParmAuthorisationProcessing processing = BeanMapping.copy(authorisationProcessingReq, ParmAuthorisationProcessing.class);

        return ParameterCompare.getBuilder().withAfter(processing).withBefore(parmAuthorisationProcessing).build(ParmAuthorisationProcessing.class);
    }

    /**
     * 删除
     *
     * @param id id
     * @return Boolean
     */
    @Override
    @DeleteParameterAnnotation(tableName = "parm_authorisation_processing", tableDesc = "Authorization Processing")
    public ParameterCompare remove(String id)  {
        ParmAuthorisationProcessing parmAuthorisationProcessing = parmAuthorisationProcessingMapper.selectByPrimaryKey(id);

        if (parmAuthorisationProcessing == null) {
            logger.error("删除授权处理参数，通过id:{}未查到数据", id);

            throw new AnyTxnParameterException(AnyTxnParameterRespCodeEnum.D_QUERY_AUTH_PROCESSING_BY_ID_FAULT);
        }

        return ParameterCompare.getBuilder().withBefore(parmAuthorisationProcessing).build(ParmAuthorisationProcessing.class);
    }

    /**
     * 新增
     *
     * @param authorisationProcessingReq 授权处理参数传入
     * @return CurrencyRateRes
     */
    @Override
    @InsertParameterAnnotation(tableName = "parm_authorisation_processing", tableDesc = "Authorization Processing")
    public ParameterCompare add(AuthorisationProcessingReqDTO authorisationProcessingReq)  {

        if (authorisationProcessingReq.getOrganizationNumber() == null) {
            authorisationProcessingReq.setOrganizationNumber(OrgNumberUtils.getOrg());
        }

        boolean isExists = parmAuthorisationProcessingSelfMapper.isExists(
                authorisationProcessingReq.getOrganizationNumber(), authorisationProcessingReq.getTableId()) > 0;
        if (isExists) {
            logger.warn("授权处理参数已存在，orgNum={},tableId={}",
                    authorisationProcessingReq.getOrganizationNumber(),authorisationProcessingReq.getTableId());

            throw new AnyTxnParameterException(AnyTxnParameterRespCodeEnum.D_EXIST_AUTH_PROCESSING_FAULT);
        }

        ParmAuthorisationProcessing parmAuthorisationProcessing = BeanMapping.copy(authorisationProcessingReq, ParmAuthorisationProcessing.class);
        parmAuthorisationProcessing.setId(String.valueOf(numberIdGenerator.generateId(TenantUtils.getTenantId())));
        return ParameterCompare.getBuilder().withAfter(parmAuthorisationProcessing).build(ParmAuthorisationProcessing.class);
    }

    @Override
    @Transactional(rollbackFor = Exception.class,propagation = Propagation.REQUIRED)
    //@ShardingSphereTransactionType(TransactionType.XA)
    protected boolean updateDb(ParmModificationRecord parmModificationRecord) {
        ParmAuthorisationProcessing authorisationProcessing = JSON.parseObject(parmModificationRecord.getParmBody(), ParmAuthorisationProcessing.class);
        authorisationProcessing.initUpdateDateTime();
        int i = parmAuthorisationProcessingMapper.updateByPrimaryKeySelective(authorisationProcessing);
        return i > 0;
    }

    @Override
    @Transactional(rollbackFor = Exception.class,propagation = Propagation.REQUIRED)
    //@ShardingSphereTransactionType(TransactionType.XA)
    protected boolean insertDb(ParmModificationRecord parmModificationRecord) {
        ParmAuthorisationProcessing authorisationProcessing = JSON.parseObject(parmModificationRecord.getParmBody(), ParmAuthorisationProcessing.class);
        authorisationProcessing.initCreateDateTime();
        authorisationProcessing.initUpdateDateTime();
        int i = parmAuthorisationProcessingMapper.insertSelective(authorisationProcessing);
        return i > 0;
    }

    @Override
    @Transactional(rollbackFor = Exception.class,propagation = Propagation.REQUIRED)
    //@ShardingSphereTransactionType(TransactionType.XA)
    protected boolean deleteDb(ParmModificationRecord parmModificationRecord) {
        ParmAuthorisationProcessing authorisationProcessing = JSON.parseObject(parmModificationRecord.getParmBody(), ParmAuthorisationProcessing.class);
        int i = parmAuthorisationProcessingMapper.deleteByPrimaryKey(authorisationProcessing.getId());
        return i > 0;
    }
}
