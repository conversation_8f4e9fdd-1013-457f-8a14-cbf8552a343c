package com.anytech.anytxn.parameter.account.service;

import com.alibaba.fastjson.JSON;
import com.github.pagehelper.Page;
import com.github.pagehelper.PageHelper;
import com.anytech.anytxn.common.core.annotation.db.DeleteParameterAnnotation;
import com.anytech.anytxn.common.core.annotation.db.InsertParameterAnnotation;
import com.anytech.anytxn.common.core.annotation.db.UpdateParameterAnnotation;
import com.anytech.anytxn.business.base.audit.AbstractParameterService;
import com.anytech.anytxn.common.core.base.ParameterCompare;
import com.anytech.anytxn.business.base.audit.ParmModificationRecord;
import com.anytech.anytxn.common.core.utils.OrgNumberUtils;
import com.anytech.anytxn.parameter.base.common.constants.Constants;
import com.anytech.anytxn.common.core.base.PageResultDTO;
import com.anytech.anytxn.common.core.utils.BeanMapping;
import com.anytech.anytxn.common.sequence.utils.Number16IdGen;
import com.anytech.anytxn.common.core.utils.TenantUtils;
import com.anytech.anytxn.parameter.base.account.domain.dto.PaymentAllocatedControlReqDTO;
import com.anytech.anytxn.parameter.base.account.domain.dto.PaymentAllocatedControlResDTO;
import com.anytech.anytxn.parameter.base.account.domain.dto.PaymentAllocatedReqDTO;
import com.anytech.anytxn.parameter.base.account.domain.dto.PaymentAllocatedResDTO;
import com.anytech.anytxn.parameter.base.account.service.IPaymentAllocatedService;
import com.anytech.anytxn.parameter.base.common.enums.AnyTxnParameterRespCodeEnum;
import com.anytech.anytxn.parameter.base.common.exception.AnyTxnParameterException;
import com.anytech.anytxn.parameter.account.mapper.ParmPaymentAllocatedControlSelfMapper;
import com.anytech.anytxn.parameter.account.mapper.ParmPaymentAllocatedDefineMapper;
import com.anytech.anytxn.parameter.account.mapper.ParmPaymentAllocatedDefineSelfMapper;
import com.anytech.anytxn.parameter.base.account.domain.model.ParmPaymentAllocatedControl;
import com.anytech.anytxn.parameter.base.account.domain.model.ParmPaymentAllocatedDefine;
//import org.apache.shardingsphere.transaction.annotation.ShardingSphereTransactionType;
//import org.apache.shardingsphere.transaction.core.TransactionType;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.ObjectUtils;
import org.springframework.util.StringUtils;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;

/**
 * 还款分配定义 业务接口
 *
 * <AUTHOR> tingting
 * @date 2018/8/16
 */
@Service(value = "parm_payment_allocated_define_serviceImpl")
public class PaymentAllocatedServiceImpl extends AbstractParameterService implements IPaymentAllocatedService {

    private Logger log = LoggerFactory.getLogger(getClass());

    @Autowired
    private ParmPaymentAllocatedControlSelfMapper parmPaymentAllocatedControlSelfMapper;
    @Autowired
    private ParmPaymentAllocatedDefineMapper parmPaymentAllocatedDefineMapper;
    @Autowired
    private ParmPaymentAllocatedDefineSelfMapper parmPaymentAllocatedDefineSelfMapper;

    @Autowired
    private Number16IdGen numberIdGenerator;

    /**
     * 查询所有还款分配定义参数
     *
     * @param pageSize
     * @param pageNum
     * @return 还款分配定义响应参数
     * @throws AnyTxnParameterException
     */
    @Override
    public PageResultDTO<PaymentAllocatedResDTO> findAll(Integer pageNum, Integer pageSize, PaymentAllocatedReqDTO paymentAllocatedReq) {
        if (null == paymentAllocatedReq) {
            paymentAllocatedReq = new PaymentAllocatedReqDTO();
        }
        paymentAllocatedReq.setOrganizationNumber(StringUtils.isEmpty(paymentAllocatedReq.getOrganizationNumber()) ? OrgNumberUtils.getOrg() : paymentAllocatedReq.getOrganizationNumber());
        Page<ParmPaymentAllocatedDefine> page = PageHelper.startPage(pageNum, pageSize);
        List<ParmPaymentAllocatedDefine> allocatedList = parmPaymentAllocatedDefineSelfMapper.selectByCondition(paymentAllocatedReq);
        if (allocatedList.isEmpty()) {
            log.error("findAll({},{}) allocatedPageResultDTO.getListData() isEmpty", pageNum, pageSize);
            allocatedList = new ArrayList<>();
            //throw new AnyTxnParameterException(AnyTxnParameterRespCodeEnum.D_GET_IS_NULL_FAULT);
        }
        List<PaymentAllocatedResDTO> res = BeanMapping.copyList(allocatedList, PaymentAllocatedResDTO.class);
        return new PageResultDTO<>(pageNum, pageSize, page.getTotal(), page.getPages(), res);
    }

    /**
     * 还款分配定义详情查询
     *
     * @param id
     * @return 还款分配定义响应参数
     * @throws AnyTxnParameterException
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public PaymentAllocatedResDTO findDetail(String id) {
        if (id == null) {
            log.error("findDetail({}) parameter is null", id);
            throw new AnyTxnParameterException(AnyTxnParameterRespCodeEnum.P_PARAMETER_EMPTY_FAULT);
        }
        // 根据id查询还款分配定义表中的信息
        ParmPaymentAllocatedDefine parmPaymentAllocated = parmPaymentAllocatedDefineMapper.selectByPrimaryKey(id);

        List<ParmPaymentAllocatedControl> controlList = null;
        if (parmPaymentAllocated != null) {
            // 根据机构号和tableId查询还款分配控制表中信息
            String orgNum = parmPaymentAllocated.getOrganizationNumber();
            String tableId = parmPaymentAllocated.getTableId();
            controlList = parmPaymentAllocatedControlSelfMapper.seletByOrgAndTableId(orgNum, tableId);
            // 组装数据
            PaymentAllocatedResDTO res = new PaymentAllocatedResDTO();
            res.setControlReqList(BeanMapping.copyList(controlList, PaymentAllocatedControlResDTO.class));

            BeanUtils.copyProperties(parmPaymentAllocated, res);
            return res;
        } else {
            log.error("findDetail({}) selectByPrimaryKey is null", id);
            throw new AnyTxnParameterException(AnyTxnParameterRespCodeEnum.D_QUERY_PARM_PAYMENT_ALLOCATED_BY_ID_FAULT);
        }
    }

    /**
     * 根据表状态查询还款分配定义
     *
     * @param status
     * @return 还款分配定义响应参数
     * @throws AnyTxnParameterException
     */
    @Override
    public List<PaymentAllocatedResDTO> findByStatus(String status) {
        List<PaymentAllocatedResDTO> paymentAllocatedResList = null;
        if (!StringUtils.isEmpty(status)) {
            List<ParmPaymentAllocatedDefine> parmPaymentAllocatedList = parmPaymentAllocatedDefineSelfMapper.selectByStatus(status, OrgNumberUtils.getOrg());
            paymentAllocatedResList = BeanMapping.copyList(parmPaymentAllocatedList, PaymentAllocatedResDTO.class);
        }
        return paymentAllocatedResList;
    }

    /**
     * 根据机构号和tableId查询
     *
     * @param orgNum  机构号
     * @param tableId 表Id
     * @return 还款分配定义响应参数
     * @throws AnyTxnParameterException
     */
    @Override
    public PaymentAllocatedResDTO findByOrgAndTableId(String orgNum, String tableId) {
        ParmPaymentAllocatedDefine parmPaymentAllocated = parmPaymentAllocatedDefineSelfMapper.isExsit(orgNum, tableId);
        return BeanMapping.copy(parmPaymentAllocated, PaymentAllocatedResDTO.class);
    }

    /**
     * 新增还款分配定义
     *
     * @param paymentAllocatedReq
     * @return 还款分配定义响应参数
     * @throws AnyTxnParameterException
     */
    @Transactional(rollbackFor = Exception.class)
    @Override
    @InsertParameterAnnotation(tableName = "parm_payment_allocated_define", tableDesc = "Strike Balance Sequence", isJoinTable = true)
    public ParameterCompare addPaymentAllocated(PaymentAllocatedReqDTO paymentAllocatedReq) {
        // 判断还款分配定义表是否唯一约束冲突
        ParmPaymentAllocatedDefine isExsit = parmPaymentAllocatedDefineSelfMapper.isExsit(OrgNumberUtils.getOrg(paymentAllocatedReq.getOrganizationNumber()), paymentAllocatedReq.getTableId());
        if (isExsit != null) {
            throw new AnyTxnParameterException(AnyTxnParameterRespCodeEnum.D_EXIST_PARM_PAYMENT_ALLOCATED_BY_ID_FAULT);
        }
        paymentAllocatedReq.setId(String.valueOf(numberIdGenerator.generateId(TenantUtils.getTenantId())));
        return ParameterCompare.getBuilder()
                .withMainParmId(paymentAllocatedReq.getTableId())
                .withAfter(paymentAllocatedReq)
                .build(PaymentAllocatedReqDTO.class);
    }

    /**
     * 编辑还款分配定义
     *
     * @param paymentAllocatedReq
     * @return 还款分配定义响应参数
     * @throws AnyTxnParameterException
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    @UpdateParameterAnnotation(tableName = "parm_payment_allocated_define", tableDesc = "Strike Balance Sequence", isJoinTable = true)
    public ParameterCompare modifyPaymentAllocated(PaymentAllocatedReqDTO paymentAllocatedReq) {
        ParmPaymentAllocatedDefine parmPaymentAllocatedDefine = parmPaymentAllocatedDefineMapper.selectByPrimaryKey(paymentAllocatedReq.getId());
        if (ObjectUtils.isEmpty(parmPaymentAllocatedDefine)) {
            log.error("根据主键ID查询还款分配定义表 数据不存在");
            throw new AnyTxnParameterException(AnyTxnParameterRespCodeEnum.D_DATA_NOT_EXISTS_FAULT);
        }
        PaymentAllocatedResDTO detail = findDetail(parmPaymentAllocatedDefine.getId());

        return ParameterCompare.getBuilder()
                .withMainParmId(paymentAllocatedReq.getTableId())
                .withAfter(paymentAllocatedReq)
                .withBefore(BeanMapping.copy(detail, PaymentAllocatedReqDTO.class))
                .build(PaymentAllocatedReqDTO.class);
    }

    /**
     * 删除还款分配定义
     *
     * @param id
     * @return 还款分配定义响应参数
     * @throws AnyTxnParameterException
     */
    @Transactional(rollbackFor = Exception.class)
    @Override
    @DeleteParameterAnnotation(tableName = "parm_payment_allocated_define", tableDesc = "Strike Balance Sequence", isJoinTable = true)
    public ParameterCompare removePaymentAllocated(String id) {
        int cnt = 0;
        // 如果id不存在，则查询该记录
        if (null == id) {
            throw new AnyTxnParameterException(AnyTxnParameterRespCodeEnum.P_PARAMETER_EMPTY_FAULT);
        }
        ParmPaymentAllocatedDefine parmPaymentAllocated = parmPaymentAllocatedDefineMapper.selectByPrimaryKey(id);
        // 如果该记录不存在，抛出异常，否则就删除该记录
        if (null == parmPaymentAllocated) {
            throw new AnyTxnParameterException(AnyTxnParameterRespCodeEnum.D_QUERY_PARM_PAYMENT_ALLOCATED_BY_ID_FAULT);
        }

        return ParameterCompare.getBuilder()
                .withMainParmId(parmPaymentAllocated.getTableId())
                .withBefore(parmPaymentAllocated)
                .build(ParmPaymentAllocatedDefine.class);
    }

    @Override
    @Transactional(rollbackFor = Exception.class, propagation = Propagation.REQUIRED)
    //@ShardingSphereTransactionType(TransactionType.XA)
    protected boolean updateDb(ParmModificationRecord parmModificationRecord) {
        PaymentAllocatedReqDTO paymentAllocatedReq = JSON.parseObject(parmModificationRecord.getParmBody(), PaymentAllocatedReqDTO.class);
        // 更新还款分配定义表
        ParmPaymentAllocatedDefine paymentAllocated = new ParmPaymentAllocatedDefine();
        BeanMapping.copy(paymentAllocatedReq, paymentAllocated);
        paymentAllocated.initUpdateDateTime();
        paymentAllocated.initCreateDateTime();
        paymentAllocated.setUpdateBy(parmModificationRecord.getApplicationBy());
        parmPaymentAllocatedDefineMapper.updateByPrimaryKeySelective(paymentAllocated);
        // 更新还款分配控制表
        List<PaymentAllocatedControlReqDTO> list = paymentAllocatedReq.getControlReqList();
        parmPaymentAllocatedControlSelfMapper.deleteByOrgNumAndTableId(paymentAllocatedReq.getOrganizationNumber(), paymentAllocatedReq.getTableId());
        List<ParmPaymentAllocatedControl> updateList = BeanMapping.copyList(list, ParmPaymentAllocatedControl.class);
        for (ParmPaymentAllocatedControl control : updateList) {
            control.setId(numberIdGenerator.generateId(TenantUtils.getTenantId()));
            control.setTableId(paymentAllocatedReq.getTableId());
            control.setUpdateBy(Constants.DEFAULT_USER);
            control.setCreateTime(LocalDateTime.now());
            control.setUpdateTime(LocalDateTime.now());
            control.setVersionNumber(1L);
            control.setOrganizationNumber(paymentAllocated.getOrganizationNumber());
        }
        if (!list.isEmpty()) {
            parmPaymentAllocatedControlSelfMapper.insertList(updateList);
        }
        return true;
    }

    @Override
    @Transactional(rollbackFor = Exception.class, propagation = Propagation.REQUIRED)
    //@ShardingSphereTransactionType(TransactionType.XA)
    protected boolean insertDb(ParmModificationRecord parmModificationRecord) {
        PaymentAllocatedReqDTO paymentAllocatedReq = JSON.parseObject(parmModificationRecord.getParmBody(), PaymentAllocatedReqDTO.class);
        // 还款分配定义表插入数据
        ParmPaymentAllocatedDefine paymentAllocated = new ParmPaymentAllocatedDefine();
        BeanMapping.copy(paymentAllocatedReq, paymentAllocated);
        paymentAllocated.initCreateDateTime();
        paymentAllocated.initUpdateDateTime();
        paymentAllocated.setUpdateBy(parmModificationRecord.getApplicationBy());
        parmPaymentAllocatedDefineMapper.insert(paymentAllocated);
        // 还款分配控制表插入数据
        List<PaymentAllocatedControlReqDTO> list = paymentAllocatedReq.getControlReqList();
        if (!list.isEmpty()) {
            for (PaymentAllocatedControlReqDTO reqList : list) {
                reqList.setOrganizationNumber(paymentAllocated.getOrganizationNumber());
                reqList.setUpdateBy(Constants.DEFAULT_USER);
                reqList.setVersionNumber(1);
                reqList.setId(numberIdGenerator.generateId(TenantUtils.getTenantId()));
                reqList.setTableId(paymentAllocatedReq.getTableId());
                reqList.setCreateTime(LocalDateTime.now());
                reqList.setUpdateTime(LocalDateTime.now());
            }
            List<ParmPaymentAllocatedControl> insertList = BeanMapping.copyList(list, ParmPaymentAllocatedControl.class);
            int count = parmPaymentAllocatedControlSelfMapper.insertList(insertList);
            if (count != list.size()) {
                throw new AnyTxnParameterException(AnyTxnParameterRespCodeEnum.D_INSERT_PARM_PAYMENT_ALLOCATED_FAULT);
            }
        }
        paymentAllocatedReq.setControlReqList(list);
        return true;
    }

    @Override
    @Transactional(rollbackFor = Exception.class, propagation = Propagation.REQUIRED)
    //@ShardingSphereTransactionType(TransactionType.XA)
    protected boolean deleteDb(ParmModificationRecord parmModificationRecord) {
        ParmPaymentAllocatedDefine parmPaymentAllocated = JSON.parseObject(parmModificationRecord.getParmBody(), ParmPaymentAllocatedDefine.class);
        // 先删除还款分配控制
        List<ParmPaymentAllocatedControl> controlList = parmPaymentAllocatedControlSelfMapper.seletByOrgAndTableId(parmPaymentAllocated.getOrganizationNumber(),
                parmPaymentAllocated.getTableId());

        if (!controlList.isEmpty()) {
            parmPaymentAllocatedControlSelfMapper.deleteByOrgNumAndTableId(parmPaymentAllocated.getOrganizationNumber(),
                    parmPaymentAllocated.getTableId());
        }
        parmPaymentAllocatedDefineMapper.deleteByPrimaryKey(parmPaymentAllocated.getId());
        return true;
    }
}
