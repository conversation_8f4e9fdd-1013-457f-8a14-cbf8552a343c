package com.anytech.anytxn.business.customer.service;

import com.anytech.anytxn.business.base.customer.domain.dto.CorporateCustomerInfoDTO;
import com.anytech.anytxn.business.dao.customer.mapper.CorporateCustomerInfoMapper;
import com.anytech.anytxn.business.dao.customer.mapper.CorporateTopDownReferenceSelfMapper;
import com.anytech.anytxn.business.dao.customer.model.CorporateCustomerInfo;
import com.anytech.anytxn.business.dao.customer.model.CorporateTopDownReference;
import com.anytech.anytxn.common.core.utils.LoginUserUtils;
import com.anytech.anytxn.common.core.utils.OrgNumberUtils;
import com.anytech.anytxn.common.core.utils.TenantUtils;
import com.anytech.anytxn.common.sequence.utils.Number16IdGen;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockedStatic;
import org.mockito.junit.jupiter.MockitoExtension;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;

import static org.assertj.core.api.Assertions.assertThat;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.*;

/**
 * CorporateTopDownReferenceServiceImpl单元测试类
 * 
 * <AUTHOR>
 * @date 2024-01-20
 */
@ExtendWith(MockitoExtension.class)
class CorporateTopDownReferenceServiceTest {

    @Mock
    private CorporateTopDownReferenceSelfMapper corporateTopDownReferenceSelfMapper;

    @Mock
    private CorporateCustomerInfoMapper corporateCustomerInfoMapper;

    @Mock
    private Number16IdGen number16IdGen;

    @InjectMocks
    private CorporateTopDownReferenceServiceImpl corporateTopDownReferenceService;

    private CorporateCustomerInfoDTO parentCustomerDTO;
    private CorporateCustomerInfoDTO childCustomerDTO;
    private CorporateCustomerInfo parentCustomerInfo;
    private CorporateCustomerInfo childCustomerInfo;
    private CorporateTopDownReference topDownReference;

    @BeforeEach
    void setUp() {
        // 在setUp中Mock静态工具类，确保DTO对象创建成功
        try (MockedStatic<OrgNumberUtils> mockOrgUtils = mockStatic(OrgNumberUtils.class)) {
            mockOrgUtils.when(OrgNumberUtils::getOrg).thenReturn("ORG001");
            
            // 初始化父公司客户DTO和子公司客户DTO
            setupCustomerDTOs();
            
            // 初始化实体对象

            // 初始化父公司客户实体
            parentCustomerInfo = new CorporateCustomerInfo();
            parentCustomerInfo.setCorporateCustomerId("PARENT001");
            parentCustomerInfo.setHierarchyLevel("9");
            parentCustomerInfo.setStatus("1");

            // 初始化子公司客户实体
            childCustomerInfo = new CorporateCustomerInfo();
            childCustomerInfo.setCorporateCustomerId("CHILD001");
            childCustomerInfo.setHierarchyLevel("0");
            childCustomerInfo.setStatus("1");

            // 初始化关系实体
            topDownReference = new CorporateTopDownReference();
            topDownReference.setId("REF001");
            topDownReference.setCorporateParentId("PARENT001");
            topDownReference.setCorporateChildId("CHILD001");
            topDownReference.setCorporateRegistrationId("REG001");
            topDownReference.setCorporateParentStatus("1");
            topDownReference.setCorporateChildStatus("1");
            topDownReference.setCorporateParentLevel("9");
            topDownReference.setCorporateChildLevel("0");
        }
    }

    @Test
    void shouldReturnAllChildren_whenValidParentIdProvided() {
        // Given
        String parentId = "PARENT001";
        List<String> directChildren = Arrays.asList("CHILD001", "CHILD002");
        List<String> grandChildren = Arrays.asList("GRANDCHILD001");
        
        when(corporateTopDownReferenceSelfMapper.selectByCorpParentId("PARENT001"))
            .thenReturn(directChildren);
        when(corporateTopDownReferenceSelfMapper.selectByCorpParentId("CHILD001"))
            .thenReturn(grandChildren);
        when(corporateTopDownReferenceSelfMapper.selectByCorpParentId("CHILD002"))
            .thenReturn(new ArrayList<>());
        when(corporateTopDownReferenceSelfMapper.selectByCorpParentId("GRANDCHILD001"))
            .thenReturn(new ArrayList<>());

        // When
        List<String> result = corporateTopDownReferenceService.selectAllByCorporateParentId(parentId);

        // Then
        assertThat(result).hasSize(3);
        assertThat(result).containsExactlyInAnyOrder("CHILD001", "CHILD002", "GRANDCHILD001");
        verify(corporateTopDownReferenceSelfMapper, times(4)).selectByCorpParentId(any());
    }

    @Test
    void shouldReturnEmptyList_whenNoChildrenExist() {
        // Given
        String parentId = "PARENT001";
        when(corporateTopDownReferenceSelfMapper.selectByCorpParentId(parentId))
            .thenReturn(new ArrayList<>());

        // When
        List<String> result = corporateTopDownReferenceService.selectAllByCorporateParentId(parentId);

        // Then
        assertThat(result).isEmpty();
        verify(corporateTopDownReferenceSelfMapper, times(1)).selectByCorpParentId(parentId);
    }

    @Test
    void shouldReturnHierLevelZeroChildren_whenValidParentIdProvided() {
        // Given
        String parentId = "PARENT001";
        List<String> allChildren = Arrays.asList("CHILD001", "CHILD002", "CHILD003");
        
        try (MockedStatic<OrgNumberUtils> mockOrgUtils = mockStatic(OrgNumberUtils.class)) {
            mockOrgUtils.when(OrgNumberUtils::getOrg).thenReturn("ORG001");
            
            // Mock parent customer info - hierarchy level 9 (not included in result)
            when(corporateCustomerInfoMapper.selectByPrimaryKey(parentId, "ORG001"))
                .thenReturn(parentCustomerInfo);
            
            // Mock all children method
            when(corporateTopDownReferenceSelfMapper.selectByCorpParentId(parentId))
                .thenReturn(allChildren);
            when(corporateTopDownReferenceSelfMapper.selectByCorpParentId("CHILD001"))
                .thenReturn(new ArrayList<>());
            when(corporateTopDownReferenceSelfMapper.selectByCorpParentId("CHILD002"))
                .thenReturn(new ArrayList<>());
            when(corporateTopDownReferenceSelfMapper.selectByCorpParentId("CHILD003"))
                .thenReturn(new ArrayList<>());
            
            // Mock child customer info - only CHILD001 and CHILD003 have hierarchy level 0
            CorporateCustomerInfo child1 = new CorporateCustomerInfo();
            child1.setCorporateCustomerId("CHILD001");
            child1.setHierarchyLevel("0");
            
            CorporateCustomerInfo child2 = new CorporateCustomerInfo();
            child2.setCorporateCustomerId("CHILD002");
            child2.setHierarchyLevel("1");
            
            CorporateCustomerInfo child3 = new CorporateCustomerInfo();
            child3.setCorporateCustomerId("CHILD003");
            child3.setHierarchyLevel("0");
            
            when(corporateCustomerInfoMapper.selectByPrimaryKey("CHILD001", "ORG001"))
                .thenReturn(child1);
            when(corporateCustomerInfoMapper.selectByPrimaryKey("CHILD002", "ORG001"))
                .thenReturn(child2);
            when(corporateCustomerInfoMapper.selectByPrimaryKey("CHILD003", "ORG001"))
                .thenReturn(child3);

            // When
            List<String> result = corporateTopDownReferenceService.selectHierLevelZeroByCorporateParentId(parentId);

            // Then - parent is hierarchy level 9 (not included), only 2 children with level 0
            assertThat(result).hasSize(2);
            assertThat(result).containsExactlyInAnyOrder("CHILD001", "CHILD003");
        }
    }

    @Test
    void shouldIncludeParent_whenParentIsHierLevelZero() {
        // Given
        String parentId = "PARENT001";

        try (MockedStatic<OrgNumberUtils> mockOrgUtils = mockStatic(OrgNumberUtils.class)) {
            mockOrgUtils.when(OrgNumberUtils::getOrg).thenReturn("ORG001");
            
            CorporateCustomerInfo zeroLevelParent = new CorporateCustomerInfo();
            zeroLevelParent.setCorporateCustomerId("PARENT001");
            zeroLevelParent.setHierarchyLevel("0");
            
            when(corporateCustomerInfoMapper.selectByPrimaryKey(parentId, "ORG001"))
                .thenReturn(zeroLevelParent);
            when(corporateTopDownReferenceSelfMapper.selectByCorpParentId(parentId))
                .thenReturn(new ArrayList<>());

            // When
            List<String> result = corporateTopDownReferenceService.selectHierLevelZeroByCorporateParentId(parentId);

            // Then
            assertThat(result).hasSize(1);
            assertThat(result).contains("PARENT001");
        }
    }

    @Test
    void shouldAddCorporateReference_whenValidCorporateInfoProvided() {
        // Given
        try (MockedStatic<TenantUtils> mockTenantUtils = mockStatic(TenantUtils.class);
             MockedStatic<OrgNumberUtils> mockOrgUtils = mockStatic(OrgNumberUtils.class)) {
            
            mockTenantUtils.when(TenantUtils::getTenantId).thenReturn("TENANT001");
            mockOrgUtils.when(OrgNumberUtils::getOrg).thenReturn("ORG001");
            when(number16IdGen.generateId("TENANT001")).thenReturn(123456789L);

            // When
            corporateTopDownReferenceService.addCorporateTopDownReference(childCustomerDTO, parentCustomerDTO);

            // Then
            verify(corporateTopDownReferenceSelfMapper).insertSelective(argThat(reference -> {
                assertThat(reference.getId()).isEqualTo("123456789");
                assertThat(reference.getCorporateChildId()).isEqualTo("CHILD001");
                assertThat(reference.getCorporateParentId()).isEqualTo("PARENT001");
                assertThat(reference.getCorporateRegistrationId()).isEqualTo("REG002");
                assertThat(reference.getCorporateChildStatus()).isEqualTo("1");
                assertThat(reference.getCorporateParentStatus()).isEqualTo("1");
                assertThat(reference.getCorporateChildLevel()).isEqualTo("0");
                assertThat(reference.getCorporateParentLevel()).isEqualTo("9");
                assertThat(reference.getCreateBy()).isEqualTo("admin");
                assertThat(reference.getRecordVersionNumber()).isEqualTo(1L);
                return true;
            }));
        }
    }

    @Test
    void shouldAddCorporateReference_whenOnlyChildInfoProvided() {
        // Given
        try (MockedStatic<TenantUtils> mockTenantUtils = mockStatic(TenantUtils.class);
             MockedStatic<OrgNumberUtils> mockOrgUtils = mockStatic(OrgNumberUtils.class)) {
            
            mockTenantUtils.when(TenantUtils::getTenantId).thenReturn("TENANT001");
            mockOrgUtils.when(OrgNumberUtils::getOrg).thenReturn("ORG001");
            when(number16IdGen.generateId("TENANT001")).thenReturn(123456789L);

            // When
            corporateTopDownReferenceService.addCorporateTopDownReference(childCustomerDTO, null);

            // Then
            verify(corporateTopDownReferenceSelfMapper).insertSelective(argThat(reference -> {
                assertThat(reference.getId()).isEqualTo("123456789");
                assertThat(reference.getCorporateChildId()).isEqualTo("CHILD001");
                assertThat(reference.getCorporateParentId()).isNull();
                assertThat(reference.getCorporateRegistrationId()).isEqualTo("REG002");
                assertThat(reference.getCorporateChildStatus()).isEqualTo("1");
                assertThat(reference.getCorporateParentStatus()).isNull();
                return true;
            }));
        }
    }

    @Test
    void shouldAddCorporateReference_whenOnlyParentInfoProvided() {
        // Given
        try (MockedStatic<TenantUtils> mockTenantUtils = mockStatic(TenantUtils.class);
             MockedStatic<OrgNumberUtils> mockOrgUtils = mockStatic(OrgNumberUtils.class)) {
            
            mockTenantUtils.when(TenantUtils::getTenantId).thenReturn("TENANT001");
            mockOrgUtils.when(OrgNumberUtils::getOrg).thenReturn("ORG001");
            when(number16IdGen.generateId("TENANT001")).thenReturn(123456789L);

            // When
            corporateTopDownReferenceService.addCorporateTopDownReference(null, parentCustomerDTO);

            // Then
            verify(corporateTopDownReferenceSelfMapper).insertSelective(argThat(reference -> {
                assertThat(reference.getId()).isEqualTo("123456789");
                assertThat(reference.getCorporateChildId()).isNull();
                assertThat(reference.getCorporateParentId()).isEqualTo("PARENT001");
                assertThat(reference.getCorporateRegistrationId()).isEqualTo("REG001");
                assertThat(reference.getCorporateChildStatus()).isNull();
                assertThat(reference.getCorporateParentStatus()).isEqualTo("1");
                return true;
            }));
        }
    }

    @Test
    void shouldModifyCorporateReference_whenValidCorporateInfoProvided() {
        // Given
        List<CorporateTopDownReference> existingReferences = Arrays.asList(topDownReference);

        try (MockedStatic<LoginUserUtils> mockLoginUtils = mockStatic(LoginUserUtils.class);
             MockedStatic<OrgNumberUtils> mockOrgUtils = mockStatic(OrgNumberUtils.class)) {
            
            mockLoginUtils.when(LoginUserUtils::getLoginUserName).thenReturn("testuser");
            mockOrgUtils.when(OrgNumberUtils::getOrg).thenReturn("ORG001");
            
            CorporateCustomerInfoDTO updatedCustomerDTO = createUpdatedCustomerDTO("CHILD001", "1", "8");
            
            when(corporateTopDownReferenceSelfMapper.selectByParentIdOrChildId("CHILD001"))
                .thenReturn(existingReferences);

            // When
            corporateTopDownReferenceService.modifyCorporateTopDownReference(updatedCustomerDTO);

            // Then
            verify(corporateTopDownReferenceSelfMapper).updateByPrimaryKeySelective(argThat(reference -> {
                assertThat(reference.getCorporateChildLevel()).isEqualTo("1");
                assertThat(reference.getCorporateChildStatus()).isEqualTo("8");
                assertThat(reference.getUpdateBy()).isEqualTo("testuser");
                assertThat(reference.getUpdateTime()).isNotNull();
                return true;
            }));
        }
    }

    @Test
    void shouldUpdateChildInfo_whenCorporateIsChild() {
        // Given
        try (MockedStatic<LoginUserUtils> mockLoginUtils = mockStatic(LoginUserUtils.class);
             MockedStatic<OrgNumberUtils> mockOrgUtils = mockStatic(OrgNumberUtils.class)) {
            
            mockLoginUtils.when(LoginUserUtils::getLoginUserName).thenReturn("testuser");
            mockOrgUtils.when(OrgNumberUtils::getOrg).thenReturn("ORG001");
            
            CorporateTopDownReference reference = new CorporateTopDownReference();
            reference.setId("REF001");
            reference.setCorporateParentId("PARENT001");
            reference.setCorporateChildId("CHILD001");
            reference.setCorporateChildLevel("0");
            reference.setCorporateChildStatus("1");

            List<CorporateTopDownReference> references = Arrays.asList(reference);
            
            CorporateCustomerInfoDTO childUpdateDTO = createUpdatedCustomerDTO("CHILD001", "1", "8");
            
            when(corporateTopDownReferenceSelfMapper.selectByParentIdOrChildId("CHILD001"))
                .thenReturn(references);

            // When
            corporateTopDownReferenceService.modifyCorporateTopDownReference(childUpdateDTO);

            // Then
            verify(corporateTopDownReferenceSelfMapper).updateByPrimaryKeySelective(argThat(ref -> {
                assertThat(ref.getCorporateChildLevel()).isEqualTo("1");
                assertThat(ref.getCorporateChildStatus()).isEqualTo("8");
                assertThat(ref.getUpdateBy()).isEqualTo("testuser");
                return true;
            }));
        }
    }

    @Test
    void shouldUpdateParentInfo_whenCorporateIsParent() {
        // Given
        try (MockedStatic<LoginUserUtils> mockLoginUtils = mockStatic(LoginUserUtils.class);
             MockedStatic<OrgNumberUtils> mockOrgUtils = mockStatic(OrgNumberUtils.class)) {
            
            mockLoginUtils.when(LoginUserUtils::getLoginUserName).thenReturn("testuser");
            mockOrgUtils.when(OrgNumberUtils::getOrg).thenReturn("ORG001");
            
            CorporateTopDownReference reference = new CorporateTopDownReference();
            reference.setId("REF001");
            reference.setCorporateParentId("PARENT001");
            reference.setCorporateChildId("CHILD001");
            reference.setCorporateParentLevel("9");
            reference.setCorporateParentStatus("1");

            List<CorporateTopDownReference> references = Arrays.asList(reference);
            
            CorporateCustomerInfoDTO parentUpdateDTO = createUpdatedCustomerDTO("PARENT001", "8", "8");
            
            when(corporateTopDownReferenceSelfMapper.selectByParentIdOrChildId("PARENT001"))
                .thenReturn(references);

            // When
            corporateTopDownReferenceService.modifyCorporateTopDownReference(parentUpdateDTO);

            // Then
            verify(corporateTopDownReferenceSelfMapper).updateByPrimaryKeySelective(argThat(ref -> {
                assertThat(ref.getCorporateParentLevel()).isEqualTo("8");
                assertThat(ref.getCorporateParentStatus()).isEqualTo("8");
                assertThat(ref.getUpdateBy()).isEqualTo("testuser");
                return true;
            }));
        }
    }

    /**
     * 初始化Customer DTO对象的辅助方法
     */
    private void setupCustomerDTOs() {
        // 初始化父公司客户DTO - 不在构造函数中使用，手动设置字段
        parentCustomerDTO = new CorporateCustomerInfoDTO();
        parentCustomerDTO.setCorporateCustomerId("PARENT001");
        parentCustomerDTO.setCorporateRegistrationId("REG001");
        parentCustomerDTO.setCycleDay(Short.valueOf("15"));
        parentCustomerDTO.setHierarchyLevel("9");
        parentCustomerDTO.setStatus("1");
        parentCustomerDTO.setStatementFlag("1");
        parentCustomerDTO.setOrganizationNumber("ORG001"); // 手动设置，避免构造函数调用

        // 初始化子公司客户DTO - 不在构造函数中使用，手动设置字段
        childCustomerDTO = new CorporateCustomerInfoDTO();
        childCustomerDTO.setCorporateCustomerId("CHILD001");
        childCustomerDTO.setCorporateRegistrationId("REG002");
        childCustomerDTO.setCycleDay(Short.valueOf("10"));
        childCustomerDTO.setHierarchyLevel("0");
        childCustomerDTO.setStatus("1");
        childCustomerDTO.setStatementFlag("1");
        childCustomerDTO.setOrganizationNumber("ORG001"); // 手动设置，避免构造函数调用
    }

    /**
     * 创建更新用的Customer DTO对象的辅助方法
     */
    private CorporateCustomerInfoDTO createUpdatedCustomerDTO(String customerId, String hierarchyLevel, String status) {
        CorporateCustomerInfoDTO dto = new CorporateCustomerInfoDTO();
        dto.setCorporateCustomerId(customerId);
        dto.setHierarchyLevel(hierarchyLevel);
        dto.setStatus(status);
        dto.setOrganizationNumber("ORG001"); // 手动设置，避免构造函数调用
        return dto;
    }
} 