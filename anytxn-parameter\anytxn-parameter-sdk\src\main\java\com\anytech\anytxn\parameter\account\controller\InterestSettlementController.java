package com.anytech.anytxn.parameter.account.controller;

import io.swagger.v3.oas.annotations.tags.Tag;
import com.anytech.anytxn.common.core.base.PageResultDTO;
import com.anytech.anytxn.common.core.utils.OrgNumberUtils;
import com.anytech.anytxn.common.core.base.AnyTxnHttpResponse;
import com.anytech.anytxn.common.core.base.BizBaseController;
import com.anytech.anytxn.parameter.base.account.domain.dto.InterestSettlementDTO;
import com.anytech.anytxn.parameter.base.account.service.IInterestSettlementService;
import com.anytech.anytxn.parameter.base.common.enums.ParameterRepDetailEnum;
import org.springframework.web.bind.annotation.*;

import jakarta.annotation.Resource;

/**
 * Description:  结息参数表
 * date: 2021/5/12 13:54
 *
 * <AUTHOR>
 */
@Tag(name = "结息参数表")
@RestController
public class InterestSettlementController extends BizBaseController {

    @Resource
    private IInterestSettlementService interestSettlementService;

    /**
     * 添加
     * @param  dto 结息参数表
     * @return Object
     */
    @PostMapping(value = "/param/addInterestSettlement")
    public AnyTxnHttpResponse<Object> add(@RequestBody InterestSettlementDTO dto){
        return AnyTxnHttpResponse.success(interestSettlementService.add(dto), ParameterRepDetailEnum.CREATE.message());
    }

    /**
     * 修改
     * @param  dto 结息参数表
     * @return Object
     */
    @PutMapping(value = "/param/modifyInterestSettlement")
    public AnyTxnHttpResponse<Object> modify(@RequestBody InterestSettlementDTO dto){
        return AnyTxnHttpResponse.success(interestSettlementService.modify(dto), ParameterRepDetailEnum.UPDATE.message());
    }

    /**
     * 分页查询
     * @param  pageNum pageSize  organizationNumber
     * @return InterestSettlementDTO
     */
    @GetMapping(value = "/param/interestSettlement/pageNum/{pageNum}/pageSize/{pageSize}")
    public AnyTxnHttpResponse<PageResultDTO<InterestSettlementDTO>> findByPage(@PathVariable(value = "pageNum") Integer pageNum,
                                                                                          @PathVariable(value = "pageSize") Integer pageSize,
                                                                                          @RequestParam(value = "tableId",required = false) String tableId,
                                                                                          @RequestParam(value = "description",required = false) String description,
                                                                                          @RequestParam(value = "organizationNumber",required = false) String organizationNumber){
        return AnyTxnHttpResponse.success(interestSettlementService.findPage(pageNum,pageSize, OrgNumberUtils.getOrg(organizationNumber),tableId, description));
    }

    /**
     * 删除
     * @param  id 结息参数表ID
     * @return Object
     */
    @DeleteMapping(value = "/param/removeInterestSettlement/id/{id}")
    public AnyTxnHttpResponse<Object> remove(@PathVariable String id){
        return AnyTxnHttpResponse.success(interestSettlementService.remove(id), ParameterRepDetailEnum.DEL.message());
    }

    /**
     * 通过ID查看
     * @param  id 结息参数表ID
     * @return InterestSettlementDTO
     */
    @GetMapping(value = "/param/interestSettlement/id/{id}")
    public AnyTxnHttpResponse<InterestSettlementDTO> findById(@PathVariable String id){
        return AnyTxnHttpResponse.success(interestSettlementService.findById(id));
    }
}
