package com.anytech.anytxn.parameter.card.service;

import com.alibaba.fastjson.JSON;
import com.github.pagehelper.Page;
import com.github.pagehelper.PageHelper;
import com.anytech.anytxn.common.core.annotation.db.DeleteParameterAnnotation;
import com.anytech.anytxn.common.core.annotation.db.InsertParameterAnnotation;
import com.anytech.anytxn.common.core.annotation.db.UpdateParameterAnnotation;
import com.anytech.anytxn.business.base.audit.AbstractParameterService;
import com.anytech.anytxn.common.core.base.ParameterCompare;
import com.anytech.anytxn.business.base.audit.ParmModificationRecord;
import com.anytech.anytxn.common.core.base.PageResultDTO;
import com.anytech.anytxn.common.core.utils.BeanMapping;
import com.anytech.anytxn.common.core.utils.OrgNumberUtils;
import com.anytech.anytxn.common.sequence.utils.Number16IdGen;
import com.anytech.anytxn.common.core.utils.TenantUtils;
import com.anytech.anytxn.parameter.base.card.domain.dto.CardFeeDefinitionReqDTO;
import com.anytech.anytxn.parameter.base.card.domain.dto.CardFeeDefinitionResDTO;
import com.anytech.anytxn.parameter.base.card.service.IParmCardFeeDefiniTionService;
import com.anytech.anytxn.parameter.base.common.enums.AnyTxnParameterRespCodeEnum;
import com.anytech.anytxn.parameter.base.common.enums.ParameterRepDetailEnum;
import com.anytech.anytxn.parameter.base.common.exception.AnyTxnParameterException;
import com.anytech.anytxn.parameter.card.mapper.broadcast.ParmCardFeeDefinitionMapper;
import com.anytech.anytxn.parameter.card.mapper.broadcast.ParmCardFeeDefinitionSelfMapper;
import com.anytech.anytxn.parameter.base.card.domain.model.ParmCardFeeDefinition;
//import org.apache.shardingsphere.transaction.annotation.ShardingSphereTransactionType;
//import org.apache.shardingsphere.transaction.core.TransactionType;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.StringUtils;

import java.util.List;

/**
 * <pre>
 * Description  卡片费用参数定义管理
 * Dept:		江融信
 * Author:		lichao
 * Version:		1.0
 * Create at:	2020-08-18 11:14
 *
 * 修改历史:
 * 日期    作者    版本  修改描述
 * ------------------------------------------------------------------
 **/
@Service(value = "parm_card_fee_definition_serviceImpl")
public class ParmCardFeeDefiniTionServiceImpl extends AbstractParameterService implements IParmCardFeeDefiniTionService {
    private final Logger log = LoggerFactory.getLogger(ParmCardFeeDefiniTionServiceImpl.class);

    @Autowired
    private ParmCardFeeDefinitionMapper parmCardFeeDefinitionMapper;
    @Autowired
    private Number16IdGen numberIdGenerator;
    @Autowired
    private ParmCardFeeDefinitionSelfMapper parmCardFeeDefinitionSelfMapper;

    /**
     * 分页查询卡片费用参数
     * @param pageSize 页码
     * @param pageNum  每页数目
     * @return 卡片费用响应参数
     *
     */
    @Override
    public PageResultDTO<CardFeeDefinitionResDTO> findByPage(Integer pageNum, Integer pageSize,String feeCode,String description,String feeTypeCode, String organizationNumber) {
        Page<ParmCardFeeDefinition> page = PageHelper.startPage(pageNum, pageSize);
        organizationNumber = StringUtils.isEmpty(organizationNumber) ? OrgNumberUtils.getOrg() : organizationNumber;
        List<ParmCardFeeDefinition> cardFeeDefinitionList = parmCardFeeDefinitionMapper.selectByCondition(feeCode, description, feeTypeCode, organizationNumber);
        List<CardFeeDefinitionResDTO> res = BeanMapping.copyList(cardFeeDefinitionList,CardFeeDefinitionResDTO.class);
        return new PageResultDTO<>(pageNum, pageSize,page.getTotal(),page.getPages(),res);
    }

    /**
     *根据ID主键查询卡片费用参数
     * @param id 技术主键
     * @return 卡片费用响应参数
     */
    @Override
    public CardFeeDefinitionResDTO findById(String id) {
        //判断id是否为空和通过ID查询到的数据是否为空
        ParmCardFeeDefinition parmCardFeeDefinition = judgeIsNull(id);
        return BeanMapping.copy(parmCardFeeDefinition,CardFeeDefinitionResDTO.class);
    }

    /**
     * 添加卡片费用参数
     * @param cardFeeDefinitionReqDTO 卡片费用请求参数
     * @return 卡片费用响应参数
     **/
    @Override
    @InsertParameterAnnotation(tableName = "parm_card_fee_definition", tableDesc = "Card Fee")
    public ParameterCompare add(CardFeeDefinitionReqDTO cardFeeDefinitionReqDTO) {
        //根据机构号、参数表ID查询是否已存在
        boolean isExists = parmCardFeeDefinitionSelfMapper.isExistByOrgAndTid(OrgNumberUtils.getOrg(cardFeeDefinitionReqDTO.getOrganizationNumber()), cardFeeDefinitionReqDTO.getFeeCode());
        if(isExists){
            log.error("卡片费用参数已存在");
            throw new AnyTxnParameterException(AnyTxnParameterRespCodeEnum.P_PARAMETER_DATA_EXIST);
        }
        ParmCardFeeDefinition parmCardFeeDefinitionReq = BeanMapping.copy(cardFeeDefinitionReqDTO,ParmCardFeeDefinition.class);
        parmCardFeeDefinitionReq.setId(String.valueOf(numberIdGenerator.generateId(TenantUtils.getTenantId())));
        return ParameterCompare.getBuilder().withAfter(parmCardFeeDefinitionReq).build(ParmCardFeeDefinition.class);
    }

    /**
     * 修改卡片费用参数
     * @param cardFeeDefinitionReqDTO 卡片费用请求参数
     * @return 卡片费用响应参数
     **/
    @Override
    @UpdateParameterAnnotation(tableName = "parm_card_fee_definition", tableDesc ="Card Fee")
    public ParameterCompare modify(CardFeeDefinitionReqDTO cardFeeDefinitionReqDTO) {
        //判断修改的数据是否存在
        ParmCardFeeDefinition parmCardFeeDefinition = judgeIsNull(cardFeeDefinitionReqDTO.getId());
        //判断修改后的数据是否唯一
        if(!cardFeeDefinitionReqDTO.getOrganizationNumber().equals(parmCardFeeDefinition.getOrganizationNumber())||
           !cardFeeDefinitionReqDTO.getFeeCode().equals(parmCardFeeDefinition.getFeeCode())){
            boolean isExists = parmCardFeeDefinitionSelfMapper.isExistByOrgAndTid(cardFeeDefinitionReqDTO.getOrganizationNumber(),cardFeeDefinitionReqDTO.getFeeCode());
            if(isExists){
                log.error("卡片费用参数已存在");
                throw new AnyTxnParameterException(AnyTxnParameterRespCodeEnum.P_PARAMETER_DATA_EXIST, ParameterRepDetailEnum.CARD_EXIST);
            }
        }
        ParmCardFeeDefinition updateCardFeeDefinition = BeanMapping.copy(cardFeeDefinitionReqDTO,ParmCardFeeDefinition.class);

        return ParameterCompare.getBuilder().withAfter(updateCardFeeDefinition).withBefore(parmCardFeeDefinition).build(ParmCardFeeDefinition.class);
    }

    /**
     * 根据ID删除卡片费用参数
     * @param id 技术主键
     * @return Boolearn
     **/
    @Override
    @DeleteParameterAnnotation(tableName = "parm_card_fee_definition", tableDesc = "Card Fee")
    public ParameterCompare remove(String id) {
        //判断id是否为空和通过ID查询到的数据是否为空
        ParmCardFeeDefinition parmCardFeeDefinition = judgeIsNull(id);
        return ParameterCompare.getBuilder().withBefore(parmCardFeeDefinition).build(ParmCardFeeDefinition.class);
    }

    /**
     * 根据机构号、费用子类,查询卡片费用定义表
     * @param orgNumber 机构号
     * @param subFeeTypeCode 费用子类
     * @return List<CardFeeDefinitionResDTO>
     */
    @Override
    public List<CardFeeDefinitionResDTO> findByOrgAndSubFeeType(String orgNumber,String subFeeTypeCode){
        List<ParmCardFeeDefinition> cardFeeDefinitions = parmCardFeeDefinitionSelfMapper.selectByOrgAndSubFeeType(orgNumber, subFeeTypeCode);
        return BeanMapping.copyList(cardFeeDefinitions,CardFeeDefinitionResDTO.class);
    }

    /**
     * 根据机构号、费用类型,费用自乐,查询卡片费用定义表
     * @param orgNumber 机构号
     * @param feeTypeCode 费用类型
     * @param subFeeTypeCode 费用子类
     * @return List<CardFeeDefinitionResDTO>
     */
    @Override
    public List<CardFeeDefinitionResDTO> findByOrgFeeTypeAndSubFeeType(String orgNumber,String feeTypeCode,String subFeeTypeCode){
        List<ParmCardFeeDefinition> cardFeeDefinitions = parmCardFeeDefinitionSelfMapper.selectByOrgFeeTypeAndSubFeeType(orgNumber, feeTypeCode,subFeeTypeCode);
        return BeanMapping.copyList(cardFeeDefinitions,CardFeeDefinitionResDTO.class);
    }

    /**
     * 根据机构号、费用类型,费用自乐,查询卡片费用定义表
     * @param orgNumber 机构号
     * @param feeTypeCode 费用类型
     * @param subFeeTypeCode 费用子类
     * @param feeCode 费用代码
     * @return ardFeeDefinitionResDTO
     */
    @Override
    public CardFeeDefinitionResDTO findByIndex(String orgNumber,String feeTypeCode,String subFeeTypeCode,String feeCode){
        ParmCardFeeDefinition cardFeeDefinition = parmCardFeeDefinitionSelfMapper.selectByIndex(orgNumber, feeTypeCode,subFeeTypeCode,feeCode);
        if(null == cardFeeDefinition){
            return null;
        }
        return BeanMapping.copy(cardFeeDefinition,CardFeeDefinitionResDTO.class);
    }

    /**
     * 根据机构号、费用代码查询卡片费用定义数据
     * @param orgNumber 机构号
     * @param feeCode 费用代码
     * @return CardFeeDefinitionResDTO
     */
    @Override
    public CardFeeDefinitionResDTO selectByOrgNumAndFeeCode(String orgNumber, String feeCode) {
        ParmCardFeeDefinition parmCardFeeDefinition = parmCardFeeDefinitionSelfMapper.selectByOrgNumAndFeeCode(orgNumber,feeCode);
        return BeanMapping.copy(parmCardFeeDefinition,CardFeeDefinitionResDTO.class);
    }

    /**
     *判断id是否为空和通过ID查询到的数据是否为空
     *@param id 技术主键
     *@return  ParmCardFeeDefinition   卡片费用参数
     */
    private ParmCardFeeDefinition judgeIsNull(String id){
        if (id == null) {
            log.error("参数为空");
            throw new AnyTxnParameterException(AnyTxnParameterRespCodeEnum.P_PARAMETER_EMPTY_FAULT);
        }
        ParmCardFeeDefinition parmCardFeeDefinition = parmCardFeeDefinitionMapper.selectByPrimaryKey(id);
        if(parmCardFeeDefinition == null){
            log.error("根据ID未查询到卡片费用参数数据");
            throw new AnyTxnParameterException(AnyTxnParameterRespCodeEnum.D_GET_CARD_FEE_DEFINITION_BY_ID_FAULT);
        }
        return parmCardFeeDefinition;
    }
    /**
     * 根据卡产品查询卡片的费用定义列表
     * @param orgNumber 机构
     * @param productNumber 卡产品
     * @return  List<CardFeeDefinitionResDTO>
     */
    @Override
    public List<CardFeeDefinitionResDTO> selectByOrgAndProductNumber(String orgNumber, String productNumber, String feeTypeCode) {
        if (StringUtils.isEmpty(orgNumber) || StringUtils.isEmpty(productNumber)) {
            log.error("参数为空");
            throw new AnyTxnParameterException(AnyTxnParameterRespCodeEnum.P_PARAMETER_EMPTY_FAULT);
        }

        List<ParmCardFeeDefinition> list ;

        if (StringUtils.isEmpty(feeTypeCode)) {
            list = parmCardFeeDefinitionSelfMapper.selectByOrgAndProductNumber(orgNumber, productNumber);
        }else{
            list = parmCardFeeDefinitionSelfMapper.selectByOrgAndProductNumberAndType(orgNumber, productNumber,feeTypeCode);
        }
        /*if (list == null || list.isEmpty()) {
            log.error("卡产品配置的卡片费用不存在");
            throw new AnyTxnParameterException(AnyTxnParameterRespCodeEnum.P_PARAMETER_DATA_NOT_EXIST);
        }*/
        return BeanMapping.copyList(list,CardFeeDefinitionResDTO.class);
    }

    /**
     * 查询卡片费用参数所有数据
     * @return List<CardFeeDefinitionResDTO>
     */
    @Override
    public List<CardFeeDefinitionResDTO> selectAll(String orgNumber) {
        List<ParmCardFeeDefinition> list = parmCardFeeDefinitionSelfMapper.selectAll(orgNumber);
        return BeanMapping.copyList(list,CardFeeDefinitionResDTO.class);
    }

    @Override
    @Transactional(rollbackFor = Exception.class,propagation = Propagation.REQUIRED)
    //@ShardingSphereTransactionType(TransactionType.XA)
    protected boolean updateDb(ParmModificationRecord parmModificationRecord) {
        ParmCardFeeDefinition parmCardFeeDefinition = JSON.parseObject(parmModificationRecord.getParmBody(), ParmCardFeeDefinition.class);
        parmCardFeeDefinition.initUpdateDateTime();
        parmCardFeeDefinitionMapper.updateByPrimaryKeySelective(parmCardFeeDefinition);
        return true;
    }

    @Override
    @Transactional(rollbackFor = Exception.class,propagation = Propagation.REQUIRED)
    //@ShardingSphereTransactionType(TransactionType.XA)
    protected boolean insertDb(ParmModificationRecord parmModificationRecord) {
        ParmCardFeeDefinition parmCardFeeDefinition = JSON.parseObject(parmModificationRecord.getParmBody(), ParmCardFeeDefinition.class);
        parmCardFeeDefinitionMapper.insertSelective(parmCardFeeDefinition);
        return true;
    }

    @Override
    @Transactional(rollbackFor = Exception.class,propagation = Propagation.REQUIRED)
    //@ShardingSphereTransactionType(TransactionType.XA)
    protected boolean deleteDb(ParmModificationRecord parmModificationRecord) {
        ParmCardFeeDefinition parmCardFeeDefinition = JSON.parseObject(parmModificationRecord.getParmBody(), ParmCardFeeDefinition.class);
        parmCardFeeDefinitionMapper.deleteByPrimaryKey(parmCardFeeDefinition.getId());
        return true;
    }
}
