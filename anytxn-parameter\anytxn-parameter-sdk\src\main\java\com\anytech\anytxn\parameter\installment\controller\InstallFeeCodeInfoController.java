package com.anytech.anytxn.parameter.installment.controller;

import io.swagger.v3.oas.annotations.tags.Tag;
import io.swagger.v3.oas.annotations.Operation;
import com.anytech.anytxn.common.core.base.PageResultDTO;
import com.anytech.anytxn.common.core.base.AnyTxnHttpResponse;
import com.anytech.anytxn.common.core.base.BizBaseController;
import com.anytech.anytxn.parameter.base.installment.domain.dto.InstallFeeCodeInfoReqDTO;
import com.anytech.anytxn.parameter.base.installment.domain.dto.InstallFeeCodeInfoResDTO;
import com.anytech.anytxn.parameter.base.installment.service.IInstallFeeCodeInfoService;
import com.anytech.anytxn.parameter.base.common.enums.ParameterRepDetailEnum;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import jakarta.validation.Valid;

/**
 *  分期费用代码表
 * <AUTHOR>
 * @date 2019-05-15 10:38
 **/
@Tag(name = "分期费用代码表")
@RestController
public class InstallFeeCodeInfoController extends BizBaseController {

    @Autowired
    private IInstallFeeCodeInfoService installFeeCodeInfoService;

    @Operation(summary = "新增分期费用代码",description = "新增分期费用代码")
    @PostMapping(value = "/param/installfeecodeinfo")
    public AnyTxnHttpResponse<Object> create(@Valid @RequestBody InstallFeeCodeInfoReqDTO feeCodeInfoReqDTO) {
        return AnyTxnHttpResponse.success(installFeeCodeInfoService.add(feeCodeInfoReqDTO),ParameterRepDetailEnum.CREATE.message());

    }

    @Operation(summary = "删除分期费用代码", description = "删除分期费用代码")
    @DeleteMapping(value = "/param/installfeecodeinfo/id/{id}")
    public AnyTxnHttpResponse<Object> remove(@PathVariable String id) {
        return AnyTxnHttpResponse.success(installFeeCodeInfoService.remove(id),ParameterRepDetailEnum.DEL.message());

    }
    @Operation(summary = "修改分期费用代码信息", description = "修改分期费用代码信息")
    @PutMapping(value = "/param/installfeecodeinfo")
    public AnyTxnHttpResponse<Object> modify(@Valid @RequestBody InstallFeeCodeInfoReqDTO feeCodeInfoReqDTO) {
        return AnyTxnHttpResponse.success(installFeeCodeInfoService.modify(feeCodeInfoReqDTO),ParameterRepDetailEnum.UPDATE.message());
    }

    @Operation(summary = "分页查询分期费用代码", description = "分页查询分期费用代码")
    @GetMapping(value = "/param/installfeecodeinfo/pageNum/{pageNum}/pageSize/{pageSize}")
    public AnyTxnHttpResponse<PageResultDTO<InstallFeeCodeInfoResDTO>> getPage(@PathVariable(value = "pageNum")Integer pageNum,
                                                                            @PathVariable(value = "pageSize")Integer pageSize,
                                                                               InstallFeeCodeInfoReqDTO feeCodeInfoReqDTO) {
        PageResultDTO<InstallFeeCodeInfoResDTO> pageResultDto = installFeeCodeInfoService.findPage(pageNum, pageSize, feeCodeInfoReqDTO);
        return AnyTxnHttpResponse.success(pageResultDto);

    }

    @Operation(summary = "根据id查询分期费用代码", description = "根据id查分期费用代码")
    @GetMapping(value = "/param/installfeecodeinfo/id/{id}")
    public AnyTxnHttpResponse<InstallFeeCodeInfoResDTO> getById(@PathVariable String id) {
        InstallFeeCodeInfoResDTO feeCodeInfoResDTO = installFeeCodeInfoService.getById(id);
        return AnyTxnHttpResponse.success(feeCodeInfoResDTO);
    }
    @Operation(summary = "根据机构号 费用代码查询分期费用代码", description = "根据机构号 费用代码查询分期费用代码")
    @GetMapping(value = "/param/installfeecodeinfo/organizationNumber/{organizationNumber}/feeCode/{feeCode}")
    public AnyTxnHttpResponse<InstallFeeCodeInfoResDTO> getOrgNumAndFeeCode(@PathVariable String organizationNumber,
                                                                         @PathVariable("feeCode") String feeCode) {
        InstallFeeCodeInfoResDTO feeCodeInfoResDTO = installFeeCodeInfoService.getByIndex(organizationNumber,feeCode);
        return AnyTxnHttpResponse.success(feeCodeInfoResDTO);
    }
}
