package com.anytech.anytxn.parameter.common.service.system;

import com.alibaba.fastjson.JSON;
import com.github.pagehelper.Page;
import com.github.pagehelper.PageHelper;
import com.anytech.anytxn.common.core.annotation.db.DeleteParameterAnnotation;
import com.anytech.anytxn.common.core.annotation.db.InsertParameterAnnotation;
import com.anytech.anytxn.common.core.annotation.db.UpdateParameterAnnotation;
import com.anytech.anytxn.business.base.audit.AbstractParameterService;
import com.anytech.anytxn.common.core.base.ParameterCompare;
import com.anytech.anytxn.business.base.audit.ParmModificationRecord;
import com.anytech.anytxn.common.core.base.PageResultDTO;
import com.anytech.anytxn.common.core.utils.BeanMapping;
import com.anytech.anytxn.common.core.utils.OrgNumberUtils;
import com.anytech.anytxn.common.sequence.utils.Number16IdGen;
import com.anytech.anytxn.common.core.utils.TenantUtils;
import com.anytech.anytxn.parameter.base.common.enums.AnyTxnParameterRespCodeEnum;
import com.anytech.anytxn.parameter.base.common.exception.AnyTxnParameterException;
import com.anytech.anytxn.parameter.base.common.domain.dto.BillChargeFeeReqDTO;
import com.anytech.anytxn.parameter.base.common.domain.dto.BillChargeFeeResDTO;
import com.anytech.anytxn.parameter.base.common.service.IParmBIllChargeFeeService;
import com.anytech.anytxn.parameter.common.mapper.broadcast.system.ParmBillChargeFeeMapper;
import com.anytech.anytxn.parameter.base.common.domain.model.system.ParmBillChargeFee;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
//import org.apache.shardingsphere.transaction.annotation.ShardingSphereTransactionType;
//import org.apache.shardingsphere.transaction.core.TransactionType;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.util.List;

/**
 * <pre>
 * Description  账单日收费参数管理
 * Dept:		江融信
 * Author:		lichao
 * Version:		1.0
 * Create at:	2020-08-18 11:14
 *
 * 修改历史:
 * 日期    作者    版本  修改描述
 * ------------------------------------------------------------------
 **/
@Slf4j
@Service("parm_bill_charge_fee_serviceImpl")
public class ParmBillChargeFeeServiceImpl extends AbstractParameterService implements IParmBIllChargeFeeService {

    @Autowired
    private Number16IdGen numberIdGenerator;
    @Autowired
    private ParmBillChargeFeeMapper parmBillChargeFeeMapper;

    /**
     * @Description 账单日收费参数add
     * @Method add
     * @Params [req]
     * @Return jrx.anytxn.parameter.system.groups.dto.BillChargeFeeResDto
     * @Date 2020/8/18
     **/
    @Override
    @InsertParameterAnnotation(tableName = "parm_bill_charge_fee",tableDesc = "Bill Charge Fee")
    public ParameterCompare add(BillChargeFeeReqDTO req) {
        if (req.getFeeId() == null) {
            throw new AnyTxnParameterException(AnyTxnParameterRespCodeEnum.P_PARAMETER_EMPTY_FAULT);
        }
        //先判断其参数表ID是否存在
        boolean isExists = parmBillChargeFeeMapper.isExistByOrgAndFid(req.getOrganizationNumber(), req.getFeeId()) > 0;
        if (isExists) {
            log.warn("参数表ID已存在, orgNumber={}, feeId={}",req.getOrganizationNumber(), req.getFeeId());
            throw new AnyTxnParameterException(AnyTxnParameterRespCodeEnum.P_PARAMETER_DATA_EXIST);
        }

        ParmBillChargeFee parmBillChargeFee = BeanMapping.copy(req, ParmBillChargeFee.class);
        parmBillChargeFee.setId(numberIdGenerator.generateId(TenantUtils.getTenantId()) + "");
        return ParameterCompare
                .getBuilder()
                .withAfter(parmBillChargeFee)
                .build(ParmBillChargeFee.class);
    }

    /**
     * @Description 修改
     * @Method modify
     * @Params [req]
     * @Return jrx.anytxn.parameter.system.groups.dto.BillChargeFeeResDto
     * @Date 2020/8/18
     **/
    @Override
    @UpdateParameterAnnotation(tableName = "parm_bill_charge_fee",tableDesc = "Bill Charge Fee")
    public ParameterCompare modify(BillChargeFeeReqDTO req) {
        if (req.getId() == null) {
            throw new AnyTxnParameterException(AnyTxnParameterRespCodeEnum.P_PARAMETER_ID_EMPTY_FAULT);
        }
        ParmBillChargeFee parmBillChargeFee = parmBillChargeFeeMapper.selectByPrimaryKey(req.getId());
        if (parmBillChargeFee == null) {
            log.error("修改{}参数规则, 通过主键id({})未找到数据", req.getFeeId(), req.getId());
            throw new AnyTxnParameterException(AnyTxnParameterRespCodeEnum.P_PARAMETER_DATA_NOT_EXIST);
        }
        // 拷贝修改的数据并更新
        ParmBillChargeFee modify = BeanMapping.copy(req, ParmBillChargeFee.class);
        if ("0".equals(req.getChargeMethod())) {
            modify.setChargeAmount(BigDecimal.ZERO);
            modify.setChargeCycle("");
            modify.setFreeTimes(0l);
        }
        return ParameterCompare
                .getBuilder()
                .withAfter(modify)
                .withBefore(parmBillChargeFee)
                .build(ParmBillChargeFee.class);
    }

    /**
     * @Description 分页查询 pageNum pageSize
     * @Method findPage
     * @Params [pageNum, pageSize]
     * @Return jrx.anytxn.biz.common.dto.PageResultDTO<jrx.anytxn.parameter.system.groups.dto.BillChargeFeeResDto>
     * @Date 2020/8/18
     **/
    @Override
    public PageResultDTO<BillChargeFeeResDTO> findPage(Integer pageNum, Integer pageSize, String organizationNumber,String feeId,String description) {
        Page page = PageHelper.startPage(pageNum, pageSize);
        organizationNumber = StringUtils.isEmpty(organizationNumber) ? OrgNumberUtils.getOrg() : organizationNumber;
        List<ParmBillChargeFee> billChargeFees = parmBillChargeFeeMapper.selectByCondition( organizationNumber,feeId,description);
        List<BillChargeFeeResDTO> listData = BeanMapping.copyList(billChargeFees, BillChargeFeeResDTO.class);
        return new PageResultDTO<>(pageNum, pageSize, page.getTotal(), page.getPages(), listData);
    }

    /**
     * @Description 根据id查询数据
     * @Method findById
     * @Params [id]
     * @Return jrx.anytxn.parameter.system.groups.dto.BillChargeFeeResDto
     * @Date 2020/8/18
     **/
    @Override
    public BillChargeFeeResDTO findById(String id) {
        if (id == null) {
            throw new AnyTxnParameterException(AnyTxnParameterRespCodeEnum.P_PARAMETER_EMPTY_FAULT);
        }
        BillChargeFeeResDTO res = new BillChargeFeeResDTO();
        ParmBillChargeFee parmBillChargeFee =
                parmBillChargeFeeMapper.selectByPrimaryKey(id);
        if (parmBillChargeFee == null) {
            log.error("获取账单日收费参数详情, 通过主键id({})未找到数据", id);
            throw new AnyTxnParameterException(AnyTxnParameterRespCodeEnum.P_PARAMETER_DATA_NOT_EXIST);
        }
        BeanMapping.copy(parmBillChargeFee, res);
        return res;
    }

    /**
     * @Description 根据id删除
     * @Method remove
     * @Params [id]
     * @Return java.lang.Boolean
     * @Date 2020/8/18
     **/
    @Override
    @DeleteParameterAnnotation(tableName = "parm_bill_charge_fee",tableDesc = "Bill Charge Fee")
    public ParameterCompare remove(String id) {

        if (id == null) {
            throw new AnyTxnParameterException(AnyTxnParameterRespCodeEnum.P_PARAMETER_EMPTY_FAULT);
        }
        ParmBillChargeFee parmBillChargeFee =
                parmBillChargeFeeMapper.selectByPrimaryKey(id);
        if (parmBillChargeFee == null) {
            log.error("删除账单日收费参数表信息, 通过主键id({})未找到数据", id);
            throw new AnyTxnParameterException(AnyTxnParameterRespCodeEnum.P_PARAMETER_DATA_NOT_EXIST);
        }
        return  ParameterCompare
                .getBuilder()
                .withBefore(parmBillChargeFee)
                .build(ParmBillChargeFee.class);
    }


    /**
     * 根据机构号 参数表ID查询账单日收费参数
     *
     * @param orgNumber orgNumber
     * @param feeId     feeId
     * @return List<BillChargeFeeResDto>
     */
    @Override
    public BillChargeFeeResDTO findByOrgAndFeeId(String orgNumber, String feeId) {
        ParmBillChargeFee parmBillChargeFee = parmBillChargeFeeMapper.selectByFeeId(orgNumber, feeId);
        BillChargeFeeResDTO res = BeanMapping.copy(parmBillChargeFee, BillChargeFeeResDTO.class);
        return res;
    }

    @Override
    @Transactional(rollbackFor = Exception.class,propagation = Propagation.REQUIRED)
    //@ShardingSphereTransactionType(TransactionType.XA)
    protected boolean updateDb(ParmModificationRecord parmModificationRecord) {
        ParmBillChargeFee parmBillChargeFee = JSON.parseObject(parmModificationRecord.getParmBody(), ParmBillChargeFee.class);
        parmBillChargeFee.initUpdateDateTime();
        int res = parmBillChargeFeeMapper.updateByPrimaryKey(parmBillChargeFee);
        return res > 0;
    }

    @Override
    @Transactional(rollbackFor = Exception.class,propagation = Propagation.REQUIRED)
    //@ShardingSphereTransactionType(TransactionType.XA)
    protected boolean insertDb(ParmModificationRecord parmModificationRecord) {
        ParmBillChargeFee parmBillChargeFee = JSON.parseObject(parmModificationRecord.getParmBody(), ParmBillChargeFee.class);
        parmBillChargeFee.initUpdateDateTime();
        parmBillChargeFee.initCreateDateTime();
        int res = parmBillChargeFeeMapper.insert(parmBillChargeFee);
        return res > 0;
    }

    @Override
    @Transactional(rollbackFor = Exception.class,propagation = Propagation.REQUIRED)
    //@ShardingSphereTransactionType(TransactionType.XA)
    protected boolean deleteDb(ParmModificationRecord parmModificationRecord) {
        ParmBillChargeFee parmBillChargeFee = JSON.parseObject(parmModificationRecord.getParmBody(), ParmBillChargeFee.class);
        int res = parmBillChargeFeeMapper.deleteByPrimaryKey(parmBillChargeFee.getId());
        return res > 0;
    }
}
