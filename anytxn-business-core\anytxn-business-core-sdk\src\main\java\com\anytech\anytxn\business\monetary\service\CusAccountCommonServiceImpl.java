package com.anytech.anytxn.business.monetary.service;

import com.anytech.anytxn.business.dao.card.model.CardAuthorizationInfo;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import java.util.List;

/**
 * <pre>
 * Description: 客户级批共用业务处理逻辑
 * Company:	    江融信
 * Version:	    1.0
 * Create at:   2023/3/16
 * <p>
 * 修改历史:
 * 日期    作者    版本  修改描述
 * ------------------------------------------------------------------
 * <AUTHOR>
 * </pre>
 */
@Slf4j
@Service
public class CusAccountCommonServiceImpl {

    public String getTmpCardNumber(List<CardAuthorizationInfo> cards) {
        for (CardAuthorizationInfo cardAuthorizationInfo : cards) {
            String productNum = cardAuthorizationInfo.getProductNumber();
            //TMP公司卡
            if (!StringUtils.isEmpty(productNum) && productNum.startsWith("TP")) {
                String tmpPrincipleCardNumber = cardAuthorizationInfo.getTmpPrincipleCardNumber();
                if (!StringUtils.isEmpty(tmpPrincipleCardNumber)) {
                    return tmpPrincipleCardNumber;
                } else {
                    return cardAuthorizationInfo.getCardNumber();
                }
            }
        }
        return CollectionUtils.isEmpty(cards) ? "" : cards.get(0).getCardNumber();
    }
}
