package com.anytech.anytxn.parameter.installment.service;

import com.alibaba.fastjson.JSON;
import com.anytech.anytxn.common.core.annotation.db.DeleteParameterAnnotation;
import com.anytech.anytxn.common.core.annotation.db.InsertParameterAnnotation;
import com.anytech.anytxn.common.core.annotation.db.UpdateParameterAnnotation;
import com.anytech.anytxn.business.base.audit.AbstractParameterService;
import com.anytech.anytxn.common.core.base.ParameterCompare;
import com.anytech.anytxn.business.base.audit.ParmModificationRecord;
import com.anytech.anytxn.common.core.base.PageResultDTO;
import com.anytech.anytxn.common.core.utils.BeanMapping;
import com.anytech.anytxn.common.sequence.utils.Number16IdGen;
import com.anytech.anytxn.common.core.utils.TenantUtils;
import com.anytech.anytxn.parameter.base.installment.domain.dto.InstallmentInterestInfoDTO;
import com.anytech.anytxn.parameter.base.installment.service.IInstallmentInterestInfoService;
import com.anytech.anytxn.parameter.base.common.enums.AnyTxnParameterRespCodeEnum;
import com.anytech.anytxn.parameter.base.common.exception.AnyTxnParameterException;
import com.anytech.anytxn.parameter.installment.mapper.ParmInstallInterestInfoMapper;
import com.anytech.anytxn.parameter.base.installment.domain.model.ParmInstallInterestInfo;
import com.github.pagehelper.Page;
import com.github.pagehelper.PageHelper;
import org.apache.commons.lang3.StringUtils;
//import org.apache.shardingsphere.transaction.annotation.ShardingSphereTransactionType;
//import org.apache.shardingsphere.transaction.core.TransactionType;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import jakarta.annotation.Resource;
import java.util.List;
import java.util.Objects;

/**
 * @Author: sukang
 * @Date: 2022/11/9 14:40
 */
@Service("parm_install_interest_info_serviceImpl")
public class InstallmentInterestInfoServiceImpl extends AbstractParameterService implements IInstallmentInterestInfoService {

    @Resource
    private ParmInstallInterestInfoMapper installInterestInfoMapper;

    @Autowired
    private Number16IdGen numberIdGenerator;


    @Override
    public PageResultDTO<InstallmentInterestInfoDTO> getPage(Integer pageNum,
                                                             Integer pageSize,
                                                             InstallmentInterestInfoDTO installmentInterestInfoDTO) {

        Page<InstallmentInterestInfoDTO> page = PageHelper.startPage(pageNum, pageSize);

        if (installmentInterestInfoDTO.getOrganizationNumber() == null){
            installmentInterestInfoDTO = new InstallmentInterestInfoDTO();
        }


        List<ParmInstallInterestInfo> parmInstallInterestInfos = installInterestInfoMapper.selectByCondition(installmentInterestInfoDTO);
        List<InstallmentInterestInfoDTO> currencyRateRes = BeanMapping.copyList(parmInstallInterestInfos, InstallmentInterestInfoDTO.class);
        return new PageResultDTO<>(pageNum, pageSize, page.getTotal(), page.getPages(), currencyRateRes);

    }

    @Override
    @InsertParameterAnnotation(tableName = "parm_install_interest_info",tableDesc = "Installment Interest Info ")
    public ParameterCompare insert(InstallmentInterestInfoDTO installmentInterestInfoDTO) {

        checkRequiredInputs(installmentInterestInfoDTO);

        ParmInstallInterestInfo parmInstallInterestInfo = BeanMapping.copy(installmentInterestInfoDTO, ParmInstallInterestInfo.class);
        parmInstallInterestInfo.setId(String.valueOf(numberIdGenerator.generateId(TenantUtils.getTenantId())));

        return ParameterCompare.getBuilder()
                .withAfter(parmInstallInterestInfo)
                .build(ParmInstallInterestInfo.class);
    }


    @Override
    @UpdateParameterAnnotation(tableName = "parm_install_interest_info",tableDesc = "Installment Interest Info ")
    public ParameterCompare updateInstallmentInterestInfo(InstallmentInterestInfoDTO installmentInterestInfoDTO) {

        checkRequiredInputs(installmentInterestInfoDTO);

        ParmInstallInterestInfo oldValue = installInterestInfoMapper.selectByPrimaryKey(installmentInterestInfoDTO.getId());

        if (oldValue == null){
            throw new AnyTxnParameterException(AnyTxnParameterRespCodeEnum.D_DATA_NOT_EXISTS_FAULT);
        }


        return ParameterCompare.getBuilder()
                .withBefore(oldValue)
                .withAfter(installmentInterestInfoDTO)
                .build(InstallmentInterestInfoDTO.class);
    }

    @Override
    @DeleteParameterAnnotation(tableName = "parm_install_interest_info",tableDesc = "Installment Interest Info ")
    public ParameterCompare deleteInstallmentInterestInfo(String id) {

        if (StringUtils.isBlank(id)){
            throw new AnyTxnParameterException(AnyTxnParameterRespCodeEnum.P_PRIMARY_KEY_NULL_FAULT);
        }

        ParmInstallInterestInfo oldValue = installInterestInfoMapper.selectByPrimaryKey(id);

        if (oldValue == null) {
            throw new AnyTxnParameterException(AnyTxnParameterRespCodeEnum.D_DATA_NOT_EXISTS_FAULT);
        }

        return ParameterCompare.getBuilder()
                .withBefore(oldValue)
                .build(ParmInstallInterestInfo.class);
    }

    @Override
    public InstallmentInterestInfoDTO getInstallmentInterestInfo(String id) {
        ParmInstallInterestInfo parmInstallInterestInfo = installInterestInfoMapper.selectByPrimaryKey(id);

        if (parmInstallInterestInfo == null){
            return null;
        }
        return BeanMapping.copy(parmInstallInterestInfo,InstallmentInterestInfoDTO.class);
    }


    private void checkRequiredInputs(InstallmentInterestInfoDTO installmentInterestInfoDTO) {

        if (StringUtils.isBlank(installmentInterestInfoDTO.getInterestCalculationFlag())){
            throw new AnyTxnParameterException(AnyTxnParameterRespCodeEnum.P_PARAMETER_EMPTY_FAULT);
        }

        if (StringUtils.isBlank(installmentInterestInfoDTO.getInterestCalculationMethod())){
            throw new AnyTxnParameterException(AnyTxnParameterRespCodeEnum.P_PARAMETER_EMPTY_FAULT);
        }

        if (Objects.isNull(installmentInterestInfoDTO.getInterestPaymentMethod())){
            throw new AnyTxnParameterException(AnyTxnParameterRespCodeEnum.P_PARAMETER_EMPTY_FAULT);
        }

        if (StringUtils.isBlank(installmentInterestInfoDTO.getInterestTableId())){
            throw new AnyTxnParameterException(AnyTxnParameterRespCodeEnum.P_PARAMETER_EMPTY_FAULT);
        }

        if (Objects.isNull(installmentInterestInfoDTO.getAnnualInterestRate())){
            throw new AnyTxnParameterException(AnyTxnParameterRespCodeEnum.P_PARAMETER_EMPTY_FAULT);
        }
    }



    @Override
    @Transactional(rollbackFor = Exception.class,propagation = Propagation.REQUIRED)
    //@ShardingSphereTransactionType(TransactionType.XA)
    protected boolean updateDb(ParmModificationRecord parmModificationRecord) {

        ParmInstallInterestInfo parmInstallInterestInfo = JSON.parseObject(parmModificationRecord.getParmBody(), ParmInstallInterestInfo.class);
        parmInstallInterestInfo.initUpdateDateTime();
        parmInstallInterestInfo.setUpdateBy(parmModificationRecord.getApplicationBy());

        int i = installInterestInfoMapper.updateSelective(parmInstallInterestInfo);

        return i == 1;
    }

    @Override
    @Transactional(rollbackFor = Exception.class,propagation = Propagation.REQUIRED)
    //@ShardingSphereTransactionType(TransactionType.XA)
    protected boolean insertDb(ParmModificationRecord parmModificationRecord) {
        ParmInstallInterestInfo parmInstallInterestInfo = JSON.parseObject(parmModificationRecord.getParmBody(), ParmInstallInterestInfo.class);

        parmInstallInterestInfo.setUpdateBy(parmModificationRecord.getApplicationBy());
        parmInstallInterestInfo.initUpdateDateTime();
        parmInstallInterestInfo.initCreateDateTime();
        int i = installInterestInfoMapper.insert(parmInstallInterestInfo);

        return i == 1;
    }

    @Override
    @Transactional(rollbackFor = Exception.class,propagation = Propagation.REQUIRED)
    //@ShardingSphereTransactionType(TransactionType.XA)
    protected boolean deleteDb(ParmModificationRecord parmModificationRecord) {
        ParmInstallInterestInfo parmInstallInterestInfo = JSON.parseObject(parmModificationRecord.getParmBody(), ParmInstallInterestInfo.class);
        int i = installInterestInfoMapper.deleteByPrimaryKey(parmInstallInterestInfo.getId());
        return i == 1;
    }



}
