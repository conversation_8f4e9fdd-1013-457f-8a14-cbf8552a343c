package com.anytech.anytxn.parameter.installment.service;

import com.alibaba.fastjson.JSON;
import com.github.pagehelper.Page;
import com.github.pagehelper.PageHelper;
import com.anytech.anytxn.common.core.annotation.db.DeleteParameterAnnotation;
import com.anytech.anytxn.common.core.annotation.db.InsertParameterAnnotation;
import com.anytech.anytxn.common.core.annotation.db.UpdateParameterAnnotation;
import com.anytech.anytxn.business.base.audit.AbstractParameterService;
import com.anytech.anytxn.common.core.base.ParameterCompare;
import com.anytech.anytxn.business.base.audit.ParmModificationRecord;
import com.anytech.anytxn.common.core.utils.OrgNumberUtils;
import com.anytech.anytxn.common.core.base.PageResultDTO;
import com.anytech.anytxn.common.core.utils.BeanMapping;
import com.anytech.anytxn.common.sequence.utils.Number16IdGen;
import com.anytech.anytxn.common.core.utils.TenantUtils;
import com.anytech.anytxn.parameter.base.common.enums.AnyTxnParameterRespCodeEnum;
import com.anytech.anytxn.parameter.base.common.exception.AnyTxnParameterException;
import com.anytech.anytxn.parameter.base.installment.domain.dto.InstallFeeCodeInfoReqDTO;
import com.anytech.anytxn.parameter.base.installment.domain.dto.InstallFeeCodeInfoResDTO;
import com.anytech.anytxn.parameter.base.installment.service.IInstallFeeCodeInfoService;
import com.anytech.anytxn.parameter.installment.mapper.InstallFeeCodeInfoMapper;
import com.anytech.anytxn.parameter.installment.mapper.InstallFeeCodeInfoSelfMapper;
import com.anytech.anytxn.parameter.base.installment.domain.model.InstallFeeCodeInfo;
import org.apache.commons.lang3.StringUtils;
//import org.apache.shardingsphere.transaction.annotation.ShardingSphereTransactionType;
//import org.apache.shardingsphere.transaction.core.TransactionType;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

/**
 *  分期费用代码表 业务层
 * <AUTHOR>
 * @date 2019-05-15 10:41
 **/
@Service(value = "parm_install_fee_code_info_serviceImpl")
public class InstallFeeCodeInfoServiceImpl extends AbstractParameterService implements IInstallFeeCodeInfoService {

    private final Logger logger = LoggerFactory.getLogger(InstallFeeCodeInfoServiceImpl.class);

    @Autowired
    private InstallFeeCodeInfoMapper installFeeCodeInfoMapper;
    @Autowired
    private InstallFeeCodeInfoSelfMapper installFeeCodeInfoSelfMapper;

    @Autowired
    private Number16IdGen numberIdGenerator;


    /**
     * 新增分期费用代码表
     * @param feeCodeInfoReqDTO 分期费用代码参数
     * @return HttpApiResponse<InstallFeeCodeInfoResDTO>
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    @InsertParameterAnnotation(tableName = "parm_install_fee_code_info", tableDesc = "install_fee_code")
    public ParameterCompare add(InstallFeeCodeInfoReqDTO feeCodeInfoReqDTO) {

        //校验必输项
        checkRequiredInputs(feeCodeInfoReqDTO);
        int isExists = installFeeCodeInfoSelfMapper.isExists(
                feeCodeInfoReqDTO.getOrganizationNumber(), feeCodeInfoReqDTO.getFeeCodeId());
        if (isExists > 0) {
            logger.error("分期费用代码已存在，,organizationNumber={},feeCodeId={}",
                    feeCodeInfoReqDTO.getOrganizationNumber(), feeCodeInfoReqDTO.getFeeCodeId());
            throw new AnyTxnParameterException(AnyTxnParameterRespCodeEnum.D_INSTALL_FEE_CODE_REFUSE_FAULT);
        }

        InstallFeeCodeInfo installFeeCodeInfo = BeanMapping.copy(feeCodeInfoReqDTO, InstallFeeCodeInfo.class);
        installFeeCodeInfo.setId(String.valueOf(numberIdGenerator.generateId(TenantUtils.getTenantId())));
        installFeeCodeInfo.setStatus(feeCodeInfoReqDTO.getStatus());
        return ParameterCompare.getBuilder()
                    .withMainParmId(feeCodeInfoReqDTO.getFeeCodeId())
                    .withAfter(installFeeCodeInfo)
                    .build(InstallFeeCodeInfo.class);
    }
    /**
     * 删除分期费用代码表
     * @param id 主键id
     * @return HttpApiResponse<InstallFeeCodeInfoResDTO>
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    @DeleteParameterAnnotation(tableName = "parm_install_fee_code_info",tableDesc = "install_fee_code")
    public ParameterCompare remove(String id) {
        if (null == id) {
            throw new AnyTxnParameterException(AnyTxnParameterRespCodeEnum.P_PRIMARY_KEY_NULL_FAULT);
        }
        InstallFeeCodeInfo feeCodeInfo = installFeeCodeInfoMapper.selectByPrimaryKey(id);

        if (feeCodeInfo == null) {
            logger.error("删除分期费用代码，通过id:{}未查到数据", id);
            throw new AnyTxnParameterException(AnyTxnParameterRespCodeEnum.D_QUERY_INSTALL_FEE_CODE_FAULT);
        }
        return ParameterCompare.getBuilder().withMainParmId(feeCodeInfo.getFeeCodeId()).withBefore(feeCodeInfo).build(InstallFeeCodeInfo.class);

    }
    /**
     * 修改分期费用代码表
     * @param feeCodeInfoReqDTO 费用代码dto
     * @return HttpApiResponse<InstallFeeCodeInfoResDTO>
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    @UpdateParameterAnnotation(tableName = "parm_install_fee_code_info",tableDesc = "install_fee_code")
    public ParameterCompare modify(InstallFeeCodeInfoReqDTO feeCodeInfoReqDTO) {
        if ( null == feeCodeInfoReqDTO.getId()) {

            throw new AnyTxnParameterException(AnyTxnParameterRespCodeEnum.P_PRIMARY_KEY_NULL_FAULT);
        }
        //校验必输项
        checkRequiredInputs(feeCodeInfoReqDTO);
        InstallFeeCodeInfo feeCodeInfo  = installFeeCodeInfoMapper.selectByPrimaryKey(feeCodeInfoReqDTO.getId());

        if (feeCodeInfo == null) {
            logger.error("修改分期费用代码表信息，通过id:{}未查到数据", feeCodeInfoReqDTO.getId());

            throw new AnyTxnParameterException(AnyTxnParameterRespCodeEnum.D_QUERY_INSTALL_FEE_CODE_FAULT);
        }

        InstallFeeCodeInfo copy = BeanMapping.copy(feeCodeInfoReqDTO, InstallFeeCodeInfo.class);


        InstallFeeCodeInfo installFeeCodeInfo = installFeeCodeInfoSelfMapper.selectByIndex(
                feeCodeInfo.getOrganizationNumber(),feeCodeInfo.getFeeCodeId());

        if (installFeeCodeInfo != null && !installFeeCodeInfo.getId().equals(feeCodeInfoReqDTO.getId())) {
            logger.warn("分期费用代码表已存在，organizationNumber={},feeCodeId={}",
                    feeCodeInfoReqDTO.getOrganizationNumber(), feeCodeInfoReqDTO.getFeeCodeId());

            throw new AnyTxnParameterException(AnyTxnParameterRespCodeEnum.D_INSTALL_FEE_CODE_REFUSE_FAULT);
        }

        return ParameterCompare.getBuilder()
                .withMainParmId(feeCodeInfoReqDTO.getFeeCodeId())
                .withAfter(copy)
                .withBefore(feeCodeInfo)
                .build(InstallFeeCodeInfo.class);

    }
    /**
     * 分页 分期费用代码表
     * @param pageNum pageSize
     * @return HttpApiResponse<InstallFeeCodeInfoResDTO>
     */
    @Override
    public PageResultDTO<InstallFeeCodeInfoResDTO> findPage(Integer pageNum, Integer pageSize ,InstallFeeCodeInfoReqDTO feeCodeInfoReqDTO) {
        if(null == feeCodeInfoReqDTO){
            feeCodeInfoReqDTO = new InstallFeeCodeInfoReqDTO();
        }
        feeCodeInfoReqDTO.setOrganizationNumber(StringUtils.isEmpty(feeCodeInfoReqDTO.getOrganizationNumber()) ? OrgNumberUtils.getOrg() : feeCodeInfoReqDTO.getOrganizationNumber());
        logger.debug("分页查询分期费用代码信息");
        Page<InstallFeeCodeInfo> page = PageHelper.startPage(pageNum, pageSize);
        List<InstallFeeCodeInfo> feeCodeInfos = installFeeCodeInfoSelfMapper.selectByCondition(feeCodeInfoReqDTO);
        List<InstallFeeCodeInfoResDTO> currencyRateRes = BeanMapping.copyList(feeCodeInfos, InstallFeeCodeInfoResDTO.class);
        return new PageResultDTO<>(pageNum, pageSize, page.getTotal(), page.getPages(), currencyRateRes);
    }

    /**
     * 根据id 查询 分期费用代码表
     * @param id 主键id
     * @return HttpApiResponse<InstallFeeCodeInfoResDTO>
     */
    @Override
    public InstallFeeCodeInfoResDTO getById(String id) {
        if (null == id ) {
            throw new AnyTxnParameterException(AnyTxnParameterRespCodeEnum.P_PRIMARY_KEY_NULL_FAULT);
        }

        InstallFeeCodeInfo feeCodeInfo  = installFeeCodeInfoMapper.selectByPrimaryKey(id);

        if (feeCodeInfo == null) {
            logger.error("查询分期费用代码信息，通过id:{}未查到数据", id);
            throw new AnyTxnParameterException(AnyTxnParameterRespCodeEnum.D_QUERY_INSTALL_FEE_CODE_FAULT);
        }
        return BeanMapping.copy(feeCodeInfo, InstallFeeCodeInfoResDTO.class);
    }
    /**
     * 根据机构号 费用代码 分期费用代码表
     * @param organizationNumber feeCode
     * @return HttpApiResponse<InstallFeeCodeInfoResDTO>
     */
    @Override
    public InstallFeeCodeInfoResDTO getByIndex(String organizationNumber, String feeCode) {
       if (null== organizationNumber || "".equals(organizationNumber)) {
           throw new AnyTxnParameterException(AnyTxnParameterRespCodeEnum.D_INSTALL_ACCOUNTING_TRANS_PARM_ORG_FAULT);
       }
       if(null == feeCode || "".equals(feeCode)) {
           throw new AnyTxnParameterException(AnyTxnParameterRespCodeEnum.D_INSTALL_FEE_CODE_FAULT);
       }
       InstallFeeCodeInfo feeCodeInfo = installFeeCodeInfoSelfMapper.selectByIndex(organizationNumber, feeCode);
       if (null == feeCodeInfo) {
           logger.error("分期费用参数信息，通过:organizationNumber{},feeCode{}未查到数据",organizationNumber,feeCode);
           throw new AnyTxnParameterException(AnyTxnParameterRespCodeEnum.D_QUERY_INSTALL_FEE_CODE_BY_ORG_FAULT);
       }
        return  BeanMapping.copy(feeCodeInfo, InstallFeeCodeInfoResDTO.class);
    }

    /**
        检查必输字段是否为空
     */
    public void checkRequiredInputs(InstallFeeCodeInfoReqDTO feeCodeInfoReq){
        String feeComputeFlagY = "Y";
        if (null == feeCodeInfoReq.getOrganizationNumber() || "".equals(feeCodeInfoReq.getOrganizationNumber())) {
            throw new AnyTxnParameterException(AnyTxnParameterRespCodeEnum.D_INSTALL_ACCOUNTING_TRANS_PARM_ORG_FAULT);
        }

        if (null == feeCodeInfoReq.getFeeCodeId() || "".equals(feeCodeInfoReq.getFeeCodeId())) {
            throw new AnyTxnParameterException(AnyTxnParameterRespCodeEnum.D_INSTALL_FEE_CODE_FAULT);
        }

        if (null ==feeCodeInfoReq.getStatus() || "".equals(feeCodeInfoReq.getStatus())) {
            throw new AnyTxnParameterException(AnyTxnParameterRespCodeEnum.D_INSTALL_ACCOUNTING_TRANS_PARM_STATUS_FAULT);
        }

        if (null == feeCodeInfoReq.getFeeComputeFlag() || "".equals(feeCodeInfoReq.getFeeComputeFlag())) {
            throw new AnyTxnParameterException(AnyTxnParameterRespCodeEnum.D_INSTALL_FEE_CODE_COMPUTE_FLAG_FAULT);
        }
        if (feeComputeFlagY.equals(feeCodeInfoReq.getFeeComputeFlag())){
            if (null == feeCodeInfoReq.getFeeType() || "".equals(feeCodeInfoReq.getFeeType())) {
                throw new AnyTxnParameterException(AnyTxnParameterRespCodeEnum.D_INSTALL_FEE_CODE_FEE_TYPE_FAULT);
            }

            if (null == feeCodeInfoReq.getChargeOption() || "".equals(feeCodeInfoReq.getChargeOption())) {
                throw new AnyTxnParameterException(AnyTxnParameterRespCodeEnum.D_INSTALL_FEE_CODE_CHARGE_OPTION_FAULT);
            }
            if (null == feeCodeInfoReq.getBaseAmountFlag() || "".equals(feeCodeInfoReq.getBaseAmountFlag())) {
                throw new AnyTxnParameterException(AnyTxnParameterRespCodeEnum.D_INSTALL_FEE_CODE_BASE_AMOUNT_FLAG_FAULT);
            }
        }
        if (null == feeCodeInfoReq.getThresholdAmount1()) {
            throw new AnyTxnParameterException(AnyTxnParameterRespCodeEnum.D_INSTALL_FEE_CODE_THRESHOLD_ONE_FLAG_FAULT);
        }
        if (null == feeCodeInfoReq.getFeeRate1()) {
            throw new AnyTxnParameterException(AnyTxnParameterRespCodeEnum.D_INSTALL_FEE_CODE_FEE_RATE_ONE_FAULT);
        }
        if (null == feeCodeInfoReq.getThresholdAmount2()) {
            throw new AnyTxnParameterException(AnyTxnParameterRespCodeEnum.D_INSTALL_FEE_CODE_THRESHOLD_TWO_FLAG_FAULT);
        }
        if (null == feeCodeInfoReq.getFeeRate2()) {
            throw new AnyTxnParameterException(AnyTxnParameterRespCodeEnum.D_INSTALL_FEE_CODE_FEE_RATE_TWO_FAULT);
        }
        if (null == feeCodeInfoReq.getThresholdAmount3()) {
            throw new AnyTxnParameterException(AnyTxnParameterRespCodeEnum.D_INSTALL_FEE_CODE_THRESHOLD_THREE_FLAG_FAULT);
        }
        if (null == feeCodeInfoReq.getFeeRate3()) {
            throw new AnyTxnParameterException(AnyTxnParameterRespCodeEnum.D_INSTALL_FEE_CODE_FEE_RATE_THREE_FAULT);
        }
        if (null == feeCodeInfoReq.getMinFeeAmount()) {
            throw new AnyTxnParameterException(AnyTxnParameterRespCodeEnum.D_INSTALL_FEE_CODE_MINI_FEE_FAULT);
        }
        if (null == feeCodeInfoReq.getMaxFeeAmount()) {
            throw new AnyTxnParameterException(AnyTxnParameterRespCodeEnum.D_INSTALL_FEE_CODE_MAX_FEE_FAULT);
        }
        if (null == feeCodeInfoReq.getDerateMethod() || "".equals(feeCodeInfoReq.getDerateMethod())) {
            throw new AnyTxnParameterException(AnyTxnParameterRespCodeEnum.D_INSTALL_FEE_CODE_DERATE_METHOD_FAULT);
        }
        if (null == feeCodeInfoReq.getDerateValue()) {
            throw new AnyTxnParameterException(AnyTxnParameterRespCodeEnum.D_INSTALL_FEE_CODE_DERATE_VALUE_FAULT);
        }
        //新增本金减免方式、本金减免值、最大减免金额
        if (null == feeCodeInfoReq.getPrincipalRelief() ||"".equals(feeCodeInfoReq.getPrincipalRelief())){
            throw new AnyTxnParameterException(AnyTxnParameterRespCodeEnum.D_INSTALL_FEE_CODE_PRINCIPAL_RELIEF_FAULT);
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class,propagation = Propagation.REQUIRED)
    //@ShardingSphereTransactionType(TransactionType.XA)
    protected boolean updateDb(ParmModificationRecord parmModificationRecord) {
        InstallFeeCodeInfo installFeeCodeInfo = JSON.parseObject(parmModificationRecord.getParmBody(), InstallFeeCodeInfo.class);
        installFeeCodeInfo.initUpdateDateTime();
        int i = installFeeCodeInfoMapper.updateByPrimaryKeySelective(installFeeCodeInfo);
        return i > 0;
    }

    @Override
    @Transactional(rollbackFor = Exception.class,propagation = Propagation.REQUIRED)
    //@ShardingSphereTransactionType(TransactionType.XA)
    protected boolean insertDb(ParmModificationRecord parmModificationRecord) {
        InstallFeeCodeInfo installFeeCodeInfo = JSON.parseObject(parmModificationRecord.getParmBody(), InstallFeeCodeInfo.class);
        installFeeCodeInfo.initCreateDateTime();
        installFeeCodeInfo.initUpdateDateTime();
        int i = installFeeCodeInfoMapper.insertSelective(installFeeCodeInfo);
        return i > 0;
    }

    @Override
    @Transactional(rollbackFor = Exception.class,propagation = Propagation.REQUIRED)
    //@ShardingSphereTransactionType(TransactionType.XA)
    protected boolean deleteDb(ParmModificationRecord parmModificationRecord) {
        InstallFeeCodeInfo installFeeCodeInfo = JSON.parseObject(parmModificationRecord.getParmBody(), InstallFeeCodeInfo.class);
        int i = installFeeCodeInfoMapper.deleteByPrimaryKey(installFeeCodeInfo.getId());
        return i > 0;
    }
}
