package com.anytech.anytxn.parameter.card.controller;

import com.anytech.anytxn.parameter.base.common.service.IBlockCodeDefIniTionService;
import io.swagger.v3.oas.annotations.tags.Tag;
import io.swagger.v3.oas.annotations.Operation;
import com.anytech.anytxn.common.core.base.ParameterCompare;
import com.anytech.anytxn.common.core.base.PageResultDTO;
import com.anytech.anytxn.common.core.base.AnyTxnHttpResponse;
import com.anytech.anytxn.common.core.base.BizBaseController;
import com.anytech.anytxn.parameter.base.card.domain.dto.BlockCodeDefinitionReqDTO;
import com.anytech.anytxn.parameter.base.card.domain.dto.BlockCodeDefinitionResDTO;
import com.anytech.anytxn.parameter.base.common.enums.ParameterRepDetailEnum;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import jakarta.validation.Valid;

/**
 * Description   封锁码定义表
 * Copyright:	Copyright (c)  2018
 * Company:		江融信
 * Author:		lichao
 * Version:		1.0
 * Create at:	2018/9/27 下午5:55
 * <p>
 * 修改历史:
 * 日期    作者    版本  修改描述
 * ------------------------------------------------------------------
 **/
@Tag(name = "卡片/账户封锁码定")
@RestController
public class CardBlockCodeController extends BizBaseController {

    @Autowired
    private IBlockCodeDefIniTionService blockCodeDefIniTionService;

    /**
     * 创建对应层级封锁码
     *
     * @param blockCodeDefinitionReq 对应层级封锁码请求信息+定义封锁码
     * @return 对应层级封锁码详情
     */
    @Operation(summary = "创建对应层级封锁码信息")
    @PostMapping(value = "/param/blockCodeDefIniTion")
    public AnyTxnHttpResponse<Object> create(@Valid @RequestBody BlockCodeDefinitionReqDTO blockCodeDefinitionReq) {
        ParameterCompare res = blockCodeDefIniTionService.add(blockCodeDefinitionReq);
        return AnyTxnHttpResponse.success(res, ParameterRepDetailEnum.CREATE.message());
    }

    /**
     * 更新对应层级封锁码
     *
     * @param blockCodeDefinitionReq 对应层级封锁码请求数据
     * @return 对应层级封锁码详情
     */
    @Operation(summary = "更新对应层级封锁码信息")
    @PutMapping(value = "/param/blockCodeDefIniTion")
    public AnyTxnHttpResponse<Object> modify(@Valid @RequestBody BlockCodeDefinitionReqDTO blockCodeDefinitionReq) {
        ParameterCompare blockCodeDefinitionRes = blockCodeDefIniTionService.modify(blockCodeDefinitionReq);
        return AnyTxnHttpResponse.success(blockCodeDefinitionRes,ParameterRepDetailEnum.UPDATE.message());
    }


    /**
     * 分页查询，当前层级封锁锁码
     *
     * @param pageNum        页码
     * @param pageSize       每页大小
     * @param blockCodeLevel 封锁码层级
     * @return
     */
    @Operation(summary = "分页查询，当前层级对应封锁码")
    @GetMapping(value = "/param/blockCodeDefIniTion/pageNum/{pageNum}/pageSize/{pageSize}/blockCodeLevel/{blockCodeLevel}")
    public AnyTxnHttpResponse<PageResultDTO<BlockCodeDefinitionResDTO>> getPageByOrgNumber(@PathVariable(value = "pageNum") Integer pageNum,
                                                                                           @PathVariable(value = "pageSize") Integer pageSize,
                                                                                           @PathVariable(value = "blockCodeLevel") String blockCodeLevel,
                                                                                           @RequestParam(required = false) String tableId,
                                                                                           @RequestParam(required = false) String description,
                                                                                           @RequestParam(required = false) String organizationNumber) {
        PageResultDTO<BlockCodeDefinitionResDTO> page = blockCodeDefIniTionService.findPageByBlockCodeLevel(pageNum, pageSize, blockCodeLevel, tableId, description, organizationNumber);
        return AnyTxnHttpResponse.success(page);

    }


    /**
     * 通过id获取封锁码定义表详情
     *
     * @param id 获取对应层级封锁码id
     * @return 层封锁码详情
     */
    @Operation(summary = "通过id获取层封锁码详情")
    @GetMapping(value = "/param/blockCodeDefIniTion/id/{id}")
    public AnyTxnHttpResponse<BlockCodeDefinitionResDTO> get(@PathVariable String id) {
        BlockCodeDefinitionResDTO blockCodeDefinitionRes = blockCodeDefIniTionService.find(id);
        return AnyTxnHttpResponse.success(blockCodeDefinitionRes);

    }

    @Operation(summary = "通过机构号和参数表id获取层封锁码详情")
    @GetMapping(value = "/param/blockCodeDefIniTion/organizationNumber/{organizationNumber}/tableId/{tableId}")
    public AnyTxnHttpResponse<BlockCodeDefinitionResDTO> getByOrgAndTableId(@PathVariable(value = "organizationNumber") String organizationNumber,
                                                             @PathVariable(value = "tableId") String tableId) {
        BlockCodeDefinitionResDTO blockCodeDefinitionRes = blockCodeDefIniTionService.findByOrgAndTableId(organizationNumber,tableId);
        return AnyTxnHttpResponse.success(blockCodeDefinitionRes);

    }

    @Operation(summary = "通过机构号和参数表id以及封锁码层级标志获取层封锁码详情")
    @GetMapping(value = "/param/blockCodeDefIniTion/organizationNumber/{organizationNumber}/tableId/{tableId}/blockCodeLevel/{blockCodeLevel}")
    public AnyTxnHttpResponse<BlockCodeDefinitionResDTO> getByOrgAndTableIdAndBlockCodeLevel(@PathVariable(value = "organizationNumber") String organizationNumber,
                                                                            @PathVariable(value = "tableId") String tableId,@PathVariable(value = "blockCodeLevel") String blockCodeLevel) {
        BlockCodeDefinitionResDTO blockCodeDefinitionRes = blockCodeDefIniTionService.findByOrgAndTableIdAndBlockCodeLevel(organizationNumber,tableId,blockCodeLevel);
        return AnyTxnHttpResponse.success(blockCodeDefinitionRes);

    }


    /**
     * 删除各层级封锁码，通过id
     *
     * @param id 各层级封锁码id
     * @return
     */
    @Operation(summary = "删除封锁码定义表信息，通过id")
    @DeleteMapping(value = "/param/blockCodeDefIniTion/id/{id}")
    public AnyTxnHttpResponse<Object> remove(@PathVariable String id) {
        ParameterCompare deleted = blockCodeDefIniTionService.remove(id);
        return AnyTxnHttpResponse.success(deleted,ParameterRepDetailEnum.DEL.message());

    }

    @Operation(summary = "删除封锁码定义表信息，通过机构号和参数表id")
    @DeleteMapping(value = "/param/blockCodeDefIniTion/organizationNumber/{organizationNumber}/tableId/{tableId}")
    public AnyTxnHttpResponse<Boolean> removeByOrgAndTableId(@PathVariable(value = "organizationNumber") String organizationNumber,
                                              @PathVariable(value = "tableId") String tableId) {
        Boolean deleted = blockCodeDefIniTionService.removeByOrgAndTableId(organizationNumber,tableId);
        return AnyTxnHttpResponse.success(deleted,ParameterRepDetailEnum.DEL.message());

    }
}
