package com.anytech.anytxn.parameter.common.controller.system;

import io.swagger.v3.oas.annotations.tags.Tag;
import io.swagger.v3.oas.annotations.Operation;
import com.anytech.anytxn.common.core.base.PageResultDTO;
import com.anytech.anytxn.common.core.base.AnyTxnHttpResponse;
import com.anytech.anytxn.common.core.base.BizBaseController;
import com.anytech.anytxn.parameter.base.common.enums.ParameterRepDetailEnum;
import com.anytech.anytxn.parameter.base.common.domain.dto.system.SystemTableDTO;
import com.anytech.anytxn.parameter.base.common.service.system.ISystemTableService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import jakarta.validation.Valid;

/**
 * 系统参数定义
 * Author:		lichao
 * Create at:	2018/9/21 上午10:41
 **/
@Tag(name = "系统定义参数")
@RestController
public class SystemTableController extends BizBaseController {
    @Autowired
    private ISystemTableService systemTableService;

    /**
     * 通过systemId查询系统参数信息
     *
     * @param systemId 系统id
     * @return
     */
    @Operation(summary = "根据systemId查询系统参数信息", description = "根据系统systemId")
    @GetMapping(value = "/param/systemTable/systemId/{systemId}")
    public AnyTxnHttpResponse<SystemTableDTO> getInfoBySystemId(@PathVariable(value = "systemId") String systemId) {
        SystemTableDTO res;
        res = systemTableService.findBySystemId(systemId);
        return AnyTxnHttpResponse.success(res);

    }

    /**
     * 更新系統參數表
     *
     * @param systemTableReq 系統參數更新
     * @return AnyTxnHttpResponse<SystemTableRes>
     */
    @Operation(summary = "根据SystemTableReq请求对象更新系统参数信息", description = "根据系统请求SystemTableReq")
    @PutMapping(value = "/param/systemTable")
    public AnyTxnHttpResponse<Object> modifySysInfo(@Valid @RequestBody SystemTableDTO systemTableReq) {
        return AnyTxnHttpResponse.success(systemTableService.modifySysInfo(systemTableReq), ParameterRepDetailEnum.UPDATE.message());

    }

    /**
     * 新增系统定义参数
     * @param systemTableReq
     * @return SystemTableRes
     *
     */
    @Operation(summary = "新增系统定义参数", description = "新增系统定义参数")
    @PostMapping("/param/systemTable")
    public AnyTxnHttpResponse<Object> create(@RequestBody SystemTableDTO systemTableReq) {
        return AnyTxnHttpResponse.success(systemTableService.addSystemTable(systemTableReq),ParameterRepDetailEnum.CREATE.message());

    }

    /**
     * 通过id删除系统定义参数信息
     * @param id
     * @return Boolean
     *
     */
    @Operation(summary = "删除系统定义参数信息", description = "通过id删除系统定义参数信息")
    @DeleteMapping(value = "/param/systemTable/id/{id}")
    public AnyTxnHttpResponse<Object> removeSystemTable(@PathVariable(value = "id") String id) {
        return AnyTxnHttpResponse.success(systemTableService.removeSystemTable(id),ParameterRepDetailEnum.DEL.message());

    }

    /**
     * 通过id查询系统定义参数信息
     * @param id
     * @return AnyTxnHttpResponse<SystemTableRes>
     *
     */
    @Operation(summary = "通过id查询系统定义参数信息", description = "通过id查询系统定义参数信息")
    @GetMapping(value = "/param/systemTable/id/{id}")
    public AnyTxnHttpResponse<SystemTableDTO> getById(@PathVariable (value = "id")String id) {
        SystemTableDTO res;
        res = systemTableService.findById(id);
        return AnyTxnHttpResponse.success(res);

    }


    /**
     * 分页查询所有系统定义参数信息
     * @return SystemTableRes
     *
     */
    @Operation(summary = "系统定义参数分页查询", description = "系统定义参数分页查询")
    @GetMapping(value = "/param/systemTable/pageNum/{pageNum}/pageSize/{pageSize}")
    public AnyTxnHttpResponse<PageResultDTO<SystemTableDTO>> getAllPage(@PathVariable(value = "pageNum") Integer pageNum,
                                                                        @PathVariable(value = "pageSize") Integer pageSize,
                                                                        @RequestParam(required = false) String systemId,
                                                                        @RequestParam(required = false) String name) {
        PageResultDTO<SystemTableDTO> response;
        response = systemTableService.findAll(pageNum,pageSize,systemId,name);
        return AnyTxnHttpResponse.success(response);

    }

}
