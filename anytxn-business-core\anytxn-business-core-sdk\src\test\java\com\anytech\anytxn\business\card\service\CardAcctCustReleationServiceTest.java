package com.anytech.anytxn.business.card.service;

import com.anytech.anytxn.business.base.account.domain.dto.AccountManagementInfoDTO;
import com.anytech.anytxn.business.base.card.domain.dto.CardAcctCustReleationDTO;
import com.anytech.anytxn.business.base.card.enums.AcctAttributeEnum;
import com.anytech.anytxn.business.dao.account.mapper.AccountManagementInfoSelfMapper;
import com.anytech.anytxn.business.dao.account.model.AccountManagementInfo;
import com.anytech.anytxn.business.dao.card.mapper.CardAcctCustReleationMapper;
import com.anytech.anytxn.business.dao.card.mapper.CardAcctCustReleationSelfMapper;
import com.anytech.anytxn.business.dao.card.model.CardAcctCustReleation;
import com.anytech.anytxn.common.core.enums.AccountStatusEnum;
import com.anytech.anytxn.common.core.enums.AnyTxnCommonRespCodeEnum;
import com.anytech.anytxn.common.core.exception.AnyTxnCommonException;
import com.anytech.anytxn.common.core.utils.BeanMapping;
import com.anytech.anytxn.common.core.utils.OrgNumberUtils;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockedStatic;
import org.mockito.junit.jupiter.MockitoExtension;

import java.time.LocalDateTime;
import java.util.Arrays;
import java.util.Collections;
import java.util.List;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.*;

/**
 * CardAcctCustReleationServiceImpl测试类
 *
 * <AUTHOR>
 * @date 2025-01-24
 */
@ExtendWith(MockitoExtension.class)
class CardAcctCustReleationServiceTest {

    @Mock
    private CardAcctCustReleationMapper cardAcctCustReleationMapper;

    @Mock
    private CardAcctCustReleationSelfMapper cardAcctCustReleationSelfMapper;

    @Mock
    private AccountManagementInfoSelfMapper accountManagementInfoSelfMapper;

    @InjectMocks
    private CardAcctCustReleationServiceImpl cardAcctCustReleationService;

    private CardAcctCustReleationDTO testDto;
    private CardAcctCustReleation testEntity;
    private AccountManagementInfoDTO testAccountDto;
    private AccountManagementInfo testAccountEntity;

    @BeforeEach
    void setUp() {
        testDto = new CardAcctCustReleationDTO();
        testDto.setId("1");
        testDto.setOrganizationNumber("0001");
        testDto.setCardNumber("****************");
        testDto.setCardProductNumber("CP001");
        testDto.setCorporateIndicator("P");
        testDto.setRelationshipIndicator("P");
        testDto.setLiability("1");
        testDto.setCurrency("CNY");
        testDto.setAcctProductNumber("AP001");
        testDto.setAttribute(AcctAttributeEnum.CREDIT_CARD.getCode());
        testDto.setAccountManagementId("AM001");
        testDto.setPrimaryCustomerId("CUST001");
        testDto.setSupplementaryCustomerId("SUPP001");
        testDto.setOutsideAcctManagementId("OAM001");
        testDto.setPrimaryEcif("ECIF001");
        testDto.setSupplementaryEcif("ECIF002");
        testDto.setPartnerId("PARTNER001");
        testDto.setIdNumber("110101199001011234");
        testDto.setIdType("1");
        testDto.setCorporateCustomerId("CORP001");
        testDto.setTenantId("TENANT001");
        testDto.setServerType("2");
        testDto.setCreateTime(LocalDateTime.now());
        testDto.setUpdateTime(LocalDateTime.now());
        testDto.setUpdateBy("admin");
        testDto.setVersionNumber(1L);

        testEntity = new CardAcctCustReleation();
        testEntity.setId("1");
        testEntity.setOrganizationNumber("0001");
        testEntity.setCardNumber("****************");
        testEntity.setCardProductNumber("CP001");
        testEntity.setAttribute(AcctAttributeEnum.CREDIT_CARD.getCode());
        testEntity.setAccountManagementId("AM001");
        testEntity.setPrimaryCustomerId("CUST001");
        testEntity.setCurrency("CNY");
        
        // 创建AccountManagementInfo的Mock对象来避免构造函数中的OrgNumberUtils调用
        setupAccountTestData();
    }
    
    private void setupAccountTestData() {
        try (MockedStatic<OrgNumberUtils> orgNumberUtilsMock = mockStatic(OrgNumberUtils.class)) {
            orgNumberUtilsMock.when(OrgNumberUtils::getOrg).thenReturn("0001");
            
            testAccountDto = new AccountManagementInfoDTO();
            testAccountDto.setAccountManagementId("AM001");
            testAccountDto.setAccountStatus(AccountStatusEnum.ACTIVE.getCode());

            testAccountEntity = new AccountManagementInfo();
            testAccountEntity.setAccountManagementId("AM001");
            testAccountEntity.setAccountStatus(AccountStatusEnum.ACTIVE.getCode());
        }
    }

    @Test
    void testInsert_Success() {
        // Mock
        when(cardAcctCustReleationMapper.insertSelective(any(CardAcctCustReleation.class))).thenReturn(1);

        try (MockedStatic<BeanMapping> beanMappingMock = mockStatic(BeanMapping.class)) {
            beanMappingMock.when(() -> BeanMapping.copy(any(CardAcctCustReleationDTO.class), eq(CardAcctCustReleation.class)))
                    .thenReturn(testEntity);

            // 执行
            assertDoesNotThrow(() -> cardAcctCustReleationService.insert(testDto));

            // 验证
            verify(cardAcctCustReleationMapper).insertSelective(any(CardAcctCustReleation.class));
        }
    }

    @Test
    void testInsert_Fail() {
        // Mock
        when(cardAcctCustReleationMapper.insertSelective(any(CardAcctCustReleation.class))).thenReturn(0);

        try (MockedStatic<BeanMapping> beanMappingMock = mockStatic(BeanMapping.class)) {
            beanMappingMock.when(() -> BeanMapping.copy(any(CardAcctCustReleationDTO.class), eq(CardAcctCustReleation.class)))
                    .thenReturn(testEntity);

            // 执行并验证异常
            AnyTxnCommonException exception = assertThrows(AnyTxnCommonException.class,
                    () -> cardAcctCustReleationService.insert(testDto));

            assertEquals(AnyTxnCommonRespCodeEnum.D_ERR.getCode(), exception.getErrCode());
        }
    }

    @Test
    void testInsertBatch() {
        List<CardAcctCustReleationDTO> dtoList = Arrays.asList(testDto);
        List<CardAcctCustReleation> entityList = Arrays.asList(testEntity);

        try (MockedStatic<BeanMapping> beanMappingMock = mockStatic(BeanMapping.class)) {
            beanMappingMock.when(() -> BeanMapping.copyList(eq(dtoList), eq(CardAcctCustReleation.class)))
                    .thenReturn(entityList);

            // 执行
            cardAcctCustReleationService.insertBatch(dtoList);

            // 验证
            verify(cardAcctCustReleationSelfMapper).insertBatch(entityList);
        }
    }

    @Test
    void testSelectByCardNumber() {
        String cardNumber = "****************";
        List<CardAcctCustReleation> entityList = Arrays.asList(testEntity);
        List<CardAcctCustReleationDTO> expectedDtoList = Arrays.asList(testDto);

        // Mock
        when(cardAcctCustReleationSelfMapper.selectByCardNumber(cardNumber)).thenReturn(entityList);

        try (MockedStatic<BeanMapping> beanMappingMock = mockStatic(BeanMapping.class)) {
            beanMappingMock.when(() -> BeanMapping.copyList(eq(entityList), eq(CardAcctCustReleationDTO.class)))
                    .thenReturn(expectedDtoList);

            // 执行
            List<CardAcctCustReleationDTO> result = cardAcctCustReleationService.selectByCardNumber(cardNumber);

            // 验证
            assertNotNull(result);
            assertEquals(1, result.size());
            assertEquals(cardNumber, result.get(0).getCardNumber());
        }
    }

    @Test
    void testSelectByCondition_Found() {
        // Mock
        when(cardAcctCustReleationSelfMapper.selectByCondition(any(CardAcctCustReleation.class)))
                .thenReturn(testEntity);

        try (MockedStatic<BeanMapping> beanMappingMock = mockStatic(BeanMapping.class)) {
            beanMappingMock.when(() -> BeanMapping.copy(any(CardAcctCustReleationDTO.class), eq(CardAcctCustReleation.class)))
                    .thenReturn(testEntity);
            beanMappingMock.when(() -> BeanMapping.copy(eq(testEntity), eq(CardAcctCustReleationDTO.class)))
                    .thenReturn(testDto);

            // 执行
            CardAcctCustReleationDTO result = cardAcctCustReleationService.selectByCondition(testDto);

            // 验证
            assertNotNull(result);
            assertEquals(testDto.getId(), result.getId());
        }
    }

    @Test
    void testSelectByCondition_NotFound() {
        // Mock
        when(cardAcctCustReleationSelfMapper.selectByCondition(any(CardAcctCustReleation.class)))
                .thenReturn(null);

        try (MockedStatic<BeanMapping> beanMappingMock = mockStatic(BeanMapping.class)) {
            beanMappingMock.when(() -> BeanMapping.copy(any(CardAcctCustReleationDTO.class), eq(CardAcctCustReleation.class)))
                    .thenReturn(testEntity);

            // 执行
            CardAcctCustReleationDTO result = cardAcctCustReleationService.selectByCondition(testDto);

            // 验证
            assertNull(result);
        }
    }

    @Test
    void testSelectAcctByCardNumber() {
        String organizationNumber = "0001";
        String cardNumber = "****************";
        List<CardAcctCustReleation> acctCustReleationList = Arrays.asList(testEntity);
        List<String> accountManagementIds = Arrays.asList("AM001");
        List<AccountManagementInfo> accountInfoList = Arrays.asList(testAccountEntity);
        List<AccountManagementInfoDTO> expectedDtoList = Arrays.asList(testAccountDto);

        // Mock
        when(cardAcctCustReleationSelfMapper.selectByCardNumber(cardNumber)).thenReturn(acctCustReleationList);
        when(accountManagementInfoSelfMapper.selectByOrgCusIdAndMids(organizationNumber, "CUST001", accountManagementIds))
                .thenReturn(accountInfoList);

        try (MockedStatic<BeanMapping> beanMappingMock = mockStatic(BeanMapping.class)) {
            beanMappingMock.when(() -> BeanMapping.copyList(eq(accountInfoList), eq(AccountManagementInfoDTO.class)))
                    .thenReturn(expectedDtoList);

            // 执行
            List<AccountManagementInfoDTO> result = cardAcctCustReleationService.selectAcctByCardNumber(organizationNumber, cardNumber);

            // 验证
            assertNotNull(result);
            assertEquals(1, result.size());
            assertEquals("AM001", result.get(0).getAccountManagementId());
        }
    }

    @Test
    void testSelectAcctByCardPdAndCur() {
        String organizationNumber = "0001";
        String cardNumber = "****************";
        String cardProduct = "CP001";
        String cur = "CNY";
        List<CardAcctCustReleation> acctCustReleationList = Arrays.asList(testEntity);
        List<String> accountManagementIds = Arrays.asList("AM001");
        List<AccountManagementInfo> accountInfoList = Arrays.asList(testAccountEntity);
        List<AccountManagementInfoDTO> expectedDtoList = Arrays.asList(testAccountDto);

        // Mock
        when(cardAcctCustReleationSelfMapper.selectAcctByCardPdAndCur(organizationNumber, cardNumber, cardProduct, cur))
                .thenReturn(acctCustReleationList);
        when(accountManagementInfoSelfMapper.selectByOrgCusIdAndMids(organizationNumber, "CUST001", accountManagementIds))
                .thenReturn(accountInfoList);

        try (MockedStatic<BeanMapping> beanMappingMock = mockStatic(BeanMapping.class)) {
            beanMappingMock.when(() -> BeanMapping.copyList(eq(accountInfoList), eq(AccountManagementInfoDTO.class)))
                    .thenReturn(expectedDtoList);

            // 执行
            List<AccountManagementInfoDTO> result = cardAcctCustReleationService.selectAcctByCardPdAndCur(organizationNumber, cardNumber, cardProduct, cur);

            // 验证
            assertNotNull(result);
            assertEquals(1, result.size());
            assertEquals("AM001", result.get(0).getAccountManagementId());
        }
    }

    @Test
    void testSelectValidCaAcctByCardNumber_Success() {
        String organizationNumber = "0001";
        String cardNumber = "****************";
        String cardProduct = "CP001";
        String cur = "CNY";

        testEntity.setAttribute(AcctAttributeEnum.CREDIT_CARD.getCode()); // 非VA、非TA
        List<CardAcctCustReleation> acctCustReleationList = Arrays.asList(testEntity);
        List<String> accountManagementIds = Arrays.asList("AM001");
        List<AccountManagementInfo> accountInfoList = Arrays.asList(testAccountEntity);

        // Mock
        when(cardAcctCustReleationSelfMapper.selectAcctByCardPdAndCur(organizationNumber, cardNumber, cardProduct, cur))
                .thenReturn(acctCustReleationList);
        when(accountManagementInfoSelfMapper.selectByOrgCusIdAndMids(organizationNumber, "CUST001", accountManagementIds))
                .thenReturn(accountInfoList);

        try (MockedStatic<BeanMapping> beanMappingMock = mockStatic(BeanMapping.class)) {
            beanMappingMock.when(() -> BeanMapping.copy(eq(testAccountEntity), eq(AccountManagementInfoDTO.class)))
                    .thenReturn(testAccountDto);

            // 执行
            AccountManagementInfoDTO result = cardAcctCustReleationService.selectValidCaAcctByCardNumber(organizationNumber, cardNumber, cardProduct, cur);

            // 验证
            assertNotNull(result);
            assertEquals("AM001", result.getAccountManagementId());
        }
    }

    @Test
    void testSelectValidCaAcctByCardNumber_NoData() {
        String organizationNumber = "0001";
        String cardNumber = "****************";
        String cardProduct = "CP001";
        String cur = "CNY";

        // Mock
        when(cardAcctCustReleationSelfMapper.selectAcctByCardPdAndCur(organizationNumber, cardNumber, cardProduct, cur))
                .thenReturn(Collections.emptyList());

        // 执行
        AccountManagementInfoDTO result = cardAcctCustReleationService.selectValidCaAcctByCardNumber(organizationNumber, cardNumber, cardProduct, cur);

        // 验证
        assertNull(result);
    }

    @Test
    void testSelectByCardNumberAndMid() {
        String cardNumber = "****************";
        String accountManagementId = "AM001";

        // Mock
        when(cardAcctCustReleationSelfMapper.selectByCardNumberAndMid(cardNumber, accountManagementId))
                .thenReturn(testEntity);

        try (MockedStatic<BeanMapping> beanMappingMock = mockStatic(BeanMapping.class)) {
            beanMappingMock.when(() -> BeanMapping.copy(eq(testEntity), eq(CardAcctCustReleationDTO.class)))
                    .thenReturn(testDto);

            // 执行
            CardAcctCustReleationDTO result = cardAcctCustReleationService.selectByCardNumberAndMid(cardNumber, accountManagementId);

            // 验证
            assertNotNull(result);
            assertEquals(testDto.getId(), result.getId());
        }
    }

    @Test
    void testSelectByOrgNumberAndMid() {
        String organizationNumber = "0001";
        String accountManagementId = "AM001";
        List<CardAcctCustReleation> entityList = Arrays.asList(testEntity);
        List<CardAcctCustReleationDTO> expectedDtoList = Arrays.asList(testDto);

        // Mock
        when(cardAcctCustReleationSelfMapper.selectListByCondition(any(CardAcctCustReleation.class)))
                .thenReturn(entityList);

        try (MockedStatic<BeanMapping> beanMappingMock = mockStatic(BeanMapping.class)) {
            beanMappingMock.when(() -> BeanMapping.copyList(eq(entityList), eq(CardAcctCustReleationDTO.class)))
                    .thenReturn(expectedDtoList);

            // 执行
            List<CardAcctCustReleationDTO> result = cardAcctCustReleationService.selectByOrgNumberAndMid(organizationNumber, accountManagementId);

            // 验证
            assertNotNull(result);
            assertEquals(1, result.size());
            assertEquals(testDto.getId(), result.get(0).getId());
        }
    }

    @Test
    void testSelectByMidAndCardProduct() {
        String accountManagementId = "AM001";
        String cardProductNumber = "CP001";

        // Mock
        when(cardAcctCustReleationSelfMapper.selectByMidAndCardProduct(accountManagementId, cardProductNumber))
                .thenReturn(testEntity);

        try (MockedStatic<BeanMapping> beanMappingMock = mockStatic(BeanMapping.class)) {
            beanMappingMock.when(() -> BeanMapping.copy(eq(testEntity), eq(CardAcctCustReleationDTO.class)))
                    .thenReturn(testDto);

            // 执行
            CardAcctCustReleationDTO result = cardAcctCustReleationService.selectByMidAndCardProduct(accountManagementId, cardProductNumber);

            // 验证
            assertNotNull(result);
            assertEquals(testDto.getId(), result.getId());
        }
    }

    @Test
    void testSelectByOrgAndCid() {
        String organizationNumber = "0001";
        String customerId = "CUST001";
        List<CardAcctCustReleation> entityList = Arrays.asList(testEntity);
        List<CardAcctCustReleationDTO> expectedDtoList = Arrays.asList(testDto);

        // Mock
        when(cardAcctCustReleationSelfMapper.selectByOrgAndCid(organizationNumber, customerId))
                .thenReturn(entityList);

        try (MockedStatic<BeanMapping> beanMappingMock = mockStatic(BeanMapping.class)) {
            beanMappingMock.when(() -> BeanMapping.copyList(eq(entityList), eq(CardAcctCustReleationDTO.class)))
                    .thenReturn(expectedDtoList);

            // 执行
            List<CardAcctCustReleationDTO> result = cardAcctCustReleationService.selectByOrgAndCid(organizationNumber, customerId);

            // 验证
            assertNotNull(result);
            assertEquals(1, result.size());
            assertEquals(testDto.getId(), result.get(0).getId());
        }
    }

    @Test
    void testSelectByOrgAndCidAndCardAndCurr() {
        String organizationNumber = "0001";
        String customerId = "CUST001";
        String cardNumber = "****************";
        String currency = "CNY";
        List<CardAcctCustReleation> entityList = Arrays.asList(testEntity);
        List<CardAcctCustReleationDTO> expectedDtoList = Arrays.asList(testDto);

        // Mock
        when(cardAcctCustReleationSelfMapper.selectByOrgAndCidAndCard(organizationNumber, customerId, cardNumber, currency))
                .thenReturn(entityList);

        try (MockedStatic<BeanMapping> beanMappingMock = mockStatic(BeanMapping.class)) {
            beanMappingMock.when(() -> BeanMapping.copyList(eq(entityList), eq(CardAcctCustReleationDTO.class)))
                    .thenReturn(expectedDtoList);

            // 执行
            List<CardAcctCustReleationDTO> result = cardAcctCustReleationService.selectByOrgAndCidAndCardAndCurr(organizationNumber, customerId, cardNumber, currency);

            // 验证
            assertNotNull(result);
            assertEquals(1, result.size());
            assertEquals(testDto.getId(), result.get(0).getId());
        }
    }

    @Test
    void testSelectVaByCaAcctAndCustId_Found() {
        String organizationNumber = "0001";
        String accountManagementId = "AM001";
        String customerId = "CUST001";

        // 创建VA类型的实体
        CardAcctCustReleation vaAcctReleation = new CardAcctCustReleation();
        vaAcctReleation.setId("2");
        vaAcctReleation.setCardNumber("****************");
        vaAcctReleation.setAccountManagementId("AM002");
        vaAcctReleation.setAttribute(AcctAttributeEnum.VA.getCode());

        CardAcctCustReleationDTO vaDto = new CardAcctCustReleationDTO();
        vaDto.setId("2");
        vaDto.setAttribute(AcctAttributeEnum.VA.getCode());

        List<CardAcctCustReleation> acctCustReleationList = Arrays.asList(testEntity, vaAcctReleation);

        // Mock
        when(cardAcctCustReleationSelfMapper.selectByOrgAndCid(organizationNumber, customerId))
                .thenReturn(acctCustReleationList);

        try (MockedStatic<BeanMapping> beanMappingMock = mockStatic(BeanMapping.class)) {
            beanMappingMock.when(() -> BeanMapping.copy(eq(vaAcctReleation), eq(CardAcctCustReleationDTO.class)))
                    .thenReturn(vaDto);

            // 执行
            CardAcctCustReleationDTO result = cardAcctCustReleationService.selectVaByCaAcctAndCustId(organizationNumber, accountManagementId, customerId);

            // 验证
            assertNotNull(result);
            assertEquals(AcctAttributeEnum.VA.getCode(), result.getAttribute());
        }
    }

    @Test
    void testSelectVaByCaAcctAndCustId_NotFound() {
        String organizationNumber = "0001";
        String accountManagementId = "AM001";
        String customerId = "CUST001";

        List<CardAcctCustReleation> acctCustReleationList = Arrays.asList(testEntity);

        // Mock
        when(cardAcctCustReleationSelfMapper.selectByOrgAndCid(organizationNumber, customerId))
                .thenReturn(acctCustReleationList);

        // 执行
        CardAcctCustReleationDTO result = cardAcctCustReleationService.selectVaByCaAcctAndCustId(organizationNumber, accountManagementId, customerId);

        // 验证
        assertNull(result);
    }

    @Test
    void testSelectByCustIdAndAttr() {
        String organizationNumber = "0001";
        String customerId = "CUST001";
        String attribute = AcctAttributeEnum.VA.getCode();

        // 创建VA类型的实体
        CardAcctCustReleation vaAcctReleation = new CardAcctCustReleation();
        vaAcctReleation.setId("2");
        vaAcctReleation.setAttribute(AcctAttributeEnum.VA.getCode());

        CardAcctCustReleationDTO vaDto = new CardAcctCustReleationDTO();
        vaDto.setId("2");
        vaDto.setAttribute(AcctAttributeEnum.VA.getCode());

        List<CardAcctCustReleation> allAcctCustReleationList = Arrays.asList(testEntity, vaAcctReleation);
        List<CardAcctCustReleationDTO> expectedDtoList = Arrays.asList(vaDto);

        // Mock
        when(cardAcctCustReleationSelfMapper.selectByOrgAndCid(organizationNumber, customerId))
                .thenReturn(allAcctCustReleationList);

        try (MockedStatic<BeanMapping> beanMappingMock = mockStatic(BeanMapping.class)) {
            beanMappingMock.when(() -> BeanMapping.copyList(anyList(), eq(CardAcctCustReleationDTO.class)))
                    .thenReturn(expectedDtoList);

            // 执行
            List<CardAcctCustReleationDTO> result = cardAcctCustReleationService.selectByCustIdAndAttr(organizationNumber, customerId, attribute);

            // 验证
            assertNotNull(result);
            assertEquals(1, result.size());
            assertEquals(AcctAttributeEnum.VA.getCode(), result.get(0).getAttribute());
        }
    }
}