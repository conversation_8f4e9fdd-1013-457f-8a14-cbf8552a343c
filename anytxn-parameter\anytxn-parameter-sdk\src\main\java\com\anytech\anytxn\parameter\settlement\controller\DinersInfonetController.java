package com.anytech.anytxn.parameter.settlement.controller;

import com.anytech.anytxn.common.core.base.AnyTxnHttpResponse;
import com.anytech.anytxn.common.core.base.BizBaseController;
import com.anytech.anytxn.parameter.settlement.service.DinersFranchiseTypeChargesServiceImpl;
import com.anytech.anytxn.parameter.base.settlement.service.IDinersFranchiseCycleRangeService;
import com.anytech.anytxn.parameter.base.settlement.domain.bo.InfonetFileBO;
import com.anytech.anytxn.parameter.base.common.utils.XmlUtils;
import io.swagger.v3.oas.annotations.tags.Tag;
import io.swagger.v3.oas.annotations.Operation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RestController;

import java.io.FileNotFoundException;

/**
 * DCI XML参数文件处理
 *
 * <AUTHOR>
 */
@Slf4j
@RestController
@Tag(name = "DinersInfonetController")
public class DinersInfonetController extends BizBaseController {

    @Autowired
    private IDinersFranchiseCycleRangeService dinersFranchiseCycleRangeService;
    @Autowired
    private DinersFranchiseTypeChargesServiceImpl dinersFranchiseTypeChargesService;
    @Operation(summary = "DCI XML参数文件上传")
    @PostMapping(value = "/param/franchise/infonet/upload")
    public AnyTxnHttpResponse<Object> uploadXml() throws FileNotFoundException {
        String property = System.getProperty("user.dir");
        System.out.println(property);
        java.io.FileInputStream fileInputStream = new java.io.FileInputStream("infonetFile.xml");
        InfonetFileBO infonetFileBO = XmlUtils.convertToObj(fileInputStream, InfonetFileBO.class);
        System.out.println("infonetFile:" + com.alibaba.fastjson.JSON.toJSONString(infonetFileBO));
        System.out.println("infonetFile.toXmlString() = " + infonetFileBO.toXmlString());
        //InfonetFile infonetFile;
        /*try {
            infonetFile = InfonetFile.parseObject(file.getInputStream());
        } catch (IOException e) {
            throw new AnyTxnParameterException(AnyTxnParameterRespCodeEnum.DINERS_INFONET_XML_EXCEPTION);
        }
        log.info("FileHeader: {}", JSON.toJSONString(infonetFile.getFileHeader()));
 */       // TODO update DCI parameter table
        //dinersFranchiseCycleRangeService.updateXmlCycleRanges(infonetFile.getCycleRanges());
        //xxxService.updateXmlCurrencies(infonetFile.getCurrencies());
        //xxxService.updateXmlMccs(infonetFile.getMccs());
        //xxxService.updateXmlGeoCodes(infonetFile.getGeoCodes());
        //xxxService.updateXmlIics(infonetFile.getIics());
        //xxxService.updateXmlInternationalEstablishments(infonetFile.getInternationalEstablishments());
        //xxxService.updateXmlIntes(infonetFile.getIntes());
        dinersFranchiseTypeChargesService.updateXmlTypeCharges(infonetFileBO.getTypeCharges());
        //xxxService.updateXmlChargeTypes(infonetFile.getChargeTypes());
        return AnyTxnHttpResponse.success();
    }

}
