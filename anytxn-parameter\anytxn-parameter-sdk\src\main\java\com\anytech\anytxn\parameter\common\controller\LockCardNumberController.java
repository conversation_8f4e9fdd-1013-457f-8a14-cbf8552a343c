package com.anytech.anytxn.parameter.common.controller;

import io.swagger.v3.oas.annotations.tags.Tag;
import io.swagger.v3.oas.annotations.Operation;
import com.anytech.anytxn.common.core.utils.OrgNumberUtils;
import com.anytech.anytxn.common.core.base.PageResultDTO;
import com.anytech.anytxn.common.core.base.AnyTxnHttpResponse;
import com.anytech.anytxn.common.core.base.BizBaseController;
import com.anytech.anytxn.parameter.base.common.domain.dto.LockCardNumberReqDTO;
import com.anytech.anytxn.parameter.base.common.domain.dto.LockCardNumberResDTO;
import com.anytech.anytxn.parameter.base.common.service.ILockCardNumberService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import jakarta.validation.Valid;

/**
 * 卡号锁定管理信息
 *
 * <AUTHOR>
 * @date 2018-12-04
 **/
@RestController
@Tag(name = "卡产品信息")
public class LockCardNumberController  extends BizBaseController {

    @Autowired
    private ILockCardNumberService lockCardNumberService;

    /**
     * 创建卡号锁定信息条目
     * @param lockCardNumberReq 卡号锁定信息请求数据
     * @return AnyTxnHttpResponse<CardProductInfoRes>
     */
    @PostMapping(value = "/param/lockCardNumber")
    @Operation(summary = "创建卡号锁定信息")
    public AnyTxnHttpResponse<LockCardNumberResDTO> create(@Valid @RequestBody LockCardNumberReqDTO lockCardNumberReq) {
        LockCardNumberResDTO lockCardNumberRes = lockCardNumberService.add(lockCardNumberReq);
        return AnyTxnHttpResponse.success(lockCardNumberRes);

    }

    /**
     * 删除号码锁定信息条目，通过id
     * @param id 技术id
     * @return AnyTxnHttpResponse<Boolean>
     */
    @DeleteMapping(value = "/param/lockCardNumber/id/{id}")
    @Operation(summary="根据id删除号码锁定信息条目", description = "需传入id")
    public AnyTxnHttpResponse<Boolean> remove(@PathVariable(value = "id") Long id) {
        Boolean bool = lockCardNumberService.remove(id);
        return AnyTxnHttpResponse.success(bool);
    }

    /**
     * 更新号码锁定信息
     * @param lockCardNumberReq 号码锁定信息请求数据
     * @return AnyTxnHttpResponse<CardProductInfoRes>
     */
    @PutMapping(value = "/param/lockCardNumber")
    @Operation(summary="根据id更新号码锁定信息", description = "")
    public AnyTxnHttpResponse<LockCardNumberResDTO> modify(@Valid @RequestBody LockCardNumberReqDTO lockCardNumberReq) {
        LockCardNumberResDTO lockCardNumberRes = lockCardNumberService.modify(lockCardNumberReq);
        return AnyTxnHttpResponse.success(lockCardNumberRes);

    }

    /**
     * 根据根据id获取详情，进行编辑
     * @param id 号码锁定信息id
     * @return AnyTxnHttpResponse<ProductInfoRes>
     */
    @Operation(summary="获取号码锁定详情，通过id", description = "")
    @GetMapping(value = "/param/lockCardNumber/id/{id}")
    public AnyTxnHttpResponse<LockCardNumberResDTO> getByIndex(@PathVariable(value = "id") Long id){
        LockCardNumberResDTO lockCardNumberRes = lockCardNumberService.find(id);
        return AnyTxnHttpResponse.success(lockCardNumberRes);

    }

    /**
     * 查询所有号码锁定参数信息
     *
     * @return TxnTypeControlRes
     */
    @Operation(summary = "号码锁定参数分页查询", description = "分页查询")
    @GetMapping(value = "/param/lockCardNumber/pageNum/{pageNum}/pageSize/{pageSize}")
    public AnyTxnHttpResponse<PageResultDTO<LockCardNumberResDTO>> getAllPage(@PathVariable(value = "pageNum") Integer pageNum,
                                                                           @PathVariable(value = "pageSize") Integer pageSize) {
        PageResultDTO<LockCardNumberResDTO> response = lockCardNumberService.findPageByOrgNumber(pageNum, pageSize, OrgNumberUtils.getOrg());
        return AnyTxnHttpResponse.success(response);
    }

}
