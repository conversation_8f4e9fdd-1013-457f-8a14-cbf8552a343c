package com.anytech.anytxn.parameter.account.controller;

import io.swagger.v3.oas.annotations.tags.Tag;
import io.swagger.v3.oas.annotations.Operation;
import com.anytech.anytxn.common.core.base.ParameterCompare;
import com.anytech.anytxn.common.core.base.PageResultDTO;
import com.anytech.anytxn.common.core.base.AnyTxnHttpResponse;
import com.anytech.anytxn.common.core.base.BizBaseController;
import com.anytech.anytxn.parameter.base.account.domain.dto.ParmBalancePricingTableResDTO;
import com.anytech.anytxn.parameter.base.account.service.IParmBalancePricingTableService;
import com.anytech.anytxn.parameter.base.common.enums.ParameterRepDetailEnum;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import jakarta.validation.Valid;

/**
 * 余额表定价参数
 * <AUTHOR>
 * @date 2019-05-05 11:09
 **/
@RestController
@Tag(name = "余额定价参数")
public class ParmBalancePricingTableController extends BizBaseController {

    /***
     * 注入parmBalancePricingTableService接口
     **/
    @Autowired
    private IParmBalancePricingTableService parmBalancePricingTableService;

    /**
     * 新建余额定价参数
     * @param parmBalancePricingTableResDTO 余额定价参数请求数据
     * @return AnyTxnHttpResponse<ParmBalancePricingTableResDTO>
     */
    @Operation(summary ="新建余额定价参数")
    @PostMapping(value = "/param/parmBalancePrice")
    public AnyTxnHttpResponse create(@RequestBody ParmBalancePricingTableResDTO parmBalancePricingTableResDTO)
    {
        ParameterCompare balancePricingTableResDTO = parmBalancePricingTableService.add(parmBalancePricingTableResDTO);
        return AnyTxnHttpResponse.success(balancePricingTableResDTO, ParameterRepDetailEnum.CREATE.message());
    }
    /**
     * 修改余额定价参数
     * @param parmBalancePricingTableResDTO 余额定价参数请求数据
     * @return AnyTxnHttpResponse<ParmBalancePricingTableResDTO>
     */
    @PutMapping(value = "/param/parmBalancePrice")
    @Operation(summary="更改余额定价参数信息根据id")
    public AnyTxnHttpResponse<Object> modify(@Valid @RequestBody ParmBalancePricingTableResDTO parmBalancePricingTableResDTO) {
        ParameterCompare balance = parmBalancePricingTableService.modify(parmBalancePricingTableResDTO);
        return AnyTxnHttpResponse.success(balance,ParameterRepDetailEnum.UPDATE.message());

    }
    /**
     * 删除余额定价参数
     * @param id
     * @return Boolean
     */
    @Operation(summary = "删除余额定价参数",description = "删除余额定价参数")
    @DeleteMapping(value = "/param/parmBalancePrice/id/{id}")
    public AnyTxnHttpResponse<Object> remove(@PathVariable String id){
        ParameterCompare deleted = parmBalancePricingTableService.remove(id);
        return AnyTxnHttpResponse.success(deleted,ParameterRepDetailEnum.DEL.message());
    }
    /**
     * 查询余额定价参数
     * @param organizationNumber tableId transactionTypeCode 余额定价参数请求数据
     * @return AnyTxnHttpResponse<ParmBalancePricingTableResDTO>
     */
    @Operation(summary = "根据机构号，参数表ID，交易类型代码查询余额定价参数",description = "查询余额定价参数")
    @GetMapping(value = "/param/parmBalancePrice/organizationNumber/{organizationNumber}/tableId/{tableId}/transactionTypeCode/{transactionTypeCode}")
    public AnyTxnHttpResponse<ParmBalancePricingTableResDTO> getByIndex(
            @PathVariable(value = "organizationNumber") String organizationNumber,
            @PathVariable(value = "tableId") String tableId,
            @PathVariable(value="transactionTypeCode")String transactionTypeCode){
        ParmBalancePricingTableResDTO balance = parmBalancePricingTableService.findByIndex(
                organizationNumber, tableId,transactionTypeCode);
        return AnyTxnHttpResponse.success(balance);
    }
    /**
     * 根据id余额定价参数
     * @param id
     * @return AnyTxnHttpResponse<ParmBalancePricingTableResDTO>
     */
    @Operation(summary = "根据id查询余额定价参数",description = "根据id查询余额定价参数")
    @GetMapping(value = "/param/parmBalancePrice/id/{id}" )
    public AnyTxnHttpResponse<ParmBalancePricingTableResDTO> getById(
            @PathVariable(value = "id")  String id){
        ParmBalancePricingTableResDTO balance = parmBalancePricingTableService.findById(id);
        return AnyTxnHttpResponse.success(balance);
    }
    @Operation(summary = "分页余额定价参数",description = "分页余额定价参数")
    @GetMapping(value = "/param/parmBalancePrice/pageNum/{pageNum}/pageSize/{pageSize}")
    public AnyTxnHttpResponse<PageResultDTO<ParmBalancePricingTableResDTO>> getPage(@PathVariable(value = "pageNum")Integer pageNum,
                                                                                 @PathVariable(value = "pageSize")Integer pageSize,
                                                                                    ParmBalancePricingTableResDTO parmBalancePricingTableResDTO){
        PageResultDTO<ParmBalancePricingTableResDTO> pageResultDto = parmBalancePricingTableService.findPage(pageNum, pageSize,parmBalancePricingTableResDTO);
        return AnyTxnHttpResponse.success(pageResultDto);
    }
}
