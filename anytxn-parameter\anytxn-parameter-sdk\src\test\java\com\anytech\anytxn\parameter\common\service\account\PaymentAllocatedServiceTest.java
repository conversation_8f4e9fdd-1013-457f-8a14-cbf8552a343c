package com.anytech.anytxn.parameter.common.service.account;

import com.alibaba.fastjson.JSON;
import com.anytech.anytxn.business.base.audit.ParmModificationRecord;
import com.anytech.anytxn.common.core.base.PageResultDTO;
import com.anytech.anytxn.common.core.base.ParameterCompare;
import com.anytech.anytxn.common.core.utils.BeanMapping;
import com.anytech.anytxn.common.core.utils.OrgNumberUtils;
import com.anytech.anytxn.common.core.utils.TenantUtils;
import com.anytech.anytxn.common.sequence.utils.Number16IdGen;
import com.anytech.anytxn.parameter.account.mapper.ParmPaymentAllocatedControlSelfMapper;
import com.anytech.anytxn.parameter.account.mapper.ParmPaymentAllocatedDefineMapper;
import com.anytech.anytxn.parameter.account.mapper.ParmPaymentAllocatedDefineSelfMapper;
import com.anytech.anytxn.parameter.account.service.PaymentAllocatedServiceImpl;
import com.anytech.anytxn.parameter.base.account.domain.dto.PaymentAllocatedReqDTO;
import com.anytech.anytxn.parameter.base.account.domain.dto.PaymentAllocatedResDTO;
import com.anytech.anytxn.parameter.base.account.domain.model.ParmPaymentAllocatedControl;
import com.anytech.anytxn.parameter.base.account.domain.model.ParmPaymentAllocatedDefine;
import com.anytech.anytxn.parameter.base.common.enums.AnyTxnParameterRespCodeEnum;
import com.anytech.anytxn.parameter.base.common.exception.AnyTxnParameterException;
import com.github.pagehelper.Page;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockedStatic;
import org.mockito.junit.jupiter.MockitoExtension;

import java.lang.reflect.Field;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.Arrays;
import java.util.Collections;
import java.util.List;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.*;

/**
 * PaymentAllocatedService单元测试类
 * 
 * <AUTHOR> Assistant
 * @date 2025-01-25
 */
@ExtendWith(MockitoExtension.class)
class PaymentAllocatedServiceTest {

    @Mock
    private ParmPaymentAllocatedDefineMapper parmPaymentAllocatedDefineMapper;

    @Mock
    private ParmPaymentAllocatedDefineSelfMapper parmPaymentAllocatedDefineSelfMapper;

    @Mock
    private ParmPaymentAllocatedControlSelfMapper parmPaymentAllocatedControlSelfMapper;

    @Mock
    private Number16IdGen number16IdGen;

    @InjectMocks
    private PaymentAllocatedServiceImpl paymentAllocatedService;

    private PaymentAllocatedReqDTO mockReqDTO;
    private ParmPaymentAllocatedDefine mockDefine;
    private ParmPaymentAllocatedControl mockControl;
    private PaymentAllocatedResDTO mockResDTO;

    @BeforeEach
    void setUp() throws Exception {
        // 初始化OrgNumberUtils的静态实例以避免NullPointerException
        Field orgNumberUtilField = OrgNumberUtils.class.getDeclaredField("orgNumberUtil");
        orgNumberUtilField.setAccessible(true);
        OrgNumberUtils orgNumberUtilInstance = new OrgNumberUtils();
        orgNumberUtilField.set(null, orgNumberUtilInstance);
        
        // Mock静态方法并创建测试数据
        try (MockedStatic<OrgNumberUtils> orgNumberUtilsMocked = mockStatic(OrgNumberUtils.class)) {
            orgNumberUtilsMocked.when(OrgNumberUtils::getOrg).thenReturn("ORG001");
            
            // 构建测试用的PaymentAllocatedReqDTO
            mockReqDTO = new PaymentAllocatedReqDTO();
            mockReqDTO.setOrganizationNumber("ORG001");
            mockReqDTO.setTableId("PAY001");
            mockReqDTO.setDescription("测试还款分配参数");
            mockReqDTO.setStatus("1");
        }

        // 构建测试用的ParmPaymentAllocatedDefine (不继承BaseParam，直接设置属性)
        mockDefine = new ParmPaymentAllocatedDefine();
        mockDefine.setId("DEF001");
        mockDefine.setTableId("PAY001");
        mockDefine.setDescription("测试还款分配参数");
        mockDefine.setStatus("1");
        mockDefine.setOrganizationNumber("ORG001");
        mockDefine.setVersionNumber(1L);
        mockDefine.setCreateTime(LocalDateTime.now());
        mockDefine.setUpdateTime(LocalDateTime.now());

        // 构建测试用的ParmPaymentAllocatedControl
        mockControl = new ParmPaymentAllocatedControl();
        mockControl.setId(1001L);
        mockControl.setTableId("PAY001");
        mockControl.setOrganizationNumber("ORG001");
        mockControl.setTransactionTypeCode("TXN001");
        mockControl.setStatementFlag("1");
        mockControl.setPriority(1);
        mockControl.setVersionNumber(1L);

        // 构建测试用的PaymentAllocatedResDTO
        mockResDTO = new PaymentAllocatedResDTO();
        mockResDTO.setId("DEF001");
        mockResDTO.setTableId("PAY001");
        mockResDTO.setDescription("测试还款分配参数");
        mockResDTO.setStatus("1");
        mockResDTO.setOrganizationNumber("ORG001");
    }

    /**
     * 测试 findAll 方法 - 成功路径
     */
    @Test
    void testFindAll_Success() {
        try (MockedStatic<OrgNumberUtils> orgUtils = mockStatic(OrgNumberUtils.class);
             MockedStatic<BeanMapping> beanMapping = mockStatic(BeanMapping.class)) {
            
            orgUtils.when(() -> OrgNumberUtils.getOrg()).thenReturn("ORG001");
            orgUtils.when(() -> OrgNumberUtils.getOrg(anyString())).thenReturn("ORG001");
            
            // Given
            Page<ParmPaymentAllocatedDefine> mockPage = new Page<>(1, 10);
            mockPage.setTotal(1);
            List<ParmPaymentAllocatedDefine> mockList = Arrays.asList(mockDefine);
            List<PaymentAllocatedResDTO> mockResList = Arrays.asList(mockResDTO);
            
            when(parmPaymentAllocatedDefineSelfMapper.selectByCondition(any())).thenReturn(mockList);
            beanMapping.when(() -> BeanMapping.copyList(mockList, PaymentAllocatedResDTO.class))
                    .thenReturn(mockResList);

            // When
            PageResultDTO<PaymentAllocatedResDTO> result = paymentAllocatedService.findAll(1, 10, mockReqDTO);

            // Then
            assertNotNull(result);
            verify(parmPaymentAllocatedDefineSelfMapper).selectByCondition(any());
        }
    }

    /**
     * 测试 findAll 方法 - 空参数
     */
    @Test
    void testFindAll_NullRequest() {
        try (MockedStatic<OrgNumberUtils> orgUtils = mockStatic(OrgNumberUtils.class);
             MockedStatic<BeanMapping> beanMapping = mockStatic(BeanMapping.class)) {
            
            orgUtils.when(() -> OrgNumberUtils.getOrg()).thenReturn("ORG001");
            
            // Given
            Page<ParmPaymentAllocatedDefine> mockPage = new Page<>(1, 10);
            mockPage.setTotal(0);
            List<ParmPaymentAllocatedDefine> mockList = Collections.emptyList();
            List<PaymentAllocatedResDTO> mockResList = Collections.emptyList();
            
            when(parmPaymentAllocatedDefineSelfMapper.selectByCondition(any())).thenReturn(mockList);
            beanMapping.when(() -> BeanMapping.copyList(mockList, PaymentAllocatedResDTO.class))
                    .thenReturn(mockResList);

            // When
            PageResultDTO<PaymentAllocatedResDTO> result = paymentAllocatedService.findAll(1, 10, null);

            // Then
            assertNotNull(result);
            verify(parmPaymentAllocatedDefineSelfMapper).selectByCondition(any());
        }
    }

    /**
     * 测试 findDetail 方法 - 成功路径
     */
    @Test
    void testFindDetail_Success() {
        try (MockedStatic<BeanMapping> beanMapping = mockStatic(BeanMapping.class)) {
            // Given
            String id = "DEF001";
            List<ParmPaymentAllocatedControl> controlList = Arrays.asList(mockControl);
            
            when(parmPaymentAllocatedDefineMapper.selectByPrimaryKey(id)).thenReturn(mockDefine);
            when(parmPaymentAllocatedControlSelfMapper.seletByOrgAndTableId(anyString(), anyString()))
                    .thenReturn(controlList);

            // When
            PaymentAllocatedResDTO result = paymentAllocatedService.findDetail(id);

            // Then
            assertNotNull(result);
            verify(parmPaymentAllocatedDefineMapper).selectByPrimaryKey(id);
        }
    }

    /**
     * 测试 findDetail 方法 - 数据不存在
     */
    @Test
    void testFindDetail_NotFound() {
        // Given
        String id = "NONEXISTENT";
        when(parmPaymentAllocatedDefineMapper.selectByPrimaryKey(id)).thenReturn(null);

        // When & Then
        AnyTxnParameterException exception = assertThrows(AnyTxnParameterException.class,
                () -> paymentAllocatedService.findDetail(id));

        assertEquals(AnyTxnParameterRespCodeEnum.D_QUERY_PARM_PAYMENT_ALLOCATED_BY_ID_FAULT.getCode(),
                exception.getErrCode());
        verify(parmPaymentAllocatedDefineMapper).selectByPrimaryKey(id);
    }

    /**
     * 测试 findByStatus 方法 - 成功路径
     */
    @Test
    void testFindByStatus_Success() {
        try (MockedStatic<OrgNumberUtils> orgUtils = mockStatic(OrgNumberUtils.class);
             MockedStatic<BeanMapping> beanMapping = mockStatic(BeanMapping.class)) {
            
            orgUtils.when(() -> OrgNumberUtils.getOrg()).thenReturn("ORG001");
            
            // Given
            String status = "1";
            List<ParmPaymentAllocatedDefine> mockList = Arrays.asList(mockDefine);
            List<PaymentAllocatedResDTO> mockResList = Arrays.asList(mockResDTO);
            
            when(parmPaymentAllocatedDefineSelfMapper.selectByStatus(status, "ORG001")).thenReturn(mockList);
            beanMapping.when(() -> BeanMapping.copyList(mockList, PaymentAllocatedResDTO.class))
                    .thenReturn(mockResList);

            // When
            List<PaymentAllocatedResDTO> result = paymentAllocatedService.findByStatus(status);

            // Then
            assertNotNull(result);
            assertFalse(result.isEmpty());
            verify(parmPaymentAllocatedDefineSelfMapper).selectByStatus(status, "ORG001");
        }
    }

    /**
     * 测试 findByStatus 方法 - 空状态参数
     */
    @Test
    void testFindByStatus_EmptyStatus() {
        // When
        List<PaymentAllocatedResDTO> result = paymentAllocatedService.findByStatus("");

        // Then
        assertNull(result);
        verifyNoInteractions(parmPaymentAllocatedDefineSelfMapper);
    }

    /**
     * 测试 findByOrgAndTableId 方法 - 成功路径
     */
    @Test
    void testFindByOrgAndTableId_Success() {
        try (MockedStatic<BeanMapping> beanMapping = mockStatic(BeanMapping.class)) {
            // Given
            String org = "ORG001";
            String tableId = "PAY001";
            
            when(parmPaymentAllocatedDefineSelfMapper.isExsit(org, tableId)).thenReturn(mockDefine);
            beanMapping.when(() -> BeanMapping.copy(mockDefine, PaymentAllocatedResDTO.class))
                    .thenReturn(mockResDTO);

            // When
            PaymentAllocatedResDTO result = paymentAllocatedService.findByOrgAndTableId(org, tableId);

            // Then
            assertNotNull(result);
            verify(parmPaymentAllocatedDefineSelfMapper).isExsit(org, tableId);
        }
    }

    /**
     * 测试 findByOrgAndTableId 方法 - 数据不存在
     */
    @Test
    void testFindByOrgAndTableId_NotFound() {
        try (MockedStatic<BeanMapping> beanMapping = mockStatic(BeanMapping.class)) {
            // Given
            String org = "ORG001";
            String tableId = "NONEXISTENT";
            
            when(parmPaymentAllocatedDefineSelfMapper.isExsit(org, tableId)).thenReturn(null);
            beanMapping.when(() -> BeanMapping.copy(null, PaymentAllocatedResDTO.class))
                    .thenReturn(null);

            // When
            PaymentAllocatedResDTO result = paymentAllocatedService.findByOrgAndTableId(org, tableId);

            // Then
            assertNull(result);
            verify(parmPaymentAllocatedDefineSelfMapper).isExsit(org, tableId);
        }
    }

    /**
     * 测试 addPaymentAllocated 方法 - 成功路径
     */
    @Test
    void testAddPaymentAllocated_Success() {
        try (MockedStatic<OrgNumberUtils> orgUtils = mockStatic(OrgNumberUtils.class)) {
            orgUtils.when(() -> OrgNumberUtils.getOrg(mockReqDTO.getOrganizationNumber())).thenReturn("ORG001");
            when(parmPaymentAllocatedDefineSelfMapper.isExsit("ORG001", mockReqDTO.getTableId())).thenReturn(null);

            // When
            ParameterCompare result = paymentAllocatedService.addPaymentAllocated(mockReqDTO);

            // Then
            assertNotNull(result);
            verify(parmPaymentAllocatedDefineSelfMapper).isExsit("ORG001", mockReqDTO.getTableId());
        }
    }

    /**
     * 测试 addPaymentAllocated 方法 - 数据已存在
     */
    @Test
    void testAddPaymentAllocated_AlreadyExists() {
        try (MockedStatic<OrgNumberUtils> orgUtils = mockStatic(OrgNumberUtils.class)) {
            orgUtils.when(() -> OrgNumberUtils.getOrg(mockReqDTO.getOrganizationNumber())).thenReturn("ORG001");
            when(parmPaymentAllocatedDefineSelfMapper.isExsit("ORG001", mockReqDTO.getTableId())).thenReturn(mockDefine);

            // When & Then
            AnyTxnParameterException exception = assertThrows(AnyTxnParameterException.class, 
                    () -> paymentAllocatedService.addPaymentAllocated(mockReqDTO));
            
            assertEquals(AnyTxnParameterRespCodeEnum.D_EXIST_PARM_PAYMENT_ALLOCATED_BY_ID_FAULT.getCode(), 
                    exception.getErrCode());
            verify(parmPaymentAllocatedDefineSelfMapper).isExsit("ORG001", mockReqDTO.getTableId());
        }
    }

    /**
     * 测试 modifyPaymentAllocated 方法 - 成功路径
     */
    @Test
    void testModifyPaymentAllocated_Success() {
        // Given
        when(parmPaymentAllocatedDefineMapper.selectByPrimaryKey(mockReqDTO.getId())).thenReturn(mockDefine);
        when(parmPaymentAllocatedControlSelfMapper.seletByOrgAndTableId(anyString(), anyString()))
                .thenReturn(Arrays.asList(mockControl));

        // When
        ParameterCompare result = paymentAllocatedService.modifyPaymentAllocated(mockReqDTO);

        // Then
        assertNotNull(result);
        verify(parmPaymentAllocatedDefineMapper).selectByPrimaryKey(mockReqDTO.getId());
    }

    /**
     * 测试 modifyPaymentAllocated 方法 - 数据不存在
     */
    @Test
    void testModifyPaymentAllocated_NotFound() {
        // Given
        when(parmPaymentAllocatedDefineMapper.selectByPrimaryKey(mockReqDTO.getId())).thenReturn(null);

        // When & Then
        AnyTxnParameterException exception = assertThrows(AnyTxnParameterException.class, 
                () -> paymentAllocatedService.modifyPaymentAllocated(mockReqDTO));
        
        assertEquals(AnyTxnParameterRespCodeEnum.D_DATA_NOT_EXISTS_FAULT.getCode(), 
                exception.getErrCode());
        verify(parmPaymentAllocatedDefineMapper).selectByPrimaryKey(mockReqDTO.getId());
    }

    /**
     * 测试 removePaymentAllocated 方法 - 成功路径
     */
    @Test
    void testRemovePaymentAllocated_Success() {
        // Given
        String id = "DEF001";
        when(parmPaymentAllocatedDefineMapper.selectByPrimaryKey(id)).thenReturn(mockDefine);

        // When
        ParameterCompare result = paymentAllocatedService.removePaymentAllocated(id);

        // Then
        assertNotNull(result);
        verify(parmPaymentAllocatedDefineMapper).selectByPrimaryKey(id);
    }

    /**
     * 测试 removePaymentAllocated 方法 - 数据不存在
     */
    @Test
    void testRemovePaymentAllocated_NotFound() {
        // Given
        String id = "NONEXISTENT";
        when(parmPaymentAllocatedDefineMapper.selectByPrimaryKey(id)).thenReturn(null);

        // When & Then
        AnyTxnParameterException exception = assertThrows(AnyTxnParameterException.class, 
                () -> paymentAllocatedService.removePaymentAllocated(id));
        
        assertEquals(AnyTxnParameterRespCodeEnum.D_QUERY_PARM_PAYMENT_ALLOCATED_BY_ID_FAULT.getCode(), 
                exception.getErrCode());
        verify(parmPaymentAllocatedDefineMapper).selectByPrimaryKey(id);
    }
} 