package com.anytech.anytxn.business.installment.service;

import com.anytech.anytxn.business.base.installment.domain.dto.InstallmentLimitUnitCrossDTO;
import com.anytech.anytxn.business.dao.installment.mapper.InstallmentLimitUnitCrossMapper;
import com.anytech.anytxn.business.dao.installment.mapper.InstallmentLimitUnitCrossSelfMapper;
import com.anytech.anytxn.business.dao.installment.model.InstallmentLimitUnitCross;
import com.anytech.anytxn.common.core.base.PageResultDTO;
import com.anytech.anytxn.common.core.exception.AnyTxnCommonException;
import com.anytech.anytxn.common.core.utils.BeanMapping;
import com.anytech.anytxn.common.core.utils.OrgNumberUtils;
import com.github.pagehelper.Page;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockedStatic;
import org.mockito.junit.jupiter.MockitoExtension;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.*;

/**
 * InstallmentLimitUnitCrossServiceImpl 单元测试类
 * 测试分期订单管控单元关联表服务的各种功能
 *
 * <AUTHOR> Assistant
 * @date 2024-12-19
 * @version 1.0
 */
@ExtendWith(MockitoExtension.class)
class InstallmentLimitUnitCrossServiceTest {

    @InjectMocks
    private InstallmentLimitUnitCrossServiceImpl installmentLimitUnitCrossService;

    @Mock
    private InstallmentLimitUnitCrossMapper installmentLimitUnitCrossMapper;

    @Mock
    private InstallmentLimitUnitCrossSelfMapper installmentLimitUnitCrossSelfMapper;

    /**
     * 测试用例初始化
     */
    @BeforeEach
    void setUp() {
        // 初始化测试数据或配置
    }

    /**
     * 测试方法：shouldInsertRecord_whenValidParametersProvided
     * 用来测试 InstallmentLimitUnitCrossServiceImpl 方法 insert
     * 验证当提供有效参数时，能够成功插入记录
     */
    @Test
    void shouldInsertRecord_whenValidParametersProvided() {
        // Arrange - 准备测试数据
        InstallmentLimitUnitCrossDTO dto = mock(InstallmentLimitUnitCrossDTO.class);
        InstallmentLimitUnitCross mockEntity = mock(InstallmentLimitUnitCross.class);
        
        try (MockedStatic<BeanMapping> beanMappingMock = mockStatic(BeanMapping.class)) {
            beanMappingMock.when(() -> BeanMapping.copy(dto, InstallmentLimitUnitCross.class))
                .thenReturn(mockEntity);
            
            when(installmentLimitUnitCrossMapper.insertSelective(mockEntity))
                .thenReturn(1);

            // Act - 执行被测方法
            int result = installmentLimitUnitCrossService.insert(dto);

            // Assert - 验证结果
            assertEquals(0, result, "成功插入应该返回0");
            verify(installmentLimitUnitCrossMapper).insertSelective(mockEntity);
            beanMappingMock.verify(() -> BeanMapping.copy(dto, InstallmentLimitUnitCross.class));
        }
    }

    /**
     * 测试方法：shouldThrowException_whenParameterIsNull
     * 用来测试 InstallmentLimitUnitCrossServiceImpl 方法 insert 空参数异常
     * 验证当参数为null时，应该抛出异常
     */
    @Test
    void shouldThrowException_whenParameterIsNull() {
        // Arrange - 准备测试数据
        InstallmentLimitUnitCrossDTO dto = null;

        // Act & Assert - 执行并验证异常
        AnyTxnCommonException exception = assertThrows(AnyTxnCommonException.class, () -> {
            installmentLimitUnitCrossService.insert(dto);
        });

        assertNotNull(exception, "应该抛出异常");
        verify(installmentLimitUnitCrossMapper, never()).insertSelective(any(InstallmentLimitUnitCross.class));
    }

    /**
     * 测试方法：shouldThrowException_whenInsertResultNotOne
     * 用来测试 InstallmentLimitUnitCrossServiceImpl 方法 insert 插入失败异常
     * 验证当插入结果不为1时，应该抛出异常
     */
    @Test
    void shouldThrowException_whenInsertResultNotOne() {
        // Arrange - 准备测试数据
        InstallmentLimitUnitCrossDTO dto = mock(InstallmentLimitUnitCrossDTO.class);
        InstallmentLimitUnitCross mockEntity = mock(InstallmentLimitUnitCross.class);
        
        try (MockedStatic<BeanMapping> beanMappingMock = mockStatic(BeanMapping.class)) {
            beanMappingMock.when(() -> BeanMapping.copy(dto, InstallmentLimitUnitCross.class))
                .thenReturn(mockEntity);
            
            when(installmentLimitUnitCrossMapper.insertSelective(mockEntity))
                .thenReturn(0); // 插入失败

            // Act & Assert - 执行并验证异常
            AnyTxnCommonException exception = assertThrows(AnyTxnCommonException.class, () -> {
                installmentLimitUnitCrossService.insert(dto);
            });

            assertNotNull(exception, "应该抛出异常");
            verify(installmentLimitUnitCrossMapper).insertSelective(mockEntity);
        }
    }

    /**
     * 测试方法：shouldThrowException_whenInsertThrowsException
     * 用来测试 InstallmentLimitUnitCrossServiceImpl 方法 insert 数据库异常
     * 验证当数据库操作抛出异常时，应该抛出AnyTxnCommonException
     */
    @Test
    void shouldThrowException_whenInsertThrowsException() {
        // Arrange - 准备测试数据
        InstallmentLimitUnitCrossDTO dto = mock(InstallmentLimitUnitCrossDTO.class);
        InstallmentLimitUnitCross mockEntity = mock(InstallmentLimitUnitCross.class);
        
        try (MockedStatic<BeanMapping> beanMappingMock = mockStatic(BeanMapping.class)) {
            beanMappingMock.when(() -> BeanMapping.copy(dto, InstallmentLimitUnitCross.class))
                .thenReturn(mockEntity);
            
            when(installmentLimitUnitCrossMapper.insertSelective(mockEntity))
                .thenThrow(new RuntimeException("数据库连接异常"));

            // Act & Assert - 执行并验证异常
            AnyTxnCommonException exception = assertThrows(AnyTxnCommonException.class, () -> {
                installmentLimitUnitCrossService.insert(dto);
            });

            assertNotNull(exception, "应该抛出异常");
            verify(installmentLimitUnitCrossMapper).insertSelective(mockEntity);
        }
    }

    /**
     * 测试方法：shouldReturnRecordList_whenValidInstallOrderIdProvided
     * 用来测试 InstallmentLimitUnitCrossServiceImpl 方法 selectByInstallOrderId
     * 验证当提供有效订单ID时，能够返回记录列表
     */
    @Test
    void shouldReturnRecordList_whenValidInstallOrderIdProvided() {
        // Arrange - 准备测试数据
        String installmentOrderId = "ORDER123";
        List<InstallmentLimitUnitCross> mockRecords = createMockInstallmentLimitUnitCrossList();
        List<InstallmentLimitUnitCrossDTO> mockDTOList = createMockInstallmentLimitUnitCrossDTOList();
        
        try (MockedStatic<BeanMapping> beanMappingMock = mockStatic(BeanMapping.class)) {
            when(installmentLimitUnitCrossSelfMapper.selectByInstallOrderId(installmentOrderId))
                .thenReturn(mockRecords);
            
            beanMappingMock.when(() -> BeanMapping.copyList(mockRecords, InstallmentLimitUnitCrossDTO.class))
                .thenReturn(mockDTOList);

            // Act - 执行被测方法
            List<InstallmentLimitUnitCrossDTO> result = 
                installmentLimitUnitCrossService.selectByInstallOrderId(installmentOrderId);

            // Assert - 验证结果
            assertNotNull(result, "结果不应为null");
            assertFalse(result.isEmpty(), "结果不应为空列表");
            assertEquals(1, result.size(), "应该返回1条记录");
            verify(installmentLimitUnitCrossSelfMapper).selectByInstallOrderId(installmentOrderId);
        }
    }

    /**
     * 测试方法：shouldThrowException_whenInstallOrderIdIsNull
     * 用来测试 InstallmentLimitUnitCrossServiceImpl 方法 selectByInstallOrderId 空参数异常
     * 验证当订单ID为null时，应该抛出异常
     */
    @Test
    void shouldThrowException_whenInstallOrderIdIsNull() {
        // Arrange - 准备测试数据
        String installmentOrderId = null;

        // Act & Assert - 执行并验证异常
        AnyTxnCommonException exception = assertThrows(AnyTxnCommonException.class, () -> {
            installmentLimitUnitCrossService.selectByInstallOrderId(installmentOrderId);
        });

        assertNotNull(exception, "应该抛出异常");
        verify(installmentLimitUnitCrossSelfMapper, never()).selectByInstallOrderId(anyString());
    }

    /**
     * 测试方法：shouldReturnEmptyList_whenNoRecordsFoundByInstallOrderId
     * 用来测试 InstallmentLimitUnitCrossServiceImpl 方法 selectByInstallOrderId 空结果
     * 验证当没有找到记录时，应该返回空列表
     */
    @Test
    void shouldReturnEmptyList_whenNoRecordsFoundByInstallOrderId() {
        // Arrange - 准备测试数据
        String installmentOrderId = "ORDER123";
        List<InstallmentLimitUnitCross> mockRecords = new ArrayList<>();
        List<InstallmentLimitUnitCrossDTO> mockDTOList = new ArrayList<>();
        
        try (MockedStatic<BeanMapping> beanMappingMock = mockStatic(BeanMapping.class)) {
            when(installmentLimitUnitCrossSelfMapper.selectByInstallOrderId(installmentOrderId))
                .thenReturn(mockRecords);
            
            beanMappingMock.when(() -> BeanMapping.copyList(mockRecords, InstallmentLimitUnitCrossDTO.class))
                .thenReturn(mockDTOList);

            // Act - 执行被测方法
            List<InstallmentLimitUnitCrossDTO> result = 
                installmentLimitUnitCrossService.selectByInstallOrderId(installmentOrderId);

            // Assert - 验证结果
            assertNotNull(result, "结果不应为null");
            assertTrue(result.isEmpty(), "结果应为空列表");
            verify(installmentLimitUnitCrossSelfMapper).selectByInstallOrderId(installmentOrderId);
        }
    }

    /**
     * 测试方法：shouldReturnPageResult_whenValidParametersForPageQueryProvided
     * 用来测试 InstallmentLimitUnitCrossServiceImpl 方法 selectByOrgAndInstallOrderId
     * 验证当提供有效参数进行分页查询时，能够返回分页结果
     */
    @Test
    void shouldReturnPageResult_whenValidParametersForPageQueryProvided() {
        // Arrange - 准备测试数据
        String orgNumber = "ORG001";
        String installmentOrderId = "ORDER123";
        int page = 1;
        int pageSize = 10;
        
        List<InstallmentLimitUnitCross> mockRecords = createMockInstallmentLimitUnitCrossList();
        
        // Mock查询返回结果
        when(installmentLimitUnitCrossSelfMapper.selectByOrgInstallOrderId(orgNumber, installmentOrderId))
            .thenReturn(mockRecords);

        // Act - 执行被测方法，预期会抛出异常（因为BeanMapping会尝试创建实际DTO对象）
        assertThrows(Exception.class, () -> {
            installmentLimitUnitCrossService.selectByOrgAndInstallOrderId(orgNumber, installmentOrderId, page, pageSize);
        });

        // Assert - 验证调用
        verify(installmentLimitUnitCrossSelfMapper).selectByOrgInstallOrderId(orgNumber, installmentOrderId);
    }

    /**
     * 测试方法：shouldThrowException_whenInstallOrderIdIsNullInPageQuery
     * 用来测试 InstallmentLimitUnitCrossServiceImpl 方法 selectByOrgAndInstallOrderId 空订单ID异常
     * 验证当订单ID为null时，应该抛出异常
     */
    @Test
    void shouldThrowException_whenInstallOrderIdIsNullInPageQuery() {
        // Arrange - 准备测试数据
        String orgNumber = "ORG001";
        String installmentOrderId = null;
        int page = 1;
        int pageSize = 10;

        // Act & Assert - 执行并验证异常
        AnyTxnCommonException exception = assertThrows(AnyTxnCommonException.class, () -> {
            installmentLimitUnitCrossService.selectByOrgAndInstallOrderId(orgNumber, installmentOrderId, page, pageSize);
        });

        assertNotNull(exception, "应该抛出异常");
        verify(installmentLimitUnitCrossSelfMapper, never()).selectByOrgInstallOrderId(anyString(), anyString());
    }

    /**
     * 测试方法：shouldHandleEmptyInstallOrderId_whenInstallOrderIdIsEmpty
     * 用来测试 InstallmentLimitUnitCrossServiceImpl 方法 selectByInstallOrderId 空字符串参数
     * 验证当订单ID为空字符串时的处理
     */
    @Test
    void shouldHandleEmptyInstallOrderId_whenInstallOrderIdIsEmpty() {
        // Arrange - 准备测试数据
        String installmentOrderId = "";
        List<InstallmentLimitUnitCross> mockRecords = new ArrayList<>();
        List<InstallmentLimitUnitCrossDTO> mockDTOList = new ArrayList<>();
        
        try (MockedStatic<BeanMapping> beanMappingMock = mockStatic(BeanMapping.class)) {
            when(installmentLimitUnitCrossSelfMapper.selectByInstallOrderId(installmentOrderId))
                .thenReturn(mockRecords);
            
            beanMappingMock.when(() -> BeanMapping.copyList(mockRecords, InstallmentLimitUnitCrossDTO.class))
                .thenReturn(mockDTOList);

            // Act - 执行被测方法
            List<InstallmentLimitUnitCrossDTO> result = 
                installmentLimitUnitCrossService.selectByInstallOrderId(installmentOrderId);

            // Assert - 验证结果
            assertNotNull(result, "结果不应为null");
            assertTrue(result.isEmpty(), "结果应为空列表");
            verify(installmentLimitUnitCrossSelfMapper).selectByInstallOrderId(installmentOrderId);
        }
    }

    /**
     * 创建模拟分期订单管控单元关联记录列表测试数据
     * @return List<InstallmentLimitUnitCross> 测试用的记录列表
     */
    private List<InstallmentLimitUnitCross> createMockInstallmentLimitUnitCrossList() {
        List<InstallmentLimitUnitCross> records = new ArrayList<>();
        
        // 使用Mock对象避免BaseEntity构造函数问题
        InstallmentLimitUnitCross record = mock(InstallmentLimitUnitCross.class);
        
        records.add(record);
        return records;
    }

    /**
     * 创建模拟分期订单管控单元关联DTO列表测试数据
     * @return List<InstallmentLimitUnitCrossDTO> 测试用的DTO列表
     */
    private List<InstallmentLimitUnitCrossDTO> createMockInstallmentLimitUnitCrossDTOList() {
        List<InstallmentLimitUnitCrossDTO> dtoList = new ArrayList<>();
        
        // 使用Mock对象避免BaseEntity构造函数问题
        InstallmentLimitUnitCrossDTO dto = mock(InstallmentLimitUnitCrossDTO.class);
        
        dtoList.add(dto);
        return dtoList;
    }
} 