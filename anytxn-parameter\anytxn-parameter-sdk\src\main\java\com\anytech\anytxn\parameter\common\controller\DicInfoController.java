package com.anytech.anytxn.parameter.common.controller;

import io.swagger.v3.oas.annotations.tags.Tag;
import io.swagger.v3.oas.annotations.Operation;
import com.anytech.anytxn.common.core.base.AnyTxnHttpResponse;
import com.anytech.anytxn.common.core.base.BizBaseController;
import com.anytech.anytxn.parameter.base.common.domain.dto.FeeTypeDicDTO;
import com.anytech.anytxn.parameter.base.common.domain.dto.TransactionDicInfoDTO;
import com.anytech.anytxn.parameter.base.common.domain.dto.TransactionVisaDicInfoDTO;
import com.anytech.anytxn.parameter.base.common.service.IDicInfoService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestHeader;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;


/**
 * @ClassName DicInfoController.java
 * @Description 规则字典表管理
 * <AUTHOR>
 * @Date 2019/8/20
 * Version 1.0
 */
@RestController
@Tag(name = "字典表管理api")
public class DicInfoController extends BizBaseController {

    @Autowired
    private IDicInfoService dicInfoService;

    /**
     *交易识别规则获取规则下拉结果
     *
     */
    @Operation(summary = "交易识别规则获取规则结果")
    @GetMapping(value="/param/dicts/transactions")
    public AnyTxnHttpResponse getTransactionIdentificationParamV2(@RequestParam String organizationNumber){
        List<TransactionDicInfoDTO> list=  dicInfoService.getTransactionIdentificationParamV2(organizationNumber);
        return AnyTxnHttpResponse.success(list);
    }

    /**
     *交易识别规则获取规则下拉结果
     *
     */
    @Operation(summary = "交易识别（VISA）规则获取规则结果")
    @GetMapping(value="/param/dicts/transactions/visa")
    public AnyTxnHttpResponse getTransactionVisaIdentificationParamV2(@RequestParam String organizationNumber){
        List<TransactionVisaDicInfoDTO> list=  dicInfoService.getTransactionVisaIdentificationParamV2(organizationNumber);
        return AnyTxnHttpResponse.success(list);
    }


    @Operation(summary = "获取费用类型字典数据")
    @GetMapping(value="/param/dicts/feeType")
    public AnyTxnHttpResponse getFeeTypeDics(@RequestHeader(name = "Messagelanguage") String messageLanguage){
        List<FeeTypeDicDTO> list=  dicInfoService.getFeeTypeDicInfoList(messageLanguage);
        return AnyTxnHttpResponse.success(list);
    }

    @Operation(summary = "获取费用子类字典数据")
    @GetMapping(value="/param/dicts/subFeeType")
    public AnyTxnHttpResponse getFeeTypeDics(@RequestParam("feeType") String feeType,@RequestHeader(name = "Messagelanguage") String messageLanguage){
        List<FeeTypeDicDTO> list=  dicInfoService.getSubFeeTypeDicInfoList(feeType,messageLanguage);
        return AnyTxnHttpResponse.success(list);
    }
}
