package com.anytech.anytxn.parameter.account.service;

import com.alibaba.fastjson.JSON;
import com.anytech.anytxn.common.core.annotation.db.DeleteParameterAnnotation;
import com.anytech.anytxn.common.core.annotation.db.InsertParameterAnnotation;
import com.anytech.anytxn.common.core.annotation.db.UpdateParameterAnnotation;
import com.anytech.anytxn.business.base.audit.AbstractParameterService;
import com.anytech.anytxn.common.core.base.ParameterCompare;
import com.anytech.anytxn.business.base.audit.ParmModificationRecord;
import com.anytech.anytxn.common.core.base.PageResultDTO;
import com.anytech.anytxn.common.core.utils.BeanMapping;
import com.anytech.anytxn.common.core.utils.OrgNumberUtils;
import com.anytech.anytxn.common.core.utils.TenantUtils;
import com.anytech.anytxn.common.sequence.utils.Number16IdGen;
import com.anytech.anytxn.parameter.base.account.domain.dto.AcctPmtBasicColReqDTO;
import com.anytech.anytxn.parameter.base.account.domain.dto.AcctPmtBasicColResDTO;
import com.anytech.anytxn.parameter.base.account.domain.dto.AcctPmtBasicInfoReqDTO;
import com.anytech.anytxn.parameter.base.account.domain.dto.AcctPmtBasicInfoResDTO;
import com.anytech.anytxn.parameter.base.account.service.IParmAcctPmtAllocBasicDefService;
import com.anytech.anytxn.parameter.base.common.constants.Constants;
import com.anytech.anytxn.parameter.base.common.enums.AnyTxnParameterRespCodeEnum;
import com.anytech.anytxn.parameter.base.common.exception.AnyTxnParameterException;
import com.anytech.anytxn.parameter.account.mapper.ParmAcctPmtAllocBasicColMapper;
import com.anytech.anytxn.parameter.account.mapper.ParmAcctPmtAllocBasicDefMapper;
import com.anytech.anytxn.parameter.base.account.domain.model.ParmAcctPmtAllocBasicCol;
import com.anytech.anytxn.parameter.base.account.domain.model.ParmAcctPmtAllocBasicDef;
import com.github.pagehelper.Page;
import com.github.pagehelper.PageHelper;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.HashSet;
import java.util.List;
import java.util.Set;

/**
 * <pre>
 * Description  账户间还款分配基本参数定义
 * Dept:		江融信
 * Author:		lichao
 * Version:		1.0
 * Create at:	2020-08-09 18:04
 *
 * 修改历史:
 * 日期    作者    版本  修改描述
 * ------------------------------------------------------------------
 **/
@Service(value = "parm_acct_pmt_alloc_basic_def_serviceImpl")
public class ParmAcctPmtAllocBasicDefServiceImpl extends AbstractParameterService implements IParmAcctPmtAllocBasicDefService {

    private final Logger logger = LoggerFactory.getLogger(ParmAcctPmtAllocBasicDefServiceImpl.class);

    @Autowired
    private ParmAcctPmtAllocBasicDefMapper parmAcctPmtAllocBasicDefMapper;
    @Autowired
    private ParmAcctPmtAllocBasicColMapper parmAcctPmtAllocBasicColMapper;
    @Autowired
    private Number16IdGen numberIdGenerator;

    /**
     * 添加还款定义
     *
     * @param req 还款定义+控制信息入参对象
     * @return 账户间还款信息
     * @throws AnyTxnParameterException
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    @InsertParameterAnnotation(tableName = "parm_acct_pmt_alloc_basic_def", tableDesc = "Accounts Repayment Allocation", isJoinTable = true)
    public ParameterCompare add(AcctPmtBasicInfoReqDTO req) {
        boolean isExists =
                parmAcctPmtAllocBasicDefMapper.isExists(req.getOrganizationNumber(), req.getTableId()) > 0;
        if (isExists) {
            logger.warn(
                    "账户还款间机构对应的参数表ID已存在, orgNumber={}, tableId={}",
                    req.getOrganizationNumber(),
                    req.getTableId());
            throw new AnyTxnParameterException(AnyTxnParameterRespCodeEnum.P_EXIST_ACCT_PMT_ALLOC_FAULT);
        }
        // 检查当前还款控制信息是否重复productNumber
        checkAcctPmt(req);
        req.setId(String.valueOf(numberIdGenerator.generateId(TenantUtils.getTenantId())));
        return ParameterCompare.getBuilder()
                .withMainParmId(req.getTableId())
                .withAfter(req).build(AcctPmtBasicInfoReqDTO.class);
    }

    /**
     * @Description save 还款分配控制信息 @Method saveParmPmtBasicColInfo @Params [req,
     * parmAcctPmtAllocBasicDef] @Return void @Date 2020/8/10
     */
    private void saveParmPmtBasicColInfo(
            AcctPmtBasicInfoReqDTO req, ParmAcctPmtAllocBasicDef parmAcctPmtAllocBasicDef) {
        req.getAcctPmtBasicColResDTOList().stream()
                .forEach(
                        acctPmtBasicColReqDTO -> {
                            ParmAcctPmtAllocBasicCol account =
                                    BeanMapping.copy(acctPmtBasicColReqDTO, ParmAcctPmtAllocBasicCol.class);
                            account.setTableId(parmAcctPmtAllocBasicDef.getTableId());
                            account.setOrganizationNumber(parmAcctPmtAllocBasicDef.getOrganizationNumber());
                            account.setCreateTime(LocalDateTime.now());
                            account.setUpdateTime(LocalDateTime.now());
                            account.setUpdateBy(Constants.DEFAULT_USER);
                            account.setVersionNumber(1L);
                            account.setStatus(Constants.ENABLED);
                            account.setId(numberIdGenerator.generateId(TenantUtils.getTenantId()));
                            parmAcctPmtAllocBasicColMapper.insert(account);
                        });
    }

    /**
     * @Description 修改还款定义参数信息 @Method modify @Params [req] @Return
     * jrx.anytxn.parameter.components.acct.dto.AcctPmtBasicInfoResDto @Date 2020/8/10
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    @UpdateParameterAnnotation(tableName = "parm_acct_pmt_alloc_basic_def", tableDesc = "Accounts Repayment Allocation", isJoinTable = true)
    public ParameterCompare modify(AcctPmtBasicInfoReqDTO req) {
        if (req.getId() == null) {
            throw new AnyTxnParameterException(AnyTxnParameterRespCodeEnum.P_PARAMETER_ID_EMPTY_FAULT);
        }
        // 检查当前还款控制信息手否重复productNumber
        checkAcctPmt(req);

        AcctPmtBasicInfoResDTO byId = findById(req.getId());
        if (byId == null) {
            logger.error("修改{}账户间还款分配, 通过主键id({})未找到数据", req.getTableId(), req.getId());
            throw new AnyTxnParameterException(AnyTxnParameterRespCodeEnum.P_PARAMETER_DATA_NOT_EXIST);
        }

        return ParameterCompare.getBuilder()
                .withMainParmId(req.getTableId())
                .withAfter(req)
                .withBefore(BeanMapping.copy(byId, AcctPmtBasicInfoReqDTO.class))
                .build(AcctPmtBasicInfoReqDTO.class);
    }

    /**
     * @Description 分页查询 @Method findPage @Params [pageNum, pageSize] @Return
     * jrx.anytxn.biz.common.dto.PageResultDTO<jrx.anytxn.parameter.components.acct.dto.AcctPmtBasicInfoResDto> @Date
     * 2020/8/10
     */
    @Override
    public PageResultDTO<AcctPmtBasicInfoResDTO> findPage(Integer pageNum, Integer pageSize, AcctPmtBasicInfoReqDTO acctPmtBasicInfoReqDto) {

        logger.info("分页查询账户间还款分配定义信息, pageNum={}, pageSize={}", pageNum, pageSize);
        if(null == acctPmtBasicInfoReqDto){
            acctPmtBasicInfoReqDto = new AcctPmtBasicInfoReqDTO();
        }
        acctPmtBasicInfoReqDto.setOrganizationNumber(StringUtils.isEmpty(acctPmtBasicInfoReqDto.getOrganizationNumber()) ? OrgNumberUtils.getOrg() : acctPmtBasicInfoReqDto.getOrganizationNumber());
        Page page = PageHelper.startPage(pageNum, pageSize);
        List<ParmAcctPmtAllocBasicDef> dataList = parmAcctPmtAllocBasicDefMapper.selectByCondition(acctPmtBasicInfoReqDto);
        List<AcctPmtBasicInfoResDTO> listData =
                BeanMapping.copyList(dataList, AcctPmtBasicInfoResDTO.class);
        return new PageResultDTO(pageNum, pageSize, page.getTotal(), page.getPages(), listData);
    }

    @Override
    public AcctPmtBasicInfoResDTO findById(String id) {
        if (id == null) {
            throw new AnyTxnParameterException(AnyTxnParameterRespCodeEnum.P_PARAMETER_EMPTY_FAULT);
        }
        AcctPmtBasicInfoResDTO res = new AcctPmtBasicInfoResDTO();
        ParmAcctPmtAllocBasicDef parmAcctPmtAllocBasicDef =
                parmAcctPmtAllocBasicDefMapper.selectByPrimaryKey(id);
        if (parmAcctPmtAllocBasicDef == null) {
            logger.error("获取账户间还款分配定义详情, 通过主键id({})未找到数据", id);
            throw new AnyTxnParameterException(AnyTxnParameterRespCodeEnum.P_ACCT_PMT_ALLOC_BY_ID_FAULT);
        }
        BeanMapping.copy(parmAcctPmtAllocBasicDef, res);
        List<AcctPmtBasicColResDTO> acctPmtBasicColResDTOList = new ArrayList<>();
        List<ParmAcctPmtAllocBasicCol> parmAcctPmtAllocBasicColList =
                parmAcctPmtAllocBasicColMapper.selectByOrgNumAndTableId(
                        parmAcctPmtAllocBasicDef.getOrganizationNumber(),
                        parmAcctPmtAllocBasicDef.getTableId());
        if (!parmAcctPmtAllocBasicColList.isEmpty()) {
            acctPmtBasicColResDTOList =
                    BeanMapping.copyList(parmAcctPmtAllocBasicColList, AcctPmtBasicColResDTO.class);
        }
        res.setAcctPmtBasicColResDTOList(acctPmtBasicColResDTOList);
        return res;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    @DeleteParameterAnnotation(tableName = "parm_acct_pmt_alloc_basic_def", tableDesc = "Accounts Repayment Allocation", isJoinTable = true)
    public ParameterCompare remove(String id) {
        if (id == null) {
            throw new AnyTxnParameterException(AnyTxnParameterRespCodeEnum.P_PARAMETER_EMPTY_FAULT);
        }
        ParmAcctPmtAllocBasicDef parmAcctPmtAllocBasicDef =
                parmAcctPmtAllocBasicDefMapper.selectByPrimaryKey(id);
        if (parmAcctPmtAllocBasicDef == null) {
            logger.error("删除账户间还款定义表信息, 通过主键id({})未找到数据", id);
            throw new AnyTxnParameterException(AnyTxnParameterRespCodeEnum.P_ACCT_PMT_ALLOC_BY_ID_FAULT);
        }

        return ParameterCompare.getBuilder()
                .withMainParmId(parmAcctPmtAllocBasicDef.getTableId())
                .withBefore(parmAcctPmtAllocBasicDef)
                .build(ParmAcctPmtAllocBasicDef.class);
    }

    /**
     * @param req 请求参数
     * @throws AnyTxnParameterException 异常
     */
    private void checkAcctPmt(AcctPmtBasicInfoReqDTO req) {
        List<AcctPmtBasicColReqDTO> acctPmtBasicColReqDTOList = req.getAcctPmtBasicColResDTOList();
        Set<String> accountSet = new HashSet<>();
        int size =
                CollectionUtils.isEmpty(acctPmtBasicColReqDTOList) ? 0 : acctPmtBasicColReqDTOList.size();
        for (AcctPmtBasicColReqDTO acctPmtBasicColReqDto : acctPmtBasicColReqDTOList) {
            if (!StringUtils.isEmpty(acctPmtBasicColReqDto.getAccountProductNumber())) {
                accountSet.add(acctPmtBasicColReqDto.getAccountProductNumber());
            } else {
                size = size - 1;
            }
        }
        if (size > accountSet.size()) {
            throw new AnyTxnParameterException(AnyTxnParameterRespCodeEnum.D_REPEAT_ACCT_PMT_ALLOC_FAULT);
        }
    }

    /**
     * @Description 根据机构号、tableId 查询账户间还款参数定义+控制详情 @Method queryAcctPmtBasicDefInfoByOrgTId @Params
     * [orgNumber, tableId] @Return jrx.anytxn.parameter.components.acct.dto.AcctPmtBasicInfoResDto @Date
     * 2020/8/10
     */
    @Override
    public AcctPmtBasicInfoResDTO queryAcctPmtBasicDefInfoByOrgTId(String orgNumber, String tableId) {
        AcctPmtBasicInfoResDTO res = new AcctPmtBasicInfoResDTO();
        ParmAcctPmtAllocBasicDef parmAcctPmtAllocBasicDef =
                parmAcctPmtAllocBasicDefMapper.selectByOrgNumAndTableId(orgNumber, tableId);
        if (parmAcctPmtAllocBasicDef != null
                && StringUtils.isNotBlank(parmAcctPmtAllocBasicDef.getOrganizationNumber())) {
            BeanMapping.copy(parmAcctPmtAllocBasicDef, res);
            List<AcctPmtBasicColResDTO> acctPmtBasicColResDTOList = new ArrayList<>();
            List<ParmAcctPmtAllocBasicCol> parmAcctPmtAllocBasicColList =
                    parmAcctPmtAllocBasicColMapper.selectByOrgNumAndTableId(orgNumber, tableId);
            if (!CollectionUtils.isEmpty(parmAcctPmtAllocBasicColList)) {
                acctPmtBasicColResDTOList =
                        BeanMapping.copyList(parmAcctPmtAllocBasicColList, AcctPmtBasicColResDTO.class);
            }
            res.setAcctPmtBasicColResDTOList(acctPmtBasicColResDTOList);
        }
        return res;
    }

    @Override
    @Transactional(rollbackFor = Exception.class, propagation = Propagation.REQUIRED)
    //@ShardingSphereTransactionType(TransactionType.XA)
    protected boolean updateDb(ParmModificationRecord parmModificationRecord) {
        AcctPmtBasicInfoReqDTO req = JSON.parseObject(parmModificationRecord.getParmBody(), AcctPmtBasicInfoReqDTO.class);
        // 拷贝修改的数据并更新
        ParmAcctPmtAllocBasicDef parmAcctPmtAllocBasicDef = BeanMapping.copy(req, ParmAcctPmtAllocBasicDef.class);
        parmAcctPmtAllocBasicDef.initUpdateDateTime();
        parmAcctPmtAllocBasicDef.setUpdateBy(parmModificationRecord.getApplicationBy());
        parmAcctPmtAllocBasicDefMapper.updateByPrimaryKey(parmAcctPmtAllocBasicDef);
        // 先根据tableId、机构号Orgnum删除现有的账户控制信息
        parmAcctPmtAllocBasicColMapper.deleteByOrgNoAndTableId(
                parmAcctPmtAllocBasicDef.getTableId(), parmAcctPmtAllocBasicDef.getOrganizationNumber());
        // 保存账户控制信息
        saveParmPmtBasicColInfo(req, parmAcctPmtAllocBasicDef);
        return true;
    }

    @Override
    @Transactional(rollbackFor = Exception.class, propagation = Propagation.REQUIRED)
    //@ShardingSphereTransactionType(TransactionType.XA)
    protected boolean insertDb(ParmModificationRecord parmModificationRecord) {
        AcctPmtBasicInfoReqDTO req = JSON.parseObject(parmModificationRecord.getParmBody(), AcctPmtBasicInfoReqDTO.class);
                // 构建封锁码定义
        ParmAcctPmtAllocBasicDef parmAcctPmtAllocBasicDef =
                BeanMapping.copy(req, ParmAcctPmtAllocBasicDef.class);
        parmAcctPmtAllocBasicDef.initCreateDateTime();
        parmAcctPmtAllocBasicDef.setVersionNumber(1L);
        parmAcctPmtAllocBasicDef.setUpdateBy(parmModificationRecord.getApplicationBy());

        parmAcctPmtAllocBasicDefMapper.insertSelective(parmAcctPmtAllocBasicDef);
        // 保存账户间还款控制信息表
        try {
            saveParmPmtBasicColInfo(req, parmAcctPmtAllocBasicDef);
        } catch (Exception e) {
            throw new AnyTxnParameterException(
                    AnyTxnParameterRespCodeEnum.S_BUILD_BLOCK_CODE_DEFINITIOIN_FAULT);
        }
        return true;
    }

    @Override
    @Transactional(rollbackFor = Exception.class, propagation = Propagation.REQUIRED)
    //@ShardingSphereTransactionType(TransactionType.XA)
    protected boolean deleteDb(ParmModificationRecord parmModificationRecord) {
        ParmAcctPmtAllocBasicDef parmAcctPmtAllocBasicDef = JSON.parseObject(parmModificationRecord.getParmBody(), ParmAcctPmtAllocBasicDef.class);
        try {
            // 先删除各个层级的标信息根据tableId和机构编号
            parmAcctPmtAllocBasicColMapper.deleteByOrgNoAndTableId(
                    parmAcctPmtAllocBasicDef.getTableId(), parmAcctPmtAllocBasicDef.getOrganizationNumber());
            // 删除主表定义表信息
            logger.warn(
                    "通过tableId：{} ,机构号:{}删除账户还款分配控制信息, {}",
                    parmAcctPmtAllocBasicDef.getTableId(),
                    parmAcctPmtAllocBasicDef.getOrganizationNumber());
            parmAcctPmtAllocBasicDefMapper.deleteByPrimaryKey(parmAcctPmtAllocBasicDef.getId());
        } catch (Exception e) {
            logger.error("通过id账户还款分配定义信息失败, {}", e.getMessage());
            throw new AnyTxnParameterException(AnyTxnParameterRespCodeEnum.D_DELETE_ACCT_PMT_ALLOC_FAULT);
        }
        return true;
    }
}
