package com.anytech.anytxn.parameter.common.service;

import com.alibaba.fastjson.JSON;
import com.anytech.anytxn.common.core.annotation.db.DeleteParameterAnnotation;
import com.anytech.anytxn.common.core.annotation.db.InsertParameterAnnotation;
import com.anytech.anytxn.common.core.annotation.db.UpdateParameterAnnotation;
import com.anytech.anytxn.business.base.audit.AbstractParameterService;
import com.anytech.anytxn.common.core.base.ParameterCompare;
import com.anytech.anytxn.business.base.audit.ParmModificationRecord;
import com.anytech.anytxn.common.core.base.PageResultDTO;
import com.anytech.anytxn.common.core.utils.BeanMapping;
import com.anytech.anytxn.common.sequence.utils.Number16IdGen;
import com.anytech.anytxn.common.core.utils.TenantUtils;
import com.anytech.anytxn.parameter.base.common.domain.dto.ParmCurrencyCodeDTO;
import com.anytech.anytxn.parameter.base.common.service.ICurrencyCodeService;
import com.anytech.anytxn.parameter.base.common.enums.AnyTxnParameterRespCodeEnum;
import com.anytech.anytxn.parameter.base.common.exception.AnyTxnParameterException;
import com.anytech.anytxn.parameter.common.mapper.broadcast.ParmCurrencyCodeMapper;
import com.anytech.anytxn.parameter.common.mapper.broadcast.ParmCurrencyCodeSelfMapper;
import com.anytech.anytxn.parameter.base.common.domain.model.unicast.ParmCurrencyCode;
import com.github.pagehelper.Page;
import com.github.pagehelper.PageHelper;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.ObjectUtils;
//import org.apache.shardingsphere.transaction.annotation.ShardingSphereTransactionType;
//import org.apache.shardingsphere.transaction.core.TransactionType;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;
import java.util.Objects;

/**
 * 国家码操作
 * <AUTHOR> 2021、10、20
 */
@Service("parm_currency_code_serviceImpl")
@Slf4j
public class CurrencyCodeServiceImpl extends AbstractParameterService implements ICurrencyCodeService {

    @Autowired
    private ParmCurrencyCodeSelfMapper parmCurrencyCodeSelfMapper;

    @Autowired
    private ParmCurrencyCodeMapper parmCurrencyCodeMapper;

    @Autowired
    private Number16IdGen numberIdGenerator;

    @Override
    public PageResultDTO<ParmCurrencyCodeDTO> selectCurrencyCodeBasic(Integer page, Integer rows) {
        log.debug("分页查询国家码信息, page={}, rows={}",page, rows);
        Page<ParmCurrencyCode> page1= PageHelper.startPage(page, rows);
        List<ParmCurrencyCode> currencyCodes=parmCurrencyCodeSelfMapper.selectPageList();
        if(CollectionUtils.isEmpty(currencyCodes)){
            log.error("未查询到信息");
            return null;
        }
        List<ParmCurrencyCodeDTO> result= BeanMapping.copyList(currencyCodes, ParmCurrencyCodeDTO.class);
        return new PageResultDTO<>(page, rows, page1.getTotal(), page1.getPages(), result);
    }

    @Override
    public ParmCurrencyCodeDTO selectById(String id) {
        ParmCurrencyCode parmCurrencyCode=parmCurrencyCodeSelfMapper.selectByPrimaryKey(id);
        ParmCurrencyCodeDTO parmCountryCodeDTO=BeanMapping.copy(parmCurrencyCode,ParmCurrencyCodeDTO.class);
        return parmCountryCodeDTO;
    }

    @Override
    @InsertParameterAnnotation(tableName = "parm_currency_code", tableDesc = "Currency Code", isJoinTable = false)
    public ParameterCompare add(ParmCurrencyCodeDTO parmCurrencyCodeDTO) {
        ParmCurrencyCode currencyCode=parmCurrencyCodeSelfMapper.selectAlphabticon(parmCurrencyCodeDTO.getAlphabeticCode());
        if(currencyCode != null){
            log.warn("该条信息已存在,alphabeticCode={}",parmCurrencyCodeDTO.getAlphabeticCode());
            throw new AnyTxnParameterException(AnyTxnParameterRespCodeEnum.D_DATA_ALREADY_EXISTS_FAULT);
        }
        parmCurrencyCodeDTO.setId(numberIdGenerator.generateId(TenantUtils.getTenantId())+"");
        parmCurrencyCodeDTO.setOrganizationNumber("0000");
        return ParameterCompare.getBuilder()
                .withAfter(parmCurrencyCodeDTO)
                .withMainParmId(parmCurrencyCodeDTO.getId()).build(ParmCurrencyCodeDTO.class);
    }



    @Override
    @DeleteParameterAnnotation(tableName = "parm_currency_code", tableDesc = "Currency Code", isJoinTable = false)
    public ParameterCompare delete(String id) {
        if (id == null) {
            throw new AnyTxnParameterException(AnyTxnParameterRespCodeEnum.P_PARAMETER_ID_EMPTY_FAULT);
        }
        ParmCurrencyCode parmCurrencyCode=parmCurrencyCodeSelfMapper.selectByPrimaryKey(id);
        if(ObjectUtils.isEmpty(parmCurrencyCode)){
            log.error("数据不存在");
            return null;
        }
        parmCurrencyCode.setOrganizationNumber("0000");
        return ParameterCompare
                .getBuilder()
                .withBefore(parmCurrencyCode)
                .build(ParmCurrencyCode.class);
    }

    @Override
    @UpdateParameterAnnotation(tableName = "parm_currency_code", tableDesc = "Currency Code", isJoinTable = false)
    public ParameterCompare update(ParmCurrencyCodeDTO parmCurrencyCodeDTO) {
        if(ObjectUtils.isEmpty(parmCurrencyCodeDTO)){
            log.warn("参数为空");
            throw new AnyTxnParameterException(AnyTxnParameterRespCodeEnum.P_PARAMETER_EMPTY_FAULT);
        }
        ParmCurrencyCode currencyCode=parmCurrencyCodeSelfMapper.selectByPrimaryKey(parmCurrencyCodeDTO.getId());
        if(Objects.isNull(currencyCode)){
            throw new AnyTxnParameterException(AnyTxnParameterRespCodeEnum.D_DATA_NOT_EXISTS_FAULT);
        }
        currencyCode.setOrganizationNumber("0000");
        return ParameterCompare.getBuilder()
                .withAfter(parmCurrencyCodeDTO)
                .withBefore(currencyCode)
                .build(ParmCurrencyCode.class);
    }

    @Override
    @Transactional(rollbackFor = Exception.class,propagation = Propagation.REQUIRED)
    //@ShardingSphereTransactionType(TransactionType.XA)
    protected boolean updateDb(ParmModificationRecord parmModificationRecord) throws Exception {
        ParmCurrencyCodeDTO parmCurrencyCodeDTO= JSON.parseObject(parmModificationRecord.getParmBody(), ParmCurrencyCodeDTO.class);
        ParmCurrencyCode currencyCode=new ParmCurrencyCode();
        currencyCode.setCurrencyCode(parmCurrencyCodeDTO.getCurrencyCode());
        currencyCode.setDescription(parmCurrencyCodeDTO.getDescription());
        currencyCode.setAlphabeticCode(parmCurrencyCodeDTO.getAlphabeticCode());
        currencyCode.setDecimalPlace(parmCurrencyCodeDTO.getDecimalPlace());
        currencyCode.setDecimalSeparator(parmCurrencyCodeDTO.getDecimalSeparator());
        currencyCode.setAlphabeticCodePosition(parmCurrencyCodeDTO.getAlphabeticCodePosition());
        currencyCode.setSignPosition(parmCurrencyCodeDTO.getSignPosition());
        currencyCode.setDisplayPriorityCode(parmCurrencyCodeDTO.getDisplayPriorityCode());
        currencyCode.setId(parmCurrencyCodeDTO.getId());
        currencyCode.setDciSettlementIndicator(parmCurrencyCodeDTO.getDciSettlementIndicator());
        currencyCode.initUpdateDateTime();
        int update = parmCurrencyCodeMapper.updateByPrimaryKey(currencyCode);
        return update>0;
    }

    @Override
    @Transactional(rollbackFor = Exception.class,propagation = Propagation.REQUIRED)
    //@ShardingSphereTransactionType(TransactionType.XA)
    protected boolean insertDb(ParmModificationRecord parmModificationRecord) throws Exception {
        ParmCurrencyCodeDTO parmCurrencyCodeDTO= JSON.parseObject(parmModificationRecord.getParmBody(), ParmCurrencyCodeDTO.class);
        ParmCurrencyCodeDTO currencyCodeDTO=new ParmCurrencyCodeDTO();
        currencyCodeDTO.setId(numberIdGenerator.generateId(TenantUtils.getTenantId())+"");
        currencyCodeDTO.setCurrencyCode(parmCurrencyCodeDTO.getCurrencyCode());
        currencyCodeDTO.setDescription(parmCurrencyCodeDTO.getDescription());
        currencyCodeDTO.setAlphabeticCode(parmCurrencyCodeDTO.getAlphabeticCode());
        currencyCodeDTO.setDecimalPlace(parmCurrencyCodeDTO.getDecimalPlace());
        currencyCodeDTO.setDecimalSeparator(parmCurrencyCodeDTO.getDecimalSeparator());
        currencyCodeDTO.setAlphabeticCodePosition(parmCurrencyCodeDTO.getAlphabeticCodePosition());
        currencyCodeDTO.setSignPosition(parmCurrencyCodeDTO.getSignPosition());
        currencyCodeDTO.setDisplayPriorityCode(parmCurrencyCodeDTO.getDisplayPriorityCode());
        currencyCodeDTO.initCreateDateTime();
        currencyCodeDTO.setVersionNumber(1L);
        currencyCodeDTO.initUpdateDateTime();
        currencyCodeDTO.setOrganizationNumber("0000");
        currencyCodeDTO.setUpdateBy(parmModificationRecord.getApplicationBy());
        currencyCodeDTO.setDciSettlementIndicator(parmCurrencyCodeDTO.getDciSettlementIndicator());
        int insert = parmCurrencyCodeSelfMapper.insert(currencyCodeDTO);
        return insert>0;
    }

    @Override
    @Transactional(rollbackFor = Exception.class,propagation = Propagation.REQUIRED)
    //@ShardingSphereTransactionType(TransactionType.XA)
    protected boolean deleteDb(ParmModificationRecord parmModificationRecord) throws Exception {
        ParmCurrencyCodeDTO parmCurrencyCodeDTO= JSON.parseObject(parmModificationRecord.getParmBody(), ParmCurrencyCodeDTO.class);
        int delete = parmCurrencyCodeSelfMapper.deleteByPrimaryKey(parmCurrencyCodeDTO.getId());
        return delete>0;
    }
}
