package com.anytech.anytxn.parameter.common.controller.system;

import com.anytech.anytxn.common.core.base.PageResultDTO;
import com.anytech.anytxn.common.core.enums.ParamFileTypeEnum;
import com.anytech.anytxn.business.base.common.domain.dto.ParamExportDTO;
import com.anytech.anytxn.business.base.common.domain.dto.ParamExportPageDTO;
import com.anytech.anytxn.business.base.common.domain.dto.ParamImportDTO;
import com.anytech.anytxn.business.base.common.domain.dto.ParamImportResultDTO;
import com.anytech.anytxn.common.core.utils.DateHelper;
import com.anytech.anytxn.common.core.base.AnyTxnHttpResponse;
import com.anytech.anytxn.common.core.base.BizBaseController;
import com.anytech.anytxn.parameter.base.common.enums.AnyTxnParameterRespCodeEnum;
import com.anytech.anytxn.parameter.base.common.exception.AnyTxnParameterException;
import com.anytech.anytxn.parameter.base.common.service.system.IParameterPoiService;
import org.apache.commons.codec.Charsets;
import org.apache.commons.codec.binary.Hex;
import org.apache.commons.lang3.StringUtils;
import org.springframework.http.*;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import jakarta.annotation.Resource;
import java.io.IOException;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;
import java.util.Optional;

/**
 * @Author: sukang
 * @Date: 2021/8/27 10:47
 */
@RestController
@CrossOrigin
public class ParameterPoiController extends BizBaseController {

    @Resource
    private IParameterPoiService parameterPoiService;


    /**
     *
     * @param paramExportDTO {@link ParamExportDTO}
     * @return 分页数据
     */
    @PostMapping("param/export/pages")
    public AnyTxnHttpResponse<PageResultDTO<ParamExportPageDTO>> exportPages(@RequestBody ParamExportDTO paramExportDTO){
        return AnyTxnHttpResponse.success(parameterPoiService.exportPages(paramExportDTO));
    }





    @PostMapping("/param/prepareData")
    public AnyTxnHttpResponse<List<ParamImportDTO>> prepareData(@RequestBody ParamExportDTO paramExportDTO) {
        return AnyTxnHttpResponse.success(parameterPoiService.prepareData(paramExportDTO));
    }


    @GetMapping("/param/export/{transactionId}")
    public ResponseEntity<byte[]> exportParamFile(@PathVariable("transactionId") String transactionId)
            throws Exception {

        byte[] bytes = parameterPoiService.getParamByteData(transactionId);

        HttpHeaders headers=new HttpHeaders();
        //设置MIME类型
        headers.setContentType(MediaType.APPLICATION_OCTET_STREAM);
        headers.setContentDisposition(ContentDisposition.builder("attachment")
                .filename(DateHelper.formatLodalDate(LocalDateTime.now(),"yyyyMMdd-HHmmss-SSS") + ".zip", Charsets.UTF_8).build());

        return new ResponseEntity<>(bytes, headers, HttpStatus.OK);
    }


    @PostMapping("/param/import")
    public AnyTxnHttpResponse<List<ParamImportResultDTO>> importParamFile(
            @RequestParam("files") MultipartFile[] files) throws IOException {
        ArrayList<ParamImportResultDTO> list = new ArrayList<>();

        for (MultipartFile file : files) {
            byte[] fileBytes = file.getBytes();
            ParamImportResultDTO build = ParamImportResultDTO.ParamImportResultDtoBuilder.getBuilder()
                    .withSuccessNum("0")
                    .withFileName(file.getOriginalFilename())
                    .withParamsCode(Optional.ofNullable(file.getOriginalFilename()).orElse("").split("\\.")[0])
                    .withBytes(fileBytes)
                    .withFileType(getFileType(fileBytes,file.getOriginalFilename()))
                    .build();
            list.add(build);
        }

        return  AnyTxnHttpResponse.success(parameterPoiService.importParamFile(list));
    }



    private String getFileType(byte[] bytes,String fileName){
        if (fileName.endsWith(".txt")){
            return ParamFileTypeEnum.TXT.getType();
        }
        if (fileName.endsWith(".csv")){
            return ParamFileTypeEnum.CSV.getType();
        }
        byte[] fileHeader = new byte[4];
        System.arraycopy(bytes,0,fileHeader,0,4);

        String hexString = Hex.encodeHexString(fileHeader, false);

        if (StringUtils.equalsAny(hexString,"504B0304","D0CF11E0")){
            return ParamFileTypeEnum.EXCEL.getType();
        }

        throw new AnyTxnParameterException(AnyTxnParameterRespCodeEnum.PARAMETER_FILE_TYPE);
    }

}
