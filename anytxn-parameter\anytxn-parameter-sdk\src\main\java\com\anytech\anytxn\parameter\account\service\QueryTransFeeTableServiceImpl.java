package com.anytech.anytxn.parameter.account.service;


import com.anytech.anytxn.parameter.base.account.domain.dto.ParmQueryFeeReqDTO;
import com.anytech.anytxn.parameter.base.account.domain.dto.ParmQueryFeeResDTO;
import com.anytech.anytxn.parameter.base.account.domain.dto.ParmQueryFeeSearchDTO;
import com.anytech.anytxn.parameter.base.account.service.IQueryTransFeeService;
import com.anytech.anytxn.parameter.base.common.enums.AnyTxnParameterRespCodeEnum;
import com.anytech.anytxn.parameter.base.common.enums.ParameterRepDetailEnum;
import com.anytech.anytxn.parameter.base.common.exception.AnyTxnParameterException;
import com.anytech.anytxn.parameter.account.mapper.ParmQueryFeeMapper;
import com.anytech.anytxn.parameter.base.account.domain.model.ParmQueryFee;
import com.anytech.anytxn.business.base.audit.AbstractParameterService;
import com.anytech.anytxn.common.core.base.ParameterCompare;
import com.anytech.anytxn.business.base.audit.ParmModificationRecord;
import com.anytech.anytxn.common.core.base.PageResultDTO;
import com.anytech.anytxn.common.core.utils.BeanMapping;
//import com.anytech.anytxn.common.sequence.generator.INumberIdGenerator;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * @Author: lt
 * @Date: 2025-02-18
 * @Package: com.anytech.anytxn.parameter.service.impl.components.acct
 */
@Service(value = "parm_atm_query_fee_serviceImpl")
public class QueryTransFeeTableServiceImpl extends AbstractParameterService implements IQueryTransFeeService {

    private final Logger log = LoggerFactory.getLogger(QueryTransFeeTableServiceImpl.class);

//
//    @Autowired
//    private INumberIdGenerator numberIdGenerator;
    @Autowired
    private ParmQueryFeeMapper parmAtmQueryFeeMapper;

    @Override
    protected boolean updateDb(ParmModificationRecord parmModificationRecord) throws Exception {
        return false;
    }

    @Override
    protected boolean insertDb(ParmModificationRecord parmModificationRecord) throws Exception {
        return false;
    }

    @Override
    protected boolean deleteDb(ParmModificationRecord parmModificationRecord) throws Exception {
        return false;
    }

    @Override
    public PageResultDTO<ParmQueryFeeResDTO> findAll(Integer pageNum, Integer pageSize, ParmQueryFeeSearchDTO parmAtmQueryFeeSearchDTO) {

        return null;
    }

    @Override
    public ParameterCompare addParmFee(ParmQueryFeeReqDTO cashFeeTableReq) {
        return null;
    }

    @Override
    public ParameterCompare modifyParmFee(ParmQueryFeeReqDTO cashFeeTableReq) {
        return null;
    }

    @Override
    public ParameterCompare removeParmFee(String id) {
        return null;
    }

    @Override
    public ParmQueryFeeResDTO findById(String id) {
        return null;
    }


    /**
     * 根据机构号,表Id查询查询手续费参数
     *
     * @param orgNum 机构号
     * @param tableId 表Id
     * @return
     */
    @Override
    public ParmQueryFeeResDTO findByOrgAndTableId(String orgNum, String tableId) {
        if (org.apache.commons.lang3.StringUtils.isAnyEmpty(orgNum, tableId)) {
            throw new AnyTxnParameterException(AnyTxnParameterRespCodeEnum.P_PARAMETER_EMPTY_FAULT);
        }
        ParmQueryFee parmAtmQueryFee = parmAtmQueryFeeMapper.selectByOrgAndTid(orgNum, tableId);
        if (parmAtmQueryFee == null) {
            log.error("取现手续费参数数据不存在");
            throw new AnyTxnParameterException(AnyTxnParameterRespCodeEnum.D_GET_IS_NULL_FAULT, ParameterRepDetailEnum.CASH_FREE);
        }
        return BeanMapping.copy(parmAtmQueryFee, ParmQueryFeeResDTO.class);
    }
}
