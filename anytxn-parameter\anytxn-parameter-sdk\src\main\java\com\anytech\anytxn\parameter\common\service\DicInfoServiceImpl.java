package com.anytech.anytxn.parameter.common.service;

import com.anytech.anytxn.parameter.base.common.domain.dto.system.ParmSysDictDTO;
import com.anytech.anytxn.parameter.base.common.domain.dto.ParmTransactionCodeResDTO;
import com.anytech.anytxn.parameter.base.common.service.system.IParmSysDictService;
import com.anytech.anytxn.parameter.base.common.service.ITransactionCodeService;
import com.anytech.anytxn.parameter.base.common.constants.ParameterConstant;
import com.anytech.anytxn.parameter.base.common.constants.RuleCodeConstant;
import com.anytech.anytxn.parameter.base.common.enums.AnyTxnParameterRespCodeEnum;
import com.anytech.anytxn.parameter.base.common.enums.FeeTypeCodeEnum;
import com.anytech.anytxn.parameter.base.common.enums.SubFeeTypeCodeEnum;
import com.anytech.anytxn.parameter.base.common.exception.AnyTxnParameterException;
import com.anytech.anytxn.parameter.base.common.domain.dto.FeeTypeDicDTO;
import com.anytech.anytxn.parameter.base.common.domain.dto.TransactionDicInfoDTO;
import com.anytech.anytxn.parameter.base.common.domain.dto.TransactionVisaDicInfoDTO;
import com.anytech.anytxn.parameter.base.common.domain.dto.TransactionDicInfoFunction;
import com.anytech.anytxn.parameter.base.common.domain.dto.TransactionVisaDicInfoFunction;
import com.anytech.anytxn.parameter.base.common.service.IDicInfoService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;

/**
 * @Auther: hanrb
 * @Date: 2018/11/6 14:06
 * @Description:
 */
@Service
public class DicInfoServiceImpl implements IDicInfoService {

    private static final Logger log = LoggerFactory.getLogger(DicInfoServiceImpl.class);

    @Autowired
    private ITransactionCodeService iTransactionCodeService;
//    @Autowired
//    private IParmSysOrgDictService parmSysOrgDictService;
    //暂时都用系统字典的
    @Autowired
    private IParmSysDictService parmSysDictService;

    @Override
    public List<TransactionDicInfoDTO> getTransactionIdentificationParam() {
        List<TransactionDicInfoDTO> list;
        try {
            list = TransactionDicInfoFunction.builtTransactionDicInfoList();
        } catch (Exception e) {
            log.error("getTransactionIdentificationParam,e:{}",e);
            throw new AnyTxnParameterException(AnyTxnParameterRespCodeEnum.D_QUERY_DIC_INFO_FAULT);
        }
        return list;
    }

    /**
     * 获取交易识别（VISA）的授权交易类型大类代码合授权交易类型细分代码
     *
     * @return List<TransactionVisaDicInfoDTO>
     */
    @Override
    public List<TransactionVisaDicInfoDTO> getTransactionVisaIdentificationParam() {
        List<TransactionVisaDicInfoDTO> list;
        try {
            list = TransactionVisaDicInfoFunction.builtTransactionVisaDicInfoList();

        } catch (Exception e) {
            throw new AnyTxnParameterException(AnyTxnParameterRespCodeEnum.D_QUERY_DIC_INFO_VISA_FAULT);
        }
        return list;
    }

    /**
     * 获取交易识别（银联）的授权交易类型大类代码合授权交易类型细分代码,以及入账交易码
     *
     * @return List<TransactionVisaDicInfoDTO>
     */
    @Override
    public List<TransactionDicInfoDTO> getTransactionIdentificationParamV2(String organizationNumber) {
        List<TransactionDicInfoDTO> list = new ArrayList<>(16);
        try {
//            list = TransactionDicInfoFunction.builtTransactionDicInfoList();
            List<ParmSysDictDTO> dictList = parmSysDictService.selectListByTypeId("AUTH_TRANSACTION_TYPE");
            if (null != dictList && !dictList.isEmpty()) {
                List<ParmSysDictDTO> dictList1 = dictList.get(0).getChildren();
                for (ParmSysDictDTO dictResDto1 : dictList1) {
                    TransactionDicInfoDTO dto1 = new TransactionDicInfoDTO();
                    dto1.setCode(dictResDto1.getCodeId());
                    dto1.setName(dictResDto1.getCodeName());
                    dto1.setLevel(1);
                    List<ParmSysDictDTO> dictList2 = dictResDto1.getChildren();
                    if (null != dictList2 && !dictList2.isEmpty()) {
                        List<TransactionDicInfoDTO> list2 = new ArrayList<>(16);
                        for (ParmSysDictDTO dictResDto2 : dictList2) {
                            TransactionDicInfoDTO dto2 = new TransactionDicInfoDTO();
                            dto2.setCode(dictResDto2.getCodeId());
                            dto2.setName(dictResDto2.getCodeName());
                            dto2.setLevel(2);
                            list2.add(dto2);
                        }
                        dto1.setDetailList(list2);
                    }
                    list.add(dto1);
                }
            }
            Map<String, List<ParmTransactionCodeResDTO>> stringListMap = iTransactionCodeService.selectDataBydebitAttrAndtransactionAttr(organizationNumber);
            //数据准备
            dataPrePare1(list, stringListMap);
        } catch (Exception e) {
            log.error("exceprion:{}",e);
            throw new AnyTxnParameterException(AnyTxnParameterRespCodeEnum.D_QUERY_DIC_INFO_DIC_FAULT);
        }
        return list;
    }


    /**
     * 获取交易识别（VISA）的授权交易类型大类代码合授权交易类型细分代码,以及入账交易码
     * @return List<TransactionVisaDicInfoDTO>
     */
    @Override
    public List<TransactionVisaDicInfoDTO> getTransactionVisaIdentificationParamV2(String organizationNumber) {
        List<TransactionVisaDicInfoDTO> list = new ArrayList<>(16);
        try {
//            list = TransactionVisaDicInfoFunction.builtTransactionVisaDicInfoList();
            List<ParmSysDictDTO> dictList = parmSysDictService.selectListByTypeId("AUTH_TRANSACTION_TYPE");
            if (null != dictList && !dictList.isEmpty()) {
                List<ParmSysDictDTO> dictList1 = dictList.get(0).getChildren();
                for (ParmSysDictDTO dictResDto1 : dictList1) {
                    TransactionVisaDicInfoDTO dto1 = new TransactionVisaDicInfoDTO();
                    dto1.setCode(dictResDto1.getCodeId());
                    dto1.setName(dictResDto1.getCodeName());
                    dto1.setLevel(1);
                    List<ParmSysDictDTO> dictList2 = dictResDto1.getChildren();
                    if (null != dictList2 && !dictList2.isEmpty()) {
                        List<TransactionVisaDicInfoDTO> list2 = new ArrayList<>(16);
                        for (ParmSysDictDTO dictResDto2 : dictList2) {
                            TransactionVisaDicInfoDTO dto2 = new TransactionVisaDicInfoDTO();
                            dto2.setCode(dictResDto2.getCodeId());
                            dto2.setName(dictResDto2.getCodeName());
                            dto2.setLevel(2);
                            list2.add(dto2);
                        }
                        dto1.setDetailList(list2);
                    }
                    list.add(dto1);
                }
            }
            //数据准备
            Map<String, List<ParmTransactionCodeResDTO>> stringListMap = iTransactionCodeService.selectDataBydebitAttrAndtransactionAttr(organizationNumber);
            dataPrePare2(list, stringListMap);
        } catch (Exception e) {
            throw new AnyTxnParameterException(AnyTxnParameterRespCodeEnum.D_QUERY_DIC_INFO_VISA_TWO_FAULT);
        }
        return list;
    }


    /**
     * 获取费用类型
     * @param messagelanguage 文本语言
     * @return List<FeeTypeDicDto>
     */
    public List<FeeTypeDicDTO> getFeeTypeDicInfoList(String messagelanguage) {
        FeeTypeCodeEnum[] feeTypeCodeEnums = FeeTypeCodeEnum.values();
        List<FeeTypeDicDTO> feeTypeDicList = new ArrayList<>();
        for (int i = 0; i < feeTypeCodeEnums.length; i++) {
            FeeTypeCodeEnum feeTypeCodeEnum = feeTypeCodeEnums[i];
            FeeTypeDicDTO feeTypeDicDto = new FeeTypeDicDTO();
            feeTypeDicDto.setCode(feeTypeCodeEnum.getCode());
            if("EN".equals(messagelanguage)){
                feeTypeDicDto.setName(feeTypeCodeEnum.getEnglishDesc());
            }else if("CN".equals(messagelanguage)){
                feeTypeDicDto.setName(feeTypeCodeEnum.getChineseDesc());
            }
            feeTypeDicList.add(feeTypeDicDto);
        }
        return feeTypeDicList;
    }

    /**
     * 获取费用子类
     * @param feeType 费用类型
     * @param messagelanguage 文本语言
     * @return List<FeeTypeDicDto>
     */
    @Override
    public List<FeeTypeDicDTO> getSubFeeTypeDicInfoList(String feeType, String messagelanguage) {
        FeeTypeCodeEnum[] feeTypeCodeEnums = FeeTypeCodeEnum.values();
        SubFeeTypeCodeEnum[] subFeeTypeCodeEnums = SubFeeTypeCodeEnum.values();
        List<FeeTypeDicDTO> subFeeTypeDicList = new ArrayList<>();
        for (int i = 0; i < feeTypeCodeEnums.length; i++) {
            FeeTypeCodeEnum feeTypeCodeEnum = feeTypeCodeEnums[i];
            if(feeTypeCodeEnum.getCode().equals(feeType)){
                for (int j = 0; j < subFeeTypeCodeEnums.length; j++) {
                    SubFeeTypeCodeEnum subFeeTypeCodeEnum = subFeeTypeCodeEnums[j];
                    if(feeTypeCodeEnum.getCode().equals(subFeeTypeCodeEnum.getCode().substring(0,4))){
                        FeeTypeDicDTO subFeeTypeDicDTO = new FeeTypeDicDTO();
                        subFeeTypeDicDTO.setCode(subFeeTypeCodeEnum.getCode());
                        if("EN".equals(messagelanguage)){
                            subFeeTypeDicDTO.setName(subFeeTypeCodeEnum.getEnglishDesc());
                        }else if("CN".equals(messagelanguage)){
                            subFeeTypeDicDTO.setName(subFeeTypeCodeEnum.getChineseDesc());
                        }
                        subFeeTypeDicList.add(subFeeTypeDicDTO);
                    }
                }
            }
        }
        return subFeeTypeDicList;
    }

    /**
     * visa下拉框准备
     *
     * @param list
     * @throws Exception
     */
    private void dataPrePare2(List<TransactionVisaDicInfoDTO> list, Map<String, List<ParmTransactionCodeResDTO>> map){
        List<ParmTransactionCodeResDTO> rd1 = map.get(ParameterConstant.RD1);
        List<ParmTransactionCodeResDTO> rc1 = map.get(ParameterConstant.RC1);
        List<ParmTransactionCodeResDTO> cd2 = map.get(ParameterConstant.CD2);
        List<ParmTransactionCodeResDTO> cc2 = map.get(ParameterConstant.CC2);
        List<ParmTransactionCodeResDTO> id3 = map.get(ParameterConstant.ID3);
        List<ParmTransactionCodeResDTO> ic3 = map.get(ParameterConstant.IC3);
        List<ParmTransactionCodeResDTO> sd4 = map.get(ParameterConstant.SD4);
        List<ParmTransactionCodeResDTO> sc4 = map.get(ParameterConstant.SC4);
        List<ParmTransactionCodeResDTO> pd7 = map.get(ParameterConstant.PD7);
        List<ParmTransactionCodeResDTO> pc7 = map.get(ParameterConstant.PC7);
        for (TransactionVisaDicInfoDTO dto : list) {
            String code = dto.getCode();
            //交易大类为R
            if (RuleCodeConstant.TRANSACTION_TYPE_R.equals(code)) {
                if (rd1 != null) {
                    dto.setCurrTimeAccountCodeList(rd1);
                }
                if (rc1 != null) {
                    dto.setCurrTimeAccountOtherCodeList(rc1);
                }
            } else if (RuleCodeConstant.TRANSACTION_TYPE_C.equals(code)) {
                if (cd2 != null) {
                    dto.setCurrTimeAccountCodeList(cd2);
                }
                if (cc2 != null) {
                    dto.setCurrTimeAccountOtherCodeList(cc2);
                }

            } else if (RuleCodeConstant.TRANSACTION_TYPE_I.equals(code)) {
                if (id3 != null) {
                    dto.setCurrTimeAccountCodeList(id3);
                }
                if (ic3 != null) {
                    dto.setCurrTimeAccountOtherCodeList(ic3);
                }
            } else if (RuleCodeConstant.TRANSACTION_TYPE_S.equals(code)) {
                if (sd4 != null) {
                    dto.setCurrTimeAccountCodeList(sd4);
                }
                if (sc4 != null) {
                    dto.setCurrTimeAccountOtherCodeList(sc4);
                }
            } else if (RuleCodeConstant.TRANSACTION_TYPE_P.equals(code)) {
                if (pd7 != null) {
                    dto.setCurrTimeAccountCodeList(pc7);
                }
                if (pc7 != null) {
                    dto.setCurrTimeAccountOtherCodeList(pd7);
                }
            }
        }
    }

    /**
     * 银联下拉框参数准备
     *
     * @param list

     * @throws Exception
     */
    private void dataPrePare1(List<TransactionDicInfoDTO> list, Map<String, List<ParmTransactionCodeResDTO>> map){
        List<ParmTransactionCodeResDTO> rd1 = map.get(ParameterConstant.RD1);
        List<ParmTransactionCodeResDTO> rc1 = map.get(ParameterConstant.RC1);
        List<ParmTransactionCodeResDTO> cd2 = map.get(ParameterConstant.CD2);
        List<ParmTransactionCodeResDTO> cc2 = map.get(ParameterConstant.CC2);
        List<ParmTransactionCodeResDTO> id3 = map.get(ParameterConstant.ID3);
        List<ParmTransactionCodeResDTO> ic3 = map.get(ParameterConstant.IC3);
        List<ParmTransactionCodeResDTO> sd4 = map.get(ParameterConstant.SD4);
        List<ParmTransactionCodeResDTO> sc4 = map.get(ParameterConstant.SC4);
        List<ParmTransactionCodeResDTO> pd7 = map.get(ParameterConstant.PD7);
        List<ParmTransactionCodeResDTO> pc7 = map.get(ParameterConstant.PC7);
        List<ParmTransactionCodeResDTO> lde = map.get(ParameterConstant.LDE);
        List<ParmTransactionCodeResDTO> lce = map.get(ParameterConstant.LCE);
        for (TransactionDicInfoDTO dto : list) {
            String code = dto.getCode();
            //交易大类为R
            if (RuleCodeConstant.TRANSACTION_TYPE_R.equals(code)) {
                if (rd1 != null) {
                    dto.setCurrTimeAccountCodeList(rd1);
                }
                if (rc1 != null) {
                    dto.setCurrTimeAccountOtherCodeList(rc1);
                }
            } else if ("C".equals(code)||"T".equals(code)) {
                if (cd2 != null) {
                    dto.setCurrTimeAccountCodeList(cd2);
                }
                if (cc2 != null) {
                    dto.setCurrTimeAccountOtherCodeList(cc2);
                }

            } else if (RuleCodeConstant.TRANSACTION_TYPE_I.equals(code)) {
                /*if (id3 != null) {
                    dto.setCurrTimeAccountCodeList(id3);
                }
                if (ic3 != null) {
                    dto.setCurrTimeAccountOtherCodeList(ic3);
                }*/
                if (lde != null) {
                    dto.setCurrTimeAccountCodeList(lde);
                }
                if (lce != null) {
                    dto.setCurrTimeAccountOtherCodeList(lce);
                }
            } else if ("S".equals(code)) {
                if (sd4 != null) {
                    dto.setCurrTimeAccountCodeList(sd4);
                }
                if (sc4 != null) {
                    dto.setCurrTimeAccountOtherCodeList(sc4);
                }
            } else if (RuleCodeConstant.TRANSACTION_TYPE_P.equals(code)) {
                if (pc7 != null) {
                    dto.setCurrTimeAccountCodeList(pc7);
                }
                if (pd7 != null) {
                    dto.setCurrTimeAccountOtherCodeList(pd7);
                }
            } else if ("L".equals(code)||"M".equals(code)||"D".equals(code)||"K".equals(code)||"E".equals(code)||"H".equals(code)) {
                if (lde != null) {
                    dto.setCurrTimeAccountCodeList(lde);
                }
                if (lce != null) {
                    dto.setCurrTimeAccountOtherCodeList(lce);
                }
            }
        }
    }



}
