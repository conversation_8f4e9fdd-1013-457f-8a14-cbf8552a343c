package com.anytech.anytxn.business.card.service;

import com.anytech.anytxn.business.base.card.domain.dto.CardCustSpecialInfoDTO;
import com.anytech.anytxn.business.dao.card.mapper.CardCustSpecialInfoMapper;
import com.anytech.anytxn.business.dao.card.mapper.CardCustSpecialInfoSelfMapper;
import com.anytech.anytxn.business.dao.card.model.CardCustSpecialInfo;
import com.anytech.anytxn.common.core.utils.BeanMapping;
import com.anytech.anytxn.common.core.utils.OrgNumberUtils;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockedStatic;
import org.mockito.junit.jupiter.MockitoExtension;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.sql.Date;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.Mockito.*;

/**
 * CardCustSpecialInfoServiceImpl 测试类
 * 
 * <AUTHOR>
 * @date 2024-01-01
 */
@ExtendWith(MockitoExtension.class)
class CardCustSpecialInfoServiceTest {

    @InjectMocks
    private CardCustSpecialInfoServiceImpl cardCustSpecialInfoService;

    @Mock
    private CardCustSpecialInfoMapper cardCustSpecialInfoMapper;

    @Mock
    private CardCustSpecialInfoSelfMapper cardCustSpecialInfoSelfMapper;

    private CardCustSpecialInfoDTO testDTO;
    private CardCustSpecialInfo testEntity;

    @BeforeEach
    void setUp() {
        try (MockedStatic<OrgNumberUtils> orgUtilsMock = mockStatic(OrgNumberUtils.class)) {
            // Mock OrgNumberUtils for BaseEntity constructor if needed
            orgUtilsMock.when(OrgNumberUtils::getOrg).thenReturn("TEST_ORG");
            
            // 准备测试数据
            testDTO = createTestDTO();
            testEntity = createTestEntity();
        }
    }

    /**
     * 测试方法：shouldInsertSuccessfully_whenValidDTOProvided
     * 用来测试 CardCustSpecialInfoServiceImpl 方法 insert
     * 验证当提供有效DTO时，能够成功插入客户特殊信息
     */
    @Test
    void shouldInsertSuccessfully_whenValidDTOProvided() {
        try (MockedStatic<BeanMapping> beanMappingMock = mockStatic(BeanMapping.class)) {
            // Arrange - 准备测试数据
            CardCustSpecialInfo expectedEntity = new CardCustSpecialInfo();
            beanMappingMock.when(() -> BeanMapping.copy(testDTO, CardCustSpecialInfo.class))
                    .thenReturn(expectedEntity);
            when(cardCustSpecialInfoMapper.insertSelective(expectedEntity)).thenReturn(1);

            // Act - 执行被测方法
            int result = cardCustSpecialInfoService.insert(testDTO);

            // Assert - 验证结果
            assertEquals(1, result);
            verify(cardCustSpecialInfoMapper).insertSelective(expectedEntity);
            beanMappingMock.verify(() -> BeanMapping.copy(testDTO, CardCustSpecialInfo.class));
        }
    }

    /**
     * 测试方法：shouldUpdateByPrimaryKeySuccessfully_whenValidDTOProvided
     * 用来测试 CardCustSpecialInfoServiceImpl 方法 updateByPrimaryKey
     * 验证当提供有效DTO时，能够成功根据主键更新客户特殊信息
     */
    @Test
    void shouldUpdateByPrimaryKeySuccessfully_whenValidDTOProvided() {
        try (MockedStatic<BeanMapping> beanMappingMock = mockStatic(BeanMapping.class)) {
            // Arrange - 准备测试数据
            CardCustSpecialInfo expectedEntity = new CardCustSpecialInfo();
            beanMappingMock.when(() -> BeanMapping.copy(testDTO, CardCustSpecialInfo.class))
                    .thenReturn(expectedEntity);
            when(cardCustSpecialInfoMapper.updateByPrimaryKey(expectedEntity)).thenReturn(1);

            // Act - 执行被测方法
            int result = cardCustSpecialInfoService.updateByPrimaryKey(testDTO);

            // Assert - 验证结果
            assertEquals(1, result);
            verify(cardCustSpecialInfoMapper).updateByPrimaryKey(expectedEntity);
            beanMappingMock.verify(() -> BeanMapping.copy(testDTO, CardCustSpecialInfo.class));
        }
    }

    /**
     * 测试方法：shouldUpdateByPrimaryKeySelectiveSuccessfully_whenValidDTOProvided
     * 用来测试 CardCustSpecialInfoServiceImpl 方法 updateByPrimaryKeySelective
     * 验证当提供有效DTO时，能够成功根据主键选择性更新客户特殊信息
     */
    @Test
    void shouldUpdateByPrimaryKeySelectiveSuccessfully_whenValidDTOProvided() {
        try (MockedStatic<BeanMapping> beanMappingMock = mockStatic(BeanMapping.class)) {
            // Arrange - 准备测试数据
            CardCustSpecialInfo expectedEntity = new CardCustSpecialInfo();
            beanMappingMock.when(() -> BeanMapping.copy(testDTO, CardCustSpecialInfo.class))
                    .thenReturn(expectedEntity);
            when(cardCustSpecialInfoMapper.updateByPrimaryKeySelective(expectedEntity)).thenReturn(1);

            // Act - 执行被测方法
            int result = cardCustSpecialInfoService.updateByPrimaryKeySelective(testDTO);

            // Assert - 验证结果
            assertEquals(1, result);
            verify(cardCustSpecialInfoMapper).updateByPrimaryKeySelective(expectedEntity);
            beanMappingMock.verify(() -> BeanMapping.copy(testDTO, CardCustSpecialInfo.class));
        }
    }

    /**
     * 测试方法：shouldReturnDTO_whenCustomerIdExists
     * 用来测试 CardCustSpecialInfoServiceImpl 方法 selectByCustomerId
     * 验证当客户ID存在时，能够正确返回客户特殊信息DTO
     */
    @Test
    void shouldReturnDTO_whenCustomerIdExists() {
        try (MockedStatic<BeanMapping> beanMappingMock = mockStatic(BeanMapping.class)) {
            // Arrange - 准备测试数据
            String customerId = "CUST001";
            when(cardCustSpecialInfoSelfMapper.selectByCustomerId(customerId)).thenReturn(testEntity);
            beanMappingMock.when(() -> BeanMapping.copy(testEntity, CardCustSpecialInfoDTO.class))
                    .thenReturn(testDTO);

            // Act - 执行被测方法
            CardCustSpecialInfoDTO result = cardCustSpecialInfoService.selectByCustomerId(customerId);

            // Assert - 验证结果
            assertNotNull(result);
            assertEquals(testDTO, result);
            verify(cardCustSpecialInfoSelfMapper).selectByCustomerId(customerId);
            beanMappingMock.verify(() -> BeanMapping.copy(testEntity, CardCustSpecialInfoDTO.class));
        }
    }

    /**
     * 测试方法：shouldReturnNull_whenCustomerIdNotExists
     * 用来测试 CardCustSpecialInfoServiceImpl 方法 selectByCustomerId
     * 验证当客户ID不存在时，能够正确返回null
     */
    @Test
    void shouldReturnNull_whenCustomerIdNotExists() {
        // Arrange - 准备测试数据
        String customerId = "NONEXISTENT";
        when(cardCustSpecialInfoSelfMapper.selectByCustomerId(customerId)).thenReturn(null);

        // Act - 执行被测方法
        CardCustSpecialInfoDTO result = cardCustSpecialInfoService.selectByCustomerId(customerId);

        // Assert - 验证结果
        assertNull(result);
        verify(cardCustSpecialInfoSelfMapper).selectByCustomerId(customerId);
    }

    /**
     * 测试方法：shouldReturnDTO_whenCorporateCustomerIdExists
     * 用来测试 CardCustSpecialInfoServiceImpl 方法 selectByCorCustomerId
     * 验证当公司客户ID存在时，能够正确返回客户特殊信息DTO
     */
    @Test
    void shouldReturnDTO_whenCorporateCustomerIdExists() {
        try (MockedStatic<BeanMapping> beanMappingMock = mockStatic(BeanMapping.class)) {
            // Arrange - 准备测试数据
            String corporateCustomerId = "CORP001";
            when(cardCustSpecialInfoSelfMapper.selectByCorCustomerId(corporateCustomerId)).thenReturn(testEntity);
            beanMappingMock.when(() -> BeanMapping.copy(testEntity, CardCustSpecialInfoDTO.class))
                    .thenReturn(testDTO);

            // Act - 执行被测方法
            CardCustSpecialInfoDTO result = cardCustSpecialInfoService.selectByCorCustomerId(corporateCustomerId);

            // Assert - 验证结果
            assertNotNull(result);
            assertEquals(testDTO, result);
            verify(cardCustSpecialInfoSelfMapper).selectByCorCustomerId(corporateCustomerId);
            beanMappingMock.verify(() -> BeanMapping.copy(testEntity, CardCustSpecialInfoDTO.class));
        }
    }

    /**
     * 测试方法：shouldReturnNull_whenCorporateCustomerIdNotExists
     * 用来测试 CardCustSpecialInfoServiceImpl 方法 selectByCorCustomerId
     * 验证当公司客户ID不存在时，能够正确返回null
     */
    @Test
    void shouldReturnNull_whenCorporateCustomerIdNotExists() {
        // Arrange - 准备测试数据
        String corporateCustomerId = "NONEXISTENT";
        when(cardCustSpecialInfoSelfMapper.selectByCorCustomerId(corporateCustomerId)).thenReturn(null);

        // Act - 执行被测方法
        CardCustSpecialInfoDTO result = cardCustSpecialInfoService.selectByCorCustomerId(corporateCustomerId);

        // Assert - 验证结果
        assertNull(result);
        verify(cardCustSpecialInfoSelfMapper).selectByCorCustomerId(corporateCustomerId);
    }

    /**
     * 测试方法：shouldReturnDTO_whenCustomerIdAndTypeMatch
     * 用来测试 CardCustSpecialInfoServiceImpl 方法 selectByCustomerIdAndType
     * 验证当客户ID和地址类型匹配时，能够正确返回客户特殊信息DTO
     */
    @Test
    void shouldReturnDTO_whenCustomerIdAndTypeMatch() {
        try (MockedStatic<BeanMapping> beanMappingMock = mockStatic(BeanMapping.class)) {
            // Arrange - 准备测试数据
            String customerId = "CUST001";
            String addressType = "HOME";
            when(cardCustSpecialInfoSelfMapper.selectByCustomerIdAndType(customerId, addressType)).thenReturn(testEntity);
            beanMappingMock.when(() -> BeanMapping.copy(testEntity, CardCustSpecialInfoDTO.class))
                    .thenReturn(testDTO);

            // Act - 执行被测方法
            CardCustSpecialInfoDTO result = cardCustSpecialInfoService.selectByCustomerIdAndType(customerId, addressType);

            // Assert - 验证结果
            assertNotNull(result);
            assertEquals(testDTO, result);
            verify(cardCustSpecialInfoSelfMapper).selectByCustomerIdAndType(customerId, addressType);
            beanMappingMock.verify(() -> BeanMapping.copy(testEntity, CardCustSpecialInfoDTO.class));
        }
    }

    /**
     * 测试方法：shouldReturnEmptyDTO_whenCustomerIdAndTypeNotMatch
     * 用来测试 CardCustSpecialInfoServiceImpl 方法 selectByCustomerIdAndType
     * 验证当客户ID和地址类型不匹配时，能够正确返回空的DTO对象
     */
    @Test
    void shouldReturnEmptyDTO_whenCustomerIdAndTypeNotMatch() {
        // Arrange - 准备测试数据
        String customerId = "CUST001";
        String addressType = "OFFICE";
        when(cardCustSpecialInfoSelfMapper.selectByCustomerIdAndType(customerId, addressType)).thenReturn(null);

        // Act - 执行被测方法
        CardCustSpecialInfoDTO result = cardCustSpecialInfoService.selectByCustomerIdAndType(customerId, addressType);

        // Assert - 验证结果
        assertNotNull(result);
        // 验证返回的是一个新的空DTO对象（实际代码逻辑）
        assertTrue(result instanceof CardCustSpecialInfoDTO);
        verify(cardCustSpecialInfoSelfMapper).selectByCustomerIdAndType(customerId, addressType);
    }

    /**
     * 测试方法：shouldReturnEmptyDTO_whenConditionNotMatch
     * 用来测试 CardCustSpecialInfoServiceImpl 方法 selectByCondition
     * 验证当条件不匹配时，能够正确返回空的DTO对象
     */
    @Test
    void shouldReturnEmptyDTO_whenConditionNotMatch() {
        try (MockedStatic<BeanMapping> beanMappingMock = mockStatic(BeanMapping.class)) {
            // Arrange - 准备测试数据
            CardCustSpecialInfo queryEntity = new CardCustSpecialInfo();
            
            beanMappingMock.when(() -> BeanMapping.copy(testDTO, CardCustSpecialInfo.class))
                    .thenReturn(queryEntity);
            when(cardCustSpecialInfoSelfMapper.selectByCondition(queryEntity)).thenReturn(null);

            // Act - 执行被测方法
            CardCustSpecialInfoDTO result = cardCustSpecialInfoService.selectByCondition(testDTO);

            // Assert - 验证结果
            assertNotNull(result);
            // 验证返回的是一个新的空DTO对象（实际代码逻辑）
            assertTrue(result instanceof CardCustSpecialInfoDTO);
            verify(cardCustSpecialInfoSelfMapper).selectByCondition(queryEntity);
        }
    }

    // 创建测试DTO数据的辅助方法
    private CardCustSpecialInfoDTO createTestDTO() {
        CardCustSpecialInfoDTO dto = new CardCustSpecialInfoDTO();
        dto.setId("INFO001");
        dto.setOrganizationNumber("ORG001");
        dto.setCustomerId("CUST001");
        dto.setOpenDate(LocalDate.of(2024, 1, 1));
        dto.setCorporateCustomerId("CORP001");
        dto.setMobilePhone("13800138000");
        dto.setCycleDay((short) 15);
        dto.setSex("M");
        dto.setType("HOME");
        dto.setAddress("北京市朝阳区建国路1号");
        dto.setAddress2("大望路SOHO现代城");
        dto.setDistrict("朝阳区");
        dto.setCity("北京市");
        dto.setProvince("北京");
        dto.setZipcode("100020");
        dto.setCountryCode("CN");
        dto.setTenantId("TENANT001");
        dto.setCreateTime(LocalDateTime.now());
        dto.setUpdateTime(LocalDateTime.now());
        dto.setUpdateBy("SYSTEM");
        dto.setVersionNumber(1L);
        return dto;
    }

    // 创建测试Entity数据的辅助方法
    private CardCustSpecialInfo createTestEntity() {
        CardCustSpecialInfo entity = new CardCustSpecialInfo();
        entity.setId("INFO001");
        entity.setOrganizationNumber("ORG001");
        entity.setCustomerId("CUST001");
        entity.setOpenDate(Date.valueOf(LocalDate.of(2024, 1, 1)));
        entity.setCorporateCustomerId("CORP001");
        entity.setMobilePhone("13800138000");
        entity.setCycleDay((short) 15);
        entity.setSex("M");
        entity.setType("HOME");
        entity.setAddress("北京市朝阳区建国路1号");
        entity.setAddress2("大望路SOHO现代城");
        entity.setDistrict("朝阳区");
        entity.setCity("北京市");
        entity.setProvince("北京");
        entity.setZipcode("100020");
        entity.setCountryCode("CN");
        entity.setTenantId("TENANT001");
        entity.setCreateTime(LocalDateTime.now());
        entity.setUpdateTime(LocalDateTime.now());
        entity.setUpdateBy("SYSTEM");
        entity.setVersionNumber(1L);
        return entity;
    }
} 