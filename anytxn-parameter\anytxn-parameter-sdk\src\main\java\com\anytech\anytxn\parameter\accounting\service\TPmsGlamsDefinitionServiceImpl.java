package com.anytech.anytxn.parameter.accounting.service;

import com.alibaba.fastjson.JSON;
import com.anytech.anytxn.business.base.audit.AbstractParameterService;
import com.anytech.anytxn.business.base.audit.ParmModificationRecord;
import com.anytech.anytxn.common.core.annotation.db.DeleteParameterAnnotation;
import com.anytech.anytxn.common.core.annotation.db.InsertParameterAnnotation;
import com.anytech.anytxn.common.core.annotation.db.UpdateParameterAnnotation;
import com.anytech.anytxn.common.core.base.ParameterCompare;
import com.anytech.anytxn.common.core.base.PageResultDTO;
import com.anytech.anytxn.common.core.utils.BeanMapping;
import com.anytech.anytxn.common.core.utils.OrgNumberUtils;
import com.anytech.anytxn.common.sequence.utils.Number16IdGen;
import com.anytech.anytxn.common.core.utils.TenantUtils;
import com.anytech.anytxn.parameter.base.accounting.domain.dto.TPmsGlamsControlDTO;
import com.anytech.anytxn.parameter.base.accounting.domain.dto.TPmsGlamsDefinitionDTO;
import com.anytech.anytxn.parameter.base.accounting.service.ITPmsGlamsDefinitionService;
import com.anytech.anytxn.parameter.base.common.constants.Constants;
import com.anytech.anytxn.parameter.base.common.enums.AnyTxnParameterRespCodeEnum;
import com.anytech.anytxn.parameter.base.common.exception.AnyTxnParameterException;
import com.anytech.anytxn.parameter.accounting.mapper.TPmsGlamsControlMapper;
import com.anytech.anytxn.parameter.accounting.mapper.TPmsGlamsControlSelfMapper;
import com.anytech.anytxn.parameter.accounting.mapper.TPmsGlamsDefinitionMapper;
import com.anytech.anytxn.parameter.accounting.mapper.TPmsGlamsDefinitionSelfMapper;
import com.anytech.anytxn.parameter.base.accounting.domain.model.TPmsGlamsControl;
import com.anytech.anytxn.parameter.base.accounting.domain.model.TPmsGlamsDefinition;
import com.github.pagehelper.Page;
import com.github.pagehelper.PageHelper;
import org.apache.commons.lang3.ObjectUtils;
//import org.apache.shardingsphere.transaction.annotation.ShardingSphereTransactionType;
//import org.apache.shardingsphere.transaction.core.TransactionType;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.cglib.beans.BeanCopier;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import jakarta.annotation.Resource;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;

/**
 * @description: 会计核算控制表参数
 * @author: ZXL
 * @create: 2019-10-09 14:26
 */

@Service("parm_pms_glams_definition_serviceImpl")
public class TPmsGlamsDefinitionServiceImpl extends AbstractParameterService implements ITPmsGlamsDefinitionService {

    private static final Logger logger = LoggerFactory.getLogger(TPmsGlamsDefinitionServiceImpl.class);

    @Resource
    private TPmsGlamsDefinitionSelfMapper tPmsGlamsDefinitionSelfMapper;
    @Resource
    private TPmsGlamsDefinitionMapper tPmsGlamsDefinitionMapper;
    @Resource
    private TPmsGlamsControlMapper tPmsGlamsControlMapper;
    @Resource
    private TPmsGlamsControlSelfMapper tPmsGlamsControlSelfMapper;

    @Resource
    private Number16IdGen numberIdGenerator;

    @Override
    public PageResultDTO<TPmsGlamsDefinitionDTO> page(int page, int rows,String organizationNumber,String tableId,String description) {
        Page pageInfo = PageHelper.startPage(page, rows);
        List<TPmsGlamsDefinitionDTO> tPmsGlamsDefinitionDTOList = null;
        try {
            organizationNumber = StringUtils.isEmpty(organizationNumber) ? OrgNumberUtils.getOrg() : organizationNumber;
            List<TPmsGlamsDefinition> tPmsGlamsDefinitionList = tPmsGlamsDefinitionSelfMapper.selectByCondition(organizationNumber, tableId, description);
            if (!CollectionUtils.isEmpty(tPmsGlamsDefinitionList)) {
                tPmsGlamsDefinitionDTOList = new ArrayList<>();
                BeanCopier beanCopier = BeanCopier.create(TPmsGlamsDefinition.class, TPmsGlamsDefinitionDTO.class,
                        false);
                for (TPmsGlamsDefinition tPmsGlamsDefinition : tPmsGlamsDefinitionList) {
                    TPmsGlamsDefinitionDTO tPmsGlamsDefinitionDTO = new TPmsGlamsDefinitionDTO();
                    beanCopier.copy(tPmsGlamsDefinition, tPmsGlamsDefinitionDTO, null);
                    tPmsGlamsDefinitionDTOList.add(tPmsGlamsDefinitionDTO);
                }
            }
            return new PageResultDTO<>(page, rows, pageInfo.getTotal(), pageInfo.getPages(),
                    tPmsGlamsDefinitionDTOList);
        } catch (Exception e) {
            logger.error("分页查询流水拆分分录参数失败，当前页：{}，每页展示条数：{},错误信息：{}", page, rows, e.getMessage());
            /*throw new AnyTXNBusRuntimeException(TransactionEnum.DATABASE_ERROR.getCode(),
                    TransactionEnum.DATABASE_ERROR.getReason());*/
            throw new AnyTxnParameterException(AnyTxnParameterRespCodeEnum.D_PAGE_TPMS_GLAMS_DEFINITION_FAULT);
        }
    }

    @Override
    public TPmsGlamsDefinitionDTO detail(String id) {
        if (id == null) {
            logger.error("({}) parameter is null", id);
            throw new AnyTxnParameterException(AnyTxnParameterRespCodeEnum.P_PARAMETER_ID_EMPTY_FAULT);
        }
        TPmsGlamsDefinition tPmsGlamsDefinition = tPmsGlamsDefinitionMapper.selectByPrimaryKey(id);
        if (tPmsGlamsDefinition != null) {
            TPmsGlamsDefinitionDTO tPmsGlamsDefinitionDTO = new TPmsGlamsDefinitionDTO();
            BeanCopier beanCopier = BeanCopier.create(TPmsGlamsDefinition.class, TPmsGlamsDefinitionDTO.class, false);
            beanCopier.copy(tPmsGlamsDefinition, tPmsGlamsDefinitionDTO, null);
            return tPmsGlamsDefinitionDTO;
        } else {
            logger.error("({}) selectByPrimaryKey is null", id);

            throw new AnyTxnParameterException(AnyTxnParameterRespCodeEnum.D_QUERY_TPMS_GLAMS_DEFINITION_BY_ID_FAULT);
        }
    }

    @Override
    @DeleteParameterAnnotation(tableName = "parm_pms_glams_definition", tableDesc = "Accounting Event Parameters")
    public ParameterCompare remove(String id) {
        if (null == id) {
            throw new AnyTxnParameterException(AnyTxnParameterRespCodeEnum.P_PARAMETER_EMPTY_FAULT);
        }
        TPmsGlamsDefinition tPmsGlamsDefinition = tPmsGlamsDefinitionMapper.selectByPrimaryKey(id);
        if(ObjectUtils.isEmpty(tPmsGlamsDefinition)){
            throw new AnyTxnParameterException(AnyTxnParameterRespCodeEnum.D_DATA_NOT_EXISTS_TEMPLATE_FAULT);
        }

        return ParameterCompare
                .getBuilder()
                .withBefore(tPmsGlamsDefinition)
                .build(TPmsGlamsDefinition.class);
    }

    @Override
    @InsertParameterAnnotation(tableName = "parm_pms_glams_definition", tableDesc = "Accounting Event Parameters", isJoinTable = true)
    public ParameterCompare add(TPmsGlamsDefinitionDTO data) {
        if(ObjectUtils.isEmpty(data)){
            throw new AnyTxnParameterException(AnyTxnParameterRespCodeEnum.P_PARAMETER_DATA_NOT_EXIST);
        }
        data.setBranchid("DINERS");
        // 判断是否唯一约束冲突
        isUnique(data.getOrganizationNumber(),data.getBranchid(), data.getTableId());
        data.setId(String.valueOf(numberIdGenerator.generateId(TenantUtils.getTenantId())));

        return ParameterCompare.getBuilder()
                .withAfter(data)
                .withMainParmId(data.getTableId()).build(TPmsGlamsDefinitionDTO.class);
    }

    @Override
    @UpdateParameterAnnotation(tableName = "parm_pms_glams_definition", tableDesc = "Accounting Event Parameters", isJoinTable = true)
    public ParameterCompare update(TPmsGlamsDefinitionDTO data) {
        if(ObjectUtils.isEmpty(data)){
            throw new AnyTxnParameterException(AnyTxnParameterRespCodeEnum.P_PARAMETER_EMPTY_FAULT);
        }
        TPmsGlamsDefinitionDTO tPmsGlamsDefinition1 = detail(String.valueOf(data.getId()));
        if(ObjectUtils.isEmpty(tPmsGlamsDefinition1)){
            throw new AnyTxnParameterException(AnyTxnParameterRespCodeEnum.D_DATA_NOT_EXISTS_TEMPLATE_FAULT);
        }
        data.setBranchid("DINERS");

        return ParameterCompare.getBuilder()
                .withAfter(data)
                .withBefore(tPmsGlamsDefinition1)
                .withMainParmId(data.getTableId())
                .build(TPmsGlamsDefinitionDTO.class);
    }

    @Override
    public TPmsGlamsDefinitionDTO selectByIndex(String organizationNumber, String branchId, String tableId) {
        TPmsGlamsDefinition glamsDefinition = tPmsGlamsDefinitionSelfMapper.selectByIndex(organizationNumber, branchId, tableId);
        return glamsDefinition == null ? null : BeanMapping.copy(glamsDefinition, TPmsGlamsDefinitionDTO.class);
    }

    @Override
    @Transactional(rollbackFor = Exception.class,propagation = Propagation.REQUIRED)
    //@ShardingSphereTransactionType(TransactionType.XA)
    protected boolean updateDb(ParmModificationRecord parmModificationRecord) {
        TPmsGlamsDefinitionDTO tPmsGlamsDefinitionDTO = JSON.parseObject(parmModificationRecord.getParmBody(), TPmsGlamsDefinitionDTO.class);
        tPmsGlamsDefinitionDTO.setUpdateTime(LocalDateTime.now());

        TPmsGlamsDefinition tPmsGlamsDefinition = BeanMapping.copy(tPmsGlamsDefinitionDTO, TPmsGlamsDefinition.class);
        tPmsGlamsDefinition.setId(String.valueOf(tPmsGlamsDefinitionDTO.getId()));

        try {
            tPmsGlamsDefinitionMapper.updateByPrimaryKeySelective(tPmsGlamsDefinition);
        } catch (Exception e) {
            logger.error("更新会计参数定义表失败", e);
            throw new AnyTxnParameterException(AnyTxnParameterRespCodeEnum.D_UPDATE_TPMS_GLAMS_DEFINITION_FAULT);
        }

        List<TPmsGlamsControlDTO> list = tPmsGlamsDefinitionDTO.getTpmsGlamsControlList();

        tPmsGlamsControlSelfMapper.deleteByOrgNumAndTableId(tPmsGlamsDefinition.getOrganizationNumber(), tPmsGlamsDefinition.getBranchid(),
                tPmsGlamsDefinition.getTableId());

        List<TPmsGlamsControl> updateList = BeanMapping.copyList(list, TPmsGlamsControl.class);

        for (TPmsGlamsControl control : updateList) {
            if (control.getId() == null) {
                control.setId(numberIdGenerator.generateId(TenantUtils.getTenantId()) + "");
            }
            control.setOrganizationNumber(tPmsGlamsDefinition.getOrganizationNumber());
            control.setBranchid(tPmsGlamsDefinition.getBranchid());
            control.setTableId(tPmsGlamsDefinition.getTableId());
            control.setUpdateBy(Constants.DEFAULT_USER);
            control.setCreateTime(LocalDateTime.now());
            control.setUpdateTime(LocalDateTime.now());
            control.setVersionNumber(1L);
        }
        if (!list.isEmpty()) {
            try {
                updateList.forEach(tPmsGlamsControl -> {
                    if (tPmsGlamsControl.getId() == null) {
                        tPmsGlamsControl.setId(numberIdGenerator.generateId(TenantUtils.getTenantId()) + "");
                    }
                    tPmsGlamsControlMapper.insert(tPmsGlamsControl);
                });
            } catch (Exception e) {
                logger.error("插入会计参数控制表失败", e);
                throw new AnyTxnParameterException(AnyTxnParameterRespCodeEnum.D_INSERT_TPMS_GLAMS_CONTROL_FAULT);
            }

        }
        return true;
    }

    @Override
    @Transactional(rollbackFor = Exception.class,propagation = Propagation.REQUIRED)
    //@ShardingSphereTransactionType(TransactionType.XA)
    protected boolean insertDb(ParmModificationRecord parmModificationRecord) {
        TPmsGlamsDefinitionDTO tPmsGlamsDefinitionDTO = JSON.parseObject(parmModificationRecord.getParmBody(), TPmsGlamsDefinitionDTO.class);

        isUnique(tPmsGlamsDefinitionDTO.getOrganizationNumber(),tPmsGlamsDefinitionDTO.getBranchid(), tPmsGlamsDefinitionDTO.getTableId());

        TPmsGlamsDefinition tPmsGlamsDefinition = BeanMapping.copy(tPmsGlamsDefinitionDTO, TPmsGlamsDefinition.class);
        tPmsGlamsDefinition.initCreateDateTime();
        tPmsGlamsDefinition.initUpdateDateTime();
        tPmsGlamsDefinition.setUpdateBy(parmModificationRecord.getApplicationBy());
        tPmsGlamsDefinition.setVersionNumber(1L);
        tPmsGlamsDefinition.setStatus("1");

        try {
            tPmsGlamsDefinitionSelfMapper.insert(tPmsGlamsDefinition);
        } catch (Exception e) {
            logger.error("新增会计参数定义表失败", e);
            throw new AnyTxnParameterException(AnyTxnParameterRespCodeEnum.D_INSERT_TPMS_GLAMS_DEFINITION_FAULT);
        }
        List<TPmsGlamsControlDTO> list = tPmsGlamsDefinitionDTO.getTpmsGlamsControlList();


        if (!list.isEmpty()) {
            for (TPmsGlamsControlDTO reqList : list) {
                if (reqList.getId() == null) {
                    reqList.setId(numberIdGenerator.generateId(TenantUtils.getTenantId())+"");
                }
                reqList.setOrganizationNumber(tPmsGlamsDefinition.getOrganizationNumber());
                reqList.setBranchid(tPmsGlamsDefinition.getBranchid());
                reqList.setUpdateBy(Constants.DEFAULT_USER);
                reqList.setVersionNumber(1L);
                reqList.setTableId(tPmsGlamsDefinition.getTableId());
                reqList.setCreateTime(LocalDateTime.now());
                reqList.setUpdateTime(LocalDateTime.now());
            }

            List<TPmsGlamsControl> insertList = BeanMapping.copyList(list, TPmsGlamsControl.class);
            try {
                insertList.forEach(tPmsGlamsControl -> {
                    tPmsGlamsControl.setId(numberIdGenerator.generateId(TenantUtils.getTenantId()) + "");
                    tPmsGlamsControlMapper.insert(tPmsGlamsControl);
                });
            } catch (Exception e) {
                logger.error("新增会计参数控制表失败", e);
                throw new AnyTxnParameterException(AnyTxnParameterRespCodeEnum.D_INSERT_TPMS_GLAMS_CONTROL_FAULT);
            }
        }
        tPmsGlamsDefinitionDTO.setTpmsGlamsControlList(list);
        return true;
    }

    @Override
    @Transactional(rollbackFor = Exception.class,propagation = Propagation.REQUIRED)
    //@ShardingSphereTransactionType(TransactionType.XA)
    protected boolean deleteDb(ParmModificationRecord parmModificationRecord) {
        TPmsGlamsDefinition tPmsGlamsDefinition = JSON.parseObject(parmModificationRecord.getParmBody(), TPmsGlamsDefinition.class);

        int i = tPmsGlamsDefinitionMapper.deleteByPrimaryKey(tPmsGlamsDefinition.getId());
        return i > 0;
    }


    /**
     * 判断是否唯一约束冲突
     * @param organizationNumber 机构号
     * @param branchid
     * @param tableId
     */
    private void isUnique(String organizationNumber, String branchid, String tableId) {
        TPmsGlamsDefinition isExsit =
                tPmsGlamsDefinitionSelfMapper.selectByIndex(organizationNumber, branchid, tableId);
        if (isExsit != null) {
            throw new AnyTxnParameterException(AnyTxnParameterRespCodeEnum.D_EXIST_TPMS_GLAMS_DEFINITION_FAULT);
        }
    }
}
