package com.anytech.anytxn.parameter.common.service.system;

import com.alibaba.fastjson.JSON;
import com.github.pagehelper.Page;
import com.github.pagehelper.PageHelper;
import com.anytech.anytxn.common.core.annotation.db.DeleteParameterAnnotation;
import com.anytech.anytxn.common.core.annotation.db.InsertParameterAnnotation;
import com.anytech.anytxn.common.core.annotation.db.UpdateParameterAnnotation;
import com.anytech.anytxn.business.base.audit.AbstractParameterService;
import com.anytech.anytxn.common.core.base.ParameterCompare;
import com.anytech.anytxn.business.base.audit.ParmModificationRecord;
import com.anytech.anytxn.common.core.base.PageResultDTO;
import com.anytech.anytxn.common.core.utils.BeanMapping;
import com.anytech.anytxn.common.core.utils.OrgNumberUtils;
import com.anytech.anytxn.common.sequence.utils.Number16IdGen;
import com.anytech.anytxn.common.core.utils.TenantUtils;
import com.anytech.anytxn.parameter.base.common.domain.dto.AccountBalanceDTO;
import com.anytech.anytxn.parameter.base.common.service.IAccountBalanceService;
import com.anytech.anytxn.parameter.base.common.enums.AnyTxnParameterRespCodeEnum;
import com.anytech.anytxn.parameter.base.common.exception.AnyTxnParameterException;
import com.anytech.anytxn.parameter.common.mapper.broadcast.system.ParmAcctBalanceCollectInfoMapper;
import com.anytech.anytxn.parameter.common.mapper.broadcast.system.ParmAcctBalanceCollectInfoSelfMapper;
import com.anytech.anytxn.parameter.base.common.domain.model.system.ParmAcctBalanceCollectInfo;
//import org.apache.shardingsphere.transaction.annotation.ShardingSphereTransactionType;
//import org.apache.shardingsphere.transaction.core.TransactionType;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.ObjectUtils;
import org.springframework.util.StringUtils;

import java.util.Collections;
import java.util.List;
import java.util.Objects;

/**
 * 账户入账参数接口实现类
 * <AUTHOR>
 * @since 2020/6/26 15:43
 **/
@Service("parm_acct_balance_collect_info_serviceImpl")
public class AccountBlanceServiceImpl extends AbstractParameterService implements IAccountBalanceService {

    private final Logger logger = LoggerFactory.getLogger(AccountBlanceServiceImpl.class);

    @Autowired
    private ParmAcctBalanceCollectInfoMapper accountBlanceMapper;
    @Autowired
    private ParmAcctBalanceCollectInfoSelfMapper acctBalanceCollectInfoSelfMapper;
    @Autowired
    private Number16IdGen numberIdGenerator;

    /**
     * 根据唯一索引查询
     * @param orgNum
     * @param tableId
     * @return
     */
    @Override
    public AccountBalanceDTO findByIndex(String orgNum, String tableId) {
        AccountBalanceDTO dto;
        ParmAcctBalanceCollectInfo bean = accountBlanceMapper.findByIndex(orgNum, tableId);
        if (bean == null) {
            logger.error("根据机构编号{}和参数表ID{}查询账户入账参数数据失败,",orgNum,tableId);
            throw new AnyTxnParameterException(AnyTxnParameterRespCodeEnum.ACCOUNTBLANCE_INDEX_NOT_EXIST);
        }else{
            dto = BeanMapping.copy(bean, AccountBalanceDTO.class);
        }
        return dto;
    }

    /**
     * 分页查询
     * @param pageNum
     * @param pageSize
     * @return
     */
    @Override
    public PageResultDTO<AccountBalanceDTO> findPage(Integer pageNum, Integer pageSize, String organizationNumber,String tableId,String description) {
        logger.debug("分页查询账户入账参数信息");
        Page<ParmAcctBalanceCollectInfo> page = PageHelper.startPage(pageNum, pageSize);
        organizationNumber = StringUtils.isEmpty(organizationNumber) ? OrgNumberUtils.getOrg() : organizationNumber;
        List<ParmAcctBalanceCollectInfo> resultList = accountBlanceMapper.selectByCondition( organizationNumber,tableId,description);
        List<AccountBalanceDTO> dtoList;
        if (null == resultList || resultList.isEmpty()) {
            logger.error(AnyTxnParameterRespCodeEnum.ACCOUNTBLANCE_NO_DATA.getMsg());
            dtoList = Collections.EMPTY_LIST;
        }else {
            dtoList = BeanMapping.copyList(resultList, AccountBalanceDTO.class);
        }

        return new PageResultDTO<>(pageNum, pageSize, page.getTotal(), page.getPages(), dtoList);
    }

    /**
     * 修改交易账户参数
     * @param dto 易账户汇总维度参数信息
     * @return
     */
    @Override
    @UpdateParameterAnnotation(tableName = "parm_acct_balance_collect_info",tableDesc = "Summary parameters of trading account")
    public ParameterCompare modify(AccountBalanceDTO dto) {
        if (Objects.isNull(dto.getOrganizationNumber()) || Objects.isNull(dto.getTableId())) {
            throw new AnyTxnParameterException(AnyTxnParameterRespCodeEnum.ACCOUNTBLANCE_NOT_NULL);
        }
        ParmAcctBalanceCollectInfo parmAcctBalanceCollectInfo = acctBalanceCollectInfoSelfMapper.selectById(dto.getId());
        if (ObjectUtils.isEmpty(parmAcctBalanceCollectInfo)) {
            throw new AnyTxnParameterException(AnyTxnParameterRespCodeEnum.ACCOUNTBLANCE_INDEX_NOT_EXIST);
        }
        boolean exist = accountBlanceMapper.isExists(dto.getOrganizationNumber(), dto.getTableId())>0;
        if (!exist) {
            logger.error("修改账户入账参数数据, 通过机构({})和参数表ID({})无法查询到数据", dto.getOrganizationNumber(), dto.getTableId());
            throw new AnyTxnParameterException(AnyTxnParameterRespCodeEnum.ACCOUNTBLANCE_INDEX_NOT_EXIST);
        }
        ParmAcctBalanceCollectInfo modify = BeanMapping.copy(dto, ParmAcctBalanceCollectInfo.class);
        return ParameterCompare
                .getBuilder()
                .withAfter(modify)
                .withBefore(parmAcctBalanceCollectInfo)
                .build(ParmAcctBalanceCollectInfo.class);
    }

    /**
     * 删除：先判断是否存在，不存在则抛出异常；否则删除
     * @param orgNum
     * @param tableId
     * @return
     */
    @Override
    @DeleteParameterAnnotation(tableName = "parm_acct_balance_collect_info",tableDesc = "Summary parameters of trading account")
    public ParameterCompare remove(String orgNum, String tableId) {
        boolean exist = accountBlanceMapper.isExists(orgNum, tableId)>0;
        if (!exist) {
            logger.error("删除账户入账参数数据, 通过机构({})和参数表ID({})无法查询到数据", orgNum, tableId);
            throw new AnyTxnParameterException(AnyTxnParameterRespCodeEnum.ACCOUNTBLANCE_INDEX_NOT_EXIST);
        }
        ParmAcctBalanceCollectInfo parmAcctBalanceCollectInfo = accountBlanceMapper.findByIndex(orgNum, tableId);
        if (ObjectUtils.isEmpty(parmAcctBalanceCollectInfo)) {
            throw new AnyTxnParameterException(AnyTxnParameterRespCodeEnum.ACCOUNTBLANCE_INDEX_NOT_EXIST);
        }
        return ParameterCompare
                .getBuilder()
                .withBefore(parmAcctBalanceCollectInfo)
                .build(ParmAcctBalanceCollectInfo.class);
    }

    /**
     * 新增账户入账参数
     * @param dto 账户入账参数
     * @return
     */
    @Override
    @InsertParameterAnnotation(tableName = "parm_acct_balance_collect_info",tableDesc = "Summary parameters of trading account")
    public ParameterCompare add(AccountBalanceDTO dto) {
        boolean exist = accountBlanceMapper.isExists(dto.getOrganizationNumber(), dto.getTableId())>0;
        if (exist) {
            logger.warn("账户入账参数已存在，orgNum={},accountProductNumber={}",
                    dto.getOrganizationNumber(), dto.getTableId());
            throw new AnyTxnParameterException(AnyTxnParameterRespCodeEnum.ACCOUNTBLANCE_INDEX_EXIST);
        }
        dto.setId(numberIdGenerator.generateId(TenantUtils.getTenantId())+"");
        ParmAcctBalanceCollectInfo bean = BeanMapping.copy(dto, ParmAcctBalanceCollectInfo.class);
        return ParameterCompare
                .getBuilder()
                .withAfter(bean)
                .build(ParmAcctBalanceCollectInfo.class);
    }

    /**
     * 根据机构号、tableId、状态查询
     * @param orgNum 机构号
     * @param tableId 参数表id
     * @param status 状态
     * @return AccountBlanceDTO
     */
    @Override
    public AccountBalanceDTO findByOrgTableIdAndStatus(String orgNum, String tableId, String status) {
        AccountBalanceDTO dto;
        ParmAcctBalanceCollectInfo bean = acctBalanceCollectInfoSelfMapper.selectByOrgTableIdAndStatus(orgNum,tableId,status);
        if (bean == null) {
            logger.error("根据机构编号{}和参数表ID{}、状态：{}，查询账户入账参数数据失败,",orgNum,tableId,status);
            throw new AnyTxnParameterException(AnyTxnParameterRespCodeEnum.ACCOUNTBLANCE_INDEX_NOT_EXIST);
        }else{
            dto = BeanMapping.copy(bean, AccountBalanceDTO.class);
        }
        return dto;
    }

    @Override
    @Transactional(rollbackFor = Exception.class,propagation = Propagation.REQUIRED)
    //@ShardingSphereTransactionType(TransactionType.XA)
    protected boolean updateDb(ParmModificationRecord parmModificationRecord) {
        ParmAcctBalanceCollectInfo parmAcctBalanceCollectInfo = JSON.parseObject(parmModificationRecord.getParmBody(), ParmAcctBalanceCollectInfo.class);
        parmAcctBalanceCollectInfo.initUpdateDateTime();
        int res = accountBlanceMapper.updateByIndex(parmAcctBalanceCollectInfo);
        return res > 0;
    }

    @Override
    @Transactional(rollbackFor = Exception.class,propagation = Propagation.REQUIRED)
    //@ShardingSphereTransactionType(TransactionType.XA)
    protected boolean insertDb(ParmModificationRecord parmModificationRecord) {
        ParmAcctBalanceCollectInfo parmAcctBalanceCollectInfo = JSON.parseObject(parmModificationRecord.getParmBody(), ParmAcctBalanceCollectInfo.class);
        parmAcctBalanceCollectInfo.initUpdateDateTime();
        parmAcctBalanceCollectInfo.initCreateDateTime();
        int res = accountBlanceMapper.insert(parmAcctBalanceCollectInfo);
        return res > 0;
    }

    @Override
    @Transactional(rollbackFor = Exception.class,propagation = Propagation.REQUIRED)
    //@ShardingSphereTransactionType(TransactionType.XA)
    protected boolean deleteDb(ParmModificationRecord parmModificationRecord) {
        ParmAcctBalanceCollectInfo parmAcctBalanceCollectInfo = JSON.parseObject(parmModificationRecord.getParmBody(), ParmAcctBalanceCollectInfo.class);
        int res = accountBlanceMapper.deleteByIndex(parmAcctBalanceCollectInfo.getOrganizationNumber(), parmAcctBalanceCollectInfo.getTableId());
        return res > 0;
    }
}
