package com.anytech.anytxn.parameter.common.service;

import com.github.pagehelper.Page;
import com.github.pagehelper.PageHelper;
import com.anytech.anytxn.common.core.base.PageResultDTO;
import com.anytech.anytxn.common.core.utils.BeanMapping;
import com.anytech.anytxn.common.core.utils.OrgNumberUtils;
import com.anytech.anytxn.parameter.base.common.domain.dto.audit.ParmMaintenanceLogDTO;
import com.anytech.anytxn.parameter.base.common.domain.dto.audit.ParmMaintenanceLogSearchKeyDTO;
import com.anytech.anytxn.parameter.base.common.service.audit.IParmMaintenanceLogService;
import com.anytech.anytxn.parameter.base.common.enums.AnyTxnParameterRespCodeEnum;
import com.anytech.anytxn.parameter.base.common.enums.ParameterRepDetailEnum;
import com.anytech.anytxn.parameter.base.common.exception.AnyTxnParameterException;
import com.anytech.anytxn.parameter.common.mapper.deprecated.ParmMaintenanceLogMapper;
import com.anytech.anytxn.parameter.common.mapper.deprecated.ParmMaintenanceLogSelfMapper;
import com.anytech.anytxn.parameter.base.common.domain.model.ParmMaintenanceLog;
import com.anytech.anytxn.common.core.utils.TenantUtils;
import com.anytech.anytxn.common.sequence.utils.Number16IdGen;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 维护日志表业务逻辑层
 * <AUTHOR>
 * @date 2019-11-23
 */
@Service
public class ParmMaintenanceLogServiceImpl implements IParmMaintenanceLogService {

    private static final Logger log = LoggerFactory.getLogger(ParmMaintenanceLogServiceImpl.class);

    @Autowired
    private ParmMaintenanceLogMapper parmMaintenanceLogMapper;

    @Autowired
    private ParmMaintenanceLogSelfMapper parmMaintenanceLogSelfMapper;

    @Autowired
    Number16IdGen number16IdGen;

    /**
     * 新建维护日志表
     * @param maintenanceLogDTO 维护日志请求参数
     */
    @Override
    public void add(ParmMaintenanceLogDTO maintenanceLogDTO) {
        if (null == maintenanceLogDTO){
            log.error("维护日志请求参数不能为空");
            throw new AnyTxnParameterException(AnyTxnParameterRespCodeEnum.P_PARAMETER_EMPTY_FAULT, ParameterRepDetailEnum.LOG_NULL);
        }
        //必输项检查
        checkRequired(maintenanceLogDTO);
        if (null != maintenanceLogDTO.getId()){
            ParmMaintenanceLog maintenanceLog = parmMaintenanceLogMapper.selectByPrimaryKey(maintenanceLogDTO.getId());
            if (null != maintenanceLog){
                log.error("根据id查询维护日志表, 数据已存在, id:{}", maintenanceLogDTO.getId());
                throw new AnyTxnParameterException(AnyTxnParameterRespCodeEnum.P_PARAMETER_DATA_EXIST, ParameterRepDetailEnum.LOG_EXIST);
            }
        }
        ParmMaintenanceLog maintenanceLog = BeanMapping.copy(maintenanceLogDTO, ParmMaintenanceLog.class);
        maintenanceLog.setId(number16IdGen.generateId(TenantUtils.getTenantId()));
        maintenanceLog.setDeleteIndicator("0");
        maintenanceLog.setCreateTime(LocalDateTime.now());
        maintenanceLog.setUpdateTime(LocalDateTime.now());
        maintenanceLog.setUpdateBy("SYSTEM");
        maintenanceLog.setVersionNumber(1L);
        try {
            parmMaintenanceLogMapper.insertSelective(maintenanceLog);
        } catch (Exception e) {
            log.error("新建维护日志表插入数据库失败",e);
            throw new AnyTxnParameterException(AnyTxnParameterRespCodeEnum.D_DATABASE_UPDATE_ERROR, ParameterRepDetailEnum.LOG_ADD_ERROR);
        }
    }

    /**
     * 根据主键值分页查询维护日志信息
     * @param searchKey 查询条件
     * @return PageResultDTO<MaintenanceLogDTO>
     */
    @Override
    public PageResultDTO<ParmMaintenanceLogDTO> selectByTransactionDataType(ParmMaintenanceLogSearchKeyDTO searchKey) {
        log.info("根据主键值查询维护日志信息");
        Page<ParmMaintenanceLog> pageInfo = PageHelper.startPage(searchKey.getPage(), searchKey.getRows());
        Map<String, String> map = new HashMap<>(2);
        map.put("transactionDataType", searchKey.getTransactionDataType());
        map.put("primaryKeyValue", searchKey.getPrimaryKeyValue());
        List<ParmMaintenanceLog> maintenanceLogs = parmMaintenanceLogSelfMapper.selectByPrimaryKeyValue(map, OrgNumberUtils.getOrg());
        List<ParmMaintenanceLogDTO> maintenanceLogDTOs = BeanMapping.copyList(maintenanceLogs, ParmMaintenanceLogDTO.class);
        return new PageResultDTO<>(searchKey.getPage(), searchKey.getRows(), pageInfo.getTotal(), pageInfo.getPages(), maintenanceLogDTOs);
    }

    /**
     * 必输项检查
     * @param maintenanceLogDTO 维护日志请求参数
     */
    private void checkRequired(ParmMaintenanceLogDTO maintenanceLogDTO){
        if (StringUtils.isEmpty(maintenanceLogDTO.getOperatorId())){
            log.error("操作员id必输");
            throw new AnyTxnParameterException(AnyTxnParameterRespCodeEnum.P_PARAMETER_EMPTY_FAULT,  ParameterRepDetailEnum.OP_NULL);
        }
        if (StringUtils.isEmpty(maintenanceLogDTO.getOperationType())){
            log.error("操作类型必输");
            throw new AnyTxnParameterException(AnyTxnParameterRespCodeEnum.P_PARAMETER_EMPTY_FAULT, ParameterRepDetailEnum.OP_TYPE_NULL);
        }
        if (StringUtils.isEmpty(maintenanceLogDTO.getTransactionDataType())){
            log.error("业务数据类型必输");
            throw new AnyTxnParameterException(AnyTxnParameterRespCodeEnum.P_PARAMETER_EMPTY_FAULT, ParameterRepDetailEnum.BUS_NULL);
        }
        if (StringUtils.isEmpty(maintenanceLogDTO.getColumnName())){
            log.error("字段名称必输");
            throw new AnyTxnParameterException(AnyTxnParameterRespCodeEnum.P_PARAMETER_EMPTY_FAULT, ParameterRepDetailEnum.FILE_NAME_NULL);
        }
        if (StringUtils.isEmpty(maintenanceLogDTO.getUpdatedValue())){
            log.error("更新后的值必输");
            throw new AnyTxnParameterException(AnyTxnParameterRespCodeEnum.P_PARAMETER_EMPTY_FAULT, ParameterRepDetailEnum.UP_NULL);
        }
        if (StringUtils.isEmpty(maintenanceLogDTO.getPrimaryKeyValue())){
            log.error("主键值必输");
            throw new AnyTxnParameterException(AnyTxnParameterRespCodeEnum.P_PARAMETER_EMPTY_FAULT, ParameterRepDetailEnum.KEY_NULL);
        }
    }
}
