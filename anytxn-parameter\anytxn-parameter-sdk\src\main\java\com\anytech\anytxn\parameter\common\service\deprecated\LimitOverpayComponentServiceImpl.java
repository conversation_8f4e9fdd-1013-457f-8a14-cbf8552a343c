package com.anytech.anytxn.parameter.common.service.deprecated;

import com.alibaba.fastjson.JSON;
import com.github.pagehelper.Page;
import com.github.pagehelper.PageHelper;
import com.anytech.anytxn.common.core.annotation.db.DeleteParameterAnnotation;
import com.anytech.anytxn.common.core.annotation.db.InsertParameterAnnotation;
import com.anytech.anytxn.common.core.annotation.db.UpdateParameterAnnotation;
import com.anytech.anytxn.business.base.audit.AbstractParameterService;
import com.anytech.anytxn.common.core.base.ParameterCompare;
import com.anytech.anytxn.business.base.audit.ParmModificationRecord;
import com.anytech.anytxn.common.core.base.PageResultDTO;
import com.anytech.anytxn.common.core.utils.BeanMapping;
import com.anytech.anytxn.common.core.utils.OrgNumberUtils;
import com.anytech.anytxn.common.sequence.utils.Number16IdGen;
import com.anytech.anytxn.common.core.utils.TenantUtils;
import com.anytech.anytxn.parameter.base.account.domain.dto.LimitOverpayComponentReqDTO;
import com.anytech.anytxn.parameter.base.account.domain.dto.LimitOverpayComponentResDTO;
import com.anytech.anytxn.parameter.base.account.service.ILimitOverpayComponentService;
import com.anytech.anytxn.parameter.base.common.enums.AnyTxnParameterRespCodeEnum;
import com.anytech.anytxn.parameter.base.common.enums.ParameterRepDetailEnum;
import com.anytech.anytxn.parameter.base.common.exception.AnyTxnParameterException;
import com.anytech.anytxn.parameter.common.mapper.deprecated.ParmLimitOverpayComponentMapper;
import com.anytech.anytxn.parameter.common.mapper.deprecated.ParmLimitOverpayComponentSelfMapper;
import com.anytech.anytxn.parameter.base.account.domain.model.ParmLimitOverpayComponent;
import com.anytech.anytxn.parameter.account.service.AcctLimitCtrlServiceImpl;
import org.apache.commons.lang3.StringUtils;
//import org.apache.shardingsphere.transaction.annotation.ShardingSphereTransactionType;
//import org.apache.shardingsphere.transaction.core.TransactionType;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import java.util.ArrayList;
import java.util.HashSet;
import java.util.List;
import java.util.Set;

/**
 * <AUTHOR>
 * @date 2020-11-30
 */
@Deprecated
@Service(value = "parm_limit_overpay_component_serviceImpl")
public class LimitOverpayComponentServiceImpl extends AbstractParameterService implements ILimitOverpayComponentService {

    private final Logger log = LoggerFactory.getLogger(AcctLimitCtrlServiceImpl.class);
    @Autowired
    private ParmLimitOverpayComponentMapper parmLimitOverpayComponentMapper;
    @Autowired
    private ParmLimitOverpayComponentSelfMapper parmLimitOverpayComponentSelfMapper;
    @Autowired
    private Number16IdGen numberIdGenerator;

    @Override
    public PageResultDTO<LimitOverpayComponentResDTO> findAll(Integer pageNum, Integer pageSize, LimitOverpayComponentReqDTO limitOverpayComponentReqDTO) {
        if(null == limitOverpayComponentReqDTO){
            limitOverpayComponentReqDTO = new LimitOverpayComponentReqDTO();
        }
        limitOverpayComponentReqDTO.setOrganizationNumber(StringUtils.isEmpty(limitOverpayComponentReqDTO.getOrganizationNumber()) ? OrgNumberUtils.getOrg() : limitOverpayComponentReqDTO.getOrganizationNumber());
        Page<ParmLimitOverpayComponent> page = PageHelper.startPage(pageNum, pageSize);
        List<ParmLimitOverpayComponent> limitOverpayComponentList = parmLimitOverpayComponentSelfMapper.selectByCondition(limitOverpayComponentReqDTO);
        if (CollectionUtils.isEmpty(limitOverpayComponentList)) {
            log.error("溢缴款留用组件参数数据不存在");
            limitOverpayComponentList = new ArrayList<>();
        }
        List<LimitOverpayComponentResDTO> res = BeanMapping.copyList(limitOverpayComponentList, LimitOverpayComponentResDTO.class);
        return new PageResultDTO<>(pageNum,pageSize,page.getTotal(), page.getPages(),res);

    }

    @Override
    @InsertParameterAnnotation(tableName = "parm_limit_overpay_component", tableDesc = "溢缴款留用组件参数", isJoinTable = true)
    public ParameterCompare addLimitOverpayComponent(LimitOverpayComponentReqDTO limitOverpayComponentReqDTO) {
        List<ParmLimitOverpayComponent> parmLimitOverpayComponents = parmLimitOverpayComponentSelfMapper.selectByOrgAndTableId(OrgNumberUtils.getOrg(limitOverpayComponentReqDTO.getOrganizationNumber()),
                limitOverpayComponentReqDTO.getTableId());
        if (!CollectionUtils.isEmpty(parmLimitOverpayComponents)) {
            log.error("溢缴款留用组件参数数据已存在");
            throw new AnyTxnParameterException(AnyTxnParameterRespCodeEnum.P_PARAMETER_DATA_EXIST, ParameterRepDetailEnum.LIMIT_OVER_PAY_COMPONENT);
        }
        //如果有溢缴款留用组件，判断是否有重复
        if (!CollectionUtils.isEmpty(limitOverpayComponentReqDTO.getLimitTypeCodes())){
            Set<String> limitTypeCodeSet = new HashSet<>(limitOverpayComponentReqDTO.getLimitTypeCodes());
            int listSize = limitOverpayComponentReqDTO.getLimitTypeCodes().size();
            int setSize = limitTypeCodeSet.size();
            if(listSize != setSize){
                throw new AnyTxnParameterException(AnyTxnParameterRespCodeEnum.D_PARAM_REPEAT_FAULT, ParameterRepDetailEnum.LIMIT_TYPE);
            }
        }

        return ParameterCompare.getBuilder()
                .withMainParmId(limitOverpayComponentReqDTO.getTableId())
                .withAfter(limitOverpayComponentReqDTO)
                .build(LimitOverpayComponentReqDTO.class);
    }

    @Override
    @UpdateParameterAnnotation(tableName = "parm_limit_overpay_component", tableDesc = "溢缴款留用组件参数", isJoinTable = true)
    public ParameterCompare modifyLimitOverpayComponent(LimitOverpayComponentReqDTO limitOverpayComponentReqDTO) {
        List<ParmLimitOverpayComponent> parmLimitOverpayComponents = parmLimitOverpayComponentMapper.selectByTableId(limitOverpayComponentReqDTO.getTableId(), limitOverpayComponentReqDTO.getOrganizationNumber());
        if (CollectionUtils.isEmpty(parmLimitOverpayComponents)) {
            throw new AnyTxnParameterException(AnyTxnParameterRespCodeEnum.D_GET_IS_NULL_FAULT, ParameterRepDetailEnum.LIMIT_OVER_PAY_COMPONENT_NOT_EXIST);
        }
        //如果有溢缴款留用组件，判断是否有重复
        if (!CollectionUtils.isEmpty(limitOverpayComponentReqDTO.getLimitTypeCodes())){
            Set<String> limitTypeCodeSet = new HashSet<>(limitOverpayComponentReqDTO.getLimitTypeCodes());
            int listSize = limitOverpayComponentReqDTO.getLimitTypeCodes().size();
            int setSize = limitTypeCodeSet.size();
            if(listSize != setSize){
                throw new AnyTxnParameterException(AnyTxnParameterRespCodeEnum.D_PARAM_REPEAT_FAULT,  ParameterRepDetailEnum.LIMIT_TYPE);
            }
        }
        limitOverpayComponentReqDTO.setId(String.valueOf(numberIdGenerator.generateId(TenantUtils.getTenantId())));

        return ParameterCompare.getBuilder()
                .withMainParmId(limitOverpayComponentReqDTO.getTableId())
                .withAfter(limitOverpayComponentReqDTO)
                .withBefore(parmLimitOverpayComponents.get(0))
                .build(LimitOverpayComponentReqDTO.class);
    }

    @Override
    public Boolean removeLimitOverpayComponent(String id) {
        ParmLimitOverpayComponent limitOverpayComponent = new ParmLimitOverpayComponent();
        if (null != id) {
            limitOverpayComponent = parmLimitOverpayComponentMapper.selectByPrimaryKey(id);
        }
        if (null == limitOverpayComponent) {
            throw new AnyTxnParameterException(AnyTxnParameterRespCodeEnum.D_GET_IS_NULL_FAULT, ParameterRepDetailEnum.LIMIT_OVER_PAY_COMPONENT_NOT_EXIST);
        }
        int i = parmLimitOverpayComponentMapper.deleteByPrimaryKey(id);
        return i > 0;
    }

    @Override
    public LimitOverpayComponentResDTO findById(String id) {
        if (id == null) {
            throw new AnyTxnParameterException(AnyTxnParameterRespCodeEnum.P_PARAMETER_ID_EMPTY_FAULT, ParameterRepDetailEnum.ID_NULL);
        }
        ParmLimitOverpayComponent acctLimitCtrl = parmLimitOverpayComponentMapper.selectByPrimaryKey(id);
        return BeanMapping.copy(acctLimitCtrl, LimitOverpayComponentResDTO.class);
    }

    @Override
    @DeleteParameterAnnotation(tableName = "parm_limit_overpay_component", tableDesc = "溢缴款留用组件参数", isJoinTable = true)
    public ParameterCompare removeLimitOverpayComponent(String orgNum, String tableId) {
        List<ParmLimitOverpayComponent> parmLimitOverpayComponent = new ArrayList<>();
        if (!StringUtils.isAllBlank(orgNum,tableId)) {
            parmLimitOverpayComponent = parmLimitOverpayComponentMapper.selectByTableId(tableId, orgNum);
        }
        if (CollectionUtils.isEmpty(parmLimitOverpayComponent)) {
            throw new AnyTxnParameterException(AnyTxnParameterRespCodeEnum.D_GET_IS_NULL_FAULT, ParameterRepDetailEnum.LIMIT_OVER_PAY_COMPONENT_NOT_EXIST);
        }
        return ParameterCompare.getBuilder()
                .withMainParmId(tableId)
                .withBefore(parmLimitOverpayComponent.get(0))
                .build(ParmLimitOverpayComponent.class);
    }

    @Override
    public LimitOverpayComponentResDTO findByOrgAndTableId(String orgNum, String tableId) {
        if (StringUtils.isAllBlank(orgNum,tableId)) {
            throw new AnyTxnParameterException(AnyTxnParameterRespCodeEnum.D_INSTALL_TYPE_NECESSITY_FAULT, ParameterRepDetailEnum.O_T_NULL);
        }
        List<ParmLimitOverpayComponent> parmLimitOverpayComponents = parmLimitOverpayComponentMapper.selectByTableId(tableId, orgNum);
        List<String> limitTypeCodes = new ArrayList<>();
        LimitOverpayComponentResDTO limitOverpayComponentResDTO = new LimitOverpayComponentResDTO();

        for (ParmLimitOverpayComponent parmLimitOverpayComponent : parmLimitOverpayComponents) {
            limitTypeCodes.add(parmLimitOverpayComponent.getLimitTypeCode());
            limitOverpayComponentResDTO.setOrganizationNumber(parmLimitOverpayComponent.getOrganizationNumber());
            limitOverpayComponentResDTO.setDescription(parmLimitOverpayComponent.getDescription());
            limitOverpayComponentResDTO.setTableId(parmLimitOverpayComponent.getTableId());
            limitOverpayComponentResDTO.setUpdateBy(parmLimitOverpayComponent.getUpdateBy());
            limitOverpayComponentResDTO.setUpdateTime(parmLimitOverpayComponent.getUpdateTime());
            limitOverpayComponentResDTO.setCreateTime(parmLimitOverpayComponent.getCreateTime());
            limitOverpayComponentResDTO.setVersionNumber(parmLimitOverpayComponent.getVersionNumber());
            limitOverpayComponentResDTO.setLimitTypeCodes(limitTypeCodes);
        }
        log.info("数据{}", JSON.toJSONString(limitOverpayComponentResDTO));
        return limitOverpayComponentResDTO;
    }

    private List<ParmLimitOverpayComponent> addTypeCode(LimitOverpayComponentReqDTO limitOverpayComponentReqDTO){
        List<ParmLimitOverpayComponent> record = new ArrayList<>();
        List<String> limitTypeCode = limitOverpayComponentReqDTO.getLimitTypeCodes();
        for (String typeCode : limitTypeCode) {
            ParmLimitOverpayComponent limitOverpayComponent = BeanMapping.copy(limitOverpayComponentReqDTO, ParmLimitOverpayComponent.class);
            limitOverpayComponent.setId(String.valueOf(numberIdGenerator.generateId(TenantUtils.getTenantId())));
            limitOverpayComponent.initCreateDateTime();
            limitOverpayComponent.initUpdateDateTime();
            limitOverpayComponent.setVersionNumber(1L);
            limitOverpayComponent.setLimitTypeCode(typeCode);
            record.add(limitOverpayComponent);
        }
        return record;
    }

    @Override
    @Transactional(rollbackFor = Exception.class, propagation = Propagation.REQUIRED)
    //@ShardingSphereTransactionType(TransactionType.XA)
    protected boolean updateDb(ParmModificationRecord parmModificationRecord) {
        LimitOverpayComponentReqDTO limitOverpayComponentReqDTO = JSON.parseObject(parmModificationRecord.getParmBody(), LimitOverpayComponentReqDTO.class);
        limitOverpayComponentReqDTO.setUpdateBy(parmModificationRecord.getApplicationBy());
        try {
            parmLimitOverpayComponentMapper.deleteByTableIdAndOrgNumber(limitOverpayComponentReqDTO.getOrganizationNumber(), limitOverpayComponentReqDTO.getTableId());
            parmLimitOverpayComponentMapper.insert(addTypeCode(limitOverpayComponentReqDTO));
        } catch (Exception e) {
            throw new AnyTxnParameterException(AnyTxnParameterRespCodeEnum.D_DATABASE_UPDATE_ERROR, ParameterRepDetailEnum.LIMIT_OVER_PAY_COMPONENT_UPDATE);
        }
        return true;
    }

    @Override
    @Transactional(rollbackFor = Exception.class, propagation = Propagation.REQUIRED)
    //@ShardingSphereTransactionType(TransactionType.XA)
    protected boolean insertDb(ParmModificationRecord parmModificationRecord) {
        LimitOverpayComponentReqDTO limitOverpayComponentReqDTO = JSON.parseObject(parmModificationRecord.getParmBody(), LimitOverpayComponentReqDTO.class);
        limitOverpayComponentReqDTO.setUpdateBy(parmModificationRecord.getApplicationBy());
        try {
            parmLimitOverpayComponentMapper.insert(addTypeCode(limitOverpayComponentReqDTO));
        } catch (Exception e) {
            throw new AnyTxnParameterException(AnyTxnParameterRespCodeEnum.D_DATABASE_UPDATE_ERROR, ParameterRepDetailEnum.LIMIT_OVER_PAY_COMPONENT_ERROR);
        }
        return true;
    }

    @Override
    @Transactional(rollbackFor = Exception.class, propagation = Propagation.REQUIRED)
    //@ShardingSphereTransactionType(TransactionType.XA)
    protected boolean deleteDb(ParmModificationRecord parmModificationRecord) {
        ParmLimitOverpayComponent parmLimitOverpayComponent = JSON.parseObject(parmModificationRecord.getParmBody(), ParmLimitOverpayComponent.class);
        int i = parmLimitOverpayComponentMapper.
                deleteByTableIdAndOrgNumber(parmLimitOverpayComponent.getOrganizationNumber(),
                        parmLimitOverpayComponent.getTableId());
        return i > 0;
    }
}
