package com.anytech.anytxn.parameter.authorization.service;

import com.alibaba.fastjson.JSON;
import com.anytech.anytxn.common.core.base.PageResultDTO;
import com.anytech.anytxn.common.core.utils.BeanMapping;
import com.anytech.anytxn.parameter.base.authorization.domain.dto.CountryMccCheckDTO;
import com.anytech.anytxn.parameter.base.authorization.service.ICountryMccCheckService;
import com.anytech.anytxn.parameter.base.common.enums.AnyTxnParameterRespCodeEnum;
import com.anytech.anytxn.parameter.base.common.exception.AnyTxnParameterException;
import com.anytech.anytxn.parameter.authorization.mapper.ParmCountryMccCheckMapper;
import com.anytech.anytxn.parameter.authorization.mapper.ParmCountryMccCheckSelfMapper;
import com.anytech.anytxn.parameter.base.authorization.domain.model.ParmCountryMccCheck;
import com.github.pagehelper.Page;
import com.github.pagehelper.PageHelper;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.cglib.beans.BeanCopier;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;

/**
 * 国家MCC具体实现类
 *
 * <AUTHOR>
 * @Date 2021/11/6 3:44 PM
 * Version 1.0
 **/
@Service
public class CountryMccCheckServiceImpl implements ICountryMccCheckService {

    private Logger logger = LoggerFactory.getLogger(CountryMccCheckServiceImpl.class);

    @Autowired
    private ParmCountryMccCheckMapper parmCountryMccCheckMapper;

    @Autowired
    private ParmCountryMccCheckSelfMapper parmCountryMccCheckSelfMapper;



    @Override
    public PageResultDTO<CountryMccCheckDTO> findListCountryMccCheck(Integer page, Integer rows) {
        logger.info("分页查询国家mcc检查详细，当前页：{}，每页展示条数：{}", page, rows);
        Page pageInfo =  PageHelper.startPage(page, rows);
        List<CountryMccCheckDTO> countryMccCheckDTOList = null;
        try {
            List<ParmCountryMccCheck> parmCountryMccCheckList = parmCountryMccCheckSelfMapper.selectAll();
            if (!CollectionUtils.isEmpty(parmCountryMccCheckList)) {
                countryMccCheckDTOList = new ArrayList<>();
                BeanCopier beanCopier = BeanCopier.create(ParmCountryMccCheck.class, CountryMccCheckDTO.class, false);
                for (ParmCountryMccCheck parmCountryMccCheck : parmCountryMccCheckList) {
                    CountryMccCheckDTO countryMccCheckDTO = new CountryMccCheckDTO();
                    beanCopier.copy(parmCountryMccCheck, countryMccCheckDTO, null);
                    countryMccCheckDTOList.add(countryMccCheckDTO);
                }
            }
            return new PageResultDTO<>(page, rows, pageInfo.getTotal(), pageInfo.getPages(),countryMccCheckDTOList);
        } catch (Exception e) {
            logger.error("分页查询国家mcc检查详细失败，当前页：{}，每页展示条数：{},错误信息：{}", page, rows, e.getMessage());
            /*throw new AnyTXNBusRuntimeException(TransactionEnum.DATABASE_ERROR.getCode(),
                    TransactionEnum.DATABASE_ERROR.getReason());*/
            throw new AnyTxnParameterException(AnyTxnParameterRespCodeEnum.D_PAGE_MCC_GROUP_DETAIL_FAULT);
        }
    }

    @Override
    public CountryMccCheckDTO findCountryMccCheck(Long id) {

        logger.info("根据主键:{},获取国家mcc详细信息",id);
        CountryMccCheckDTO countryMccCheckDTO = null;
        BigDecimal id1 = new BigDecimal(id);
        try{
            ParmCountryMccCheck parmCountryMccCheck = parmCountryMccCheckMapper.selectByPrimaryKey(id1);
            if (parmCountryMccCheck != null) {
                countryMccCheckDTO = new CountryMccCheckDTO();
                BeanCopier beanCopier = BeanCopier.create(ParmCountryMccCheck.class, CountryMccCheckDTO.class, false);
                beanCopier.copy(countryMccCheckDTO, countryMccCheckDTO, null);
            }
        }
        catch(Exception e){
            logger.error("根据主键:{},获取国家mcc失败,错误信息:{}",id,e.getMessage());
            /*throw new AnyTXNBusRuntimeException(TransactionEnum.DATABASE_ERROR.getCode(),
                    TransactionEnum.DATABASE_ERROR.getReason());*/
            throw new AnyTxnParameterException(AnyTxnParameterRespCodeEnum.D_QUERY_MCC_GROUP_DETAIL_BY_ID_FAULT);

        }
        if (countryMccCheckDTO == null) {
            logger.error("根据主键:{},获取国家mcc详细信息失败", id);

            throw new AnyTxnParameterException(AnyTxnParameterRespCodeEnum.D_QUERY_MCC_GROUP_DETAIL_NULL_BY_ID_FAULT);
        }
        return countryMccCheckDTO;

    }

    @Override
    public Boolean modifyCountryMccCheck(CountryMccCheckDTO countryMccCheckDTO) {

        logger.info("修改国家码MCC详细信息,国家码:{} 商户类别码:{}", countryMccCheckDTO.getNumericCountryCode(), countryMccCheckDTO.getMccCde());
        try {
            ParmCountryMccCheck parmCountryMccCheck= new ParmCountryMccCheck();
            BeanCopier beanCopier = BeanCopier.create(CountryMccCheckDTO.class, ParmCountryMccCheck.class, false);
            beanCopier.copy(countryMccCheckDTO, parmCountryMccCheck, null);
            parmCountryMccCheck.setUpdateTime(LocalDateTime.now());
            return parmCountryMccCheckMapper.updateByPrimaryKeySelective(parmCountryMccCheck) > 0;
        } catch (Exception e) {
            logger.error("调用[{}]更新国家MCC表[{}]失败,错误信息[{}]",
                    "updateByPrimaryKeySelective", "parm_country_mcc_check", e);

            throw new AnyTxnParameterException(AnyTxnParameterRespCodeEnum.D_UPDATE_MCC_GROUP_DETAIL_FAULT);
        }

    }

    @Override
    public Boolean removeCountryMccCheck(Long id) {

        BigDecimal id1 = new BigDecimal(id);
        ParmCountryMccCheck parmCountryMccCheck = parmCountryMccCheckMapper.selectByPrimaryKey(id1);
        logger.info("查询国家MCC详细信息 id:{}", id);
        if (parmCountryMccCheck == null) {
            logger.error("待删除国家MCC不存在。 id:{}", id);
            throw new AnyTxnParameterException(AnyTxnParameterRespCodeEnum.D_QUERY_MCC_GROUP_DETAIL_NULL_BY_ID_FAULT);
        }
        try {
            return parmCountryMccCheckMapper.deleteByPrimaryKey(id1) > 0;
        } catch (Exception e) {
            /*throw new AnyTXNBusRuntimeException(TransactionEnum.DATABASE_ERROR.getCode(),
                    TransactionEnum.DATABASE_ERROR.getReason());*/
            throw new AnyTxnParameterException(AnyTxnParameterRespCodeEnum.D_DELETE_MCC_GROUP_DETAIL_FAULT);
        }

    }

    @Override
    public Boolean addCountryMccCheck(CountryMccCheckDTO countryMccCheckDTO) {

        ParmCountryMccCheck parmCountryMccCheck;
        //转换
        if (countryMccCheckDTO == null) {
            /*throw new AnyTXNRuntimeException(ResultEnum.NOT_EMPTY.getCode(),
                    "mccGroupDetailDTO 不能为空");*/
            throw new AnyTxnParameterException(AnyTxnParameterRespCodeEnum.P_PARAMETER_EMPTY_FAULT);
        }
        parmCountryMccCheck = BeanMapping.copy(countryMccCheckDTO, ParmCountryMccCheck.class);
        int isExists = parmCountryMccCheckSelfMapper.isExists(countryMccCheckDTO.getNumericCountryCode(),countryMccCheckDTO.getMccCde());
        if (isExists > 0) {
            logger.error("国家MCC已存在");
            /*throw new AnyTXNRuntimeException(ResultEnum.DATA_EXISTS.getCode(),
                    "商户类别群详细已存在");*/
            throw new AnyTxnParameterException(AnyTxnParameterRespCodeEnum.D_EXIST_MCC_GROUP_DETAIL_FAULT);
        }
        parmCountryMccCheck.setCreateTime(LocalDateTime.now());
        parmCountryMccCheck.setUpdateTime(LocalDateTime.now());
        parmCountryMccCheck.setUpdateBy("admin");
        try {
            return parmCountryMccCheckMapper.insertSelective(parmCountryMccCheck) > 0;
        } catch (Exception e) {
            logger.error("exception:{}",e);
            /*throw new AnyTXNRuntimeException(TransactionEnum.DATABASE_ERROR.getCode(),
                    TransactionEnum.DATABASE_ERROR.getReason(),e.getCause());*/
            throw new AnyTxnParameterException(AnyTxnParameterRespCodeEnum.D_INSERT_MCC_GROUP_DETAIL_FAULT);
        }

    }

    @Override
    public List<CountryMccCheckDTO> getListByMccCode(String mccCde) {

        List<CountryMccCheckDTO> countryMccCheckDTOList = null;
        try {
            List<ParmCountryMccCheck> parmCountryMccCheckList = parmCountryMccCheckSelfMapper.selectListByMccCde(mccCde);
            if (!CollectionUtils.isEmpty(parmCountryMccCheckList)) {
                countryMccCheckDTOList = new ArrayList<>();
                for (ParmCountryMccCheck parmCountryMccCheck : parmCountryMccCheckList) {
                    CountryMccCheckDTO countryMccCheckDTO = new CountryMccCheckDTO();
                    BeanMapping.copy(parmCountryMccCheck, countryMccCheckDTO);
                    countryMccCheckDTOList.add(countryMccCheckDTO);
                }
            }
        } catch (Exception e) {
            logger.error("国家MCC获取异常,exception:{}",e);
            /*throw new AnyTXNBusRuntimeException(TransactionEnum.DATABASE_ERROR.getCode(),
                    TransactionEnum.DATABASE_ERROR.getReason());*/
            throw new AnyTxnParameterException(AnyTxnParameterRespCodeEnum.D_QUERY_MCC_GROUP_DETAIL_BY_MCC_FAULT);
        }
        return countryMccCheckDTOList;

    }

    @Override
    public List<CountryMccCheckDTO> getListByCountryCode(String countryCode) {

        List<CountryMccCheckDTO> countryMccCheckDTOList = null;
        try {
            logger.info("根据CountryCode id{}",countryCode);
            List<ParmCountryMccCheck> parmCountryMccCheckList = parmCountryMccCheckSelfMapper.selectListByCountryCode(countryCode);
            logger.info("根据CountryCode 查询详情：{}", JSON.toJSON(countryCode));
            if (!CollectionUtils.isEmpty(parmCountryMccCheckList)) {
                countryMccCheckDTOList = new ArrayList<>();
                for (ParmCountryMccCheck parmCountryMccCheck : parmCountryMccCheckList) {
                    CountryMccCheckDTO countryMccCheckDTO = new CountryMccCheckDTO();
                    BeanMapping.copy(parmCountryMccCheck, countryMccCheckDTO);
                    countryMccCheckDTOList.add(countryMccCheckDTO);
                }
            }
            return countryMccCheckDTOList;
        } catch (Exception e) {
            logger.info("出现异常：{}",e.getMessage());
            /*throw new AnyTXNBusRuntimeException(TransactionEnum.DATABASE_ERROR.getCode(),
                    TransactionEnum.DATABASE_ERROR.getReason());*/
            throw new AnyTxnParameterException(AnyTxnParameterRespCodeEnum.D_QUERY_MCC_GROUP_DETAIL_BY_MCC_FAULT);
        }

    }

    @Override
    public Integer modifyCountryMccCheckByCode(CountryMccCheckDTO countryMccCheckDTO) {

        logger.info("修改国家MCC信息,国家码:{} 商户类别码:{}", countryMccCheckDTO.getNumericCountryCode(), countryMccCheckDTO.getMccCde());
        try {
            ParmCountryMccCheck parmCountryMccCheck= new ParmCountryMccCheck();
            BeanCopier beanCopier = BeanCopier.create(CountryMccCheckDTO.class, ParmCountryMccCheck.class, false);
            beanCopier.copy(countryMccCheckDTO, parmCountryMccCheck, null);
            parmCountryMccCheck.setUpdateTime(LocalDateTime.now());
            return parmCountryMccCheckMapper.updateByPrimaryKeySelective(parmCountryMccCheck);
        } catch (Exception e) {
            logger.error("调用[{}]更新商户类别群详细表[{}]失败,错误信息[{}]",
                    "updateByPrimaryKeySelective", "parm_country_mcc_check", e);

            throw new AnyTxnParameterException(AnyTxnParameterRespCodeEnum.D_UPDATE_MCC_GROUP_DETAIL_FAULT);
        }

    }

    @Override
    public boolean deleteByCountryCode(String countryCode) {

        int i = parmCountryMccCheckSelfMapper.deleteByCountryCode(countryCode);
        if(i==0){
            logger.error("根据CountryCode:{}删除失败",countryCode);

            throw new AnyTxnParameterException(AnyTxnParameterRespCodeEnum.D_DELETE_MCC_GROUP_DETAIL_FAULT);
        }
        return true;
    }

}
