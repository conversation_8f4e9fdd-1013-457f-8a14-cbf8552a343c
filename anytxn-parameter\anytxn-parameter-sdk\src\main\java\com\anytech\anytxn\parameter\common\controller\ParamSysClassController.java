package com.anytech.anytxn.parameter.common.controller;

import com.anytech.anytxn.common.core.base.PageResultDTO;
import com.anytech.anytxn.common.core.base.AnyTxnHttpResponse;
import com.anytech.anytxn.common.core.base.BizBaseController;
import com.anytech.anytxn.parameter.base.common.domain.dto.system.ParmSysClassReqDTO;
import com.anytech.anytxn.parameter.base.common.enums.ParameterRepDetailEnum;
import com.anytech.anytxn.parameter.base.common.service.system.IParmSysClassService;
import io.swagger.v3.oas.annotations.tags.Tag;
import io.swagger.v3.oas.annotations.Operation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import jakarta.validation.Valid;

/**
 * <AUTHOR>   fengjun
 * @date :  2022/4/12
 **/
@Tag(name = "参数SysClass服务接口")
@RestController
public class ParamSysClassController extends BizBaseController {

    @Autowired
    private IParmSysClassService parmSysClassService;


    @Operation(summary = "分页查询")
    @PostMapping(value = "/param/sysclass/findByPage")
    public AnyTxnHttpResponse<PageResultDTO<ParmSysClassReqDTO>> findByPage(@RequestParam(value = "pageNum", required = false, defaultValue = "1") Integer pageNum,
                                                                            @RequestParam(value = "pageSize", required = false, defaultValue = "10") Integer pageSize,
                                                                            @RequestBody(required = false) ParmSysClassReqDTO parmSysClassDTO) {
        return AnyTxnHttpResponse.success(parmSysClassService.findByPage(pageNum, pageSize, parmSysClassDTO));

    }


    @Operation(summary = "获取参数详情")
    @GetMapping(value = "/param/sysclass/id/{id}")
    public AnyTxnHttpResponse<ParmSysClassReqDTO> findOne(@PathVariable String id) {
        return AnyTxnHttpResponse.success(parmSysClassService.findOne(id));
    }


    @Operation(summary = "删除参数")
    @DeleteMapping(value = "/param/sysclass/delete/{id}")
    public AnyTxnHttpResponse<Object> remove(@PathVariable String id) {
        return AnyTxnHttpResponse.success(parmSysClassService.remove(id), ParameterRepDetailEnum.DEL.message());

    }


    @Operation(summary = "添加参数")
    @PostMapping(value = "/param/sysclass/save")
    public AnyTxnHttpResponse<Object> create(@Valid @RequestBody ParmSysClassReqDTO parmSysClassDTO) {
        return AnyTxnHttpResponse.success(parmSysClassService.add(parmSysClassDTO), ParameterRepDetailEnum.CREATE.message());
    }

    @Operation(summary = "更新参数")
    @PutMapping("/param/sysclass/update")
    public AnyTxnHttpResponse<Object> update(@RequestBody ParmSysClassReqDTO parmSysClassDTO) {
        return AnyTxnHttpResponse.success(parmSysClassService.update(parmSysClassDTO), ParameterRepDetailEnum.UPDATE.message());
    }

}
