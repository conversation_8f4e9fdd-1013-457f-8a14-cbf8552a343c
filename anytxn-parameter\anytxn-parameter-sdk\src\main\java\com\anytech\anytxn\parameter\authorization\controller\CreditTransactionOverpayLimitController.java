package com.anytech.anytxn.parameter.authorization.controller;

import io.swagger.v3.oas.annotations.tags.Tag;
import com.anytech.anytxn.common.core.base.PageResultDTO;
import com.anytech.anytxn.common.core.utils.OrgNumberUtils;
import com.anytech.anytxn.common.core.base.AnyTxnHttpResponse;
import com.anytech.anytxn.common.core.base.BizBaseController;
import com.anytech.anytxn.parameter.base.authorization.domain.dto.CreditTransactionOverpayLimitDTO;
import com.anytech.anytxn.parameter.base.authorization.service.ICreditTransactionOverpayLimitService;
import com.anytech.anytxn.parameter.base.common.enums.ParameterRepDetailEnum;
import org.springframework.web.bind.annotation.*;

import jakarta.annotation.Resource;

/**
 * Description:贷方交易类型关联溢缴款额度类型参数
 * date: 2021/5/11 17:36
 *
 * <AUTHOR>
 */
@Tag(name = "贷方交易类型关联溢缴款额度类型参数")
@RestController
public class CreditTransactionOverpayLimitController extends BizBaseController {

    @Resource
    private ICreditTransactionOverpayLimitService creditTransactionOverpayLimitService;

    /**
     * 添加
     * @param  dto 贷方交易类型关联溢缴款额度类型参数
     * @return Object
     */
    @PostMapping(value = "/param/addCreditTransactionOverpayLimit")
    public AnyTxnHttpResponse<Object> add(@RequestBody CreditTransactionOverpayLimitDTO dto){
        return AnyTxnHttpResponse.success(creditTransactionOverpayLimitService.add(dto), ParameterRepDetailEnum.CREATE.message());
    }

    /**
     * 修改
     * @param  dto 贷方交易类型关联溢缴款额度类型参数
     * @return Object
     */
    @PutMapping(value = "/param/modifyCreditTransactionOverpayLimit")
    public AnyTxnHttpResponse<Object> modify(@RequestBody CreditTransactionOverpayLimitDTO dto){
        return AnyTxnHttpResponse.success(creditTransactionOverpayLimitService.modify(dto), ParameterRepDetailEnum.UPDATE.message());
    }

    /**
     * 分页查询
     * @param  pageNum pageSize  organizationNumber
     * @return CreditTransactionOverpayLimitDTO
     */
    @GetMapping(value = "/param/creditTransactionOverpayLimit/pageNum/{pageNum}/pageSize/{pageSize}")
    public AnyTxnHttpResponse<PageResultDTO<CreditTransactionOverpayLimitDTO>> findByPage(@PathVariable(value = "pageNum") Integer pageNum,
                                                                                  @PathVariable(value = "pageSize") Integer pageSize,
                                                                                  @RequestParam(value = "tableId",required = false) String tableId,
                                                                                  @RequestParam(value = "description",required = false) String description,
                                                                                  @RequestParam(value = "organizationNumber",required = false) String organizationNumber){
        return AnyTxnHttpResponse.success(creditTransactionOverpayLimitService.findPage(pageNum,pageSize, OrgNumberUtils.getOrg(organizationNumber),tableId, description));
    }

    /**
     * 删除
     * @param  id 贷方交易类型关联溢缴款额度类型参数ID
     * @return Object
     */
    @DeleteMapping(value = "/param/removeCreditTransactionOverpayLimit/id/{id}")
    public AnyTxnHttpResponse<Object> remove(@PathVariable String id){
        return AnyTxnHttpResponse.success(creditTransactionOverpayLimitService.remove(id), ParameterRepDetailEnum.DEL.message());
    }

    /**
     * 通过ID查看
     * @param  id 贷方交易类型关联溢缴款额度类型参数ID
     * @return CreditTransactionOverpayLimitDTO
     */
    @GetMapping(value = "/param/creditTransactionOverpayLimit/id/{id}")
    public AnyTxnHttpResponse<CreditTransactionOverpayLimitDTO> findById(@PathVariable String id){
        return AnyTxnHttpResponse.success(creditTransactionOverpayLimitService.findById(id));
    }
}
