package com.anytech.anytxn.parameter.authorization.service;


import com.alibaba.fastjson.JSON;
import com.github.pagehelper.Page;
import com.github.pagehelper.PageHelper;
import com.anytech.anytxn.common.core.base.PageResultDTO;
import com.anytech.anytxn.common.core.utils.BeanMapping;
import com.anytech.anytxn.common.core.utils.OrgNumberUtils;
import com.anytech.anytxn.parameter.base.authorization.domain.dto.MccGroupDetailDTO;
import com.anytech.anytxn.parameter.base.authorization.service.IMccGroupDetailService;
import com.anytech.anytxn.parameter.base.common.enums.AnyTxnParameterRespCodeEnum;
import com.anytech.anytxn.parameter.base.common.exception.AnyTxnParameterException;
import com.anytech.anytxn.parameter.common.mapper.deprecated.MccGroupDetailMapper;
import com.anytech.anytxn.parameter.common.mapper.deprecated.MccGroupDetailSelfMapper;
import com.anytech.anytxn.parameter.base.authorization.domain.model.ParmMccGroupDetail;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.cglib.beans.BeanCopier;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;


/**
 * @ClassName MccGroupDetailServiceImpl
 * @Description 商户类别群具体实现类
 * <AUTHOR>
 * @Date 2019/4/4 3:44 PM
 * Version 1.0
 **/
@Service
public class MccGroupDetailServiceImpl implements IMccGroupDetailService {

    private Logger logger = LoggerFactory.getLogger(MccGroupDetailServiceImpl.class);

    @Autowired
    private MccGroupDetailMapper mccGroupDetailMapper;
    @Autowired
    private MccGroupDetailSelfMapper mccGroupDetailSelfMapper;


    @Override
    public PageResultDTO<MccGroupDetailDTO> findListMccGroupDetail(Integer page, Integer rows) {
        logger.info("分页查询商户类别群详细，当前页：{}，每页展示条数：{}", page, rows);
        Page pageInfo =  PageHelper.startPage(page, rows);
        List<MccGroupDetailDTO> mccGroupDetailDTOList = null;
        try {
            List<ParmMccGroupDetail> mccGroupDetailList = mccGroupDetailSelfMapper.selectAll(false, OrgNumberUtils.getOrg());
            if (!CollectionUtils.isEmpty(mccGroupDetailList)) {
                mccGroupDetailDTOList = new ArrayList<>();
                BeanCopier beanCopier = BeanCopier.create(ParmMccGroupDetail.class, MccGroupDetailDTO.class, false);
                for (ParmMccGroupDetail mccGroupDetail : mccGroupDetailList) {
                    MccGroupDetailDTO mccGroupDetailDTO = new MccGroupDetailDTO();
                    beanCopier.copy(mccGroupDetail, mccGroupDetailDTO, null);
                    mccGroupDetailDTOList.add(mccGroupDetailDTO);
                }
            }
            return new PageResultDTO<>(page, rows, pageInfo.getTotal(), pageInfo.getPages(),mccGroupDetailDTOList);
        } catch (Exception e) {
            logger.error("分页查询商户类别群详细信息失败，当前页：{}，每页展示条数：{},错误信息：{}", page, rows, e.getMessage());
            /*throw new AnyTXNBusRuntimeException(TransactionEnum.DATABASE_ERROR.getCode(),
                    TransactionEnum.DATABASE_ERROR.getReason());*/
            throw new AnyTxnParameterException(AnyTxnParameterRespCodeEnum.D_PAGE_MCC_GROUP_DETAIL_FAULT);
        }
    }

    @Override
    public MccGroupDetailDTO findMccGroupDetail(Long id) {
        logger.info("根据主键:{},获取商户类别群详细信息",id);
        MccGroupDetailDTO mccGroupDetailDTO = null;
        try{
            ParmMccGroupDetail mccGroupDetail = mccGroupDetailMapper.selectByPrimaryKey(id);
            if (mccGroupDetail != null) {
                mccGroupDetailDTO = new MccGroupDetailDTO();
                BeanCopier beanCopier = BeanCopier.create(ParmMccGroupDetail.class, MccGroupDetailDTO.class, false);
                beanCopier.copy(mccGroupDetail, mccGroupDetailDTO, null);
            }
        }
        catch(Exception e){
            logger.error("根据主键:{},获取商户类别群详细信息失败,错误信息:{}",id,e.getMessage());
            /*throw new AnyTXNBusRuntimeException(TransactionEnum.DATABASE_ERROR.getCode(),
                    TransactionEnum.DATABASE_ERROR.getReason());*/
            throw new AnyTxnParameterException(AnyTxnParameterRespCodeEnum.D_QUERY_MCC_GROUP_DETAIL_BY_ID_FAULT);

        }
        if (mccGroupDetailDTO == null) {
            logger.error("根据主键:{},获取商户类别群详细信息失败", id);

            throw new AnyTxnParameterException(AnyTxnParameterRespCodeEnum.D_QUERY_MCC_GROUP_DETAIL_NULL_BY_ID_FAULT);
        }
        return mccGroupDetailDTO;
    }

    @Override
    public Boolean modifyMccGroupDetail(MccGroupDetailDTO mccGroupDetailDTO) {
        logger.info("修改商户类别群详细信息,商户类别群:{} 商户类别码:{}", mccGroupDetailDTO.getMccGroup(), mccGroupDetailDTO.getMccCde());
        try {
            ParmMccGroupDetail mccGroupDetail= new ParmMccGroupDetail();
            BeanCopier beanCopier = BeanCopier.create(MccGroupDetailDTO.class, ParmMccGroupDetail.class, false);
            beanCopier.copy(mccGroupDetailDTO, mccGroupDetail, null);
            mccGroupDetail.setUpdateTime(LocalDateTime.now());
            return mccGroupDetailMapper.updateByPrimaryKeySelective(mccGroupDetail) > 0;
        } catch (Exception e) {
            logger.error("调用[{}]更新商户类别群详细表[{}]失败,错误信息[{}]",
                    "updateByPrimaryKeySelective", "MCC_GROUP_DETAIL", e);

            throw new AnyTxnParameterException(AnyTxnParameterRespCodeEnum.D_UPDATE_MCC_GROUP_DETAIL_FAULT);
        }
    }

    @Override
    public Boolean removeMccGroupDetail(Long id) {
        ParmMccGroupDetail mccGroupDetail = mccGroupDetailMapper.selectByPrimaryKey(id);
        logger.info("查询商户类别群详细信息 id:{}", id);
        if (mccGroupDetail == null) {
            logger.error("待删除商户类别群详细信息不存在。 id:{}", id);

            throw new AnyTxnParameterException(AnyTxnParameterRespCodeEnum.D_QUERY_MCC_GROUP_DETAIL_NULL_BY_ID_FAULT);
        }
        try {
            return mccGroupDetailMapper.deleteByPrimaryKey(id) > 0;
        } catch (Exception e) {
            /*throw new AnyTXNBusRuntimeException(TransactionEnum.DATABASE_ERROR.getCode(),
                    TransactionEnum.DATABASE_ERROR.getReason());*/
            throw new AnyTxnParameterException(AnyTxnParameterRespCodeEnum.D_DELETE_MCC_GROUP_DETAIL_FAULT);
        }
    }

    @Override
    public Boolean addMccGroupDetail(MccGroupDetailDTO mccGroupDetailDTO) {
        ParmMccGroupDetail mccGroupDetail;
        //转换
        if (mccGroupDetailDTO == null) {
            /*throw new AnyTXNRuntimeException(ResultEnum.NOT_EMPTY.getCode(),
                    "mccGroupDetailDTO 不能为空");*/
            throw new AnyTxnParameterException(AnyTxnParameterRespCodeEnum.P_PARAMETER_EMPTY_FAULT);
        }
        mccGroupDetail = BeanMapping.copy(mccGroupDetailDTO, ParmMccGroupDetail.class);
        int isExists = mccGroupDetailSelfMapper.isExists(mccGroupDetailDTO.getMccGroup(),mccGroupDetailDTO.getMccCde(),mccGroupDetailDTO.getOrganizationNumber());
        if (isExists > 0) {
            logger.error("商户类别群detail已存在");
            /*throw new AnyTXNRuntimeException(ResultEnum.DATA_EXISTS.getCode(),
                    "商户类别群详细已存在");*/
            throw new AnyTxnParameterException(AnyTxnParameterRespCodeEnum.D_EXIST_MCC_GROUP_DETAIL_FAULT);
        }
        mccGroupDetail.setCreateTime(LocalDateTime.now());
        mccGroupDetail.setUpdateTime(LocalDateTime.now());
        mccGroupDetail.setUpdateBy("admin");
        try {
            return mccGroupDetailMapper.insertSelective(mccGroupDetail) > 0;
        } catch (Exception e) {
            logger.error("exception:{}",e);
            /*throw new AnyTXNRuntimeException(TransactionEnum.DATABASE_ERROR.getCode(),
                    TransactionEnum.DATABASE_ERROR.getReason(),e.getCause());*/
            throw new AnyTxnParameterException(AnyTxnParameterRespCodeEnum.D_INSERT_MCC_GROUP_DETAIL_FAULT);
        }
    }

    @Override
    public List<MccGroupDetailDTO> getListByMccCode(String mccCde) {
        List<MccGroupDetailDTO> mccGroupDetailDTOList = null;
        try {
            List<ParmMccGroupDetail> mccGroupDetailList = mccGroupDetailSelfMapper.selectListByMccCde(mccCde,OrgNumberUtils.getOrg());
            if (!CollectionUtils.isEmpty(mccGroupDetailList)) {
                mccGroupDetailDTOList = new ArrayList<>();
                for (ParmMccGroupDetail mccGroupDetail : mccGroupDetailList) {
                    MccGroupDetailDTO mccGroupDetailDTO = new MccGroupDetailDTO();
                    BeanMapping.copy(mccGroupDetail, mccGroupDetailDTO);
                    mccGroupDetailDTOList.add(mccGroupDetailDTO);
                }
            }
        } catch (Exception e) {
            logger.error("mccGroupDetail,exception:{}",e);
            /*throw new AnyTXNBusRuntimeException(TransactionEnum.DATABASE_ERROR.getCode(),
                    TransactionEnum.DATABASE_ERROR.getReason());*/
            throw new AnyTxnParameterException(AnyTxnParameterRespCodeEnum.D_QUERY_MCC_GROUP_DETAIL_BY_MCC_FAULT);
        }
        return mccGroupDetailDTOList;
    }

    @Override
    public List<MccGroupDetailDTO> getListByMccGroup(String mccGroup, String organizationNumber) {
        List<MccGroupDetailDTO> mccGroupDetailDTOList = null;
        try {
            logger.info("根据mccGroup id{}",mccGroup);
            List<ParmMccGroupDetail> mccGroupDetailList = mccGroupDetailSelfMapper.selectListByMccGroup(mccGroup,organizationNumber);
            logger.info("根据mccGroup 查询详情：{}", JSON.toJSON(mccGroup));
            if (!CollectionUtils.isEmpty(mccGroupDetailList)) {
                mccGroupDetailDTOList = new ArrayList<>();
                for (ParmMccGroupDetail mccGroupDetail : mccGroupDetailList) {
                    MccGroupDetailDTO mccGroupDetailDTO = new MccGroupDetailDTO();
                    BeanMapping.copy(mccGroupDetail, mccGroupDetailDTO);
                    mccGroupDetailDTOList.add(mccGroupDetailDTO);
                }
            }
            return mccGroupDetailDTOList;
        } catch (Exception e) {
            logger.info("出现异常：{}",e.getMessage());
            /*throw new AnyTXNBusRuntimeException(TransactionEnum.DATABASE_ERROR.getCode(),
                    TransactionEnum.DATABASE_ERROR.getReason());*/
            throw new AnyTxnParameterException(AnyTxnParameterRespCodeEnum.D_QUERY_MCC_GROUP_DETAIL_BY_MCC_FAULT);
        }
    }

    @Override
    public int modifyMccGroupDetailByCode(MccGroupDetailDTO mccGroupDetailDTO) {
        logger.info("修改商户类别群详细信息,商户类别群:{} 商户类别码:{}", mccGroupDetailDTO.getMccGroup(), mccGroupDetailDTO.getMccCde());
        try {
            ParmMccGroupDetail mccGroupDetail= new ParmMccGroupDetail();
            BeanCopier beanCopier = BeanCopier.create(MccGroupDetailDTO.class, ParmMccGroupDetail.class, false);
            beanCopier.copy(mccGroupDetailDTO, mccGroupDetail, null);
            mccGroupDetail.setUpdateTime(LocalDateTime.now());
            return mccGroupDetailMapper.updateByPrimaryCodeSelective(mccGroupDetail);
        } catch (Exception e) {
            logger.error("调用[{}]更新商户类别群详细表[{}]失败,错误信息[{}]",
                    "updateByPrimaryKeySelective", "MCC_GROUP_DETAIL", e);

            throw new AnyTxnParameterException(AnyTxnParameterRespCodeEnum.D_UPDATE_MCC_GROUP_DETAIL_FAULT);
        }
    }


    @Override
    public boolean deleteByMccGroup(String mccGroup) {
        int i = mccGroupDetailMapper.deleteByMccGroup(mccGroup,OrgNumberUtils.getOrg());
        if(i==0){
            logger.error("根据mccGroup:{}删除失败",mccGroup);

            throw new AnyTxnParameterException(AnyTxnParameterRespCodeEnum.D_DELETE_MCC_GROUP_DETAIL_FAULT);
        }
        return true;
    }
}
