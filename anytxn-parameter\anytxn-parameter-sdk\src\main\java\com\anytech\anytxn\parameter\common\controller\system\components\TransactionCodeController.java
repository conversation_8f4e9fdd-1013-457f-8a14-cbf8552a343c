package com.anytech.anytxn.parameter.common.controller.system.components;

import io.swagger.v3.oas.annotations.tags.Tag;
import io.swagger.v3.oas.annotations.Operation;
import com.anytech.anytxn.common.core.base.PageResultDTO;
import com.anytech.anytxn.common.core.utils.OrgNumberUtils;
import com.anytech.anytxn.common.core.base.AnyTxnHttpResponse;
import com.anytech.anytxn.common.core.base.BizBaseController;
import com.anytech.anytxn.parameter.base.common.constants.Constants;
import com.anytech.anytxn.parameter.base.common.enums.ParameterRepDetailEnum;
import com.anytech.anytxn.parameter.base.common.domain.dto.ParmTransactionCodeResDTO;
import com.anytech.anytxn.parameter.base.common.domain.dto.TransactionCodeReqDTO;
import com.anytech.anytxn.parameter.base.common.domain.dto.TransactionCodeResDTO;
import com.anytech.anytxn.parameter.base.common.service.ITransactionCodeService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.util.StringUtils;
import org.springframework.web.bind.annotation.*;

import jakarta.validation.Valid;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;

/**
 * 交易码管理
 * <AUTHOR>
 * @date 2018-08-14
 **/
@Tag(name = "交易码管理")
@RestController
public class TransactionCodeController extends BizBaseController {

    private final Logger logger = LoggerFactory.getLogger(TransactionCodeController.class);

    @Autowired
    private ITransactionCodeService transactionCodeService;


    /**
     * business需要的接口
     * 获得交易码参数
     * @param transactionCode 交易码
     * @param organizationNumber 机构号
     * @return AnyTxnHttpResponse<TransactionCodeRes>
     */
    @Operation(summary = "获取交易码")
    @GetMapping(value = "/param/transactionCode/organizationNumber/{organizationNumber}/transactionCode/{transactionCode}")
    public AnyTxnHttpResponse<TransactionCodeResDTO> getTransactionCode(@PathVariable(value = "organizationNumber") String organizationNumber ,
                                                                     @PathVariable(value = "transactionCode") String transactionCode) {
        TransactionCodeResDTO transactionCodeRes = transactionCodeService.findTransactionCode(organizationNumber, transactionCode);
        return AnyTxnHttpResponse.success(transactionCodeRes);

    }


    /**
     * 创建交易码
     * @param transactionCodeReq 交易码请求数据
     * @return
     */
    @Operation(summary = "创建交易码")
    @PostMapping("/param/transactionCode")
    public AnyTxnHttpResponse<Object> create(@Valid @RequestBody TransactionCodeReqDTO transactionCodeReq) {
        return AnyTxnHttpResponse.success(transactionCodeService.add(transactionCodeReq), ParameterRepDetailEnum.CREATE.message());

    }

    /**
     * 更新交易码
     * @param req 交易码请求数据
     * @return
     */
    @Operation(summary = "更新交易码")
    @PutMapping("/param/transactionCode")
    public AnyTxnHttpResponse<Object> modify(@Valid @RequestBody TransactionCodeReqDTO req) {
        logger.info("更新交易码, id={}, code={}",
                req.getId(), req.getTransactionCode());
        return AnyTxnHttpResponse.success(transactionCodeService.modify(req),ParameterRepDetailEnum.UPDATE.message());

    }

    /**
     * 删除交易码，通过id
     * @param id 交易码id
     * @return
     */
    @Operation(summary = "删除交易码，通过id")
    @DeleteMapping(value = "/param/transactionCode/id/{id}")
    public AnyTxnHttpResponse<Object> remove(@PathVariable(value = "id") String id) {
        return AnyTxnHttpResponse.success(transactionCodeService.remove(id),ParameterRepDetailEnum.DEL.message());

    }

    /**
     * 通过id获取交易码详情
     * @param id 交易码id
     * @return
     */
    @Operation(summary = "通过id获取交易码详情")
    @GetMapping(value = "/param/transactionCode/id/{id}")
    public AnyTxnHttpResponse<TransactionCodeResDTO> get(@PathVariable String id) {
        TransactionCodeResDTO transactionCodeRes = transactionCodeService.find(id);
        return AnyTxnHttpResponse.success(transactionCodeRes);

    }

    /**
     * 获取所有启用状态的交易码
     * @return
     */
    @Operation(summary = "获取所有启用状态的交易码")
    @GetMapping(value = "/param/transactionCode/list")
    public AnyTxnHttpResponse<ArrayList<TransactionCodeResDTO>> getList(@RequestParam(required = false) String organizationNumber) {
        ArrayList<TransactionCodeResDTO> codeResList = null;
        organizationNumber = StringUtils.isEmpty(organizationNumber) ? OrgNumberUtils.getOrg() : organizationNumber;
        codeResList = (ArrayList)transactionCodeService.findListByOrgNumber(organizationNumber, Constants.ENABLED);
        return AnyTxnHttpResponse.success(codeResList);

    }

    /**
     * 分页查询，当前机构下交易码
     * 机构条件查询可修改dto中的organizationNumber实现
     * @param pageNum 页码
     * @param pageSize 每页大小
     * @return
     */
    @Operation(summary = "分页查询，当前机构下交易码")
    @GetMapping(value = "/param/transactionCode/pageNum/{pageNum}/pageSize/{pageSize}")
    public AnyTxnHttpResponse<PageResultDTO<TransactionCodeResDTO>> getPageByOrgNumber(@PathVariable(value = "pageNum") Integer pageNum,
                                                                                    @PathVariable(value = "pageSize") Integer pageSize,
                                                                                       TransactionCodeReqDTO transactionCodeReqDTO) {
        PageResultDTO<TransactionCodeResDTO> page = null;
        page = transactionCodeService.findPageByOrgNumber(pageNum, pageSize, transactionCodeReqDTO);
        return AnyTxnHttpResponse.success(page);

    }

    /**
     * 获取所有的交易码
     * @return AnyTxnHttpResponse
     */
    @Operation(summary = "获取所有的交易码")
    @GetMapping(value = "/param/transactionCode/all")
    public AnyTxnHttpResponse<List<TransactionCodeResDTO>> getAll(@RequestParam(required = false) String organizationNumber) {
        List<TransactionCodeResDTO> codeResList = transactionCodeService.findAll(organizationNumber);
        return AnyTxnHttpResponse.success(codeResList);

    }

    /**
     * 获取所有借记类型的交易码
     * @return AnyTxnHttpResponse
     */
    @Operation(summary = "获取借记类型的交易码")
    @GetMapping(value = "/param/transactionCode/debit")
    public AnyTxnHttpResponse<List<TransactionCodeResDTO>> getTransactionCode(@RequestParam(required = false) String organizationNumber) {
        List<TransactionCodeResDTO> codeResList = transactionCodeService.findTransctionCode(organizationNumber);
        return AnyTxnHttpResponse.success(codeResList);

    }

    /**
     * 根据固定的借贷记属性，交易属性查询数据
     * @return AnyTxnHttpResponse
     */
    @Operation(summary = "获取所有的交易码")
    @GetMapping(value = "/param/transactionCode/forCurrTimeTranCode")
    public AnyTxnHttpResponse<Map<String, List<ParmTransactionCodeResDTO>>> getDataforCurrTimeTranCode(@RequestParam String organizationNumber) {
        Map<String, List<ParmTransactionCodeResDTO>> map = transactionCodeService.selectDataBydebitAttrAndtransactionAttr(organizationNumber);
        return AnyTxnHttpResponse.success(map);
    }

    /**
     * 根据交易属性和借贷记属性查询各类分期入账交易码
     * @return AnyTxnHttpResponse
     */
    @Operation(summary = "根据交易属性和借贷记属性查询各类分期入账交易码")
    @GetMapping(value = "/param/transactionCode/installCode")
    public AnyTxnHttpResponse<Map<String, List<ParmTransactionCodeResDTO>>> getInstallCode(@RequestParam String organizationNumber) {
        Map<String, List<ParmTransactionCodeResDTO>> map = transactionCodeService.findInstallCode(organizationNumber);
        return AnyTxnHttpResponse.success(map);
    }

    /**
     * 获取交易属性是5、借贷记属性为D的所有交易码参数表
     * @return List<TransactionCodeResDTO>
     */
    @Operation(summary = "获取交易属性是5、借贷记属性为D的所有交易码")
    @GetMapping(value = "/param/transactionCode/startSixAndDebitCreditInd")
    public AnyTxnHttpResponse<ArrayList<TransactionCodeResDTO>> getBySixAndDebitCreditInd(@RequestParam String organizationNumber) {
        ArrayList<TransactionCodeResDTO> codeResList = null;
        codeResList = (ArrayList)transactionCodeService.getBySixAndDebitCreditInd(organizationNumber);
        return AnyTxnHttpResponse.success(codeResList);

    }

}
