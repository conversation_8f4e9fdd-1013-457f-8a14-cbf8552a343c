package com.anytech.anytxn.parameter.common.controller.audit;
import com.anytech.anytxn.common.core.base.PageResultDTO;
import com.anytech.anytxn.common.core.base.AnyTxnHttpResponse;
import com.anytech.anytxn.common.core.base.BizBaseController;
import com.anytech.anytxn.parameter.base.authorization.domain.dto.CmBizCommitAuditDTO;
import com.anytech.anytxn.parameter.base.authorization.domain.dto.CmBizCommitAuditReqDTO;
import com.anytech.anytxn.parameter.base.authorization.service.ICmBizCommitAuditService;
import com.anytech.anytxn.parameter.base.authorization.domain.dto.CmBizCommitAuditPageDTO;
import com.anytech.anytxn.parameter.base.common.enums.AuthStatusEnum;
import io.swagger.v3.oas.annotations.tags.Tag;
import io.swagger.v3.oas.annotations.Operation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

/**
 * <AUTHOR>   fengjun
 * @date :  2022/1/3
 **/
@RestController
@Tag(name = "业务数据审核接口服务")
public class CmBizCommitAuditController extends BizBaseController {


    @Autowired
    private ICmBizCommitAuditService cmBizCommitAuditService;


    @Operation(summary = "分页查审核记录",description = "分页查审核记录")
    @PostMapping(value = "param/biz/auditInfo/pageInfo")
    public AnyTxnHttpResponse<PageResultDTO<CmBizCommitAuditDTO>> getPage(@RequestBody CmBizCommitAuditPageDTO cmBizCommitAuditVO){
        PageResultDTO<CmBizCommitAuditDTO> pageResultDto = cmBizCommitAuditService.findByPage(cmBizCommitAuditVO.getPageNum(),cmBizCommitAuditVO.getPageSize(), cmBizCommitAuditVO);
        return AnyTxnHttpResponse.success(pageResultDto);

    }

    @Operation(summary = "查看审核记录详情",description = "查看审核记录详情")
    @GetMapping(value = "param/biz/auditInfo/{id}")
    public AnyTxnHttpResponse<CmBizCommitAuditDTO> detail(@PathVariable("id") String id){

        return AnyTxnHttpResponse.success(cmBizCommitAuditService.auditDetail(id));

    }

    @Operation(summary = "/拒绝",description = "拒绝")
    @PostMapping(value = "param/biz/auditHandle")
    public AnyTxnHttpResponse<Boolean> auditHandle(@RequestBody CmBizCommitAuditReqDTO cmBizCommitAuditReqDTO){
        return AnyTxnHttpResponse.success(cmBizCommitAuditService.auditHandle(cmBizCommitAuditReqDTO.getCommitAuditId(), AuthStatusEnum.AUTH_REJECT.getCode(),cmBizCommitAuditReqDTO.getRefuseInfo(),cmBizCommitAuditReqDTO.getVersionNumber()));
    }

    @Operation(summary = "获取url",description = "获取跳转的url全路径")
    @GetMapping(value = "param/biz/auditGetUrl/{id}")
    public AnyTxnHttpResponse<String> auditGetUrl(@PathVariable("id")String id){
        return AnyTxnHttpResponse.success(cmBizCommitAuditService.auditGetUrl(id));
    }
}
