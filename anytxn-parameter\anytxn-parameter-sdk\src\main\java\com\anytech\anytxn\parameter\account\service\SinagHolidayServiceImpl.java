package com.anytech.anytxn.parameter.account.service;

import com.alibaba.fastjson.JSON;
import com.anytech.anytxn.common.core.annotation.db.DeleteParameterAnnotation;
import com.anytech.anytxn.common.core.annotation.db.InsertParameterAnnotation;
import com.anytech.anytxn.common.core.annotation.db.UpdateParameterAnnotation;
import com.anytech.anytxn.business.base.audit.AbstractParameterService;
import com.anytech.anytxn.common.core.base.ParameterCompare;
import com.anytech.anytxn.business.base.audit.ParmModificationRecord;
import com.anytech.anytxn.common.core.base.PageResultDTO;
import com.anytech.anytxn.common.core.utils.BeanMapping;
import com.anytech.anytxn.common.core.utils.OrgNumberUtils;
import com.anytech.anytxn.common.sequence.utils.Number16IdGen;
import com.anytech.anytxn.common.core.utils.TenantUtils;
import com.anytech.anytxn.parameter.base.account.domain.dto.ParmChequePaymentTableDTO;
import com.anytech.anytxn.parameter.base.account.service.ISinagHolidayService;
import com.anytech.anytxn.parameter.base.common.enums.AnyTxnParameterRespCodeEnum;
import com.anytech.anytxn.parameter.base.common.enums.ParameterRepDetailEnum;
import com.anytech.anytxn.parameter.base.common.exception.AnyTxnParameterException;
import com.anytech.anytxn.parameter.account.mapper.ParmChequePaymentHolidayListMapper;
import com.anytech.anytxn.parameter.account.mapper.ParmChequePaymentTableMapper;
import com.anytech.anytxn.parameter.base.account.domain.model.ParmAutoPaymentTable;
import com.anytech.anytxn.parameter.base.account.domain.model.ParmChequePaymentHolidayList;
import com.anytech.anytxn.parameter.base.account.domain.model.ParmChequePaymentTable;
import com.anytech.anytxn.parameter.base.common.domain.dto.system.HolidayListResDTO;
import com.github.pagehelper.Page;
import com.github.pagehelper.PageHelper;
import lombok.extern.slf4j.Slf4j;
//import org.apache.shardingsphere.transaction.annotation.ShardingSphereTransactionType;
//import org.apache.shardingsphere.transaction.core.TransactionType;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Slf4j
@Service(value = "parm_cheque_payment_table_serviceImpl")
public class SinagHolidayServiceImpl extends AbstractParameterService implements ISinagHolidayService {

    @Autowired
    private ParmChequePaymentTableMapper parmChequePaymentTableMapper;

    @Autowired
    private Number16IdGen numberIdGenerator;

    @Autowired
    private ParmChequePaymentHolidayListMapper parmChequePaymentHolidayListMapper;

    @Override
    public ParmChequePaymentTableDTO findByTableIdAndOrgNo(String tableId, String organizationNumber) {

        if (tableId == null || StringUtils.isEmpty(organizationNumber)) {
            throw new AnyTxnParameterException(AnyTxnParameterRespCodeEnum.P_PARAMETER_EMPTY_FAULT);
        }
        ParmChequePaymentTable parmChequePaymentTable = parmChequePaymentTableMapper.queryByTableIdAndOrgNo(tableId, organizationNumber);
        if (parmChequePaymentTable == null) {
            throw new AnyTxnParameterException(AnyTxnParameterRespCodeEnum.D_QUERY_PARA_AUTO_PAYMENT_TABLE_FAULT);

        }
        return BeanMapping.copy(parmChequePaymentTable, ParmChequePaymentTableDTO.class);
    }

    @Override
    @InsertParameterAnnotation(tableName = "parm_cheque_payment_table", tableDesc = "Cheque Holiday")
    public ParameterCompare add(ParmChequePaymentTableDTO chequePaymentTableDTO) {
        ParmChequePaymentTable parmChequePaymentTable = parmChequePaymentTableMapper.queryByTableIdAndOrgNo(chequePaymentTableDTO.getTableId(), OrgNumberUtils.getOrg(chequePaymentTableDTO.getOrganizationNumber()));
        if (parmChequePaymentTable != null) {
            log.warn("假日参数信息已存在, TableId={} Organization ={}",
                    chequePaymentTableDTO.getTableId(), OrgNumberUtils.getOrg());
            throw new AnyTxnParameterException(AnyTxnParameterRespCodeEnum.D_EXIST_PARA_AUTO_PAYMENT_TABLE_FAULT);
        }

        ParmChequePaymentTable chequePaymentTable = BeanMapping.copy(chequePaymentTableDTO, ParmChequePaymentTable.class);
        chequePaymentTable.setId(String.valueOf(numberIdGenerator.generateId(TenantUtils.getTenantId())));

        return ParameterCompare.getBuilder()
                .withMainParmId(chequePaymentTable.getTableId())
                .withAfter(chequePaymentTable).build(ParmChequePaymentTable.class);
    }

    @Override
    @UpdateParameterAnnotation(tableName = "parm_cheque_payment_table", tableDesc = "Cheque Holiday")
    public ParameterCompare modify(ParmChequePaymentTableDTO chequePaymentTableDTO) {

        if (chequePaymentTableDTO.getId() == null) {
            throw new AnyTxnParameterException(AnyTxnParameterRespCodeEnum.P_PARAMETER_EMPTY_PARA_AUTO_PAYMENT_TABLE_FAULT);
        }
        ParmChequePaymentTable chequePaymentTable = parmChequePaymentTableMapper.selectByPrimaryKey(chequePaymentTableDTO.getId());

        if (chequePaymentTable == null) {
            log.error("修改支票假日数据, 通过主键id({})未找到数据", chequePaymentTable.getId());
            throw new AnyTxnParameterException(AnyTxnParameterRespCodeEnum.D_MODIFY_PARA_AUTO_PAYMENT_TABLE_BY_ID_FAULT);
        }
        // 拷贝修改的数据并更新
        ParmChequePaymentTable modify = BeanMapping.copy(chequePaymentTableDTO, ParmChequePaymentTable.class);

        return ParameterCompare.getBuilder()
                .withMainParmId(chequePaymentTable.getTableId())
                .withAfter(modify).withBefore(chequePaymentTable).build(ParmChequePaymentTable.class);
    }

    @Override
    @DeleteParameterAnnotation(tableName = "parm_cheque_payment_table", tableDesc = "Cheque Holiday")
    public ParameterCompare remove(String id) {
        if (id == null) {
            throw new AnyTxnParameterException(AnyTxnParameterRespCodeEnum.P_DELETE_EMPTY_PARA_AUTO_PAYMENT_TABLE_FAULT);
        }

        ParmChequePaymentTable chequePaymentTable = parmChequePaymentTableMapper.selectByPrimaryKey(id);
        if (chequePaymentTable == null) {
            log.error("删除支票假日数据, 通过主键id({})未找到数据", id);
            throw new AnyTxnParameterException(AnyTxnParameterRespCodeEnum.D_DELETE_PARA_AUTO_PAYMENT_TABLE_BY_ID_FAULT);
        }

        return ParameterCompare.getBuilder()
                .withMainParmId(chequePaymentTable.getTableId())
                .withBefore(chequePaymentTable).build(ParmAutoPaymentTable.class);
    }

    @Override
    public ParmChequePaymentTableDTO find(String id) {
        if (id == null) {
            throw new AnyTxnParameterException(AnyTxnParameterRespCodeEnum.P_PRIMARY_KEY_NULL_FAULT);
        }

        ParmChequePaymentTable chequePaymentTable = parmChequePaymentTableMapper.selectByPrimaryKey(id);
        if (chequePaymentTable == null) {
            log.error("查询支票假日数据, 通过主键id({})未找到数据", id);
            throw new AnyTxnParameterException(AnyTxnParameterRespCodeEnum.D_DELETE_PARA_AUTO_PAYMENT_TABLE_BY_ID_FAULT);
        }

        // 查询假日表
        List<ParmChequePaymentHolidayList> parmHolidayLists = parmChequePaymentHolidayListMapper.selectByHolidayListId(chequePaymentTable.getHolidayListId(), chequePaymentTable.getOrganizationNumber());
        ParmChequePaymentTableDTO chequePaymentTableDTO = new ParmChequePaymentTableDTO();
        BeanMapping.copy(chequePaymentTable, chequePaymentTableDTO);

        List<LocalDate> holidayDayList = new ArrayList<>();
        chequePaymentTableDTO.setHolidayDayList(holidayDayList);
        for (ParmChequePaymentHolidayList hl : parmHolidayLists) {
            chequePaymentTableDTO.getHolidayDayList().add(hl.getHolidayDay());
        }

        return chequePaymentTableDTO;
    }

    @Override
    public PageResultDTO<ParmChequePaymentTableDTO> findAll(Integer pageNum, Integer pageSize, String tableId, String description, String organizationNumber) {

        Map<String, Object> map = new HashMap<>(8);
        map.put("tableId", tableId);
        map.put("description", description);

        map.put("organizationNumber", StringUtils.isEmpty(organizationNumber) ? OrgNumberUtils.getOrg() : organizationNumber);
        Page<ParmChequePaymentTable> page = PageHelper.startPage(pageNum, pageSize);
        List<ParmChequePaymentTable> allocatedList = parmChequePaymentTableMapper.selectAll(map);
        List<ParmChequePaymentTableDTO> res = BeanMapping.copyList(allocatedList, ParmChequePaymentTableDTO.class);
        return new PageResultDTO<>(pageNum, pageSize, page.getTotal(), page.getPages(), res);
    }

    @Override
    public HolidayListResDTO findByHolidayListId(String holidayListId, String organizationNumber) {
        if (StringUtils.isEmpty(holidayListId)) {
            throw new AnyTxnParameterException(AnyTxnParameterRespCodeEnum.P_PARAMETER_EMPTY_FAULT);
        }

        organizationNumber = StringUtils.isEmpty(organizationNumber) ? OrgNumberUtils.getOrg() : organizationNumber;
        List<ParmChequePaymentHolidayList> ParmHolidayListByHolidayListId = parmChequePaymentHolidayListMapper.selectByHolidayListId(holidayListId, organizationNumber);
        if (CollectionUtils.isEmpty(ParmHolidayListByHolidayListId)) {
            throw new AnyTxnParameterException(AnyTxnParameterRespCodeEnum.D_DATA_NOT_EXISTS_FAULT, ParameterRepDetailEnum.HOLIDAY_NOT_EXIST);
        }
        HolidayListResDTO res = new HolidayListResDTO();
        List<LocalDate> holidayDayList = new ArrayList<>();
        res.setHolidayDayList(holidayDayList);
        for (ParmChequePaymentHolidayList hl : ParmHolidayListByHolidayListId) {
            res.getHolidayDayList().add(hl.getHolidayDay());
        }

        res.setCreateTime(ParmHolidayListByHolidayListId.get(0).getCreateTime());
        res.setDescription(ParmHolidayListByHolidayListId.get(0).getDescription());
        res.setHolidayListId(holidayListId);
        res.setStatus(ParmHolidayListByHolidayListId.get(0).getStatus());
        res.setUpdateTime(ParmHolidayListByHolidayListId.get(0).getUpdateTime());
        res.setUpdateBy(ParmHolidayListByHolidayListId.get(0).getUpdateBy());
        res.setVersionNumber(ParmHolidayListByHolidayListId.get(0).getVersionNumber());
        res.setOrganizationNumber(ParmHolidayListByHolidayListId.get(0).getOrganizationNumber());
        return res;
    }

    @Override
    @Transactional(rollbackFor = Exception.class, propagation = Propagation.REQUIRED)
    //@ShardingSphereTransactionType(TransactionType.XA)
    protected boolean updateDb(ParmModificationRecord parmModificationRecord) throws Exception {

        ParmChequePaymentTable chequePaymentTable = JSON.parseObject(parmModificationRecord.getParmBody(), ParmChequePaymentTable.class);
        chequePaymentTable.setUpdateTime(LocalDateTime.now());
        int i = parmChequePaymentTableMapper.updateByPrimaryKeySelective(chequePaymentTable);

        parmChequePaymentHolidayListMapper.deleteHolidayListByHolidayListId(chequePaymentTable.getHolidayListId(), chequePaymentTable.getOrganizationNumber());

        List<LocalDate> holidayList = chequePaymentTable.getHolidayDayList();
        if (CollectionUtils.isEmpty(holidayList)) {
            log.info("插入错误。listId为空！");
            throw new AnyTxnParameterException(AnyTxnParameterRespCodeEnum.P_PARAMETER_EMPTY_PARA_AUTO_PAYMENT_TABLE_FAULT);
        }
        for (LocalDate date : holidayList) {
            String id = String.valueOf(numberIdGenerator.generateId(TenantUtils.getTenantId()));
            parmChequePaymentHolidayListMapper.addHolidays(id, date, chequePaymentTable.getHolidayListId(), chequePaymentTable);
            i++;
        }

        return i > 0;
    }

    @Override
    @Transactional(rollbackFor = Exception.class, propagation = Propagation.REQUIRED)
    //@ShardingSphereTransactionType(TransactionType.XA)
    protected boolean insertDb(ParmModificationRecord parmModificationRecord) throws Exception {

        ParmChequePaymentTable chequePaymentTable = JSON.parseObject(parmModificationRecord.getParmBody(), ParmChequePaymentTable.class);
        chequePaymentTable.setCreateTime(LocalDateTime.now());
        chequePaymentTable.setUpdateTime(LocalDateTime.now());
        chequePaymentTable.initApplyBy();
        chequePaymentTable.setVersionNumber(1L);
        chequePaymentTable.setHolidayListId(numberIdGenerator.generateId(TenantUtils.getTenantId()) + "");
        int i = parmChequePaymentTableMapper.insertSelective(chequePaymentTable);

        String holidaysId = chequePaymentTable.getHolidayListId();
        if (StringUtils.isEmpty(holidaysId)) {
            log.info("holidayId为空");
            throw new AnyTxnParameterException(AnyTxnParameterRespCodeEnum.P_PARAMETER_EMPTY_FAULT);
        }

        List<LocalDate> holidayList = chequePaymentTable.getHolidayDayList();
        if (CollectionUtils.isEmpty(holidayList)) {
            log.info("插入错误。listId为空！");
            throw new AnyTxnParameterException(AnyTxnParameterRespCodeEnum.P_PARAMETER_EMPTY_FAULT);
        }
        for (LocalDate date : holidayList) {
            String id = String.valueOf(numberIdGenerator.generateId(TenantUtils.getTenantId()));
            parmChequePaymentHolidayListMapper.addHolidays(id, date, holidaysId, chequePaymentTable);
            i++;
        }

        return i > 0;
    }

    @Override
    @Transactional(rollbackFor = Exception.class, propagation = Propagation.REQUIRED)
    //@ShardingSphereTransactionType(TransactionType.XA)
    protected boolean deleteDb(ParmModificationRecord parmModificationRecord) throws Exception {

        ParmChequePaymentTable chequePaymentTable = JSON.parseObject(parmModificationRecord.getParmBody(), ParmChequePaymentTable.class);
        int i = parmChequePaymentTableMapper.deleteByPrimaryKey(chequePaymentTable.getId());
        int y = parmChequePaymentHolidayListMapper.deleteHolidayListByHolidayListId(chequePaymentTable.getHolidayListId(), chequePaymentTable.getOrganizationNumber());
        return (i + y) > 1;
    }
}
