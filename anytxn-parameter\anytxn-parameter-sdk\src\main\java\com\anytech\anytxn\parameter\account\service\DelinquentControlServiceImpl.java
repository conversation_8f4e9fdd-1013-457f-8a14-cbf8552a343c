package com.anytech.anytxn.parameter.account.service;

import com.github.pagehelper.Page;
import com.github.pagehelper.PageHelper;
import com.anytech.anytxn.common.core.utils.OrgNumberUtils;
import com.anytech.anytxn.parameter.base.common.constants.Constants;
import com.anytech.anytxn.common.core.base.PageResultDTO;
import com.anytech.anytxn.common.core.utils.BeanMapping;
import com.anytech.anytxn.common.sequence.utils.Number16IdGen;
import com.anytech.anytxn.common.core.utils.TenantUtils;
import com.anytech.anytxn.parameter.base.account.domain.dto.DelinquentControlReqDTO;
import com.anytech.anytxn.parameter.base.account.domain.dto.DelinquentControlResDTO;
import com.anytech.anytxn.parameter.base.account.service.IDelinquentControlService;
import com.anytech.anytxn.parameter.base.common.enums.AnyTxnParameterRespCodeEnum;
import com.anytech.anytxn.parameter.base.common.exception.AnyTxnParameterException;
import com.anytech.anytxn.parameter.account.mapper.ParmDelinquentControlMapper;
import com.anytech.anytxn.parameter.account.mapper.ParmDelinquentControlSelfMapper;
import com.anytech.anytxn.parameter.base.account.domain.model.ParmDelinquentControl;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.util.List;

/**
 * 延滞控制参数 业务接口实现
 * <AUTHOR> tingting
 * @date 2018/8/21
 */
@Service
public class DelinquentControlServiceImpl implements IDelinquentControlService {

    private Logger log = LoggerFactory.getLogger(DelinquentControlServiceImpl.class);

    @Autowired
    private ParmDelinquentControlMapper parmDelinquentControlMapper;
    @Autowired
    private ParmDelinquentControlSelfMapper parmDelinquentControlSelfMapper;

    @Autowired
    private Number16IdGen numberIdGenerator;

    /**
     * 查询所有延滞控制参数
     * @return 延滞控制参数响应参数
     * @param pageNum
     * @param pageSize
     * @throws AnyTxnParameterException
     *
     */
    @Override
    public PageResultDTO<DelinquentControlResDTO> findAll(Integer pageNum, Integer pageSize){
        Page<ParmDelinquentControl> page = PageHelper.startPage(pageNum, pageSize);
        List<ParmDelinquentControl> delinquentControlList = parmDelinquentControlSelfMapper.selectAll(false, OrgNumberUtils.getOrg());
        if (delinquentControlList.isEmpty()) {
            throw new AnyTxnParameterException(AnyTxnParameterRespCodeEnum.D_GET_IS_NULL_FAULT);
        }
        List<DelinquentControlResDTO> res = BeanMapping.copyList(delinquentControlList, DelinquentControlResDTO.class);
        return new PageResultDTO<>(pageNum,pageSize,page.getTotal(), page.getPages(),res);
    }

    /**
     * 通过Id查询延滞控制参数信息
     * @param id
     * @return DelinquentControlRes
     * @throws AnyTxnParameterException
     */
    @Override
    public DelinquentControlResDTO findById(Long id){
        if (id == null) {
            throw new AnyTxnParameterException(AnyTxnParameterRespCodeEnum.P_PARAMETER_EMPTY_FAULT);
        }

        ParmDelinquentControl parmDelinquentControls = parmDelinquentControlMapper.selectByPrimaryKey(id);
        return BeanMapping.copy(parmDelinquentControls, DelinquentControlResDTO.class);


    }

    /**
     * 添加延滞控制参数
     * @param  delinquentControlReq 延滞控制参数入参对象
     * @return 延滞控制参数响应参数
     * @throws AnyTxnParameterException
     */
    @Override
    public DelinquentControlResDTO addDelinquentControl(DelinquentControlReqDTO delinquentControlReq){
        // 根据机构号和table_id查询该记录是否已经存在
        List<ParmDelinquentControl> list= parmDelinquentControlSelfMapper.selectByOrgAndTableIdAndCycleDue(OrgNumberUtils.getOrg(), delinquentControlReq.getTableId(),delinquentControlReq.getCycleDue());
        if (!list.isEmpty()) {
            throw new AnyTxnParameterException(AnyTxnParameterRespCodeEnum.D_QUERY_PARM_DELINQUENT_CONTROL_FAULT);
        }
        ParmDelinquentControl parmDelinquentControlReq = BeanMapping.copy(delinquentControlReq, ParmDelinquentControl.class);

        // 设置默认值
        parmDelinquentControlReq.setCreateTime(LocalDateTime.now());
        parmDelinquentControlReq.setUpdateTime(LocalDateTime.now());
        parmDelinquentControlReq.setUpdateBy(Constants.DEFAULT_USER);
        parmDelinquentControlReq.setVersionNumber(1L);
        parmDelinquentControlReq.setId(numberIdGenerator.generateId(TenantUtils.getTenantId()));
        parmDelinquentControlMapper.insertSelective(parmDelinquentControlReq);

        return BeanMapping.copy(parmDelinquentControlReq, DelinquentControlResDTO.class);
    }

    /**
     * 更新延滞控制参数
     * @param  delinquentControlReq 延滞控制参数入参对象
     * @return 延滞控制参数响应参数
     * @throws AnyTxnParameterException
     */
    @Override
    public DelinquentControlResDTO modifyDelinquentControl(DelinquentControlReqDTO delinquentControlReq){
        // 查询此记录是否存在
        ParmDelinquentControl delinquentControl = parmDelinquentControlMapper.selectByPrimaryKey(delinquentControlReq.getId());
        if (null == delinquentControl) {
            throw new AnyTxnParameterException(AnyTxnParameterRespCodeEnum.D_QUERY_PARM_DELINQUENT_CONTROL_BY_ID_FAULT);
        }
        // 更新
        delinquentControlReq.setUpdateTime(LocalDateTime.now());
        delinquentControlReq.setUpdateBy(Constants.DEFAULT_USER);
        ParmDelinquentControl parmFeeTableReq = BeanMapping.copy(delinquentControlReq, ParmDelinquentControl.class);
        parmDelinquentControlMapper.updateByPrimaryKeySelective(parmFeeTableReq);

        return BeanMapping.copy(parmFeeTableReq, DelinquentControlResDTO.class);

    }

    /**
     * 通过Id主键删除延滞控制参数
     * @param  id 主键
     * @return Boolean
     * @throws AnyTxnParameterException
     */
    @Override
    public Boolean removeDelinquentControl(Long id){
        ParmDelinquentControl delinquentControl = new ParmDelinquentControl();
        // 如果id不为空，则查询该记录
        if (null != id) {
            delinquentControl = parmDelinquentControlMapper.selectByPrimaryKey(id);
        }
        // 如果该记录不存在，抛出异常，否则就删除该记录
        if (null == delinquentControl) {
            throw new AnyTxnParameterException(AnyTxnParameterRespCodeEnum.D_QUERY_PARM_DELINQUENT_CONTROL_BY_ID_FAULT);
        }
        int i = parmDelinquentControlMapper.deleteByPrimaryKey(id);
        return i > 0;
    }

    /**
     * 通过机构号,tableId,延滞等级查询延滞控制参数
     *
     * @param orgNum   机构号
     * @param tableId  表id
     * @param cycleDue 延滞等级
     * @return List<DelinquentControlRes> 延滞控制参数响应参数list
     */
    @Override
    public List<DelinquentControlResDTO> findDelinquentControl(String orgNum, String tableId, Integer cycleDue){
        List<ParmDelinquentControl> delinquentControlList = parmDelinquentControlSelfMapper.selectByOrgAndTableIdAndCycleDue(orgNum,tableId,cycleDue);
        if (delinquentControlList.isEmpty()) {
            throw new AnyTxnParameterException(AnyTxnParameterRespCodeEnum.D_QUERY_PARM_DELINQUENT_CONTROL_BY_ID_ORG_LEVEL_FAULT);
        }
        return BeanMapping.copyList(delinquentControlList, DelinquentControlResDTO.class);
    }

    /**
     * 通过机构号,tableId,延滞等级查询延滞控制参数
     * @param orgNum   机构号
     * @param tableId  参数表id
     * @param cycleDue 延滞等级
     * @return DelinquentControlResDTO
     */
    @Override
    public DelinquentControlResDTO findByOrgNumTableIdCycleDue(String orgNum, String tableId, Integer cycleDue) {
        ParmDelinquentControl parmDelinquentControl = parmDelinquentControlSelfMapper.selectByOrgNumTableIdCycleDue(orgNum, tableId, cycleDue);
        if (null == parmDelinquentControl){
            throw new AnyTxnParameterException(AnyTxnParameterRespCodeEnum.D_QUERY_PARM_DELINQUENT_CONTROL_BY_ID_ORG_LEVEL_FAULT);
        }
        return BeanMapping.copy(parmDelinquentControl, DelinquentControlResDTO.class);
    }


}
