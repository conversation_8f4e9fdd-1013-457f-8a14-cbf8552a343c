package com.anytech.anytxn.parameter.installment.controller;

import io.swagger.v3.oas.annotations.tags.Tag;
import io.swagger.v3.oas.annotations.Operation;
import com.anytech.anytxn.common.core.base.PageResultDTO;
import com.anytech.anytxn.common.core.base.BizBaseController;
import com.anytech.anytxn.common.core.base.AnyTxnHttpResponse;
import com.anytech.anytxn.parameter.base.common.enums.ParameterRepDetailEnum;
import com.anytech.anytxn.parameter.base.common.exception.AnyTxnParameterException;
import com.anytech.anytxn.parameter.base.installment.domain.dto.InstallEarlyTerminationParmReqDTO;
import com.anytech.anytxn.parameter.base.installment.domain.dto.InstallEarlyTerminationParmResDTO;
import com.anytech.anytxn.parameter.base.installment.service.IInstallEarlyTerminationParmService;
import com.anytech.anytxn.parameter.base.common.utils.AnyTxnHttpResponseHelper;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import jakarta.validation.Valid;

/**
 *分期提前终止参数
 * <AUTHOR>
 * @date 2019-05-14 17:20
 **/
@Tag(name = "分期提前终止参数")
@RestController
public class InstallEarlyTerminationParmController extends BizBaseController {

    @Autowired
    private IInstallEarlyTerminationParmService installEarlyTerminationParmService ;

    /**
     * 新建分期提前终止参数
     * @param  installEarlyTerminationParmReqDTO 分期提前终止参数
     * @return AnyTxnHttpResponse<InstallEarlyTerminationParmResDTO>
     */
    @Operation(summary ="新建分期提前终止参数")
    @PostMapping(value = "/param/installearlyterminationparm")
    public AnyTxnHttpResponse create(@Valid @RequestBody InstallEarlyTerminationParmReqDTO installEarlyTerminationParmReqDTO)
    {
        installEarlyTerminationParmService.add(installEarlyTerminationParmReqDTO);
        return AnyTxnHttpResponseHelper.succ(ParameterRepDetailEnum.CREATE.message());
    }
    /**
     * 修改分期提前终止参数
     * @param  installEarlyTerminationParmReqDTO 分期提前终止参数
     * @return AnyTxnHttpResponse<InstallEarlyTerminationParmResDTO>
     */
    @Operation(summary ="修改分期提前终止参数")
    @PutMapping(value = "/param/installearlyterminationparm")
    public AnyTxnHttpResponse modify(@Valid @RequestBody InstallEarlyTerminationParmReqDTO installEarlyTerminationParmReqDTO)
    {
        installEarlyTerminationParmService.modify(installEarlyTerminationParmReqDTO);
        return AnyTxnHttpResponseHelper.succ(ParameterRepDetailEnum.UPDATE.message());

    }
    /**
     * 删除分期提前终止参数
     * @param  id 分期提前终止参数
     * @return AnyTxnHttpResponse<Boolean>
     */
    @Operation(summary ="删除分期提前终止参数")
    @DeleteMapping(value = "/param/installearlyterminationparm/id/{id}")
    public AnyTxnHttpResponse remove(@PathVariable String id )
    {
        installEarlyTerminationParmService.remove(id);
        return AnyTxnHttpResponseHelper.succ(ParameterRepDetailEnum.DEL.message());
    }
    /**
     * 根据id 查询分期提前终止参数
     * @param  id 分期提前终止参数
     * @return AnyTxnHttpResponse<Boolean>
     */
    @Operation(summary = "根据id查询分期提前终止参数", description = "根据id查询分期提前终止参数")
    @GetMapping(value = "/param/installearlyterminationparm/id/{id}")
    public AnyTxnHttpResponse<InstallEarlyTerminationParmResDTO> getById(@PathVariable String id) throws AnyTxnParameterException {
        InstallEarlyTerminationParmResDTO earlyTerminationParmRes = installEarlyTerminationParmService.getById(id);
        return AnyTxnHttpResponse.success(earlyTerminationParmRes);

    }
    @Operation(summary = "分页查询分期提前终止参数信息", description = "分页分期提前终止参数信息")
    @GetMapping(value = "/param/installearlyterminationparm/pageNum/{pageNum}/pageSize/{pageSize}")
    public AnyTxnHttpResponse<PageResultDTO<InstallEarlyTerminationParmResDTO>> getPage(@PathVariable(value = "pageNum")Integer pageNum,
                                                                                     @PathVariable(value = "pageSize")Integer pageSize,
                                                                                        InstallEarlyTerminationParmReqDTO installEarlyTerminationParmReqDTO) throws AnyTxnParameterException{
        PageResultDTO<InstallEarlyTerminationParmResDTO> pageResultDto = installEarlyTerminationParmService.findPage(pageNum, pageSize,installEarlyTerminationParmReqDTO);
        return AnyTxnHttpResponse.success(pageResultDto);
    }
    /**
     * 根据organizationNumber tableId查询分期提前终止参数
     * @param  organizationNumber tableId分期提前终止参数s
     * @return AnyTxnHttpResponse<Boolean>
     */
    @Operation(summary = "根据机构号 分期提前终止参数表ID 查询", description = "根据机构号 分期提前终止参数表ID 查询")
    @GetMapping(value = "/param/installearlyterminationparm/organizationNumber/{organizationNumber}/tableId/{tableId}")
    public AnyTxnHttpResponse<InstallEarlyTerminationParmResDTO> getByOrgNumAndTableId(@PathVariable String organizationNumber, @PathVariable String tableId) throws AnyTxnParameterException{
        InstallEarlyTerminationParmResDTO earlyTerminationParmRes = installEarlyTerminationParmService.findByIndex(organizationNumber,tableId);
        return AnyTxnHttpResponse.success(earlyTerminationParmRes);

    }
}
