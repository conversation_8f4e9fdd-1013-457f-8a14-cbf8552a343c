package com.anytech.anytxn.parameter.common.service.system;

import com.alibaba.fastjson.JSON;
import com.anytech.anytxn.common.core.annotation.db.UpdateParameterAnnotation;
import com.anytech.anytxn.business.base.audit.AbstractParameterService;
import com.anytech.anytxn.common.core.base.ParameterCompare;
import com.anytech.anytxn.business.base.audit.ParmModificationRecord;
import com.anytech.anytxn.common.sequence.utils.Number16IdGen;
import com.anytech.anytxn.common.core.utils.TenantUtils;
import com.anytech.anytxn.parameter.base.common.enums.AnyTxnParameterRespCodeEnum;
import com.anytech.anytxn.parameter.base.common.exception.AnyTxnParameterException;
import com.anytech.anytxn.parameter.common.mapper.broadcast.system.ParmOrganizationCycleDayMapper;
import com.anytech.anytxn.parameter.common.mapper.broadcast.system.ParmOrganizationCycleDaySelfMapper;
import com.anytech.anytxn.parameter.common.mapper.deprecated.ParmMaintenanceLogMapper;
import com.anytech.anytxn.parameter.base.common.domain.model.system.ParmOrganizationCycleDay;
import com.anytech.anytxn.parameter.base.common.service.system.IOrganizationCycleDayService;
import com.anytech.anytxn.parameter.base.common.domain.dto.system.OrganizationCycleDayDTO;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.ObjectUtils;
//import org.apache.shardingsphere.transaction.annotation.ShardingSphereTransactionType;
//import org.apache.shardingsphere.transaction.core.TransactionType;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;
import java.util.*;

/**
 * Description:
 * date: 2021/4/23 9:18
 *
 * <AUTHOR>
 */
@Slf4j
@Service("parm_organization_cycle_day_serviceImpl")
public class OrganizationCycleDayServiceImpl extends AbstractParameterService implements IOrganizationCycleDayService {

    @Autowired
    private ParmOrganizationCycleDayMapper cycleDayMapper;
    @Autowired
    private ParmOrganizationCycleDaySelfMapper cycleDaySelfMapper;
    @Autowired
    private ParmMaintenanceLogMapper parmMaintenanceLogMapper;
    @Autowired
    private Number16IdGen numberIdGenerator;

    @Override
    @UpdateParameterAnnotation(tableName = "parm_organization_cycle_day",tableDesc = "Organization Cycle Day",isJoinTable = true)
    public ParameterCompare saveOrgCycleDay(OrganizationCycleDayDTO orgCycleDay) {
        OrganizationCycleDayDTO before = findOrgCycleDay(orgCycleDay.getOrganizationNumber());
        if (ObjectUtils.isEmpty(before)){
            throw new AnyTxnParameterException(AnyTxnParameterRespCodeEnum.P_PARAMETER_DATA_NOT_EXIST);
        }
        return ParameterCompare
                .getBuilder()
                .withMainParmId(orgCycleDay.getOrganizationNumber())
                .withAfter(orgCycleDay)
                .withBefore(before)
                .build(OrganizationCycleDayDTO.class);
    }

    @Override
    public OrganizationCycleDayDTO findOrgCycleDay(String orgNum) {
        OrganizationCycleDayDTO dto = new OrganizationCycleDayDTO();
        dto.setOrganizationNumber(orgNum);
        List<ParmOrganizationCycleDay> list = cycleDaySelfMapper.selectByOrgNumber(orgNum);
        Set<Integer> days = new HashSet<>();
        list.forEach(it->{
            days.add(it.getCycleDay());
        });
        dto.setCycleDay(days);
        return dto;
    }

    @Override
    @Transactional(rollbackFor = Exception.class,propagation = Propagation.REQUIRED)
    //@ShardingSphereTransactionType(TransactionType.XA)
    protected boolean updateDb(ParmModificationRecord parmModificationRecord) {
        try {
            OrganizationCycleDayDTO organizationCycleDayDTO = JSON.parseObject(parmModificationRecord.getParmBody(), OrganizationCycleDayDTO.class);
            List<ParmOrganizationCycleDay> parmOrganizationCycleDays = cycleDaySelfMapper.selectByOrgNumber(organizationCycleDayDTO.getOrganizationNumber());
            cycleDaySelfMapper.deleteByOrgNumber(organizationCycleDayDTO.getOrganizationNumber());
            Set<Integer> days = organizationCycleDayDTO.getCycleDay();
            for (Integer day : days) {
                ParmOrganizationCycleDay record = new ParmOrganizationCycleDay();
                record.setId(numberIdGenerator.generateId(TenantUtils.getTenantId())+"");
                record.initCreateDateTime();
                record.setCycleDay(day);
                record.setOrganizationNumber(organizationCycleDayDTO.getOrganizationNumber());
                record.setStatus("1");
                cycleDayMapper.insert(record);
            }
        } catch (Exception e) {
            log.error("修改有效账单日失败：{}",e.getMessage());
            throw new AnyTxnParameterException(AnyTxnParameterRespCodeEnum.D_DATABASE_UPDATE_ERROR);
        }
        return true;
    }

    @Override
    @Transactional(rollbackFor = Exception.class,propagation = Propagation.REQUIRED)
    //@ShardingSphereTransactionType(TransactionType.XA)
    protected boolean insertDb(ParmModificationRecord parmModificationRecord) {
        return false;
    }

    @Override
    @Transactional(rollbackFor = Exception.class,propagation = Propagation.REQUIRED)
    //@ShardingSphereTransactionType(TransactionType.XA)
    protected boolean deleteDb(ParmModificationRecord parmModificationRecord) {
        return false;
    }
}
