package com.anytech.anytxn.parameter.card.service;

import com.alibaba.fastjson.JSON;
import com.anytech.anytxn.parameter.base.card.domain.dto.CardFaceLayoutInfoDTO;
import com.anytech.anytxn.parameter.base.card.service.ICardFaceLayoutInfoService;
import com.github.pagehelper.Page;
import com.github.pagehelper.PageHelper;
import com.anytech.anytxn.common.core.annotation.db.DeleteParameterAnnotation;
import com.anytech.anytxn.common.core.annotation.db.InsertParameterAnnotation;
import com.anytech.anytxn.common.core.annotation.db.UpdateParameterAnnotation;
import com.anytech.anytxn.business.base.audit.AbstractParameterService;
import com.anytech.anytxn.common.core.base.ParameterCompare;
import com.anytech.anytxn.business.base.audit.ParmModificationRecord;
import com.anytech.anytxn.common.core.utils.OrgNumberUtils;
import com.anytech.anytxn.common.core.base.PageResultDTO;
import com.anytech.anytxn.common.core.utils.BeanMapping;
import com.anytech.anytxn.common.sequence.utils.Number16IdGen;
import com.anytech.anytxn.common.core.utils.TenantUtils;
import com.anytech.anytxn.parameter.base.card.domain.dto.CardFaceReqDTO;
import com.anytech.anytxn.parameter.base.card.domain.dto.CardFaceResDTO;
import com.anytech.anytxn.parameter.base.card.service.ICardFaceService;
import com.anytech.anytxn.parameter.base.common.enums.AnyTxnParameterRespCodeEnum;
import com.anytech.anytxn.parameter.base.common.exception.AnyTxnParameterException;
import com.anytech.anytxn.parameter.card.mapper.broadcast.ParmCardFaceMapper;
import com.anytech.anytxn.parameter.card.mapper.broadcast.ParmCardFaceSelfMapper;
import com.anytech.anytxn.parameter.base.card.domain.model.ParmCardFace;
//import org.apache.shardingsphere.transaction.annotation.ShardingSphereTransactionType;
//import org.apache.shardingsphere.transaction.core.TransactionType;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;
import org.springframework.util.ObjectUtils;
import org.springframework.util.StringUtils;

import java.util.ArrayList;
import java.util.List;

/**
 * 卡版参数
 *
 * <AUTHOR>
 * @date 2018-12-27
 **/
@Service(value = "parm_card_face_serviceImpl")
public class CardFaceServiceImpl extends AbstractParameterService implements ICardFaceService {

    private Logger logger= LoggerFactory.getLogger(CardFaceServiceImpl.class);

    @Autowired
    private ParmCardFaceSelfMapper parmCardFaceSelfMapper;
    @Autowired
    private ParmCardFaceMapper parmCardFaceMapper;
    @Autowired
    private Number16IdGen numberIdGenerator;

    @Autowired
    private ICardFaceLayoutInfoService cardFaceLayoutInfoService;


    /**
     * 根据机构号、参数表id查询卡版参数
     * @param organizationNumber 机构号
     * @param tableId 参数表id
     * @return ParmCardFace
     */
    @Override
    public CardFaceResDTO findByOrgAndTableId(String organizationNumber, String tableId) {
        ParmCardFace parmCardFace = parmCardFaceSelfMapper.selectByOrgAndTableId(organizationNumber, tableId);
        if (parmCardFace==null) {
            logger.error("获取卡版参数, 通过organizationNumber={}, tableId={} 未查到数据", organizationNumber, tableId);
            throw new AnyTxnParameterException(AnyTxnParameterRespCodeEnum.D_QUERY_PARM_CARD_FACE_FAULT);
        }
        CardFaceResDTO cardFaceResDTO = BeanMapping.copy(parmCardFace, CardFaceResDTO.class);
        List<CardFaceLayoutInfoDTO> cardFaceLayoutInfoDTOS = cardFaceLayoutInfoService.queryByOrgAndFaceId(parmCardFace.getOrganizationNumber(), parmCardFace.getTableId());
        cardFaceResDTO.setCardFaceLayoutInfoDTOList(cardFaceLayoutInfoDTOS);
        return cardFaceResDTO;
    }

    /**
     * 新增
     *
     * @param cardFaceReqDTO 传入数据
     * @return CardFaceResDTO
     */
    @Override
    @Transactional
    @InsertParameterAnnotation(tableName = "parm_card_face", tableDesc = "Card face", isJoinTable = true)
    public ParameterCompare add(CardFaceReqDTO cardFaceReqDTO) {
        if (cardFaceReqDTO == null) {
            logger.error("传入参数不能为空，卡版参数新增接口");
            throw new AnyTxnParameterException(AnyTxnParameterRespCodeEnum.P_PARAMETER_EMPTY_FAULT);
        }
        if (cardFaceReqDTO.getOrganizationNumber() == null) {
            cardFaceReqDTO.setOrganizationNumber(OrgNumberUtils.getOrg());
        }
        if (parmCardFaceSelfMapper.isExists(cardFaceReqDTO.getOrganizationNumber(), cardFaceReqDTO.getTableId()) > 0) {
            logger.error("卡版参数已存在,机构号{}，参数表id{}",cardFaceReqDTO.getOrganizationNumber(), cardFaceReqDTO.getTableId());
            throw new AnyTxnParameterException(AnyTxnParameterRespCodeEnum.D_EXIST_QUERY_PARM_CARD_FACE_FAULT);
        }

        //新增卡版版面信息
        cardFaceLayoutInfoService.addCardFaceLayoutCodes(cardFaceReqDTO);
        cardFaceReqDTO.setId(String.valueOf(numberIdGenerator.generateId(TenantUtils.getTenantId())));
        return ParameterCompare.getBuilder()
                .withMainParmId(cardFaceReqDTO.getTableId())
                .withAfter(cardFaceReqDTO).build(CardFaceReqDTO.class);
    }



    /**
     * 删除
     *
     * @param id id
     * @return CardFaceResDTO
     */
    @Override
    @Transactional
    @DeleteParameterAnnotation(tableName = "parm_card_face", tableDesc = "Card face", isJoinTable = true)
    public ParameterCompare removeById(String id) {
        if (id == null) {
            logger.error("传入参数不能为空，卡版参数删除接口");
            throw new AnyTxnParameterException(AnyTxnParameterRespCodeEnum.P_PARAMETER_EMPTY_FAULT);
        }
        ParmCardFace parmCardFace = parmCardFaceMapper.selectByPrimaryKey(id);
        if (parmCardFace == null) {
            logger.error("待删除卡版参数不存在，id:{}",id);
            throw new AnyTxnParameterException(AnyTxnParameterRespCodeEnum.D_QUERY_PARM_CARD_FACE_BY_ID_FAULT);
        }

        //删除卡版版面信息
        cardFaceLayoutInfoService.deleteByOrgAndFaceId(parmCardFace.getOrganizationNumber(),parmCardFace.getTableId());
        return ParameterCompare.getBuilder()
                .withMainParmId(parmCardFace.getTableId())
                .withBefore(parmCardFace).build(ParmCardFace.class);
    }

    @Override
    public Boolean removeByOrgAndTableId(String organizationNumber, String tableId) {

        if (StringUtils.isEmpty(organizationNumber) || StringUtils.isEmpty(tableId)) {
            logger.error("传入参数不能为空，卡版参数删除接口");
            throw new AnyTxnParameterException(AnyTxnParameterRespCodeEnum.P_PARAMETER_EMPTY_FAULT);
        }

        ParmCardFace parmCardFace = parmCardFaceSelfMapper.selectByOrgAndTableId(organizationNumber,tableId);

        if (parmCardFace == null) {
            logger.error("待删除卡版参数不存在,Org:{},tableId:{}",organizationNumber,tableId);
            throw new AnyTxnParameterException(AnyTxnParameterRespCodeEnum.D_QUERY_PARM_CARD_FACE_BY_ID_FAULT);
        }

        int count = parmCardFaceMapper.deleteByPrimaryKey(parmCardFace.getId());

        if (count < 1) {
            logger.warn("未能成功删除该卡版参数，id:{}",parmCardFace.getId());
            throw new AnyTxnParameterException(AnyTxnParameterRespCodeEnum.S_DELETE_PARM_CARD_FACE_FAULT);
        }

        //删除卡版版面信息
        cardFaceLayoutInfoService.deleteByOrgAndFaceId(parmCardFace.getOrganizationNumber(),parmCardFace.getTableId());
        return true;
    }

    /**
     * 修改
     *
     * @param cardFaceReqDTO 传入数据
     * @return CardFaceResDTO
     */
    @Override
    @Transactional
    @UpdateParameterAnnotation(tableName = "parm_card_face", tableDesc = "Card face", isJoinTable = true)
    public ParameterCompare modify(CardFaceReqDTO cardFaceReqDTO) {
        if (cardFaceReqDTO == null) {
            logger.error("传入参数不能为空，卡版参数修改接口");
            throw new AnyTxnParameterException(AnyTxnParameterRespCodeEnum.P_PARAMETER_EMPTY_FAULT);
        }
        ParmCardFace oldParmCardFace = parmCardFaceMapper.selectByPrimaryKey(cardFaceReqDTO.getId());
        if (!oldParmCardFace.getOrganizationNumber().equals(cardFaceReqDTO.getOrganizationNumber()) ||
                !oldParmCardFace.getTableId().equals(cardFaceReqDTO.getTableId())) {
            Boolean ex = parmCardFaceSelfMapper.isExists(cardFaceReqDTO.getOrganizationNumber(), cardFaceReqDTO.getTableId()) > 0;
            if (ex) {
                logger.error("卡版参数已存在,机构号{}，参数表id{}", cardFaceReqDTO.getOrganizationNumber(), cardFaceReqDTO.getTableId());
                throw new AnyTxnParameterException(AnyTxnParameterRespCodeEnum.D_EXIST_QUERY_PARM_CARD_FACE_FAULT);
            }
        }

        //修改卡版信息
        //先删除卡版参数下所有卡版半年面信息，再新增
        cardFaceLayoutInfoService.deleteByOrgAndFaceId(cardFaceReqDTO.getOrganizationNumber(),cardFaceReqDTO.getTableId());
        if(!CollectionUtils.isEmpty(cardFaceReqDTO.getCardFaceLayoutInfoDTOList())){
            cardFaceLayoutInfoService.addCardFaceLayoutCodes(cardFaceReqDTO);
        }
        ParmCardFace parmFace = BeanMapping.copy(cardFaceReqDTO, ParmCardFace.class);
        return ParameterCompare.getBuilder()
                .withMainParmId(cardFaceReqDTO.getTableId())
                .withAfter(parmFace)
                .withBefore(cardFaceReqDTO)
                .build(CardFaceReqDTO.class);
    }

    /**
     * 根据id查询
     *
     * @param id id
     * @return CardFaceResDTO
     */
    @Override
    //@PreGetProcess(byPrimaryKey = true)
    public CardFaceResDTO findById(String id) {

        if (id == null) {
            logger.error("传入参数不能为空，卡版参数根据id查询接口");
            throw new AnyTxnParameterException(AnyTxnParameterRespCodeEnum.P_PARAMETER_EMPTY_FAULT);
        }

        ParmCardFace parmCardFace = parmCardFaceMapper.selectByPrimaryKey(id);

        if (parmCardFace == null) {
            logger.error("卡版参数不存在，id:{}",id);
            throw new AnyTxnParameterException(AnyTxnParameterRespCodeEnum.D_QUERY_PARM_CARD_FACE_BY_ID_FAULT);
        }
        CardFaceResDTO cardFaceResDTO = BeanMapping.copy(parmCardFace, CardFaceResDTO.class);
        List<CardFaceLayoutInfoDTO> cardFaceLayoutInfoDTOS = cardFaceLayoutInfoService.queryByOrgAndFaceId(parmCardFace.getOrganizationNumber(), parmCardFace.getTableId());
        cardFaceResDTO.setCardFaceLayoutInfoDTOList(cardFaceLayoutInfoDTOS);
        return cardFaceResDTO;
    }

    /**
     * 分页
     *
     * @param pageNum  页码
     * @param pageSize 页面大小
     * @return PageResultDTO<CardFaceResDTO>
     */
    @Override
    public PageResultDTO<CardFaceResDTO> findList(Integer pageNum, Integer pageSize,CardFaceReqDTO cardFaceReqDTO) {
        if(null == cardFaceReqDTO){
            cardFaceReqDTO = new CardFaceReqDTO();
        }
        Page<ParmCardFace> page = PageHelper.startPage(pageNum, pageSize);
        cardFaceReqDTO.setOrganizationNumber(StringUtils.isEmpty(cardFaceReqDTO.getOrganizationNumber()) ? OrgNumberUtils.getOrg() : cardFaceReqDTO.getOrganizationNumber());
        List<ParmCardFace> parmCardFaceList = parmCardFaceSelfMapper.selectByCondition(cardFaceReqDTO);
        List<CardFaceResDTO> cardFaceResDTOList = BeanMapping.copyList(parmCardFaceList, CardFaceResDTO.class);
        if(!StringUtils.isEmpty(cardFaceReqDTO.getTableId())) {
            if (!CollectionUtils.isEmpty(cardFaceResDTOList)) {
                for (CardFaceResDTO parmCardFaceDto : cardFaceResDTOList) {
                    List<CardFaceLayoutInfoDTO> cardFaceLayoutInfoDTOS = cardFaceLayoutInfoService.queryByOrgAndFaceId(parmCardFaceDto.getOrganizationNumber(),
                            parmCardFaceDto.getTableId());
                    parmCardFaceDto.setCardFaceLayoutInfoDTOList(CollectionUtils.isEmpty(cardFaceLayoutInfoDTOS) ? null : cardFaceLayoutInfoDTOS);
                }
            }
        }

        return new PageResultDTO<>(pageNum,pageSize,page.getTotal(),page.getPages(),cardFaceResDTOList);
    }

    @Override
    public List<CardFaceResDTO> findList(String state) {
        List<ParmCardFace> parmCardFaceList = parmCardFaceSelfMapper.selectAllByStatus(state,false, OrgNumberUtils.getOrg());
        if (parmCardFaceList == null || parmCardFaceList.isEmpty()) {
            logger.error("卡版参数查询为空");
            throw new AnyTxnParameterException(AnyTxnParameterRespCodeEnum.S_GET_PARM_CARD_ISSUE_FAULT);
        }
        List<CardFaceResDTO> cardFaceResDTOList = BeanMapping.copyList(parmCardFaceList, CardFaceResDTO.class);
        if(!CollectionUtils.isEmpty(cardFaceResDTOList)){
            for (CardFaceResDTO cardFaceResDTO : cardFaceResDTOList) {
                List<CardFaceLayoutInfoDTO> cardFaceLayoutInfoDTOS = cardFaceLayoutInfoService.queryByOrgAndFaceId(cardFaceResDTO.getOrganizationNumber(), cardFaceResDTO.getTableId());
                cardFaceResDTO.setCardFaceLayoutInfoDTOList(CollectionUtils.isEmpty(cardFaceLayoutInfoDTOS) ? null : cardFaceLayoutInfoDTOS);
            }
        }
        return cardFaceResDTOList;
    }

    @Override
    @Transactional(rollbackFor = Exception.class, propagation = Propagation.REQUIRED)
    //@ShardingSphereTransactionType(TransactionType.XA)
    protected boolean updateDb(ParmModificationRecord parmModificationRecord) {
        CardFaceReqDTO cardFaceReqDTO = JSON.parseObject(parmModificationRecord.getParmBody(), CardFaceReqDTO.class);
        ParmCardFace parmCardFace = BeanMapping.copy(cardFaceReqDTO, ParmCardFace.class);
        parmCardFace.initUpdateDateTime();
        parmCardFace.setUpdateBy(parmModificationRecord.getApplicationBy());
        int count = parmCardFaceMapper.updateByPrimaryKeySelective(parmCardFace);
        if (count < 1) {
            logger.warn("未能成功修改该卡版参数,机构号{}，参数表id{}", cardFaceReqDTO.getOrganizationNumber(), cardFaceReqDTO.getTableId());
            throw new AnyTxnParameterException(AnyTxnParameterRespCodeEnum.S_MODIFY_PARM_CARD_FACE_FAULT);
        }
        cardFaceLayoutInfoService.deleteByOrgAndFaceId(cardFaceReqDTO.getOrganizationNumber(), cardFaceReqDTO.getTableId());
        cardFaceLayoutInfoService.addCardFaceLayoutCodes(cardFaceReqDTO);
        return true;
    }

    @Override
    @Transactional(rollbackFor = Exception.class, propagation = Propagation.REQUIRED)
    //@ShardingSphereTransactionType(TransactionType.XA)
    protected boolean insertDb(ParmModificationRecord parmModificationRecord) {
        CardFaceReqDTO cardFaceReqDTO = JSON.parseObject(parmModificationRecord.getParmBody(), CardFaceReqDTO.class);
        ParmCardFace parmCardFace = BeanMapping.copy(cardFaceReqDTO, ParmCardFace.class);
        parmCardFace.initCreateDateTime();
        parmCardFace.initUpdateDateTime();
        parmCardFace.setUpdateBy(parmModificationRecord.getApplicationBy());
        int count = parmCardFaceMapper.insertSelective(parmCardFace);
        if (count < 1) {
            logger.warn("未能成功新增该卡版参数,机构号{}，参数表id{}",cardFaceReqDTO.getOrganizationNumber(), cardFaceReqDTO.getTableId());
            throw new AnyTxnParameterException(AnyTxnParameterRespCodeEnum.S_INSERT_PARM_CARD_FACE_FAULT);
        }
        cardFaceLayoutInfoService.addCardFaceLayoutCodes(cardFaceReqDTO);
        return true;
    }

    @Override
    @Transactional(rollbackFor = Exception.class, propagation = Propagation.REQUIRED)
    //@ShardingSphereTransactionType(TransactionType.XA)
    protected boolean deleteDb(ParmModificationRecord parmModificationRecord) {
        ParmCardFace parmCardFace = JSON.parseObject(parmModificationRecord.getParmBody(), ParmCardFace.class);
        int count = parmCardFaceMapper.deleteByPrimaryKey(parmCardFace.getId());
        if (count < 1) {
            logger.warn("未能成功删除该卡版参数，id:{}", parmCardFace.getId());
            throw new AnyTxnParameterException(AnyTxnParameterRespCodeEnum.S_DELETE_PARM_CARD_FACE_FAULT);
        }
        cardFaceLayoutInfoService.deleteByOrgAndFaceId(parmCardFace.getOrganizationNumber(), parmCardFace.getTableId());
        return true;
    }


    /**
     * 根据参数表Id,查询卡版参数
     * @param tableId
     * @return
     */
    @Override
    public List<CardFaceLayoutInfoDTO> getLayoutCode(String tableId) {
        List<CardFaceLayoutInfoDTO> layoutCodeList = new ArrayList<>();
        CardFaceResDTO cardFaceResDTO = findByOrgAndTableId(OrgNumberUtils.getOrg(), tableId);
        if(!ObjectUtils.isEmpty(cardFaceResDTO)&&!CollectionUtils.isEmpty(cardFaceResDTO.getCardFaceLayoutInfoDTOList())){
            List<CardFaceLayoutInfoDTO> cardFaceLayoutInfoDTOList = cardFaceResDTO.getCardFaceLayoutInfoDTOList();
            for (CardFaceLayoutInfoDTO cardFaceLayoutInfoDTO : cardFaceLayoutInfoDTOList) {
                CardFaceLayoutInfoDTO layoutInfoDTO = new CardFaceLayoutInfoDTO();
                layoutInfoDTO.setDescription(cardFaceLayoutInfoDTO.getDescription());
                layoutInfoDTO.setCardLayoutCode(cardFaceLayoutInfoDTO.getCardLayoutCode());
                layoutCodeList.add(layoutInfoDTO);
            }
        }
        return layoutCodeList;
    }
}
