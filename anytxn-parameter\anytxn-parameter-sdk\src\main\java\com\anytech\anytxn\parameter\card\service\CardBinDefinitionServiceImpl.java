package com.anytech.anytxn.parameter.card.service;

import com.alibaba.fastjson.JSON;
import com.anytech.anytxn.business.base.audit.AbstractParameterService;
import com.anytech.anytxn.business.base.audit.ParmModificationRecord;
import com.anytech.anytxn.parameter.card.mapper.broadcast.ParmCardBinControlMapper;
import com.anytech.anytxn.parameter.card.mapper.broadcast.ParmCardBinControlSelfMapper;
import com.anytech.anytxn.parameter.card.mapper.broadcast.ParmCardBinDefinitionMapper;
import com.anytech.anytxn.parameter.card.mapper.broadcast.ParmCardBinDefinitionSelfMapper;
import com.github.pagehelper.Page;
import com.github.pagehelper.PageHelper;
import com.anytech.anytxn.common.core.annotation.db.DeleteParameterAnnotation;
import com.anytech.anytxn.common.core.annotation.db.InsertParameterAnnotation;
import com.anytech.anytxn.common.core.annotation.db.UpdateParameterAnnotation;
import com.anytech.anytxn.common.core.base.ParameterCompare;
import com.anytech.anytxn.common.core.base.PageResultDTO;
import com.anytech.anytxn.common.core.utils.BeanMapping;
import com.anytech.anytxn.common.core.utils.OrgNumberUtils;
import com.anytech.anytxn.common.sequence.utils.Number16IdGen;
import com.anytech.anytxn.common.core.utils.TenantUtils;
import com.anytech.anytxn.parameter.base.card.domain.dto.CardBinControlResDTO;
import com.anytech.anytxn.parameter.base.card.domain.dto.CardBinDefinitionReqDTO;
import com.anytech.anytxn.parameter.base.card.domain.dto.CardBinDefinitionResDTO;
import com.anytech.anytxn.parameter.base.card.service.ICardBinDefinitionService;
import com.anytech.anytxn.parameter.base.common.constants.Constants;
import com.anytech.anytxn.parameter.base.common.enums.AnyTxnParameterRespCodeEnum;
import com.anytech.anytxn.parameter.base.common.exception.AnyTxnParameterException;
import com.anytech.anytxn.parameter.card.mapper.unicast.BinCardNumberUsedSelfMapper;
import com.anytech.anytxn.parameter.base.card.domain.model.ParmCardBinControl;
import com.anytech.anytxn.parameter.base.card.domain.model.ParmCardBinDefinition;
//import org.apache.shardingsphere.transaction.annotation.ShardingSphereTransactionType;
//import org.apache.shardingsphere.transaction.core.TransactionType;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.StringUtils;

import java.time.LocalDateTime;
import java.util.List;

/**
 * bin参数 业务接口实现
 *
 * <AUTHOR>
 * @date 2018-09-28 11:52
 **/
@Service(value = "parm_card_bin_definition_serviceImpl")
public class CardBinDefinitionServiceImpl extends AbstractParameterService implements ICardBinDefinitionService {

    private Logger logger = LoggerFactory.getLogger(CardBinDefinitionServiceImpl.class);

    @Autowired
    private ParmCardBinControlMapper cardBinControlMapper;
    @Autowired
    private ParmCardBinControlSelfMapper cardBinControlSelfMapper;
    @Autowired
    private BinCardNumberUsedSelfMapper binCardNumberUsedSelfMapper;
    @Autowired
    private ParmCardBinDefinitionMapper cardBinDefinitionMapper;
    @Autowired
    private ParmCardBinDefinitionSelfMapper cardBinDefinitionSelfMapper;

    @Autowired
    private Number16IdGen numberIdGenerator;


    /**
     * 新增记录
     *
     * @param req bin定义请求对象
     * @return bin参数详情
     * @throws AnyTxnParameterException
     */
    @Override
    @Transactional(rollbackFor = Exception.class,propagation = Propagation.REQUIRED)
    @InsertParameterAnnotation(tableName = "parm_card_bin_definition", tableDesc = "Card BIN", isJoinTable = true)
    public ParameterCompare add(CardBinDefinitionReqDTO req) {
        boolean isExists = cardBinDefinitionSelfMapper.isExists(req.getOrganizationNumber(), req.getTableId());
        if (isExists) {
            logger.warn("参数表ID已存在, orgNumber={}, tableId={}",
                    OrgNumberUtils.getOrg(), req.getTableId());
            throw new AnyTxnParameterException(AnyTxnParameterRespCodeEnum.P_EXIST_BLOCK_CODE_AND_ID_FAULT);
        }
        // 构建bin参数定义
//        ParmCardBinDefinition cardBinDefinition = BeanMapping.copy(req, ParmCardBinDefinition.class);
        req.setId(String.valueOf(numberIdGenerator.generateId(TenantUtils.getTenantId())));
        //保存
//        saveCardBin(cardBinDefinition, req);
        return ParameterCompare.getBuilder()
                .withMainParmId(req.getTableId())
                .withAfter(req).build(CardBinDefinitionReqDTO.class);
    }

    @Transactional(rollbackFor = Exception.class)
    public void saveCardBin(ParmCardBinDefinition cardBinDefinition, CardBinDefinitionReqDTO req) {
        try {
            req.getCardBinControlList().stream().forEach(cardBinControlReq -> {
                ParmCardBinControl cardBinControl = BeanMapping.copy(cardBinControlReq, ParmCardBinControl.class);
                cardBinControl.setTableId(cardBinDefinition.getTableId());
                cardBinControl.setOrganizationNumber(cardBinDefinition.getOrganizationNumber());
                cardBinControl.setCreateTime(LocalDateTime.now());
                cardBinControl.setUpdateTime(LocalDateTime.now());
                cardBinControl.setUpdateBy(Constants.DEFAULT_USER);
                cardBinControl.setVersionNumber(1);
                cardBinControl.setId(numberIdGenerator.generateId(TenantUtils.getTenantId()));
                cardBinControlMapper.insertSelective(cardBinControl);
            });
        } catch (Exception e) {
            logger.error("Cause:{}\nMessage:{}", e.getCause(), e.getMessage());
            throw new AnyTxnParameterException(AnyTxnParameterRespCodeEnum.S_SAVE_CARD_BIN_DEFINITION_FAULT);


        }
    }

    /**
     * 修改记录
     *
     * @param req bin定义入参对象
     * @return bin定义详情
     * @throws AnyTxnParameterException
     */
    @Override
    @Transactional(rollbackFor = Exception.class,propagation = Propagation.REQUIRED)
    @UpdateParameterAnnotation(tableName = "parm_card_bin_definition", tableDesc = "Card BIN", isJoinTable = true)
    public ParameterCompare modify(CardBinDefinitionReqDTO req) {
        if (req.getId() == null) {
            throw new AnyTxnParameterException(AnyTxnParameterRespCodeEnum.P_PARAMETER_ID_EMPTY_FAULT);
        }
        CardBinDefinitionResDTO cardBinDefinitionResDTO = find(req.getId());

        CardBinDefinitionReqDTO cardBinDefinitionReqDTO = BeanMapping.copy(cardBinDefinitionResDTO, CardBinDefinitionReqDTO.class);

        return ParameterCompare.getBuilder()
                .withMainParmId(req.getTableId())
                .withAfter(req).withBefore(cardBinDefinitionReqDTO).build(CardBinDefinitionReqDTO.class);
    }

    /**
     * 级联删除
     *
     * @param cardBinDefinition
     */
    @Transactional(rollbackFor = Exception.class,propagation = Propagation.REQUIRED)
    public void deleteCardBin(ParmCardBinDefinition cardBinDefinition) {
        try {
            cardBinControlSelfMapper.delectByTableIdAndOrgNum(cardBinDefinition.getTableId(), cardBinDefinition.getOrganizationNumber());
        } catch (Exception e) {
            throw new AnyTxnParameterException(AnyTxnParameterRespCodeEnum.S_DELETE_CARD_BIN_FAULT);

        }
    }

    /**
     * 分页查询bin参数信息
     *
     * @param pageNum  页号
     * @param pageSize 每页大小
     * @param orgNum   机构号
     * @return bin参数详情分页
     * @throws AnyTxnParameterException
     */
    @Override
    public PageResultDTO<CardBinDefinitionResDTO> findPage(Integer pageNum, Integer pageSize, String orgNum, String tableId, String description) {
        logger.info("分页查询bin参数, pageNum={}, pageSize={}",
                pageNum, pageSize);
        Page<ParmCardBinDefinition> page = PageHelper.startPage(pageNum, pageSize);
        orgNum = StringUtils.isEmpty(orgNum) ? OrgNumberUtils.getOrg() : orgNum;
        List<ParmCardBinDefinition> dataList = cardBinDefinitionSelfMapper.selectPageByCondition(orgNum, tableId, description);
        List<CardBinDefinitionResDTO> listData = BeanMapping.copyList(dataList, CardBinDefinitionResDTO.class);

        return new PageResultDTO<>(pageNum, pageSize, page.getTotal(), page.getPages(), listData);
    }

    /**
     * 通过id获取详情
     *
     * @param id 主键id
     * @return bin定义详情
     * @throws AnyTxnParameterException
     */
    @Override
    public CardBinDefinitionResDTO find(String id) {
        if (id == null) {
            throw new AnyTxnParameterException(AnyTxnParameterRespCodeEnum.P_PARAMETER_EMPTY_FAULT);
        }
        CardBinDefinitionResDTO res = new CardBinDefinitionResDTO();
        ParmCardBinDefinition cardBinDefinition = cardBinDefinitionMapper.selectByPrimaryKey(id);
        if (cardBinDefinition == null) {
            logger.error("获取bin参数详情, 通过主键id({})未找到数据", id);
            throw new AnyTxnParameterException(AnyTxnParameterRespCodeEnum.D_GET_CARD_BIN_DEFINITION_BY_ID_FAULT);
        }
        BeanMapping.copy(cardBinDefinition, res);
        List<ParmCardBinControl> cardBinControlList = cardBinControlSelfMapper.selectAll(cardBinDefinition.getOrganizationNumber(), cardBinDefinition.getTableId());
        if (!cardBinControlList.isEmpty()) {
            List<CardBinControlResDTO> cardBinControlResList = BeanMapping.copyList(cardBinControlList, CardBinControlResDTO.class);

            cardBinControlResList.stream().forEach(cardBinControl -> {
                String lastUsedNumber = "0";
                if (binCardNumberUsedSelfMapper.selectLastUsedNumberByIndex(cardBinControl.getOrganizationNumber(), cardBinControl.getTableId(), cardBinControl.getBinSequence()) != null) {
                    lastUsedNumber = binCardNumberUsedSelfMapper.selectLastUsedNumberByIndex(cardBinControl.getOrganizationNumber(), cardBinControl.getTableId(), cardBinControl.getBinSequence())
                            .getLastUsedNumber();
                }

                cardBinControl.setLastUsedNumber(lastUsedNumber);
            });
            res.setCardBinControlList(cardBinControlResList);
        }

        return res;
    }


    /**
     * 根据机构号、参数表id查询
     *
     * @param organizationNumber 机构号
     * @param tableId            参数表id
     * @return CardBinDefinitionResDTO
     */
    @Override
    public CardBinDefinitionResDTO findByOrgAndTableId(String organizationNumber, String tableId) {
        if (StringUtils.isEmpty(organizationNumber) || StringUtils.isEmpty(tableId)) {
            throw new AnyTxnParameterException(AnyTxnParameterRespCodeEnum.P_PARAMETER_EMPTY_FAULT);
        }
        CardBinDefinitionResDTO res = new CardBinDefinitionResDTO();
        ParmCardBinDefinition cardBinDefinition = cardBinDefinitionSelfMapper.selectByOrgAndTableId(organizationNumber, tableId);
        if (cardBinDefinition == null) {
            logger.error("获取bin参数详情, 通过机构号：({}),参数表id：{}未找到数据", organizationNumber, tableId);
            throw new AnyTxnParameterException(AnyTxnParameterRespCodeEnum.D_GET_CARD_BIN_DEFINITION_BY_ID_FAULT);
        }
        BeanMapping.copy(cardBinDefinition, res);
        List<ParmCardBinControl> cardBinControlList = cardBinControlSelfMapper.selectAll(cardBinDefinition.getOrganizationNumber(), cardBinDefinition.getTableId());
        if (!cardBinControlList.isEmpty()) {
            List<CardBinControlResDTO> cardBinControlResList = BeanMapping.copyList(cardBinControlList, CardBinControlResDTO.class);

            cardBinControlResList.stream().forEach(cardBinControl -> {
                String lastUsedNumber = "0";
                if (binCardNumberUsedSelfMapper.selectLastUsedNumberByIndex(cardBinControl.getOrganizationNumber(), cardBinControl.getTableId(), cardBinControl.getBinSequence()) != null) {
                    lastUsedNumber = binCardNumberUsedSelfMapper.selectLastUsedNumberByIndex(cardBinControl.getOrganizationNumber(), cardBinControl.getTableId(), cardBinControl.getBinSequence())
                            .getLastUsedNumber();
                }

                cardBinControl.setLastUsedNumber(lastUsedNumber);
            });
            res.setCardBinControlList(cardBinControlResList);
        }

        return res;
    }

    /**
     * 通过id删除信息
     *
     * @param id
     * @return Boolean
     */
    @Override
    @DeleteParameterAnnotation(tableName = "parm_card_bin_definition", tableDesc = "Card BIN", isJoinTable = true)
    @Transactional(rollbackFor = Exception.class)
    public ParameterCompare remove(String id) {
        if (id == null) {
            throw new AnyTxnParameterException(AnyTxnParameterRespCodeEnum.P_PARAMETER_EMPTY_FAULT);
        }
        ParmCardBinDefinition cardBinDefinition = cardBinDefinitionMapper.selectByPrimaryKey(id);
        if (cardBinDefinition == null) {
            logger.error("删除bin参数定义表信息, 通过主键id({})未找到数据", id);
            throw new AnyTxnParameterException(AnyTxnParameterRespCodeEnum.D_DELETE_CARD_BIN_DEFINITION_BY_ID_FAULT);
        }
//        try {
//            //先删除各个层级的标信息根据tableId和机构编号
//            deleteCardBin(cardBinDefinition);
//            //删除主表定义表信息
//            logger.warn("通过id删除bin参数表信息, {}", cardBinDefinition);
//            deleteRow = cardBinDefinitionMapper.deleteByPrimaryKey(id);
//        } catch (Exception e) {
//            logger.error("通过id删除删除bin参数表信息失败, {}", e.getCause());
//            throw new AnyTxnParameterException(AnyTxnParameterRespCodeEnum.S_DELETE_CARD_BIN_DEFINITION_FAULT);
//        }

        return ParameterCompare.getBuilder()
                .withMainParmId(cardBinDefinition.getTableId())
                .withBefore(cardBinDefinition).build(ParmCardBinDefinition.class);
    }


    @Override
    @Transactional(rollbackFor = Exception.class,propagation = Propagation.REQUIRED)
    public Boolean removeByOrgAndTableId(String organizationNumber, String tableId) {
        if (StringUtils.isEmpty(organizationNumber) || StringUtils.isEmpty(tableId)) {
            throw new AnyTxnParameterException(AnyTxnParameterRespCodeEnum.P_PARAMETER_EMPTY_FAULT);
        }
        int deleteRow;
        ParmCardBinDefinition cardBinDefinition = cardBinDefinitionSelfMapper.selectByOrgAndTableId(organizationNumber, tableId);
        if (cardBinDefinition == null) {
            logger.error("删除bin参数定义表信息, 通过orgNum:({}),tableId:{}未找到数据", organizationNumber, tableId);
            throw new AnyTxnParameterException(AnyTxnParameterRespCodeEnum.D_DELETE_CARD_BIN_DEFINITION_BY_ID_FAULT);
        }
        try {
            //先删除各个层级的标信息根据tableId和机构编号
            deleteCardBin(cardBinDefinition);
            //删除主表定义表信息
            logger.warn("通过id删除bin参数表信息, {}", cardBinDefinition);
            deleteRow = cardBinDefinitionMapper.deleteByPrimaryKey(cardBinDefinition.getId());
        } catch (Exception e) {
            logger.error("通过id删除删除bin参数表信息失败, {}", e.getCause());
            throw new AnyTxnParameterException(AnyTxnParameterRespCodeEnum.S_DELETE_CARD_BIN_DEFINITION_FAULT);
        }

        return deleteRow > 0;
    }

    @Override
    @Transactional(rollbackFor = Exception.class,propagation = Propagation.REQUIRED)
    //@ShardingSphereTransactionType(TransactionType.XA)
    protected boolean updateDb(ParmModificationRecord parmModificationRecord) {
        CardBinDefinitionReqDTO cardBinDefinitionReqDTO = JSON.parseObject(parmModificationRecord.getParmBody(), CardBinDefinitionReqDTO.class);
        ParmCardBinDefinition cardBinDefinition = cardBinDefinitionMapper.selectByPrimaryKey(cardBinDefinitionReqDTO.getId());
        BeanMapping.copy(cardBinDefinitionReqDTO, cardBinDefinition);
        cardBinDefinition.initUpdateDateTime();
        cardBinDefinition.setUpdateBy(parmModificationRecord.getApplicationBy());
        cardBinDefinitionMapper.updateByPrimaryKeySelective(cardBinDefinition);
        deleteCardBin(cardBinDefinition);
        saveCardBin(cardBinDefinition, cardBinDefinitionReqDTO);
        return true;
    }

    @Override
    @Transactional(rollbackFor = Exception.class,propagation = Propagation.REQUIRED)
    //@ShardingSphereTransactionType(TransactionType.XA)
    protected boolean insertDb(ParmModificationRecord parmModificationRecord) {
        CardBinDefinitionReqDTO cardBinDefinitionReqDTO = JSON.parseObject(parmModificationRecord.getParmBody(), CardBinDefinitionReqDTO.class);
        ParmCardBinDefinition cardBinDefinition = BeanMapping.copy(cardBinDefinitionReqDTO, ParmCardBinDefinition.class);
        cardBinDefinition.initCreateDateTime();
        cardBinDefinition.initUpdateDateTime();
        cardBinDefinition.setUpdateBy(parmModificationRecord.getApplicationBy());
        cardBinDefinition.setVersionNumber(1L);
        cardBinDefinitionMapper.insertSelective(cardBinDefinition);
        saveCardBin(cardBinDefinition, cardBinDefinitionReqDTO);
        return true;
    }

    @Override
    @Transactional(rollbackFor = Exception.class,propagation = Propagation.REQUIRED)
    //@ShardingSphereTransactionType(TransactionType.XA)
    protected boolean deleteDb(ParmModificationRecord parmModificationRecord) {
        ParmCardBinDefinition cardBinDefinition = JSON.parseObject(parmModificationRecord.getParmBody(), ParmCardBinDefinition.class);
        //先删除各个层级的标信息根据tableId和机构编号
        deleteCardBin(cardBinDefinition);
        //删除主表定义表信息
        cardBinDefinitionMapper.deleteByPrimaryKey(cardBinDefinition.getId());
        return true;
    }
}
