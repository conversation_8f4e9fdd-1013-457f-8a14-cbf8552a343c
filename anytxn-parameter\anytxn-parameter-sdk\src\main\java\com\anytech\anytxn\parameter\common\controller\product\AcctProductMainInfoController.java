package com.anytech.anytxn.parameter.common.controller.product;

import io.swagger.v3.oas.annotations.tags.Tag;
import io.swagger.v3.oas.annotations.Operation;
import com.anytech.anytxn.common.core.base.PageResultDTO;
import com.anytech.anytxn.common.core.base.AnyTxnHttpResponse;
import com.anytech.anytxn.common.core.base.BizBaseController;
import com.anytech.anytxn.parameter.base.common.enums.ParameterRepDetailEnum;
import com.anytech.anytxn.parameter.base.common.domain.dto.product.AcctProductInfoDTO;
import com.anytech.anytxn.parameter.base.common.domain.dto.product.AcctProductMainInfoResDTO;
import com.anytech.anytxn.parameter.base.common.service.product.IAcctProductMainInfoService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

/**
 * <AUTHOR>
 * @date 2020-11-30
 */
@Tag(name = "账户产品基本信息")
@RestController
public class AcctProductMainInfoController extends BizBaseController {

    @Autowired
    private IAcctProductMainInfoService acctProductMainInfoService;

    /**
     * 新增账户产品基本信息
     * @param acctProductInfoDTO AcctProductInfoDTO
     * @return AnyTxnHttpResponse
     *
     */
    @Operation(summary = "新增账户产品基本信息",description = "新增账户产品基本信息")
    @PostMapping(value = "/param/acctProductMainInfo")
    public AnyTxnHttpResponse<Object> create(@RequestBody AcctProductInfoDTO acctProductInfoDTO) {
        return AnyTxnHttpResponse.success(acctProductMainInfoService.addAcctProductMainInfo(acctProductInfoDTO),ParameterRepDetailEnum.CREATE.message());
    }

    /**
     * 查询账户产品基本信息信息
     * @return PageResultDTO<AcctLimitCtrlResDTO>
     */
    @Operation(summary =  "账户产品基本信息分页查询",description = "分页查询")
    @GetMapping(value = "/param/acctProductMainInfo/pageNum/{pageNum}/pageSize/{pageSize}")
    public AnyTxnHttpResponse<PageResultDTO<AcctProductMainInfoResDTO>> getAllPage(@PathVariable(value = "pageNum") Integer pageNum,
                                                                                   @PathVariable(value = "pageSize") Integer pageSize,
                                                                                   @RequestParam(required = false) String productNumber,
                                                                                   @RequestParam(required = false) String attribute,
                                                                                   @RequestParam(required = false) String description,
                                                                                   @RequestParam(required = false) String organizationNumber) {
        PageResultDTO<AcctProductMainInfoResDTO> acctProductMainInfoPage = acctProductMainInfoService.findAll(pageNum,pageSize,productNumber,attribute,description, organizationNumber);
        return AnyTxnHttpResponse.success(acctProductMainInfoPage);

    }

    /**
     * 修改账户产品基本信息信息
     * @param acctProductInfoDTO AcctProductInfoDTO
     * @return AnyTxnHttpResponse
     */
    @Operation(summary = "账户产品基本信息信息修改", description = "只更新需要更新的,其他不变")
    @PutMapping(value = "/param/acctProductMainInfo")
    public AnyTxnHttpResponse<Object> modify(@RequestBody AcctProductInfoDTO acctProductInfoDTO) {
        return AnyTxnHttpResponse.success(acctProductMainInfoService.modifyAcctProductMainInfo(acctProductInfoDTO),ParameterRepDetailEnum.UPDATE.message());
    }

    /**
     * 通过id删除账户产品基本信息信息
     * @param id Long
     * @return Boolean
     */
    @Operation(summary = "删除账户产品基本信息信息", description = "通过id删除账户产品基本信息信息")
    @DeleteMapping(value = "/param/acctProductMainInfo/id/{id}")
    public AnyTxnHttpResponse<Object> remove(@PathVariable String id) {
        return AnyTxnHttpResponse.success(acctProductMainInfoService.removeAcctProductMainInfo(id),ParameterRepDetailEnum.DEL.message());
    }

    /**
     * 通过id查询账户产品基本信息信息
     * @param id Long
     * @return acctProductMainInfoResDTO
     */
    @Operation(summary = "通过id查询账户产品基本信息信息", description = "通过id查询账户产品基本信息信息")
    @GetMapping(value = "/param/acctProductMainInfo/id/{id}")
    public AnyTxnHttpResponse<AcctProductInfoDTO> getById(@PathVariable String id) {
        AcctProductInfoDTO acctProductInfoDTO = acctProductMainInfoService.findById(id);
        return AnyTxnHttpResponse.success(acctProductInfoDTO);

    }

    /**
     * 根据账产品查询账产品的公司卡标志、公司卡类型、清偿责任
     * @param productNumber Long
     * @return acctProductMainInfoResDTO
     */
    @Operation(summary = "通过id查询账户产品基本信息信息", description = "通过id查询账户产品基本信息信息")
    @GetMapping(value = "/param/acctProductMainInfo/organizationNumber/organizationNumber/{organizationNumber}/productNumber/{productNumber}")
    public AnyTxnHttpResponse<AcctProductMainInfoResDTO> getByProductNumber(@PathVariable(value = "organizationNumber") String organizationNumber,
                                                                     @PathVariable(value = "productNumber") String productNumber) {
        AcctProductMainInfoResDTO acctProductMainInfoResDTO = acctProductMainInfoService.findByProductNumber(organizationNumber,productNumber);
        return AnyTxnHttpResponse.success(acctProductMainInfoResDTO);

    }
}
