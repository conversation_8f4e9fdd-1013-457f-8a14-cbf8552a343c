package com.anytech.anytxn.parameter.account.service;

import com.alibaba.fastjson.JSON;
import com.github.pagehelper.Page;
import com.github.pagehelper.PageHelper;
import com.anytech.anytxn.common.core.annotation.db.DeleteParameterAnnotation;
import com.anytech.anytxn.common.core.annotation.db.InsertParameterAnnotation;
import com.anytech.anytxn.common.core.annotation.db.UpdateParameterAnnotation;
import com.anytech.anytxn.business.base.audit.AbstractParameterService;
import com.anytech.anytxn.common.core.base.ParameterCompare;
import com.anytech.anytxn.business.base.audit.ParmModificationRecord;
import com.anytech.anytxn.common.core.utils.OrgNumberUtils;
import com.anytech.anytxn.parameter.base.account.domain.dto.CashFeeTableSearchDTO;
import com.anytech.anytxn.common.core.base.PageResultDTO;
import com.anytech.anytxn.common.core.utils.BeanMapping;
import com.anytech.anytxn.common.sequence.utils.Number16IdGen;
import com.anytech.anytxn.common.core.utils.TenantUtils;
import com.anytech.anytxn.parameter.base.account.domain.dto.CashFeeTableReqDTO;
import com.anytech.anytxn.parameter.base.account.domain.dto.CashFeeTableResDTO;
import com.anytech.anytxn.parameter.base.account.service.ICashFeeTableService;
import com.anytech.anytxn.parameter.base.common.enums.AnyTxnParameterRespCodeEnum;
import com.anytech.anytxn.parameter.base.common.enums.ParameterRepDetailEnum;
import com.anytech.anytxn.parameter.base.common.exception.AnyTxnParameterException;
import com.anytech.anytxn.parameter.account.mapper.ParmCashFeeTableMapper;
import com.anytech.anytxn.parameter.account.mapper.ParmCashFeeTableSelfMapper;
import com.anytech.anytxn.parameter.base.account.domain.model.ParmCashFeeTable;
//import org.apache.shardingsphere.transaction.annotation.ShardingSphereTransactionType;
//import org.apache.shardingsphere.transaction.core.TransactionType;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;

/**
 * 取现手续费参数 业务接口实现
 * <AUTHOR>
 * @date 2020/3/26
 */
@Service(value = "parm_cash_fee_table_serviceImpl")
public class CashFeeTableServiceImpl extends AbstractParameterService implements ICashFeeTableService {

    private final Logger log = LoggerFactory.getLogger(CashFeeTableServiceImpl.class);

    @Autowired
    private ParmCashFeeTableMapper parmCashFeeTableMapper;
    @Autowired
    private ParmCashFeeTableSelfMapper parmCashFeeTableSelfMapper;

    @Autowired
    private Number16IdGen numberIdGenerator;

    /**
     * 查询取现手续费参数
     * @return 取现手续费参数响应参数
     * @param pageNum
     * @param pageSize
     */
    @Override
    public PageResultDTO<CashFeeTableResDTO> findAll(Integer pageNum, Integer pageSize, CashFeeTableSearchDTO cashFeeTableSearchDTO){
        if(null == cashFeeTableSearchDTO){
            cashFeeTableSearchDTO = new CashFeeTableSearchDTO();
        }

        if(!StringUtils.isEmpty(cashFeeTableSearchDTO.getChargePercent())){
            BigDecimal chargePercent = null;
            try {
                chargePercent = new BigDecimal(cashFeeTableSearchDTO.getChargePercent());
            }catch (NumberFormatException e){
                throw new AnyTxnParameterException(AnyTxnParameterRespCodeEnum.PARAMETER_EXCEPTION, ParameterRepDetailEnum.NUMBER_ERROR);
            }
            cashFeeTableSearchDTO.setChargePercentDeci(chargePercent);
        }

        if(!StringUtils.isEmpty(cashFeeTableSearchDTO.getFixedAmnt())){
            BigDecimal fixedAmnt = null;
            try {
                fixedAmnt = new BigDecimal(cashFeeTableSearchDTO.getFixedAmnt());
            }catch (NumberFormatException e){
                throw new AnyTxnParameterException(AnyTxnParameterRespCodeEnum.PARAMETER_EXCEPTION, ParameterRepDetailEnum.NUMBER_ERROR);
            }
            cashFeeTableSearchDTO.setFixedAmntDeci(fixedAmnt);
        }

        if(!StringUtils.isEmpty(cashFeeTableSearchDTO.getMaxAmnt())){
            BigDecimal maxAmnt = null;
            try {
                maxAmnt = new BigDecimal(cashFeeTableSearchDTO.getMaxAmnt());
            }catch (NumberFormatException e){
                throw new AnyTxnParameterException(AnyTxnParameterRespCodeEnum.PARAMETER_EXCEPTION, ParameterRepDetailEnum.NUMBER_ERROR);
            }
            cashFeeTableSearchDTO.setMaxAmntDeci(maxAmnt);
        }

        if(!StringUtils.isEmpty(cashFeeTableSearchDTO.getMinAmnt())) {
            BigDecimal minAmnt = null;
            try {
                minAmnt = new BigDecimal(cashFeeTableSearchDTO.getMinAmnt());
            } catch (NumberFormatException e) {
                throw new AnyTxnParameterException(AnyTxnParameterRespCodeEnum.PARAMETER_EXCEPTION, ParameterRepDetailEnum.NUMBER_ERROR);
            }
            cashFeeTableSearchDTO.setMinAmntDeci(minAmnt);
        }

        Page<ParmCashFeeTable> page = PageHelper.startPage(pageNum, pageSize);
        cashFeeTableSearchDTO.setOrganizationNumber(StringUtils.isEmpty(cashFeeTableSearchDTO.getOrganizationNumber()) ? OrgNumberUtils.getOrg() : cashFeeTableSearchDTO.getOrganizationNumber());
        List<ParmCashFeeTable> cashFeeTableList = parmCashFeeTableSelfMapper.selectByCondition(cashFeeTableSearchDTO);
        if (CollectionUtils.isEmpty(cashFeeTableList)) {
            log.error("取现手续费参数数据不存在");
            cashFeeTableList = new ArrayList<>();
            //throw new AnyTxnParameterException(AnyTxnParameterRespCodeEnum.D_GET_IS_NULL_FAULT,"取现手续费参数数据不存在");
        }
        List<CashFeeTableResDTO> res = BeanMapping.copyList(cashFeeTableList, CashFeeTableResDTO.class);
        return new PageResultDTO<>(pageNum,pageSize,page.getTotal(), page.getPages(),res);
    }



    /**
     * 添加取现手续费参数
     * @param  cashFeeTableReq 取现手续费参数入参对象
     * @return 取现手续费参数响应参数
     */
    @Override
    @InsertParameterAnnotation(tableName = "parm_cash_fee_table", tableDesc = "Cash Fee")
    public ParameterCompare addParmFee(CashFeeTableReqDTO cashFeeTableReq){

        // 根据机构号和table_id查询该记录是否已经存在
        ParmCashFeeTable parmCashFeeTable = parmCashFeeTableSelfMapper.selectByOrgAndTid(OrgNumberUtils.getOrg(cashFeeTableReq.getOrganizationNumber()), cashFeeTableReq.getTableId());
        if (parmCashFeeTable != null) {
            log.error("取现手续费参数已存在,id:{}",cashFeeTableReq.getId());
            throw new AnyTxnParameterException(AnyTxnParameterRespCodeEnum.P_PARAMETER_TABLE_ID_EXIST);
        }
        ParmCashFeeTable parmCashFeeTableReq = BeanMapping.copy(cashFeeTableReq, ParmCashFeeTable.class);

        // 设置默认值
        parmCashFeeTableReq.setId(String.valueOf(numberIdGenerator.generateId(TenantUtils.getTenantId())));

        return ParameterCompare.getBuilder().withAfter(parmCashFeeTableReq).build(ParmCashFeeTable.class);
    }

    /**
     * 更新取现手续费参数
     * @param  cashFeeTableReq 取现手续费数入参对象
     * @return 取现手续费参数响应参数
     */
    @Override
    @UpdateParameterAnnotation(tableName = "parm_cash_fee_table", tableDesc = "Cash Fee")
    public ParameterCompare modifyParmFee(CashFeeTableReqDTO cashFeeTableReq) {
        ParmCashFeeTable parmCashFeeTable = parmCashFeeTableMapper.selectByPrimaryKey(cashFeeTableReq.getId());
        if (null == parmCashFeeTable) {
            log.error("取现手续费参数数据不存在");
            throw new AnyTxnParameterException(AnyTxnParameterRespCodeEnum.D_GET_IS_NULL_FAULT, ParameterRepDetailEnum.CASH_FREE);
        }
        // 判断修改后的数据在表里是否是以机构号,表id唯一确定一条数据
        if (!cashFeeTableReq.getOrganizationNumber().equals(parmCashFeeTable.getOrganizationNumber())
                || !cashFeeTableReq.getTableId().equals(parmCashFeeTable.getTableId())) {
            ParmCashFeeTable isExsit = parmCashFeeTableSelfMapper.selectByOrgAndTid(cashFeeTableReq.getOrganizationNumber(), cashFeeTableReq.getTableId());
            if (isExsit != null) {
                log.error("取现手续费参数已存在,id:{}",isExsit.getId());
                throw new AnyTxnParameterException(AnyTxnParameterRespCodeEnum.P_PARAMETER_TABLE_ID_EXIST);
            }
        }

        ParmCashFeeTable updateCashFee = BeanMapping.copy(cashFeeTableReq, ParmCashFeeTable.class);

        return ParameterCompare.getBuilder().withAfter(updateCashFee).withBefore(parmCashFeeTable).build(ParmCashFeeTable.class);

    }

    /**
     * 通过Id主键删除取现手续费参数
     * @param  id 主键
     * @return Boolean
     */
    @Transactional(rollbackFor = Exception.class,propagation = Propagation.REQUIRED)
    @Override
    @DeleteParameterAnnotation(tableName = "parm_cash_fee_table", tableDesc = "Cash Fee")
    public ParameterCompare removeParmFee(String id){
        ParmCashFeeTable parmCashFeeTable = new ParmCashFeeTable();
        // 如果id不存在，则查询该记录
        if (null != id) {
            parmCashFeeTable = parmCashFeeTableMapper.selectByPrimaryKey(id);
        }
        // 如果该记录不存在，抛出异常，否则就删除该记录
        if (null == parmCashFeeTable) {
            log.error("取现手续费参数数据不存在");
            throw new AnyTxnParameterException(AnyTxnParameterRespCodeEnum.D_GET_IS_NULL_FAULT, ParameterRepDetailEnum.CASH_FREE);
        }
        return ParameterCompare.getBuilder().withBefore(parmCashFeeTable).build(ParmCashFeeTable.class);

    }

    /**
     * 通过Id查询取现手续费参数信息
     * @param id 主键id
     * @return AnnualFeeTableResDTO
     */
    @Override
    public CashFeeTableResDTO findById(String id){
        if (id == null) {
            log.error("id不能为空");
            throw new AnyTxnParameterException(AnyTxnParameterRespCodeEnum.P_PARAMETER_ID_EMPTY_FAULT, ParameterRepDetailEnum.ID_NULL);
        }
        ParmCashFeeTable parmCashFeeTable = parmCashFeeTableMapper.selectByPrimaryKey(id);
        return BeanMapping.copy(parmCashFeeTable, CashFeeTableResDTO.class);
    }

    /**
     * 根据表Id查询取现手续费参数
     *
     * @param tableId 表Id
     * @return 取现手续费参数响应参数
     */
    @Override
    public CashFeeTableResDTO findByOrgAndTableId(String orgNum, String tableId){
        ParmCashFeeTable parmCashFeeTable = parmCashFeeTableSelfMapper.selectByOrgAndTid(orgNum, tableId);
        if (parmCashFeeTable == null) {
            log.error("取现手续费参数数据不存在");
            throw new AnyTxnParameterException(AnyTxnParameterRespCodeEnum.D_GET_IS_NULL_FAULT, ParameterRepDetailEnum.CASH_FREE);
        }
        return BeanMapping.copy(parmCashFeeTable, CashFeeTableResDTO.class);
    }


    @Override
    @Transactional(rollbackFor = Exception.class,propagation = Propagation.REQUIRED)
    //@ShardingSphereTransactionType(TransactionType.XA)
    protected boolean updateDb(ParmModificationRecord parmModificationRecord) {
        ParmCashFeeTable parmCashFeeTable = JSON.parseObject(parmModificationRecord.getParmBody(), ParmCashFeeTable.class);
        parmCashFeeTable.initUpdateDateTime();
        int i = parmCashFeeTableMapper.updateByPrimaryKeySelective(parmCashFeeTable);
        return i > 0;
    }

    @Override
    @Transactional(rollbackFor = Exception.class,propagation = Propagation.REQUIRED)
    //@ShardingSphereTransactionType(TransactionType.XA)
    protected boolean insertDb(ParmModificationRecord parmModificationRecord) {
        ParmCashFeeTable parmCashFeeTable = JSON.parseObject(parmModificationRecord.getParmBody(), ParmCashFeeTable.class);
        parmCashFeeTable.initCreateDateTime();
        parmCashFeeTable.initUpdateDateTime();
        int i = parmCashFeeTableMapper.insertSelective(parmCashFeeTable);
        return i > 0;
    }

    @Override
    @Transactional(rollbackFor = Exception.class,propagation = Propagation.REQUIRED)
    //@ShardingSphereTransactionType(TransactionType.XA)
    protected boolean deleteDb(ParmModificationRecord parmModificationRecord) {
        ParmCashFeeTable parmCashFeeTable = JSON.parseObject(parmModificationRecord.getParmBody(), ParmCashFeeTable.class);
        int i = parmCashFeeTableMapper.deleteByPrimaryKey(parmCashFeeTable.getId());
        return i > 0;
    }
}
