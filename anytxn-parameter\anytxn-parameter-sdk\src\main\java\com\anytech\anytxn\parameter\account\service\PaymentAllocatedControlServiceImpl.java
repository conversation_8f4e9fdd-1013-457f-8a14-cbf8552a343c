package com.anytech.anytxn.parameter.account.service;

import com.anytech.anytxn.common.core.utils.OrgNumberUtils;
import com.anytech.anytxn.common.core.utils.BeanMapping;
import com.anytech.anytxn.common.sequence.utils.Number16IdGen;
import com.anytech.anytxn.common.core.utils.TenantUtils;
import com.anytech.anytxn.parameter.base.account.domain.dto.PaymentAllocatedControlReqDTO;
import com.anytech.anytxn.parameter.base.account.domain.dto.PaymentAllocatedControlResDTO;
import com.anytech.anytxn.parameter.base.account.service.IPaymentAllocatedControlService;
import com.anytech.anytxn.parameter.base.common.enums.AnyTxnParameterRespCodeEnum;
import com.anytech.anytxn.parameter.base.common.exception.AnyTxnParameterException;
import com.anytech.anytxn.parameter.account.mapper.ParmPaymentAllocatedControlMapper;
import com.anytech.anytxn.parameter.account.mapper.ParmPaymentAllocatedControlSelfMapper;
import com.anytech.anytxn.parameter.base.account.domain.model.ParmPaymentAllocatedControl;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * 还款分配控制 业务接口
 * <AUTHOR> tingting
 * @date 2018/8/16
 */
@Service
public class PaymentAllocatedControlServiceImpl implements IPaymentAllocatedControlService {

    private Logger logger = LoggerFactory.getLogger(PaymentAllocatedControlServiceImpl.class);

    @Autowired
    private ParmPaymentAllocatedControlMapper parmPaymentAllocatedControlMapper;
    @Autowired
    private ParmPaymentAllocatedControlSelfMapper parmPaymentAllocatedControlSelfMapper;

    @Autowired
    private Number16IdGen numberIdGenerator;

    /**
     * 新增还款分配控制
     * @param paymentAllocatedReq 还款分配控制请求参数
     * @return PaymentAllocatedControlRes 还款分配控制响应参数
     */
    @Override
    public PaymentAllocatedControlResDTO addPaymentAllocatedControl(PaymentAllocatedControlReqDTO paymentAllocatedReq){
        ParmPaymentAllocatedControl control = parmPaymentAllocatedControlSelfMapper.selectByMultipleConditions(OrgNumberUtils.getOrg(paymentAllocatedReq.getOrganizationNumber()), paymentAllocatedReq.getTableId(),
                paymentAllocatedReq.getStatementFlag(), paymentAllocatedReq.getTransactionTypeCode());
        if (control != null) {
            throw new AnyTxnParameterException(AnyTxnParameterRespCodeEnum.D_EXIST_PARM_PAYMENT_ALLOCATED_CONTROL_FAULT);
        }
        ParmPaymentAllocatedControl parmPaymentAllocatedControl = BeanMapping.copy(paymentAllocatedReq, ParmPaymentAllocatedControl.class);
        parmPaymentAllocatedControl.setId(numberIdGenerator.generateId(TenantUtils.getTenantId()));
        parmPaymentAllocatedControlMapper.insert(parmPaymentAllocatedControl);
        return BeanMapping.copy(parmPaymentAllocatedControl, PaymentAllocatedControlResDTO.class);
    }

    /**
     * 删除还款分配控制
     * @param id 主键id
     * @return Boolean 是否删除成功
     *
     */
    @Override
    public Boolean removePaymentAllocatedControl(Long id){
        ParmPaymentAllocatedControl parmPaymentAllocatedControl = new ParmPaymentAllocatedControl();
        // 如果id不存在，则查询该记录
        if (null != id) {
            parmPaymentAllocatedControl = parmPaymentAllocatedControlMapper.selectByPrimaryKey(id);
        }
        // 如果该记录不存在，抛出异常，否则就删除该记录
        if (null == parmPaymentAllocatedControl) {
            throw new AnyTxnParameterException(AnyTxnParameterRespCodeEnum.D_QUERY_PARM_PAYMENT_ALLOCATED_CONTROL_FAULT);
        }
        int i = parmPaymentAllocatedControlMapper.deleteByPrimaryKey(id);
        return i > 0;
    }

    /**
     * 根据 根据机构号、参数表id、交易类型、已出账单标志、应计非应计标志查询
     *
     * @param transactionTypeCode 交易类型
     * @param statementFlag       已出账单标志
     * @param orgNumber           机构号
     * @param tableId             参数表id
     * @return PaymentAllocatedControlRes
     * @throws AnyTxnParameterException
     */
//    @Override
//    public PaymentAllocatedControlResDTO findByMultipleConditions(String orgNumber, String tableId, String statementFlag,
//                                                                  String transactionTypeCode){
//
//        ParmPaymentAllocatedControl parmPaymentAllocatedControl = parmPaymentAllocatedControlSelfMapper
//                .selectByMultipleConditions(orgNumber, tableId, statementFlag, transactionTypeCode);
//        //查询为空 抛出异常
//        if (parmPaymentAllocatedControl == null) {
//
//            logger.error("根据交易类型{}、已出账单标志{}，未查出数据",
//                    transactionTypeCode, statementFlag);
//

//    }

    /**
     * 通过机构号和表Id查询
     *
     * @param orgNum  机构号
     * @param tableId 表Id
     * @return List<ParmPaymentAllocatedControl>
     */
    @Override
    public List<PaymentAllocatedControlResDTO> findByOrgAndTableId(String orgNum, String tableId){
        List<ParmPaymentAllocatedControl> paymentAllocatedControlList = parmPaymentAllocatedControlSelfMapper.seletByOrgAndTableId(orgNum, tableId);
        if (paymentAllocatedControlList.isEmpty()) {
            throw new AnyTxnParameterException(AnyTxnParameterRespCodeEnum.D_QUERY_PARM_PAYMENT_ALLOCATED_CONTROL_BY_ID_ORG_FAULT);
        }
        return BeanMapping.copyList(paymentAllocatedControlList, PaymentAllocatedControlResDTO.class);
    }

    /**
     * 根据 根据机构号、参数表id、交易类型、已出账单标志、超长免息期标志查询
     * @param orgNumber 机构号
     * @param tableId 参数表id
     * @param statementFlag 已出账单标志
     * @param transactionTypeCode 交易类型
     * @param largeGraceFlag 超长免息期标志
     * @return ParmPaymentAllocatedControl
     */
//    @Override
//    public PaymentAllocatedControlResDTO findByMultipleConditions(String orgNumber, String tableId, String statementFlag,
//                                                                  String transactionTypeCode, String largeGraceFlag){
//
//        ParmPaymentAllocatedControl parmPaymentAllocatedControl = parmPaymentAllocatedControlSelfMapper
//                .selectByOrgTidSflagTcodeAndLgflag(orgNumber, tableId, statementFlag, transactionTypeCode,largeGraceFlag);
//        //查询为空 抛出异常
//        if (parmPaymentAllocatedControl == null) {
//
//            logger.error("根据交易类型{}、已出账单标志{}，未查出数据",
//                    transactionTypeCode, statementFlag);
//

//    }

    /**
     * 根据 根据机构号、参数表id、交易类型、已出账单标志、超长免息期标志、资产出表状态查询
     * @param orgNumber 机构号
     * @param tableId 参数表id
     * @param statementFlag 已出账单标志
     * @param transactionTypeCode 交易类型
     * @param largeGraceFlag 超长免息期标志
     * @param absStatus 资产出表状态
     * @return ParmPaymentAllocatedControl
     */
    @Override
    public PaymentAllocatedControlResDTO findByMultipleConditions(String orgNumber, String tableId, String statementFlag, String transactionTypeCode, String largeGraceFlag, String absStatus) {
        ParmPaymentAllocatedControl parmPaymentAllocatedControl = parmPaymentAllocatedControlSelfMapper
                .selectByOrgTidSflagTcodeLgflagAndAbs(orgNumber, tableId, statementFlag, transactionTypeCode,largeGraceFlag,absStatus);
        if (parmPaymentAllocatedControl == null) {
            logger.error("根据交易类型{}、已出账单标志{},largeGraceFlag:{},absStatus:{}，未查出数据",
                    transactionTypeCode, statementFlag,largeGraceFlag,absStatus);
            throw new AnyTxnParameterException(AnyTxnParameterRespCodeEnum.D_GET_IS_NULL_FAULT);
        }
        return BeanMapping.copy(parmPaymentAllocatedControl, PaymentAllocatedControlResDTO.class);
    }
}
