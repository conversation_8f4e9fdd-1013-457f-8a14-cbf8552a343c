package com.anytech.anytxn.parameter.authorization.service;

import com.github.pagehelper.Page;
import com.github.pagehelper.PageHelper;
import com.anytech.anytxn.common.core.base.PageResultDTO;
import com.anytech.anytxn.common.core.utils.BeanMapping;
import com.anytech.anytxn.common.core.utils.OrgNumberUtils;
import com.anytech.anytxn.common.sequence.utils.Number16IdGen;
import com.anytech.anytxn.common.core.utils.TenantUtils;
import com.anytech.anytxn.parameter.base.authorization.domain.dto.MccCodeDTO;
import com.anytech.anytxn.parameter.base.authorization.service.IMccCodeService;
import com.anytech.anytxn.parameter.base.common.enums.AnyTxnParameterRespCodeEnum;
import com.anytech.anytxn.parameter.base.common.exception.AnyTxnParameterException;
import com.anytech.anytxn.parameter.common.mapper.deprecated.MccCodeMapper;
import com.anytech.anytxn.parameter.common.mapper.deprecated.MccCodeSelfMapper;
import com.anytech.anytxn.parameter.base.authorization.domain.model.ParmMccCode;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.cglib.beans.BeanCopier;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import java.time.LocalDateTime;
import java.util.*;

/**
 * @ClassName MccCodeServiceImpl
 * @Description 商户类别码具体实现类
 * <AUTHOR>
 * @Date 2019/4/4 3:44 PM
 * Version 1.0
 **/
@Service
public class MccCodeServiceImpl implements IMccCodeService {

    private Logger logger = LoggerFactory.getLogger(MccCodeServiceImpl.class);

    @Autowired
    private MccCodeMapper mccCodeMapper;
    @Autowired
    private MccCodeSelfMapper mccCodeSelfMapper;

    @Autowired
    private Number16IdGen numberIdGenerator;


    @Override
    public PageResultDTO<MccCodeDTO> findListMccCode(Integer page, Integer rows, String mccCde,String description, String organizationNumber) {
        logger.info("分页查询商户类别码，当前页：{}，每页展示条数：{}", page, rows);
        Page pageInfo =  PageHelper.startPage(page, rows);
        List<MccCodeDTO> mccCodeDTOList = null;
        try {
            organizationNumber = StringUtils.isEmpty(organizationNumber) ? OrgNumberUtils.getOrg() : organizationNumber;
            List<ParmMccCode> mccCodeList = mccCodeSelfMapper.selectByCondition(organizationNumber,mccCde,description);
            if (!CollectionUtils.isEmpty(mccCodeList)) {
                mccCodeDTOList = new ArrayList<>();
                BeanCopier beanCopier = BeanCopier.create(ParmMccCode.class, MccCodeDTO.class, false);
                for (ParmMccCode mccCode : mccCodeList) {
                    MccCodeDTO mccCodeDTO = new MccCodeDTO();
                    beanCopier.copy(mccCode, mccCodeDTO, null);
                    mccCodeDTOList.add(mccCodeDTO);
                }
            }
            return new PageResultDTO<>(page, rows, pageInfo.getTotal(), pageInfo.getPages(),mccCodeDTOList);
        } catch (Exception e) {
            logger.error("分页查询商户类别码信息失败，当前页：{}，每页展示条数：{},错误信息：{}", page, rows, e.getMessage());
            /*throw new AnyTXNBusRuntimeException(TransactionEnum.DATABASE_ERROR.getCode(),
                    TransactionEnum.DATABASE_ERROR.getReason());*/
            throw new AnyTxnParameterException(AnyTxnParameterRespCodeEnum.D_PAGE_MCC_CODE_FAULT);
        }
    }

    @Override
    public MccCodeDTO findMccCode(Long id) {
        logger.info("根据主键:{},获取商户类别码信息",id);
        MccCodeDTO mccCodeDTO = null;
        try{
            ParmMccCode mccCode = mccCodeMapper.selectByPrimaryKey(id);
            if (mccCode != null) {
                mccCodeDTO = new MccCodeDTO();
                BeanCopier beanCopier = BeanCopier.create(ParmMccCode.class, MccCodeDTO.class, false);
                beanCopier.copy(mccCode, mccCodeDTO, null);
            }
        }
        catch(Exception e){
            logger.error("根据主键:{},获取商户类别码信息失败,错误信息:{}",id,e.getMessage());
            /*throw new AnyTXNBusRuntimeException(TransactionEnum.DATABASE_ERROR.getCode(),
                    TransactionEnum.DATABASE_ERROR.getReason());*/
            throw new AnyTxnParameterException(AnyTxnParameterRespCodeEnum.D_QUERY_MCC_CODE_BY_ID_FAULT);

        }
        if (mccCodeDTO == null) {
            logger.error("根据主键:{},获取商户类别码信息失败", id);

            throw new AnyTxnParameterException(AnyTxnParameterRespCodeEnum.D_QUERY_MCC_CODE_NULL_BY_ID_FAULT);
        }
        return mccCodeDTO;
    }

    @Override
    public Boolean modifyMccCode(MccCodeDTO mccCodeDTO) {
        logger.info("修改商户类别码信息,商户类别码:{} 商户状态:{}", mccCodeDTO.getMccCde(), mccCodeDTO.getStatus());
        try {
            ParmMccCode mccCode= new ParmMccCode();
            BeanCopier copierMccCodeDtoToModel = BeanCopier.create(MccCodeDTO.class, ParmMccCode.class, false);
            copierMccCodeDtoToModel.copy(mccCodeDTO, mccCode, null);
            mccCode.setUpdateTime(LocalDateTime.now());
            return mccCodeMapper.updateByPrimaryKeySelective(mccCode) > 0;
        } catch (Exception e) {
            logger.error("调用[{}]更新商户类别码表[{}]失败,错误信息[{}]",
                    "updateByPrimaryKeySelective", "MCC_COD", e);

            throw new AnyTxnParameterException(AnyTxnParameterRespCodeEnum.D_UPDATE_MCC_CODE_FAULT);
        }
    }

    @Override
    public Boolean removeMccCode(Long id) {
        ParmMccCode mccCode = mccCodeMapper.selectByPrimaryKey(id);
        logger.info("查询商户类别码信息 id:{}", id);
        if (mccCode == null) {
            logger.error("待删除商户类别码信息不存在。 id:{}", id);
            throw new AnyTxnParameterException(AnyTxnParameterRespCodeEnum.D_QUERY_MCC_CODE_NULL_BY_ID_FAULT);
        }
        try {
            return mccCodeMapper.deleteByPrimaryKey(id) > 0;
        } catch (Exception e) {
            throw new AnyTxnParameterException(AnyTxnParameterRespCodeEnum.D_DELETE_MCC_CODE_FAULT);
        }
    }

    @Override
    public Boolean addMccCode(MccCodeDTO mccCodeDTO) {
        //转换
        ParmMccCode mccCode ;
        if (mccCodeDTO == null) {
            throw new AnyTxnParameterException(AnyTxnParameterRespCodeEnum.P_PARAMETER_EMPTY_FAULT);
        }
        mccCode = BeanMapping.copy(mccCodeDTO, ParmMccCode.class);
        int isExists = mccCodeSelfMapper.isExists(mccCode.getMccCde(),mccCodeDTO.getOrganizationNumber());
        if (isExists > 0) {
            logger.error("已存在!");
            throw new AnyTxnParameterException(AnyTxnParameterRespCodeEnum.D_EXIST_MCC_CODE_FAULT);
        }
        mccCode.setId(numberIdGenerator.generateId(TenantUtils.getTenantId()));
        mccCode.setCreateTime(LocalDateTime.now());
        mccCode.setUpdateTime(LocalDateTime.now());
        mccCode.setUpdateBy("admin");
        try {
            return mccCodeMapper.insertSelective(mccCode) > 0;
        } catch (Exception e) {
            logger.error("exception:{}",e);
            throw new AnyTxnParameterException(AnyTxnParameterRespCodeEnum.D_INSERT_MCC_CODE_FAULT);
        }
    }

    @Override
    public List<Map<String,String>> getMccCodes(String organizationNumber) {
        logger.info("获取所有商户类别码");
        List<ParmMccCode> mccCodeList = mccCodeSelfMapper.selectAll(true, organizationNumber);
        List<Map<String,String>> list = new ArrayList<>();
        mccCodeList.forEach(mccCode -> {
            Map<String,String> map = new HashMap<>(4);
            map.put("value",mccCode.getMccCde());
            map.put("label",mccCode.getDescription());
            list.add(map);
        });
        return list;
    }

    @Override
    public List<MccCodeDTO> selectMccByCondition(String organizationNumber, String mccCde, String status) {
        List<ParmMccCode> mccCodeList = mccCodeSelfMapper.selectMccByCondition(organizationNumber, mccCde, status);
        if (!CollectionUtils.isEmpty(mccCodeList)) {
            List<MccCodeDTO> mccCodeDTOList = new ArrayList<>();
            BeanCopier beanCopier = BeanCopier.create(ParmMccCode.class, MccCodeDTO.class, false);
            for (ParmMccCode mccCode : mccCodeList) {
                MccCodeDTO mccCodeDTO = new MccCodeDTO();
                beanCopier.copy(mccCode, mccCodeDTO, null);
                mccCodeDTOList.add(mccCodeDTO);
            }
            return mccCodeDTOList;
        }
        return Collections.emptyList();
    }
}
