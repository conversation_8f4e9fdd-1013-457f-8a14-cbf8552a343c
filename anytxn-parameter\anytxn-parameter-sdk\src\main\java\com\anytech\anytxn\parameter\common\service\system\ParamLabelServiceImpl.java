package com.anytech.anytxn.parameter.common.service.system;

import com.alibaba.fastjson.JSON;
import com.anytech.anytxn.common.core.annotation.db.DeleteParameterAnnotation;
import com.anytech.anytxn.common.core.annotation.db.InsertParameterAnnotation;
import com.anytech.anytxn.common.core.annotation.db.UpdateParameterAnnotation;
import com.anytech.anytxn.business.base.audit.AbstractParameterService;
import com.anytech.anytxn.common.core.base.ParameterCompare;
import com.anytech.anytxn.business.base.audit.ParmModificationRecord;
import com.anytech.anytxn.common.core.base.PageResultDTO;
import com.anytech.anytxn.common.core.utils.BeanMapping;
import com.anytech.anytxn.common.core.utils.OrgNumberUtils;
import com.anytech.anytxn.common.sequence.utils.Number16IdGen;
import com.anytech.anytxn.common.core.utils.TenantUtils;
import com.anytech.anytxn.parameter.base.common.enums.AnyTxnParameterRespCodeEnum;
import com.anytech.anytxn.parameter.base.common.enums.ParameterRepDetailEnum;
import com.anytech.anytxn.parameter.base.common.exception.AnyTxnParameterException;
import com.anytech.anytxn.parameter.common.mapper.broadcast.system.ParmLabelAgentHisSelfMapper;
import com.anytech.anytxn.parameter.common.mapper.broadcast.system.ParmLabelAgentMapper;
import com.anytech.anytxn.parameter.base.common.domain.model.system.ParmLabelAgent;
import com.anytech.anytxn.parameter.base.common.domain.model.system.ParmLabelAgentHis;
import com.anytech.anytxn.parameter.base.common.domain.dto.system.LabelAgentDTO;
import com.anytech.anytxn.parameter.base.common.domain.dto.system.LabelAgentSearchKeyDTO;
import com.anytech.anytxn.parameter.base.common.service.system.IParmLabelService;
import com.github.pagehelper.Page;
import com.github.pagehelper.PageHelper;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
//import org.apache.shardingsphere.transaction.annotation.ShardingSphereTransactionType;
//import org.apache.shardingsphere.transaction.core.TransactionType;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import jakarta.annotation.Resource;
import java.util.*;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

@Slf4j
@Service("parm_label_agent_serviceImpl")
public class ParamLabelServiceImpl extends AbstractParameterService implements IParmLabelService {
    @Resource
    private Number16IdGen numberIdGenerator;
    @Resource
    private ParmLabelAgentMapper parmLabelAgentMapper;
    @Resource
    private ParmLabelAgentHisSelfMapper parmLabelAgentHisSelfMapper;

    /**
     * 查询所有标签数据
     *
     * @return List<LabelAgentDTO>
     */
    @Override
    public List<LabelAgentDTO> selectAll(String organizationNumber) {
        List<ParmLabelAgent> list = parmLabelAgentMapper.selectAll(organizationNumber);
        if (null != list && !list.isEmpty()) {
            List<LabelAgentDTO> allList = BeanMapping.copyList(list, LabelAgentDTO.class);
            List<LabelAgentDTO> parentList = allList.stream().filter(l ->
                    StringUtils.isEmpty(l.getLabelValue())
                            && StringUtils.isEmpty(l.getLabelAgentCode())).collect(Collectors.toList());
            allList.removeAll(parentList);
            for (LabelAgentDTO parentLabelAgent : parentList) {
                String parentLabelType = parentLabelAgent.getLabelType();
                if (!StringUtils.isEmpty(parentLabelType)) {
                    List<LabelAgentDTO> labelAgentDTOS = new ArrayList<>(16);
                    for (LabelAgentDTO parmLabelAgent : allList) {
                        String labelType = parmLabelAgent.getLabelType();
                        if (!StringUtils.isEmpty(labelType)
                                && parentLabelType.equals(labelType)) {
                            parmLabelAgent.setLabelTag(parmLabelAgent.getLabelValue()
                                    + parmLabelAgent.getLabelAgentCode());
                            labelAgentDTOS.add(parmLabelAgent);
                        }
                    }
                    parentLabelAgent.setChildren(labelAgentDTOS);
                }
            }
            return parentList;
        }
        return null;
    }

    @Override
    public PageResultDTO<LabelAgentDTO> findPage(LabelAgentSearchKeyDTO searchKeyDTO) {
        int startPage = searchKeyDTO.getPage() == null ? 1 : searchKeyDTO.getPage();
        int row = searchKeyDTO.getRows() == null ? 10 : searchKeyDTO.getRows();

        String orgNumber = StringUtils.isEmpty(searchKeyDTO.getOrganizationNumber())
                ? OrgNumberUtils.getOrg() : searchKeyDTO.getOrganizationNumber();
        String labelTagParam = searchKeyDTO.getLabelTag();
        String labelTypeParam = searchKeyDTO.getLabelType();
        Map<String, Object> searchKeyMap = new HashMap<>(4);

        String labelType = labelTypeParam == null ? null : "%" + labelTypeParam.trim() + "%";
        String labelValue = labelTagParam == null ? null : "%" + labelTagParam.trim() + "%";

        searchKeyMap.put("labelType", labelType);
        searchKeyMap.put("labelValue", labelValue);
        searchKeyMap.put("rows", startPage * row);
        searchKeyMap.put("organizationNumber", orgNumber);

        String subStringValue = null;
        List<ParmLabelAgent> labelAgents = null;
        if (org.apache.commons.lang3.StringUtils.isNotBlank(labelValue)) {
            // labelTag小于等于6位的时候和 和超过6位的时候(8为拼接了模糊百分号)不同处理
            if (labelValue.length() > 8) {
                // tag中存在code
                subStringValue = labelValue.substring(1, 7);
                String substringCode = "%" + labelValue.substring(7);
                searchKeyMap.put("labelValue", substringCode);
                Page<ParmLabelAgent> page = PageHelper.startPage(startPage, row);
                labelAgents = parmLabelAgentMapper.selectByOptionsByCode(searchKeyMap);

                if (CollectionUtils.isEmpty(labelAgents)) {
                    return null;
                }

                List<LabelAgentDTO> labelAgentDTOSCopy = BeanMapping.copyList(labelAgents, LabelAgentDTO.class);
                if (labelAgentDTOSCopy.size() == 1) {
                    return new PageResultDTO<>(startPage, row, page.getTotal(), page.getPages(), labelAgentDTOSCopy);
                }

                List<ParmLabelAgent> results = new ArrayList<>();
                // labelAgentCode值存在多条的情况下 进行labelValue值的匹配 数据的校验
                for (ParmLabelAgent labelAgent : labelAgents) {
                    if (labelAgent != null) {
                        if (labelAgent.getLabelValue().equals(subStringValue)) {
                            results.add(labelAgent);
                        }
                    }
                }
                List<LabelAgentDTO> labelAgentDTOS = BeanMapping.copyList(results, LabelAgentDTO.class);
                return new PageResultDTO<>(startPage, row, page.getTotal(), page.getPages(), labelAgentDTOS);
            }
        }

        // 若labelValue为空 或者 labelTag长度不大于8 可直接和labelValue模糊匹配
        Page<ParmLabelAgent> page = PageHelper.startPage(startPage, row);
        labelAgents = parmLabelAgentMapper.selectByOptions(searchKeyMap);

        if (CollectionUtils.isEmpty(labelAgents)) {
            log.info("未查询到系统标签类型信息,标签类型:{},标签值:{}", labelType, labelValue);
        }

        List<LabelAgentDTO> labelAgentDTOS = BeanMapping.copyList(labelAgents, LabelAgentDTO.class);
        return new PageResultDTO<>(startPage, row, page.getTotal(), page.getPages(), labelAgentDTOS);
    }

    /**
     * 查询某类型下的所有
     *
     * @param labelType 类型
     * @return List<LabelAgentDTO>
     */
    @Override
    public List<LabelAgentDTO> selectListByLabelType(String labelType, String organizationNumber, List<String> labelValues) {
        if (!StringUtils.isEmpty(labelType)) {
            List<ParmLabelAgent> list = parmLabelAgentMapper.selectListByLabelType(labelType, organizationNumber, labelValues);
            return BeanMapping.copyList(list, LabelAgentDTO.class);
        }
        return null;
    }

    /**
     * 通过id查询
     *
     * @param id 代码类型名称
     * @return LabelAgentDTO
     */
    @Override
    public LabelAgentDTO findParamLabelAgentById(String id) {
        if (StringUtils.isBlank(id)) {
            throw new AnyTxnParameterException(AnyTxnParameterRespCodeEnum.P_PARAMETER_ID_EMPTY_FAULT);
        }
        ParmLabelAgent parmLabelAgent = parmLabelAgentMapper.selectByPrimaryKey(id);
        if (parmLabelAgent == null) {
            return null;
        }
        String organizationNumber = StringUtils.isEmpty(parmLabelAgent.getOrganizationNumber())
                ? OrgNumberUtils.getOrg() : parmLabelAgent.getOrganizationNumber();

        List<ParmLabelAgent> labelAgents = parmLabelAgentMapper.
                selectByLabelTypeAndValue(parmLabelAgent.getLabelType(), null, organizationNumber);
        if (CollectionUtils.isEmpty(labelAgents)) {
            log.error("根据标签类型:{} 标签值:{} 查询标签代理商参数不存在", parmLabelAgent.getLabelType(),
                    parmLabelAgent.getLabelValue());
            throw new AnyTxnParameterException(AnyTxnParameterRespCodeEnum.D_GET_IS_NULL_FAULT);
        }
        List<LabelAgentDTO> labelAgentDTOS = BeanMapping.copyList(labelAgents, LabelAgentDTO.class);
        LabelAgentDTO labelAgentDTO = labelAgentDTOS.get(0);
        String labelValue = labelAgentDTO.getLabelValue();
        String labelAgentCode = labelAgentDTO.getLabelAgentCode();
        StringBuilder stringBuilder = new StringBuilder();
        StringBuilder labelTag = stringBuilder.append(labelValue).append(labelAgentCode);
        labelAgentDTO.setLabelTag(String.valueOf(labelTag));
        return labelAgentDTO;
    }

    @Override
    public LabelAgentDTO selectByIndex(String labelType, String status) {
        parameterCheck(status, labelType);
        List<ParmLabelAgent> labelAgents = parmLabelAgentMapper.selectByOrgAndLabelTypeStatus(OrgNumberUtils.getOrg(), labelType, status);
        if (CollectionUtils.isEmpty(labelAgents)) {
            return null;
        }

        ParmLabelAgent labelAgent = labelAgents.stream().filter(l -> StringUtils.isEmpty(l.getLabelValue())).findFirst().orElse(null);
        if (labelAgent == null) {
            throw new AnyTxnParameterException(AnyTxnParameterRespCodeEnum.D_DATA_NOT_EXISTS_FAULT,
                    ParameterRepDetailEnum.LABEL_AGENT_PARENT_NOT_EXIST, labelType, status);
        }

        LabelAgentDTO labelAgentDTO = BeanMapping.copy(labelAgent, LabelAgentDTO.class);
        List<ParmLabelAgent> children = labelAgents.stream().filter(l -> !StringUtils.isEmpty(l.getLabelValue())).collect(Collectors.toList());
        if (org.apache.commons.collections4.CollectionUtils.isNotEmpty(children)) {
            List<LabelAgentDTO> allChilrendList = BeanMapping.copyList(children, LabelAgentDTO.class);
            for (LabelAgentDTO parmLabelAgent : allChilrendList) {
                String labelValue = parmLabelAgent.getLabelValue();
                String labelAgentCode = parmLabelAgent.getLabelAgentCode();
                StringBuilder labelTagBuilder = new StringBuilder();
                String labelTag = String.valueOf(labelTagBuilder.append(labelValue).append(labelAgentCode));
                parmLabelAgent.setLabelTag(labelTag);
            }
            labelAgentDTO.setChildren(allChilrendList);
        }
        return labelAgentDTO;
    }

    /**
     * 入参校验
     *
     * @param status    String
     * @param labelType String
     */
    private void parameterCheck(String status, String labelType) {
        if (StringUtils.isEmpty(status)) {
            throw new AnyTxnParameterException(AnyTxnParameterRespCodeEnum.P_PARAMETER_EMPTY_FAULT,
                    ParameterRepDetailEnum.LABEL_STATUS_NULL);
        }
        if (StringUtils.isEmpty(labelType)) {
            throw new AnyTxnParameterException(AnyTxnParameterRespCodeEnum.P_PARAMETER_EMPTY_FAULT,
                    ParameterRepDetailEnum.LABEL_TYPE_NULL);
        }

    }

    /**
     * 添加  审核操作
     *
     * @param labelAgentDTO LabelAgentDTO
     * @return ParameterCompare
     */
    @Override
    @InsertParameterAnnotation(tableName = "parm_label_agent", tableDesc = "Label Agents", isJoinTable = true)
    public ParameterCompare addParamLabelAgent(LabelAgentDTO labelAgentDTO) {
        if (labelAgentDTO == null) {
            throw new AnyTxnParameterException(AnyTxnParameterRespCodeEnum.P_PARAMETER_EMPTY_FAULT);
        }

        parameterCheck(labelAgentDTO.getOrganizationNumber(), labelAgentDTO.getLabelType());
        List<LabelAgentDTO> childrenLabelAgentDTOList = labelAgentDTO.getChildren();
        checkLabelTag(childrenLabelAgentDTOList);

        List<ParmLabelAgent> notDelLabelAgents = parmLabelAgentMapper.selectByOrgAndLabelTypeStatus(labelAgentDTO.getOrganizationNumber(),
                labelAgentDTO.getLabelType(), labelAgentDTO.getStatus());

        // 进行重复标签值的校验
        if (!CollectionUtils.isEmpty(notDelLabelAgents)) {
            // 进行标签值的转换
            List<LabelAgentDTO> notDelLabelAgentsCopy = BeanMapping.copyList(notDelLabelAgents, LabelAgentDTO.class);
            for (LabelAgentDTO parmLabelAgent : notDelLabelAgentsCopy) {
                if (parmLabelAgent != null) {
                    String labelValue = parmLabelAgent.getLabelValue();
                    String labelAgentCode = parmLabelAgent.getLabelAgentCode();
                    StringBuilder labelTagBuilder = new StringBuilder();
                    // 双方都为null 没存在标签值 外层数据
                    if (labelValue != null && labelAgentCode != null) {
                        String labelTag = String.valueOf(labelTagBuilder.append(labelValue).append(labelAgentCode));
                        parmLabelAgent.setLabelTag(labelTag);
                    }
                }
            }
            // 进行标签值的重复性校验
            List<String> labelTagList = notDelLabelAgentsCopy.stream().map(LabelAgentDTO::getLabelTag).collect(Collectors.toList());
            labelTagList.removeAll(Collections.singleton(null));
            for (LabelAgentDTO agentDTO : childrenLabelAgentDTOList) {
                if (agentDTO != null) {
                    if (labelTagList.contains(agentDTO.getLabelTag())) {
                        log.error("该标签值:{} 已存在", agentDTO.getLabelTag());
                        throw new AnyTxnParameterException(AnyTxnParameterRespCodeEnum.D_DATA_ALREADY_EXISTS_FAULT);
                    }
                }
            }
        }
        return ParameterCompare
                .getBuilder()
                .withAfter(labelAgentDTO)
                .withMainParmId(labelAgentDTO.getLabelType())
                .build(LabelAgentDTO.class);
    }

    public void checkLabelTag(List<LabelAgentDTO> childrenLabelAgentDTOList) {
        // 标签值不能存在特殊符号(只能是数字加字母 不区分大小写)
        if (!CollectionUtils.isEmpty(childrenLabelAgentDTOList)) {
            for (LabelAgentDTO child : childrenLabelAgentDTOList) {
                if (child != null) {
                    if (org.apache.commons.lang3.StringUtils.isNotBlank(child.getLabelTag())) {
                        Pattern pattern = Pattern.compile("^[A-Za-z0-9]+$");
                        Matcher matcher = pattern.matcher(child.getLabelTag());
                        if (!matcher.matches()) {
                            throw new AnyTxnParameterException(AnyTxnParameterRespCodeEnum.S_LABEL_VALUE_EXIST_SPECIAL_SYMBOL);
                        }
                    }
                }
            }
        }
    }

    /**
     * 修改
     *
     * @param labelAgentDTO LabelAgentDTO
     * @return ParameterCompare
     */
    @Override
    @UpdateParameterAnnotation(tableName = "parm_label_agent", tableDesc = "Label Agents", isJoinTable = true)
    public ParameterCompare modifyParamLabelAgent(LabelAgentDTO labelAgentDTO) {
        parameterCheck(labelAgentDTO.getOrganizationNumber(), labelAgentDTO.getLabelType());
        List<LabelAgentDTO> labelAgentDTOList = labelAgentDTO.getChildren();
        checkLabelTag(labelAgentDTOList);

        String organizationNumber = labelAgentDTO.getOrganizationNumber();
        String labelType = labelAgentDTO.getLabelType();
        String status = labelAgentDTO.getStatus();

        List<ParmLabelAgent> labelAgents = parmLabelAgentMapper.selectByOrgAndLabelTypeStatus(organizationNumber, labelType, status);
        ParmLabelAgent labelAgent = labelAgents.stream().filter(l -> StringUtils.isEmpty(l.getLabelValue())).findFirst().orElse(null);
        // 如果status为1的时候 labelAgents为空的时候不报错  在去查status为0的时候数据不存在 再进行报错
        if (labelAgent == null) {
            if ("1".equals(status)) {
                status = "0";
            } else if ("0".equals(status)) {
                status = "1";
            }
            List<ParmLabelAgent> parmLabelAgentList = parmLabelAgentMapper.selectByOrgAndLabelTypeStatus(organizationNumber, labelType, status);
            labelAgent = parmLabelAgentList.stream().filter(l -> StringUtils.isEmpty(l.getLabelValue())).findFirst().orElse(null);
            if (Objects.isNull(labelAgent)) {
                throw new AnyTxnParameterException(AnyTxnParameterRespCodeEnum.D_DATA_NOT_EXISTS_FAULT,
                        ParameterRepDetailEnum.LABEL_AGENT_PARENT_NOT_EXIST, labelType, labelAgentDTO.getStatus());
            }
            // 恢复接口中要进行修改的数据
            labelAgent.setStatus(labelAgentDTO.getStatus());
        }

        List<ParmLabelAgent> labelAgentChildren = labelAgents.stream().filter(l -> !StringUtils.isEmpty(l.getLabelValue())).collect(Collectors.toList());
        LabelAgentDTO labelAgentParent = BeanMapping.copy(labelAgent, LabelAgentDTO.class);
        if (!CollectionUtils.isEmpty(labelAgentChildren)) {
            labelAgentParent.setChildren(BeanMapping.copyList(labelAgentChildren, LabelAgentDTO.class));
        }

        return ParameterCompare
                .getBuilder()
                .withAfter(labelAgentDTO)
                .withBefore(labelAgentParent)
                .withMainParmId(labelAgentDTO.getLabelType())
                .build(LabelAgentDTO.class);

    }

    /**
     * 根据标签类型+状态删除标签代理商信息
     *
     * @param labelType String
     * @param status    String
     * @return ParameterCompare
     */
    @Override
    @DeleteParameterAnnotation(tableName = "parm_label_agent", tableDesc = "Label Agents", isJoinTable = true)
    public ParameterCompare deleteParamLabelAgent(String labelType, String status) {
        List<ParmLabelAgent> labelAgents = parmLabelAgentMapper
                .selectByOrgAndLabelTypeStatus(OrgNumberUtils.getOrg(), labelType, status);
        if (CollectionUtils.isEmpty(labelAgents)) {
            throw new AnyTxnParameterException(AnyTxnParameterRespCodeEnum.D_DATA_NOT_EXISTS_FAULT,
                    ParameterRepDetailEnum.ORG_LABEL_TYPE_NOT_EXIST, labelType, status);
        }

        // 这里不需要进行tag的转换  当value为空时 tag 必为空 当value 不为空时 无需考虑 code 的值是否存在
        ParmLabelAgent labelAgent = labelAgents.stream().filter(l -> !StringUtils.isEmpty(l.getLabelValue())).findFirst().orElse(null);
        if (Objects.nonNull(labelAgent)) {
            throw new AnyTxnParameterException(AnyTxnParameterRespCodeEnum.D_DATA_NOT_EXISTS_FAULT,
                    ParameterRepDetailEnum.VALID_LABEL_VALUE_NOT_DEL, OrgNumberUtils.getOrg(), labelType);
        }
        return ParameterCompare
                .getBuilder()
                .withBefore(labelAgents.get(0))
                .build(ParmLabelAgent.class);
    }

    @Override
    public List<String> queryLabelType() {
        List<String> strings = parmLabelAgentMapper.selectLabelType(OrgNumberUtils.getOrg());
        if (CollectionUtils.isEmpty(strings)) {
            log.info("机构:{} 下不存在标签信息", OrgNumberUtils.getOrg());
            return null;
        }
        return strings;
    }

    @Override
    public List<String> queryLabelValue(String labelType) {
        List<ParmLabelAgent> labelAgentDTOSList = parmLabelAgentMapper.selectValidLabelValue(OrgNumberUtils.getOrg(), labelType);
        labelAgentDTOSList.removeAll(Collections.singleton(null));
        List<LabelAgentDTO> labelAgentDTOList = BeanMapping.copyList(labelAgentDTOSList, LabelAgentDTO.class);
        if (CollectionUtils.isEmpty(labelAgentDTOList)) {
            log.info("机构:{} 标签类型:{} 下不存在有效且未删除的标签值", OrgNumberUtils.getOrg(), labelType);
        }
        return labelAgentDTOList.stream().map(LabelAgentDTO::getLabelValue).filter(org.apache.commons.lang3.StringUtils::isNotBlank).collect(Collectors.toList());
    }

    /**
     * 修改
     *
     * @param parmModificationRecord ParmModificationRecord
     * @return boolean
     */
    @Override
    @Transactional(rollbackFor = Exception.class, propagation = Propagation.REQUIRED)
    //@ShardingSphereTransactionType(TransactionType.XA)
    public boolean updateDb(ParmModificationRecord parmModificationRecord) {
        LabelAgentDTO labelAgentDTO = JSON.parseObject(parmModificationRecord.getParmBody(), LabelAgentDTO.class);
        ParmLabelAgent copied = BeanMapping.copy(labelAgentDTO, ParmLabelAgent.class);
        List<ParmLabelAgent> parmLabelAgents = Lists.newArrayList(copied);
        if (Objects.nonNull(labelAgentDTO)) {
            List<LabelAgentDTO> children = labelAgentDTO.getChildren();
            if (!CollectionUtils.isEmpty(children)) {
                // 进行值的转换
                for (LabelAgentDTO child : children) {
                    String labelTag = child.getLabelTag();
                    String labelValue = labelTag.substring(0, labelTag.length() - 4);
                    String labelCode = labelTag.substring(6);
                    child.setLabelValue(labelValue);
                    child.setLabelAgentCode(labelCode);
                }
                List<ParmLabelAgent> labelAgents = BeanMapping.copyList(children, ParmLabelAgent.class);
                labelAgents.add(BeanMapping.copy(labelAgentDTO, ParmLabelAgent.class));
                labelAgents.forEach(l -> {
                    l.initUpdateValue(l.getVersionNumber());
                    l.setStatus(labelAgentDTO.getStatus());
                });

                parmLabelAgentMapper.updateByPrimaryKeyBatch(labelAgents);

                List<ParmLabelAgent> insertLabelAgent = labelAgents.stream().filter(l -> StringUtils.isEmpty(l.getId())).collect(Collectors.toList());
                List<String> labelValues = insertLabelAgent.stream().map(ParmLabelAgent::getLabelValue).collect(Collectors.toList());

                if (!CollectionUtils.isEmpty(insertLabelAgent)) {
                    insertLabelAgent.forEach(l -> {
                        l.initCreateValue();
                        l.setId(String.valueOf(numberIdGenerator.generateId(TenantUtils.getTenantId())));
                        l.setStatus(labelAgentDTO.getStatus());
                        l.setDelFlag("0");
                    });

                    // 除去status 根据 labelType labelValue labelAgentCode orgNumber 查询 防止当status不可用时 添加数据 出现脏数据
                    String labelType = labelAgentDTO.getLabelType();
                    String organizationNumber = labelAgentDTO.getOrganizationNumber();
                    String orgNumber = OrgNumberUtils.getOrg(organizationNumber);
                    List<ParmLabelAgent> parmLabelAgentList = parmLabelAgentMapper.selectListByOther(labelType, orgNumber, labelValues);
                    List<String> labelCodeList = parmLabelAgentList.stream().map(ParmLabelAgent::getLabelAgentCode).collect(Collectors.toList());

                    // 获取在本次的添加中 已存在的值
                    List<String> idsList = getIdsList(insertLabelAgent, labelCodeList, parmLabelAgentList);
                    if (!CollectionUtils.isEmpty(idsList)) {
                        // 备份历史数据
                        List<ParmLabelAgentHis> parmLabelAgentHis = BeanMapping.copyList(parmLabelAgentList, ParmLabelAgentHis.class);
                        parmLabelAgentHisSelfMapper.batchInsert(parmLabelAgentHis);

                        // 由于此处建立了联合唯一索引 因此需要将存在的值进行删除
                        parmLabelAgentMapper.deleteByPrimaryKeyBatch(idsList);
                    }
                    parmLabelAgentMapper.batchInsert(insertLabelAgent);
                }
                return true;
            }
        }
        // 编辑时 标签值为空 则不需要对标签值进行处理 只需要对标签类型进行处理即可
        parmLabelAgentMapper.updateByPrimaryKeyBatch(parmLabelAgents);
        return true;
    }

    public List<String> getIdsList(List<ParmLabelAgent> insertLabelAgent, List<String> labelCodeList, List<ParmLabelAgent> parmLabelAgentList) {
        List<String> idsList = new ArrayList<>();
        for (ParmLabelAgent parmLabelAgent : insertLabelAgent) {
            if (parmLabelAgent != null) {
                String labelAgentVOCode = parmLabelAgent.getLabelAgentCode();
                if (labelCodeList.contains(labelAgentVOCode)) {
                    for (ParmLabelAgent labelAgent : parmLabelAgentList) {
                        if (labelAgent.getLabelAgentCode().equals(labelAgentVOCode)) {
                            idsList.add(labelAgent.getId());
                        }
                    }
                }
            }
        }
        return idsList;
    }

    /**
     * 新增
     */
    @Override
    @Transactional(rollbackFor = Exception.class, propagation = Propagation.REQUIRED)
    //@ShardingSphereTransactionType(TransactionType.XA)
    protected boolean insertDb(ParmModificationRecord parmModificationRecord) {
        LabelAgentDTO labelAgentDTO = JSON.parseObject(parmModificationRecord.getParmBody(), LabelAgentDTO.class);
        if (Objects.nonNull(labelAgentDTO)) {
            ParmLabelAgent labelAgent = BeanMapping.copy(labelAgentDTO, ParmLabelAgent.class);
            labelAgent.setId(String.valueOf(numberIdGenerator.generateId(TenantUtils.getTenantId())));
            labelAgent.setDelFlag("0");
            labelAgent.initCreateValue();
            int i = parmLabelAgentMapper.insertSelective(labelAgent);
            if (i == 1) {
                List<LabelAgentDTO> childrenLabelAgentList = labelAgentDTO.getChildren();
                List<ParmLabelAgent> labelAgentList = new ArrayList<>();
                if (!CollectionUtils.isEmpty(childrenLabelAgentList)) {
                    for (LabelAgentDTO agentDTO : childrenLabelAgentList) {
                        if (agentDTO != null) {
                            String labelTag = agentDTO.getLabelTag();
                            String labelValue = labelTag.substring(0, labelTag.length() - 4);
                            String labelCode = labelTag.substring(6);
                            ParmLabelAgent parmLabel = BeanMapping.copy(agentDTO, ParmLabelAgent.class);
                            parmLabel.initCreateValue();
                            long id = numberIdGenerator.generateId(TenantUtils.getTenantId());
                            parmLabel.setLabelType(labelAgentDTO.getLabelType());
                            parmLabel.setLabelValue(labelValue);
                            parmLabel.setLabelAgentCode(labelCode);
                            parmLabel.setStatus(labelAgentDTO.getStatus());
                            parmLabel.setDelFlag("0");
                            parmLabel.setId(String.valueOf(id));
                            parmLabel.setOrganizationNumber(OrgNumberUtils.getOrg());
                            labelAgentList.add(parmLabel);
                        }
                    }
                    int result = parmLabelAgentMapper.batchInsert(labelAgentList);
                    return result > 0;
                }
            }
            return true;
        }
        return false;
    }

    /**
     * 逻辑删除
     *
     * @param parmModificationRecord ParmModificationRecord
     * @return boolean
     */
    @Override
    @Transactional(rollbackFor = Exception.class, propagation = Propagation.REQUIRED)
    //@ShardingSphereTransactionType(TransactionType.XA)
    protected boolean deleteDb(ParmModificationRecord parmModificationRecord) {
        ParmLabelAgent parmLabelAgent = JSON.parseObject(parmModificationRecord.getParmBody(),
                ParmLabelAgent.class);
        int result = parmLabelAgentMapper.deleteByLabelType(parmLabelAgent.getOrganizationNumber(),
                parmLabelAgent.getLabelType());
        return result > 0;
    }
}
