package com.anytech.anytxn.parameter.common.service.system;

import com.anytech.anytxn.common.core.utils.BeanMapping;
import com.anytech.anytxn.common.core.utils.OrgNumberUtils;
import com.anytech.anytxn.common.sequence.utils.Number16IdGen;
import com.anytech.anytxn.common.core.utils.TenantUtils;
import com.anytech.anytxn.parameter.base.common.enums.AnyTxnParameterRespCodeEnum;
import com.anytech.anytxn.parameter.base.common.enums.ParameterRepDetailEnum;
import com.anytech.anytxn.parameter.base.common.exception.AnyTxnParameterException;
import com.anytech.anytxn.parameter.common.mapper.broadcast.system.ParmLabelAgentHisSelfMapper;
import com.anytech.anytxn.parameter.common.mapper.broadcast.system.ParmLabelAgentMapper;
import com.anytech.anytxn.parameter.base.common.domain.model.system.ParmLabelAgent;
import com.anytech.anytxn.parameter.base.common.domain.model.system.ParmLabelAgentHis;
import com.anytech.anytxn.parameter.common.service.system.OrganizationInfoServiceImpl;
import com.anytech.anytxn.parameter.base.common.domain.dto.system.LabelAgentDTO;
import com.anytech.anytxn.parameter.base.common.domain.dto.system.LabelDefImportDTO;
import com.anytech.anytxn.parameter.base.common.domain.dto.system.OrganizationInfoResDTO;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.csv.CSVFormat;
import org.apache.commons.csv.CSVParser;
import org.apache.commons.csv.CSVRecord;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;
import org.springframework.web.multipart.MultipartFile;

import jakarta.annotation.Resource;
import java.io.BufferedReader;
import java.io.InputStreamReader;
import java.lang.reflect.Field;
import java.lang.reflect.Method;
import java.time.LocalDateTime;
import java.util.*;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

import static java.nio.charset.StandardCharsets.UTF_8;

/**
 * <pre> 标签定价CSV文件导入处理
 * Description:
 * Company:	    江融信
 * Version:	    1.0
 * Create at:   2023/7/10
 * <p>
 * 修改历史:
 * 日期    作者    版本  修改描述
 * ------------------------------------------------------------------
 * <AUTHOR>
 * </pre>
 */
@Service
@Slf4j
public class LabelDefImportService {

    @Resource
    private ParmLabelAgentMapper labelAgentMapper;
    @Resource
    private ParmLabelAgentHisSelfMapper labelAgentHisSelfMapper;
    @Resource
    private OrganizationInfoServiceImpl organizationInfoService;
    @Resource
    private Number16IdGen numberIdGenerator;

    /**
     * 导入CSV文件
     */
    @Transactional(rollbackFor = Exception.class, propagation = Propagation.REQUIRED)
    public void importLabelCsv(MultipartFile file) throws Exception {
        OrganizationInfoResDTO organizationInfo = organizationInfoService.
                findOrganizationInfo(OrgNumberUtils.getOrg());
        String header = "no,labelType,labelValue,labelDescription,firstAgentDescription,secondAgentDescription,thirdAgentDescription,";
        String[] headers = header.split(",");
        List<LabelDefImportDTO> labelDefImportDTOS = readCsv(file, headers, LabelDefImportDTO.class);
        if (!CollectionUtils.isEmpty(labelDefImportDTOS) && labelDefImportDTOS.size() > 0) {
            //按照标签序号升序排序
            List<LabelDefImportDTO> collect = labelDefImportDTOS.stream().sorted(
                    Comparator.comparing(LabelDefImportDTO::getNo)).collect(Collectors.toList());
            //校验
            List<LabelDefImportDTO> labelDefImportDTOList = checkLabelAgent(collect, organizationInfo);
            Map<String, List<LabelDefImportDTO>> mapLabelDefIn = labelDefImportDTOList.stream()
                    .collect(Collectors.groupingBy(LabelDefImportDTO::getLabelType));
            for (Map.Entry<String, List<LabelDefImportDTO>> map : mapLabelDefIn.entrySet()) {
                String labelType = map.getKey();
                List<LabelDefImportDTO> valueList = map.getValue();
                List<ParmLabelAgent> labelAgents = labelAgentMapper.selectByLabelTypeAndValue(
                        labelType, null, organizationInfo.getOrganizationNumber());
                //从0层开始导入
                ParmLabelAgent zeroLabel;
                if (CollectionUtils.isEmpty(labelAgents)) {
                    zeroLabel = insertZeroLabelAgent(valueList.get(0));
                } else {
                    //非0层导入
                    zeroLabel = labelAgents.get(0);
                }
                importFromChildLabel(valueList, zeroLabel, labelType, organizationInfo);
            }
            log.info("标签定价信息导入完毕,共导入:{}条记录", labelDefImportDTOS.size());
        } else {
            log.error("标签定价CSV文件为空");
        }
    }

    /**
     * 构建并新增0层标签代理商
     *
     * @param pLabelDefImportDTO LabelDefImportDTO
     */
    @Transactional(rollbackFor = Exception.class, propagation = Propagation.REQUIRED)
    ParmLabelAgent insertZeroLabelAgent(LabelDefImportDTO pLabelDefImportDTO) {
        ParmLabelAgent parmLabelAgent = BeanMapping.copy(pLabelDefImportDTO, ParmLabelAgent.class);
        parmLabelAgent.setId(String.valueOf(numberIdGenerator.generateId(TenantUtils.getTenantId())));
//        parmLabelAgent.initCreateValue();
        parmLabelAgent.setUpdateBy("FromCsv");
        parmLabelAgent.setLabelValue("0");
        parmLabelAgent.setOrganizationNumber(OrgNumberUtils.getOrg());
        parmLabelAgent.setStatus("1");
        labelAgentMapper.insertSelective(parmLabelAgent);
        return parmLabelAgent;
    }

    /**
     * 构建并新增一级标签代理商
     *
     * @param pLabelDefImportDTO LabelDefImportDTO
     */
    @Transactional(rollbackFor = Exception.class, propagation = Propagation.REQUIRED)
    ParmLabelAgent insertAllLabelAgent(LabelDefImportDTO pLabelDefImportDTO,
                                       String pid, String agentDesc, String agentCode) {
        ParmLabelAgent parmLabelAgent = BeanMapping.copy(pLabelDefImportDTO, ParmLabelAgent.class);
        parmLabelAgent.setId(String.valueOf(numberIdGenerator.generateId(TenantUtils.getTenantId())));
        parmLabelAgent.setOrganizationNumber(OrgNumberUtils.getOrg());
        parmLabelAgent.setStatus("1");
//        parmLabelAgent.initCreateValue();
        parmLabelAgent.setUpdateBy("FromCsv");
        labelAgentMapper.insertSelective(parmLabelAgent);
        return parmLabelAgent;
    }

    /**
     * 校验CSV文件传入标签记录
     *
     * @param collect          List<LabelDefImportDTO>
     * @param organizationInfo OrganizationInfoResDTO
     * @return List<LabelDefImportDTO>
     */
    private List<LabelDefImportDTO> checkLabelAgent(List<LabelDefImportDTO> collect,
                                                    OrganizationInfoResDTO organizationInfo) {
        List<LabelDefImportDTO> labelDefImportDTOS = new ArrayList<>(16);
        collect.forEach(l -> {
            String labelValue = l.getLabelValue() == null ? "" : l.getLabelValue();
            Integer no = l.getNo() == null ? 0 : l.getNo();
            Pattern compile = Pattern.compile("[0-9|A-Z]");
            if (StringUtils.isEmpty(labelValue)) {
                log.error("导入记录,labelValue为空,跳过处理,序号:{}", no);
            } else if (!StringUtils.isEmpty(labelValue) && (labelValue.length() != 6)) {
                log.error("导入记录,labelValue:{},不等于6位,跳过处理,序号:{}", labelValue, no);
            } else if (!compile.matcher(labelValue).find()) {
                log.error("导入记录,labelValue:{},数据格式异常,不是由大写字母或者数字组成,跳过处理,序号:{}",
                        labelValue, no);
            } else if (!StringUtils.isEmpty(labelValue) && "00".equals(labelValue)) {
                log.error("导入记录,labelValue:{},前两位为00,代表一级代理商不存在,跳过处理,序号:{}",
                        labelValue, no);
            } else {
                labelDefImportDTOS.add(l);
            }
        });
        return labelDefImportDTOS;
    }

    /**
     * 读取csv文件  (一次性读取文件不宜过大)
     *
     * @param file    文件
     * @param headers csv列头
     * @param tClass  返回对象的类型
     * @return CSVRecord 列表
     **/
    private <T> List<T> readCsv(MultipartFile file, String[] headers, Class<T> tClass) throws Exception {
        //创建CSVFormat
        CSVFormat format = CSVFormat.DEFAULT.withHeader(headers);
        //创建CSVParser对象
        List<T> tList = new ArrayList<>();
        BufferedReader br = new BufferedReader(new InputStreamReader(file.getInputStream(), UTF_8));
        br.readLine();
        CSVParser parser = new CSVParser(br, format);
        //获取对象的PropertyDescriptor
        Map<String, String> descriptorMap = getCsvFieldMapPropertyDescriptor(tClass);
        Map<String, Integer> map = parser.getHeaderMap();
        for (CSVRecord record : parser) {
            T t = tClass.newInstance();
            for (Map.Entry<String, Integer> entry : map.entrySet()) {
                if (descriptorMap.containsKey(entry.getKey())
                        && record.size() > entry.getValue()) {
                    String name = entry.getKey();
                    String type = descriptorMap.get(name);
                    String values = record.get(entry.getValue());
                    if (!StringUtils.isEmpty(values)) {
                        if ("class java.lang.String".equals(type)) {
                            Method m = tClass.getDeclaredMethod("set" +
                                    name.replaceFirst(name.substring(0, 1),
                                            name.substring(0, 1).toUpperCase()), String.class);
                            m.invoke(t, record.get(entry.getValue()));
                        }
                        if ("class java.lang.Integer".equals(type)) {
                            Method m = tClass.getDeclaredMethod("set" +
                                    name.replaceFirst(name.substring(0, 1), name.substring(0, 1).toUpperCase()), Integer.class);
                            Integer value = Integer.valueOf(record.get(entry.getValue()));
                            m.invoke(t, value);
                        }
                    }
                }
            }
            tList.add(t);
        }
        return tList;
    }

    /**
     * 获取对应对象中包含CsvCsvField字段的 PropertyDescriptor
     *
     * @param tClass 对象的class
     * @return Map
     */
    private Map<String, String> getCsvFieldMapPropertyDescriptor(Class tClass) {
        Map<String, String> descriptorMap = new HashMap<>(16);
        Field[] fields = tClass.getDeclaredFields();
        for (Field field : fields) {
            String fieldName = field.getName();
            String type = field.getGenericType().toString();
            descriptorMap.put(fieldName, type);
        }
        return descriptorMap;
    }

    /**
     * 从非0层开始导入
     *
     * @param valueList        List<LabelDefImportDTO>
     * @param zeroLabel        ParmLabelAgent
     * @param labelType        String
     * @param organizationInfo OrganizationInfoResDTO
     */
    private void importFromChildLabel(List<LabelDefImportDTO> valueList,
                                      ParmLabelAgent zeroLabel,
                                      String labelType,
                                      OrganizationInfoResDTO organizationInfo) {
        for (LabelDefImportDTO value : valueList) {
            String labelValue = value.getLabelValue();
            if (StringUtils.isEmpty(labelValue)) {
                log.error("导入标签值不能为空,序号:{}", value.getNo());
                throw new AnyTxnParameterException(AnyTxnParameterRespCodeEnum
                        .D_DATA_NOT_EXISTS_FAULT);
            } else {
                String first = labelValue.substring(0, 2);
                String second = labelValue.substring(2, 4);
                String third = labelValue.substring(4, 6);
                String id = zeroLabel.getId();
                List<ParmLabelAgent> firstLabelList = labelAgentMapper.
                        selectByLabelTypeAndValue(labelType, null,
                                organizationInfo.getOrganizationNumber());
                if (!CollectionUtils.isEmpty(firstLabelList)) {
                    importFromSubLabel(firstLabelList.get(0), labelType, second, organizationInfo, value,
                            labelValue, third);
                } else {
                    ParmLabelAgent firstLabelAgent = insertAllLabelAgent(value, id,
                            value.getFirstAgentDescription(), first);
                    importFromSubLabel(firstLabelAgent, labelType, second, organizationInfo, value,
                            labelValue, third);
                }
            }
        }
    }

    /**
     * 导入子Label
     *
     * @param firstLabel       ParmLabelAgent
     * @param labelType        String
     * @param second           String
     * @param organizationInfo OrganizationInfoResDTO
     * @param value            LabelDefImportDTO
     * @param labelValue       String
     * @param third            String
     */
    private void importFromSubLabel(ParmLabelAgent firstLabel,
                                    String labelType,
                                    String second,
                                    OrganizationInfoResDTO organizationInfo,
                                    LabelDefImportDTO value,
                                    String labelValue,
                                    String third) {
        if (!StringUtils.isEmpty(second) && !"00".equals(second)) {
            List<ParmLabelAgent> secondLabelList = labelAgentMapper.
                    selectByLabelTypeAndValue(labelType, null, organizationInfo.getOrganizationNumber());
            if (CollectionUtils.isEmpty(secondLabelList)) {
                ParmLabelAgent secondLabel = insertAllLabelAgent(value, firstLabel.getId(),
                        value.getSecondAgentDescription(), second);
                String pid = secondLabel.getId();
                if (!StringUtils.isEmpty(third) && !"00".equals(third)) {
                    insertAllLabelAgent(value, pid, value.getThirdAgentDescription(), third);
                } else {
                    log.info("标签类型:{},标签值:{},不存在三级代理商", labelType, labelValue);
                }
            } else {
                ParmLabelAgent secondLabel = secondLabelList.get(0);
                List<ParmLabelAgent> thirdLabelList = labelAgentMapper.
                        selectByLabelTypeAndValue(labelType, labelValue,
                                organizationInfo.getOrganizationNumber());
                if (!CollectionUtils.isEmpty(thirdLabelList)) {
                    log.error("根据唯一索引查询: labelType:{},labelValue:{},pid:{}, " +
                                    "agentCode:{},organizationNumber:{},status:{} " +
                                    "数据已存在", labelType, labelValue, secondLabel.getId(), third,
                            organizationInfo.getOrganizationNumber(), "1");
                    throw new AnyTxnParameterException(AnyTxnParameterRespCodeEnum
                            .P_PARAMETER_DATA_EXIST);
                } else {
                    insertAllLabelAgent(value, secondLabel.getId(),
                            value.getThirdAgentDescription(), third);
                }
            }
        } else {
            log.info("标签类型:{},标签值:{},不存在二级三级代理商", labelType, labelValue);
        }
    }

    /**
     * 导入标签代理商信息(新增)
     */
    @Transactional(rollbackFor = Exception.class, propagation = Propagation.REQUIRED)
    public void importAddLabelInfo(List<LabelAgentDTO> labelAgentDTOS) {
        if (CollectionUtils.isEmpty(labelAgentDTOS)) {
            throw new AnyTxnParameterException(AnyTxnParameterRespCodeEnum
                    .P_PARAMETER_DATA_EXIST, ParameterRepDetailEnum.IMPORT_DATA_NULL);
        }
        log.info("导入标签类型条数:{}", labelAgentDTOS.size());
        for (LabelAgentDTO labelAgentDTO : labelAgentDTOS) {
            if (Objects.nonNull(labelAgentDTO)) {
                //标签值重复性校验
//                dumpLabelDataCheck(labelAgentDTO);
                List<LabelAgentDTO> children = labelAgentDTO.getChildren();
                if (CollectionUtils.isEmpty(children)) {
                    log.info("标签类型:{} 下没有要导入的标签值 跳过", labelAgentDTO.getLabelType());
                    continue;
                }
                ParmLabelAgent labelAgent = BeanMapping.copy(labelAgentDTO, ParmLabelAgent.class);
                labelAgent.setId(String.valueOf(numberIdGenerator.generateId(TenantUtils.getTenantId())));
                labelAgent.setOrganizationNumber(OrgNumberUtils.getOrg());
                labelAgent.setDelFlag("0");
                labelAgent.initCreateValue();
                int i = labelAgentMapper.insertSelective(labelAgent);
                if (i == 1) {
                    List<ParmLabelAgent> labelAgentList = new ArrayList<>();
                    log.info("标签类型:{} 下共存在:{}条标签值", labelAgentDTO.getLabelType(),
                            labelAgentDTO.getChildren().size());
                    AtomicInteger jumpCount = new AtomicInteger();
                    children.forEach(c -> {
                        if (Objects.nonNull(c)) {
                            String labelTag = c.getLabelTag();
                            if (StringUtils.isEmpty(labelTag)) {
                                log.error("标签类型:{} 下的标签Tag为空 跳过");
                                jumpCount.incrementAndGet();
                                return;
                            }
                            if (!StringUtils.isEmpty(c.getLabelTag())) {
                                Pattern p = Pattern.compile("^[A-Za-z0-9]+$");
                                Matcher matcher = p.matcher(c.getLabelTag());
                                if (!matcher.matches()) {
                                    log.error("标签类型:{} 下的标签Tag存在特殊字符 跳过");
                                    jumpCount.incrementAndGet();
                                    return;
                                }
                            }
                            String labelValue = labelTag.substring(0, 6);
                            String agentCode = labelTag.substring(6);
                            ParmLabelAgent paramLabel = BeanMapping.copy(c, ParmLabelAgent.class);
                            paramLabel.setId(String.valueOf(numberIdGenerator.generateId(TenantUtils.getTenantId())));
                            paramLabel.setOrganizationNumber(OrgNumberUtils.getOrg());
                            paramLabel.setLabelType(labelAgentDTO.getLabelType());
                            paramLabel.setLabelValue(labelValue);
                            paramLabel.setLabelAgentCode(agentCode);
                            paramLabel.setDelFlag("0");
                            paramLabel.setStatus(labelAgentDTO.getStatus());
                            paramLabel.initCreateValue();
                            labelAgentList.add(paramLabel);
                        }
                    });
                    log.info("共跳过:{}条数据", jumpCount);
                    if (!CollectionUtils.isEmpty(labelAgentList)) {
                        labelAgentMapper.batchInsert(labelAgentList);
                    } else {
                        throw new AnyTxnParameterException(
                                AnyTxnParameterRespCodeEnum.P_PARAMETER_DATA_NOT_EXIST,
                                ParameterRepDetailEnum.LABEL_TAG_DATA_ERROR, labelAgentDTO.getLabelType());
                    }
                }
            }
        }
    }

    /**
     * 导入标签代理商信息(修改)
     */
    @Transactional(rollbackFor = Exception.class, propagation = Propagation.REQUIRED)
    public void importModifyLabelInfo(List<LabelAgentDTO> labelAgentDTOS) {
        if (CollectionUtils.isEmpty(labelAgentDTOS)) {
            throw new AnyTxnParameterException(AnyTxnParameterRespCodeEnum
                    .P_PARAMETER_DATA_EXIST, ParameterRepDetailEnum.IMPORT_DATA_NULL);
        }
        log.info("导入标签类型条数:{}", labelAgentDTOS.size());
        for (LabelAgentDTO labelAgentDTO : labelAgentDTOS) {
            if (Objects.nonNull(labelAgentDTO)) {
                existLabelDataCheck(labelAgentDTO);
                List<LabelAgentDTO> children = labelAgentDTO.getChildren();
                if (!CollectionUtils.isEmpty(children)) {
                    for (LabelAgentDTO child : children) {
                        String labelTag = child.getLabelTag();
                        String labelValue = labelTag.substring(0, 6);
                        String labelCode = labelTag.substring(6);
                        child.setLabelValue(labelValue);
                        child.setLabelAgentCode(labelCode);
                    }
                    List<ParmLabelAgent> labelAgents = BeanMapping.copyList(children, ParmLabelAgent.class);
                    labelAgents.add(BeanMapping.copy(labelAgentDTO, ParmLabelAgent.class));
                    labelAgents.forEach(l -> {
                        l.initUpdateValue(l.getVersionNumber());
                        l.setStatus(labelAgentDTO.getStatus());
                    });
                    labelAgentMapper.updateByPrimaryKeyBatch(labelAgents);
                    List<ParmLabelAgent> insertLabelAgent = labelAgents.stream().filter(l ->
                            StringUtils.isEmpty(l.getId())).collect(Collectors.toList());
                    List<String> labelValues = insertLabelAgent.stream().map(ParmLabelAgent::getLabelValue)
                            .collect(Collectors.toList());
                    if (!CollectionUtils.isEmpty(insertLabelAgent)) {
                        insertLabelAgent.forEach(l -> {
                            l.setId(String.valueOf(numberIdGenerator.generateId(TenantUtils.getTenantId())));
                            l.initCreateValue();
                            l.setStatus(labelAgentDTO.getStatus());
                            l.setDelFlag("0");
                        });
                        String labelType = labelAgentDTO.getLabelType();
                        String orgNumber = OrgNumberUtils.getOrg();
                        List<ParmLabelAgent> parmLabelAgentList = labelAgentMapper.
                                selectListByOther(labelType, orgNumber, labelValues);
                        List<String> labelCodeList = parmLabelAgentList.stream().map
                                (ParmLabelAgent::getLabelAgentCode).collect(Collectors.toList());
                        List<String> idsList = new ArrayList<>();
                        for (ParmLabelAgent parmLabelAgent : insertLabelAgent) {
                            if (Objects.nonNull(parmLabelAgent)) {
                                String labelAgentVOCode = parmLabelAgent.getLabelAgentCode();
                                if (labelCodeList.contains(labelAgentVOCode)) {
                                    for (ParmLabelAgent labelAgent : parmLabelAgentList) {
                                        if (labelAgent.getLabelAgentCode().equals(labelAgentVOCode)) {
                                            idsList.add(labelAgent.getId());
                                        }
                                    }
                                }
                            }
                        }
                        if (!CollectionUtils.isEmpty(idsList)) {
                            List<ParmLabelAgentHis> parmLabelAgentHis = BeanMapping.copyList
                                    (parmLabelAgentList, ParmLabelAgentHis.class);
                            labelAgentHisSelfMapper.batchInsert(parmLabelAgentHis);
                            labelAgentMapper.deleteByPrimaryKeyBatch(idsList);
                        }
                        labelAgentMapper.batchInsert(insertLabelAgent);
                    }
                }
            }
        }
    }

    /**
     * 导入标签代理商信息：新增修改删除的更新操作合三为一
     */
    @Transactional(rollbackFor = Exception.class, propagation = Propagation.REQUIRED)
    public void importLabelInfo(List<LabelAgentDTO> labelAgentDTOS) {
        if (CollectionUtils.isEmpty(labelAgentDTOS)) {
            throw new AnyTxnParameterException(AnyTxnParameterRespCodeEnum
                    .P_PARAMETER_DATA_EXIST, ParameterRepDetailEnum.IMPORT_DATA_NULL);
        }
        log.info("导入标签类型条数:{}", labelAgentDTOS.size());
        for (LabelAgentDTO labelAgentDTO : labelAgentDTOS) {
            if (Objects.nonNull(labelAgentDTO)) {
                List<LabelAgentDTO> children = labelAgentDTO.getChildren();
                if (CollectionUtils.isEmpty(children)) {
                    log.info("标签类型:{} 下没有要导入的标签值 跳过", labelAgentDTO.getLabelType());
                    continue;
                }
                //标签代理商信息格式校验
                List<LabelAgentDTO> labelAgentDTOList = labelFormatCheck(children);
                String labelType = labelAgentDTO.getLabelType();
                String status = labelAgentDTO.getStatus();
                LabelAgentDTO childLabelAgent = children.stream().filter(l ->
                        !StringUtils.isEmpty(l.getLabelTag())
                                && (!StringUtils.isEmpty(l.getDelFlag()) && "0".equals(l.getDelFlag())))
                        .findFirst().orElse(null);
                String delFlag = StringUtils.isEmpty(labelAgentDTO.getDelFlag()) ? ""
                        : labelAgentDTO.getDelFlag();
                if ("1".equals(delFlag)) {
                    if (Objects.nonNull(childLabelAgent)) {
                        log.error("标签类型:{}下还存在未删除的标签值,不允许删除", labelType, childLabelAgent
                                .getLabelTag());
                        throw new AnyTxnParameterException(AnyTxnParameterRespCodeEnum.D_DARA_DELETE_ERROR,
                                ParameterRepDetailEnum.VALID_LABEL_VALUE_NOT_DEL, OrgNumberUtils.getOrg(),
                                labelType);
                    } else {
                        List<ParmLabelAgent> labelAgents = labelAgentMapper
                                .selectByOrgAndLabelTypeStatus(OrgNumberUtils.getOrg(), labelType, status);
                        if (CollectionUtils.isEmpty(labelAgents)){
                            log.error("标签类型:{},不存在,不允许删除", labelType);
                            throw new AnyTxnParameterException(AnyTxnParameterRespCodeEnum.D_DARA_DELETE_ERROR,
                                    ParameterRepDetailEnum.ORG_LABEL_TYPE_NOT_EXIST, OrgNumberUtils.getOrg(),
                                    labelType);
                        }
                        ParmLabelAgent labelAgent = labelAgentMapper.selectByParentLabel(OrgNumberUtils
                                .getOrg(), labelType, status, labelAgentDTO.getDelFlag());
                        if (Objects.nonNull(labelAgent)){
                            log.error("标签类型:{},状态:{},数据已删除", labelType, status);
                            throw new AnyTxnParameterException(AnyTxnParameterRespCodeEnum.D_DARA_DELETE_ERROR,
                                    ParameterRepDetailEnum.LABEL_VALUE_DEL, OrgNumberUtils.getOrg(),
                                    labelType, status);
                        }
                    }
                }

                //根据标签类型+状态查询标签代理商参数表，有值则更新，没值则新增
                List<ParmLabelAgent> labelAgents = labelAgentMapper.selectByOrgAndLabelType
                        (OrgNumberUtils.getOrg(), labelType);
                if (!CollectionUtils.isEmpty(labelAgents)) {
                    dumpLabelDataCheck(children);
                    List<ParmLabelAgentHis> labelAgentHisList = BeanMapping.copyList(labelAgents,
                            ParmLabelAgentHis.class);
                    labelAgentHisList.forEach(l -> {
                        l.setId(String.valueOf(numberIdGenerator.generateId(TenantUtils.getTenantId())));
                        l.setOrganizationNumber(OrgNumberUtils.getOrg());
                        l.setCreateTime(LocalDateTime.now());
                        l.setUpdateTime(LocalDateTime.now());
                        l.setVersionNumber(1L);
                    });
                    labelAgentHisSelfMapper.batchInsert(labelAgentHisList);
                    labelAgentMapper.deleteByIndex(OrgNumberUtils.getOrg(), labelType);
                }
                ParmLabelAgent labelAgent = BeanMapping.copy(labelAgentDTO, ParmLabelAgent.class);
                labelAgent.setId(String.valueOf(numberIdGenerator.generateId(TenantUtils.getTenantId())));
                labelAgent.setOrganizationNumber(OrgNumberUtils.getOrg());
                labelAgent.initCreateValue();
                labelAgent.setLabelValue(null);
                labelAgent.setLabelAgentCode(null);
                labelAgent.setVersionNumber(1L);
                int i = labelAgentMapper.insertSelective(labelAgent);
                if (i == 1) {
                    labelAgentDTOList.forEach(l -> {
                        String labelTag = l.getLabelTag();
                        String labelValue = labelTag.substring(0, 6);
                        String agentCode = labelTag.substring(6);
                        l.setId(String.valueOf(numberIdGenerator.generateId(TenantUtils.getTenantId())));
                        l.setLabelType(labelType);
                        l.setLabelValue(labelValue);
                        l.setLabelAgentCode(agentCode);
                        l.setStatus(labelAgent.getStatus());
                        l.setOrganizationNumber(OrgNumberUtils.getOrg());
                        l.setCreateTime(LocalDateTime.now());
                        l.setUpdateTime(LocalDateTime.now());
                        l.setVersionNumber(1L);
                    });
                    List<ParmLabelAgent> labelAgentList = BeanMapping.copyList(labelAgentDTOList, ParmLabelAgent.class);
                    labelAgentMapper.batchInsert(labelAgentList);
                }
            }
        }
    }

    /**
     * 标签代理商信息格式校验
     *
     * @param labelAgentDTOS List<LabelAgentDTO>
     */
    private List<LabelAgentDTO> labelFormatCheck(List<LabelAgentDTO> labelAgentDTOS) {
        AtomicInteger jumpCount = new AtomicInteger();
        List<LabelAgentDTO> labelAgentDTOList = new ArrayList<>(16);
        labelAgentDTOS.forEach(l -> {
            if (Objects.nonNull(l)) {
                String labelTag = l.getLabelTag();
                if (StringUtils.isEmpty(labelTag)) {
                    log.error("标签类型:{} 下的标签Tag为空 跳过");
                    jumpCount.incrementAndGet();
                } else {
                    Pattern p = Pattern.compile("^[A-Za-z0-9]+$");
                    Matcher matcher = p.matcher(l.getLabelTag());
                    if (!matcher.matches()) {
                        log.error("标签类型:{} 下的标签Tag存在特殊字符 跳过");
                        jumpCount.incrementAndGet();
                    } else {
                        labelAgentDTOList.add(l);
                    }
                }
            }
        });
        log.info("共跳过:{}条数据", jumpCount);
        return labelAgentDTOList;
    }

    /**
     * 标签值重复性校验
     *
     * @param children List<LabelAgentDTO>
     */
    @Transactional(rollbackFor = Exception.class, propagation = Propagation.REQUIRED)
    void dumpLabelDataCheck(List<LabelAgentDTO> children) {
        for (LabelAgentDTO labelAgentDTO : children) {
            String labelAgentDTOLabelTag = labelAgentDTO.getLabelTag();
            String labelValue = labelAgentDTOLabelTag.substring(0, 6);
            String labelAgentCode = labelAgentDTOLabelTag.substring(6);
            ParmLabelAgent labelAgent = labelAgentMapper
                    .selectByNoDelData(OrgNumberUtils.getOrg(), labelAgentDTO.getLabelType(),
                            labelAgentDTO.getStatus(), labelValue, labelAgentCode);
            if (Objects.nonNull(labelAgent)) {
                log.error("标签类型:{},标签值:{},agentCode:{},status:{},数据已存在",
                        labelAgentDTO.getLabelType(), labelValue, labelAgentCode, labelAgentDTO.getStatus());
                throw new AnyTxnParameterException(AnyTxnParameterRespCodeEnum
                        .D_DATA_ALREADY_EXISTS_FAULT);
            }
        }
    }

    //校验导入修改的标签值是否存在
    private void existLabelDataCheck(LabelAgentDTO labelAgentDTO) {
        List<ParmLabelAgent> labelAgents = labelAgentMapper.
                selectByOrgAndLabelTypeStatus(labelAgentDTO.getOrganizationNumber(),
                        labelAgentDTO.getLabelType(), labelAgentDTO.getStatus());
        if (CollectionUtils.isEmpty(labelAgents)) {
            throw new AnyTxnParameterException(AnyTxnParameterRespCodeEnum.D_DATA_NOT_EXISTS_FAULT,
                    ParameterRepDetailEnum.ORG_LABEL_TYPE_NOT_EXIST,
                    labelAgentDTO.getOrganizationNumber(), labelAgentDTO.getLabelType());
        }
        ParmLabelAgent labelAgent = labelAgents.stream().filter(l -> StringUtils.isEmpty(
                l.getLabelValue())).findFirst().orElse(null);
        if (Objects.isNull(labelAgent)) {
            throw new AnyTxnParameterException(AnyTxnParameterRespCodeEnum.D_DATA_NOT_EXISTS_FAULT,
                    ParameterRepDetailEnum.LABEL_AGENT_PARENT_NOT_EXIST, labelAgentDTO.getLabelType(),
                    labelAgentDTO.getStatus());
        }
    }
}
