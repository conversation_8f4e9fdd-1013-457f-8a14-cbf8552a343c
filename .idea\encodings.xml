<?xml version="1.0" encoding="UTF-8"?>
<project version="4">
  <component name="Encoding">
    <file url="file://$PROJECT_DIR$/anytxn-business-core/anytxn-business-core-base/src/main/java" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/anytxn-business-core/anytxn-business-core-base/src/main/resources" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/anytxn-business-core/anytxn-business-core-dao/src/main/java" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/anytxn-business-core/anytxn-business-core-dao/src/main/resources" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/anytxn-business-core/anytxn-business-core-sdk/src/main/java" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/anytxn-business-core/anytxn-business-core-sdk/src/main/resources" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/anytxn-business-core/src/main/java" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/anytxn-business-core/src/main/resources" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/anytxn-parameter/anytxn-parameter-base/src/main/java" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/anytxn-parameter/anytxn-parameter-base/src/main/resources" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/anytxn-parameter/anytxn-parameter-batch/src/main/java" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/anytxn-parameter/anytxn-parameter-batch/src/main/resources" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/anytxn-parameter/anytxn-parameter-client/src/main/java" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/anytxn-parameter/anytxn-parameter-client/src/main/resources" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/anytxn-parameter/anytxn-parameter-sdk/src/main/java" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/anytxn-parameter/anytxn-parameter-sdk/src/main/resources" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/anytxn-parameter/src/main/java" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/anytxn-parameter/src/main/resources" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/anytxn-parent/anytxn-common/anytxn-common-core/src/main/java" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/anytxn-parent/anytxn-common/anytxn-common-core/src/main/resources" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/anytxn-parent/anytxn-common/anytxn-common-loadbalancer/src/main/java" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/anytxn-parent/anytxn-common/anytxn-common-loadbalancer/src/main/resources" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/anytxn-parent/anytxn-common/anytxn-common-redis/src/main/java" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/anytxn-parent/anytxn-common/anytxn-common-redis/src/main/resources" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/anytxn-parent/anytxn-common/anytxn-common-rule/src/main/java" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/anytxn-parent/anytxn-common/anytxn-common-rule/src/main/resources" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/anytxn-parent/anytxn-common/anytxn-common-security/src/main/java" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/anytxn-parent/anytxn-common/anytxn-common-security/src/main/resources" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/anytxn-parent/anytxn-common/anytxn-common-sequence/src/main/java" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/anytxn-parent/anytxn-common/anytxn-common-sequence/src/main/resources" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/anytxn-parent/anytxn-common/anytxn-common-sharding/src/main/java" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/anytxn-parent/anytxn-common/anytxn-common-sharding/src/main/resources" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/anytxn-parent/anytxn-common/src/main/java" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/anytxn-parent/anytxn-common/src/main/resources" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/anytxn-parent/src/main/java" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/anytxn-parent/src/main/resources" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/anytxn-third-party-service/anytxn-file-manager/anytxn-file-manager-sdk/src/main/java" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/anytxn-third-party-service/anytxn-file-manager/anytxn-file-manager-sdk/src/main/resources" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/anytxn-third-party-service/anytxn-file-manager/anytxn-file-manager-server/src/main/java" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/anytxn-third-party-service/anytxn-file-manager/anytxn-file-manager-server/src/main/resources" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/anytxn-third-party-service/anytxn-file-manager/src/main/java" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/anytxn-third-party-service/anytxn-file-manager/src/main/resources" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/anytxn-third-party-service/anytxn-hsm/anytxn-hsm-sdk/src/main/java" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/anytxn-third-party-service/anytxn-hsm/anytxn-hsm-sdk/src/main/resources" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/anytxn-third-party-service/anytxn-hsm/anytxn-hsm-server/src/main/java" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/anytxn-third-party-service/anytxn-hsm/anytxn-hsm-server/src/main/resources" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/anytxn-third-party-service/anytxn-hsm/src/main/java" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/anytxn-third-party-service/anytxn-hsm/src/main/resources" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/anytxn-third-party-service/anytxn-notification/anytxn-notification-sdk/src/main/java" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/anytxn-third-party-service/anytxn-notification/anytxn-notification-sdk/src/main/resources" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/anytxn-third-party-service/anytxn-notification/anytxn-notification-server/src/main/java" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/anytxn-third-party-service/anytxn-notification/anytxn-notification-server/src/main/resources" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/anytxn-third-party-service/anytxn-notification/src/main/java" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/anytxn-third-party-service/anytxn-notification/src/main/resources" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/anytxn-third-party-service/src/main/java" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/anytxn-third-party-service/src/main/resources" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/src/main/java" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/src/main/resources" charset="UTF-8" />
  </component>
</project>