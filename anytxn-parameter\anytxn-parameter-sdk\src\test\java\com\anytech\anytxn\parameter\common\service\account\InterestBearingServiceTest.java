package com.anytech.anytxn.parameter.common.service.account;

import com.anytech.anytxn.common.core.base.ParameterCompare;
import com.anytech.anytxn.common.core.base.PageResultDTO;
import com.anytech.anytxn.common.core.utils.BeanMapping;
import com.anytech.anytxn.common.core.utils.OrgNumberUtils;
import com.anytech.anytxn.common.sequence.utils.Number16IdGen;
import com.anytech.anytxn.common.core.utils.TenantUtils;
import com.anytech.anytxn.parameter.account.service.InterestBearingServiceImpl;
import com.anytech.anytxn.parameter.account.mapper.InterestBearingMapper;
import com.anytech.anytxn.parameter.account.mapper.InterestBearingSelfMapper;
import com.anytech.anytxn.parameter.base.account.domain.dto.InterestBearingDTO;
import com.anytech.anytxn.parameter.base.account.domain.model.InterestBearing;
import com.anytech.anytxn.parameter.base.common.enums.AnyTxnParameterRespCodeEnum;
import com.anytech.anytxn.parameter.base.common.exception.AnyTxnParameterException;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockedStatic;
import org.mockito.junit.jupiter.MockitoExtension;
import org.mockito.junit.jupiter.MockitoSettings;
import org.mockito.quality.Strictness;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.Arrays;
import java.util.Collections;
import java.util.List;

import static org.assertj.core.api.Assertions.assertThat;
import static org.junit.jupiter.api.Assertions.assertThrows;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.*;

/**
 * InterestBearingServiceImpl 单元测试类
 * 
 * 测试 InterestBearingServiceImpl 的所有公共方法：
 * 1. add(InterestBearingDTO dto) - 添加计息参数
 * 2. modify(InterestBearingDTO dto) - 修改计息参数
 * 3. remove(String id) - 删除计息参数
 * 4. findById(String id) - 根据ID查询计息参数
 * 5. findPage(Integer pageNum, Integer pageSize, String organizationNumber, String tableId, String description) - 分页查询计息参数
 * 6. findByOrgAndTableId(String organizationNumber, String tableId) - 根据机构号和表ID查询计息参数
 */
@ExtendWith(MockitoExtension.class)
@MockitoSettings(strictness = Strictness.LENIENT)
public class InterestBearingServiceTest {

    @Mock
    private Number16IdGen numberIdGenerator;

    @Mock
    private InterestBearingMapper interestBearingMapper;

    @Mock
    private InterestBearingSelfMapper interestBearingSelfMapper;

    @InjectMocks
    private InterestBearingServiceImpl interestBearingService;

    private InterestBearingDTO testDTO;
    private InterestBearing testEntity;

    @BeforeEach
    void setUp() {
        // 设置测试数据
        testDTO = new InterestBearingDTO();
        testDTO.setId("TEST_ID_001");
        testDTO.setOrganizationNumber("001");
        testDTO.setTableId("TABLE_001");
        testDTO.setDescription("测试计息参数");
        testDTO.setInterestMark("Y");
        testDTO.setInterestMode("1");
        testDTO.setStartDateOption("1");
        testDTO.setInterestType("1");
        testDTO.setYearBase("360");
        testDTO.setMonthBase("30");
        testDTO.setBaseRate(new BigDecimal("0.05"));
        testDTO.setBaseRatePercent(new BigDecimal("5.00"));
        testDTO.setAdjustRate(new BigDecimal("0.01"));
        testDTO.setAdjustRatePercent(new BigDecimal("1.00"));
        testDTO.setInterestBackdateOption("0");
        testDTO.setInterestBackdateMaxDays(30);
        testDTO.setStatus("A");
        testDTO.setVersionNumber(1L);
        testDTO.setPenaltyRate(new BigDecimal("0.15"));

        testEntity = new InterestBearing();
        testEntity.setId("TEST_ID_001");
        testEntity.setOrganizationNumber("001");
        testEntity.setTableId("TABLE_001");
        testEntity.setDescription("测试计息参数");
        testEntity.setInterestMark("Y");
        testEntity.setInterestMode("1");
        testEntity.setStartDateOption("1");
        testEntity.setInterestType("1");
        testEntity.setYearBase("360");
        testEntity.setMonthBase("30");
        testEntity.setBaseRate(new BigDecimal("0.05"));
        testEntity.setBaseRatePercent(new BigDecimal("5.00"));
        testEntity.setAdjustRate(new BigDecimal("0.01"));
        testEntity.setAdjustRatePercent(new BigDecimal("1.00"));
        testEntity.setInterestBackdateOption("0");
        testEntity.setInterestBackdateMaxDays(30);
        testEntity.setStatus("A");
        testEntity.setVersionNumber(1L);
        testEntity.setPenaltyRate(new BigDecimal("0.15"));
        testEntity.setCreateTime(LocalDateTime.now());
        testEntity.setUpdateTime(LocalDateTime.now());
    }

    @Test
    void testAdd_Success() {
        // Arrange
        try (MockedStatic<OrgNumberUtils> orgNumberUtilsMock = mockStatic(OrgNumberUtils.class);
             MockedStatic<BeanMapping> beanMappingMock = mockStatic(BeanMapping.class);
             MockedStatic<TenantUtils> tenantUtilsMock = mockStatic(TenantUtils.class)) {
            
            orgNumberUtilsMock.when(() -> OrgNumberUtils.getOrg("001")).thenReturn("001");
            tenantUtilsMock.when(() -> TenantUtils.getTenantId()).thenReturn("tenant_001");
            beanMappingMock.when(() -> BeanMapping.copy(testDTO, InterestBearing.class)).thenReturn(testEntity);
            
            when(interestBearingMapper.selectByTableIdAndOrg("TABLE_001", "001")).thenReturn(null);
            when(numberIdGenerator.generateId("tenant_001")).thenReturn(123456L);

            // Act
            ParameterCompare result = interestBearingService.add(testDTO);

            // Assert
            assertThat(result).isNotNull();
            assertThat(result.getAfter()).isNotNull();
            verify(interestBearingMapper).selectByTableIdAndOrg("TABLE_001", "001");
            verify(numberIdGenerator).generateId("tenant_001");
        }
    }

    @Test
    void testAdd_EmptyDTO_ThrowsException() {
        // Act & Assert
        AnyTxnParameterException exception = assertThrows(AnyTxnParameterException.class, () -> {
            interestBearingService.add(null);
        });
        
        assertThat(exception.getErrCode()).isEqualTo(AnyTxnParameterRespCodeEnum.P_PARAMETER_EMPTY_FAULT.getCode());
    }

    @Test
    void testAdd_EmptyTableId_ThrowsException() {
        // Arrange
        testDTO.setTableId("");

        // Act & Assert
        AnyTxnParameterException exception = assertThrows(AnyTxnParameterException.class, () -> {
            interestBearingService.add(testDTO);
        });
        
        assertThat(exception.getErrCode()).isEqualTo(AnyTxnParameterRespCodeEnum.P_PARAMETER_TABLE_ID_EMPTY_FAULT.getCode());
    }

    @Test
    void testAdd_AlreadyExists_ThrowsException() {
        // Arrange
        try (MockedStatic<OrgNumberUtils> orgNumberUtilsMock = mockStatic(OrgNumberUtils.class)) {
            orgNumberUtilsMock.when(() -> OrgNumberUtils.getOrg("001")).thenReturn("001");
            when(interestBearingMapper.selectByTableIdAndOrg("TABLE_001", "001")).thenReturn(testEntity);

            // Act & Assert
            AnyTxnParameterException exception = assertThrows(AnyTxnParameterException.class, () -> {
                interestBearingService.add(testDTO);
            });
            
            assertThat(exception.getErrCode()).isEqualTo(AnyTxnParameterRespCodeEnum.D_EXIST_PARM_PAYMENT_ALLOCATED_BY_ID_FAULT.getCode());
        }
    }

    @Test
    void testModify_Success() {
        // Arrange
        try (MockedStatic<BeanMapping> beanMappingMock = mockStatic(BeanMapping.class)) {
            beanMappingMock.when(() -> BeanMapping.copy(testDTO, InterestBearing.class)).thenReturn(testEntity);
            when(interestBearingMapper.selectByPrimaryKey("TEST_ID_001")).thenReturn(testEntity);

            // Act
            ParameterCompare result = interestBearingService.modify(testDTO);

            // Assert
            assertThat(result).isNotNull();
            assertThat(result.getBefore()).isNotNull();
            assertThat(result.getAfter()).isNotNull();
            verify(interestBearingMapper).selectByPrimaryKey("TEST_ID_001");
        }
    }

    @Test
    void testModify_EmptyDTO_ThrowsException() {
        // Act & Assert
        AnyTxnParameterException exception = assertThrows(AnyTxnParameterException.class, () -> {
            interestBearingService.modify(null);
        });
        
        assertThat(exception.getErrCode()).isEqualTo(AnyTxnParameterRespCodeEnum.P_PARAMETER_EMPTY_FAULT.getCode());
    }

    @Test
    void testModify_DataNotExist_ThrowsException() {
        // Arrange
        when(interestBearingMapper.selectByPrimaryKey("TEST_ID_001")).thenReturn(null);

        // Act & Assert
        AnyTxnParameterException exception = assertThrows(AnyTxnParameterException.class, () -> {
            interestBearingService.modify(testDTO);
        });
        
        assertThat(exception.getErrCode()).isEqualTo(AnyTxnParameterRespCodeEnum.P_PARAMETER_DATA_NOT_EXIST.getCode());
    }

    @Test
    void testRemove_Success() {
        // Arrange
        when(interestBearingMapper.selectByPrimaryKey("TEST_ID_001")).thenReturn(testEntity);

        // Act
        ParameterCompare result = interestBearingService.remove("TEST_ID_001");

        // Assert
        assertThat(result).isNotNull();
        assertThat(result.getBefore()).isNotNull();
        verify(interestBearingMapper).selectByPrimaryKey("TEST_ID_001");
    }

    @Test
    void testRemove_EmptyId_ThrowsException() {
        // Act & Assert
        AnyTxnParameterException exception = assertThrows(AnyTxnParameterException.class, () -> {
            interestBearingService.remove("");
        });
        
        assertThat(exception.getErrCode()).isEqualTo(AnyTxnParameterRespCodeEnum.P_PARAMETER_EMPTY_FAULT.getCode());
    }

    @Test
    void testRemove_DataNotExist_ThrowsException() {
        // Arrange
        when(interestBearingMapper.selectByPrimaryKey("TEST_ID_001")).thenReturn(null);

        // Act & Assert
        AnyTxnParameterException exception = assertThrows(AnyTxnParameterException.class, () -> {
            interestBearingService.remove("TEST_ID_001");
        });
        
        assertThat(exception.getErrCode()).isEqualTo(AnyTxnParameterRespCodeEnum.P_PARAMETER_DATA_NOT_EXIST.getCode());
    }

    @Test
    void testFindById_Success() {
        // Arrange
        try (MockedStatic<BeanMapping> beanMappingMock = mockStatic(BeanMapping.class)) {
            beanMappingMock.when(() -> BeanMapping.copy(testEntity, InterestBearingDTO.class)).thenReturn(testDTO);
            when(interestBearingMapper.selectByPrimaryKey("TEST_ID_001")).thenReturn(testEntity);

            // Act
            InterestBearingDTO result = interestBearingService.findById("TEST_ID_001");

            // Assert
            assertThat(result).isNotNull();
            assertThat(result.getId()).isEqualTo("TEST_ID_001");
            verify(interestBearingMapper).selectByPrimaryKey("TEST_ID_001");
        }
    }

    @Test
    void testFindById_EmptyId_ThrowsException() {
        // Act & Assert
        AnyTxnParameterException exception = assertThrows(AnyTxnParameterException.class, () -> {
            interestBearingService.findById("");
        });
        
        assertThat(exception.getErrCode()).isEqualTo(AnyTxnParameterRespCodeEnum.P_PARAMETER_EMPTY_FAULT.getCode());
    }

    @Test
    void testFindById_NotFound() {
        // Arrange
        try (MockedStatic<BeanMapping> beanMappingMock = mockStatic(BeanMapping.class)) {
            beanMappingMock.when(() -> BeanMapping.copy(null, InterestBearingDTO.class)).thenReturn(null);
            when(interestBearingMapper.selectByPrimaryKey("TEST_ID_001")).thenReturn(null);

            // Act
            InterestBearingDTO result = interestBearingService.findById("TEST_ID_001");

            // Assert
            assertThat(result).isNull();
            verify(interestBearingMapper).selectByPrimaryKey("TEST_ID_001");
        }
    }

    @Test
    void testFindPage_Success() {
        // Arrange
        List<InterestBearing> entityList = Arrays.asList(testEntity);
        List<InterestBearingDTO> dtoList = Arrays.asList(testDTO);
        
        try (MockedStatic<OrgNumberUtils> orgNumberUtilsMock = mockStatic(OrgNumberUtils.class);
             MockedStatic<BeanMapping> beanMappingMock = mockStatic(BeanMapping.class)) {
            
            orgNumberUtilsMock.when(() -> OrgNumberUtils.getOrg("001")).thenReturn("001");
            beanMappingMock.when(() -> BeanMapping.copyList(entityList, InterestBearingDTO.class)).thenReturn(dtoList);
            
            when(interestBearingMapper.selectByConditionAndPage("TABLE_001", "测试", "001")).thenReturn(entityList);

            // Act
            PageResultDTO<InterestBearingDTO> result = interestBearingService.findPage(1, 10, "001", "TABLE_001", "测试");

            // Assert
            assertThat(result).isNotNull();
            assertThat(result.getData()).hasSize(1);
            assertThat(result.getPage()).isEqualTo(1);
            assertThat(result.getRows()).isEqualTo(10);
            verify(interestBearingMapper).selectByConditionAndPage("TABLE_001", "测试", "001");
        }
    }

    @Test
    void testFindPage_EmptyResult() {
        // Arrange
        List<InterestBearing> entityList = Collections.emptyList();
        List<InterestBearingDTO> dtoList = Collections.emptyList();
        
        try (MockedStatic<OrgNumberUtils> orgNumberUtilsMock = mockStatic(OrgNumberUtils.class);
             MockedStatic<BeanMapping> beanMappingMock = mockStatic(BeanMapping.class)) {
            
            orgNumberUtilsMock.when(() -> OrgNumberUtils.getOrg("001")).thenReturn("001");
            beanMappingMock.when(() -> BeanMapping.copyList(entityList, InterestBearingDTO.class)).thenReturn(dtoList);
            
            when(interestBearingMapper.selectByConditionAndPage("TABLE_001", "测试", "001")).thenReturn(entityList);

            // Act
            PageResultDTO<InterestBearingDTO> result = interestBearingService.findPage(1, 10, "001", "TABLE_001", "测试");

            // Assert
            assertThat(result).isNotNull();
            assertThat(result.getData()).isEmpty();
            verify(interestBearingMapper).selectByConditionAndPage("TABLE_001", "测试", "001");
        }
    }

    @Test
    void testFindByOrgAndTableId_Success() {
        // Arrange
        try (MockedStatic<BeanMapping> beanMappingMock = mockStatic(BeanMapping.class)) {
            beanMappingMock.when(() -> BeanMapping.copy(testEntity, InterestBearingDTO.class)).thenReturn(testDTO);
            when(interestBearingSelfMapper.selectByOrgAndTableId("001", "TABLE_001")).thenReturn(testEntity);

            // Act
            InterestBearingDTO result = interestBearingService.findByOrgAndTableId("001", "TABLE_001");

            // Assert
            assertThat(result).isNotNull();
            assertThat(result.getId()).isEqualTo("TEST_ID_001");
            verify(interestBearingSelfMapper).selectByOrgAndTableId("001", "TABLE_001");
        }
    }

    @Test
    void testFindByOrgAndTableId_NotFound() {
        // Arrange
        when(interestBearingSelfMapper.selectByOrgAndTableId("001", "TABLE_001")).thenReturn(null);

        // Act
        InterestBearingDTO result = interestBearingService.findByOrgAndTableId("001", "TABLE_001");

        // Assert
        assertThat(result).isNull();
        verify(interestBearingSelfMapper).selectByOrgAndTableId("001", "TABLE_001");
    }
} 