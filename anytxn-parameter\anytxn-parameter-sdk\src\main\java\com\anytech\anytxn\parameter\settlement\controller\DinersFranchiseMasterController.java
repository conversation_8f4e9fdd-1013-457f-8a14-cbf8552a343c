package com.anytech.anytxn.parameter.settlement.controller;

import com.anytech.anytxn.common.core.base.PageResultDTO;
import com.anytech.anytxn.common.core.base.AnyTxnHttpResponse;
import com.anytech.anytxn.common.core.base.BizBaseController;
import com.anytech.anytxn.parameter.base.common.domain.dto.ParmCountryCodeDTO;
import com.anytech.anytxn.parameter.base.common.constants.Constants;
import com.anytech.anytxn.parameter.base.settlement.domain.dto.DinersFranchiseMasterDTO;
import com.anytech.anytxn.parameter.base.settlement.service.IDinersFranchiseMasterService;
import io.swagger.v3.oas.annotations.tags.Tag;
import io.swagger.v3.oas.annotations.Operation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * <AUTHOR>
 */
@RestController
@Tag(name = "DinersFranchiseMasterController")
public class DinersFranchiseMasterController extends BizBaseController {

    @Autowired
    private IDinersFranchiseMasterService dinersFranchiseMasterTableService;

    @Operation(summary = "FranchiseMasterTable添加")
    @PostMapping(value="/param/franchise/master/add")
    public AnyTxnHttpResponse addDinersCpDcfrmt(@RequestBody DinersFranchiseMasterDTO dinersFranchiseMasterDTO){
        dinersFranchiseMasterDTO.setUpdateBy(Constants.DEFAULT_USER);
        dinersFranchiseMasterTableService.add(dinersFranchiseMasterDTO);
        return AnyTxnHttpResponse.success();
    }

    @Operation(summary = "FranchiseMasterTable更新")
    @PostMapping(value="/param/franchise/master/update")
    public AnyTxnHttpResponse updateDinersCpDcfrmt(@RequestBody DinersFranchiseMasterDTO dinersFranchiseMasterDTO) {
        dinersFranchiseMasterTableService.modify(dinersFranchiseMasterDTO);
        return AnyTxnHttpResponse.success();
    }

    @Operation(summary = "FranchiseMasterTable分页列表")
    @GetMapping(value="/param/franchise/master/select/{pageNum}/{pageSize}")
    public AnyTxnHttpResponse<PageResultDTO<DinersFranchiseMasterDTO>> selectDinersCpDcfrmt(@PathVariable(value = "pageNum") Integer pageNum,
                                                                                            @PathVariable(value = "pageSize") Integer pageSize,
                                                                                            @RequestParam String fmFranchiseMasterId){
        DinersFranchiseMasterDTO dinersFranchiseMasterDTO = new DinersFranchiseMasterDTO();
        dinersFranchiseMasterDTO.setFmFranchiseMasterId(fmFranchiseMasterId);
        PageResultDTO<DinersFranchiseMasterDTO> dinersFranchiseMasterList = dinersFranchiseMasterTableService.page(pageNum,pageSize, dinersFranchiseMasterDTO);
        return AnyTxnHttpResponse.success(dinersFranchiseMasterList);
    }

    @Operation(summary = "FranchiseMasterTable查询")
    @GetMapping(value="/param/franchise/master/enquiry")
    public AnyTxnHttpResponse<DinersFranchiseMasterDTO> enquiryDinersCpDcfrmt(@RequestParam String fmFranchiseMasterId){
        return AnyTxnHttpResponse.success(dinersFranchiseMasterTableService.findOne(fmFranchiseMasterId));
    }

    @Operation(summary = "FranchiseMasterTable删除")
    @DeleteMapping(value="/param/franchise/master/delete")
    public AnyTxnHttpResponse deleteDinersCpDcfrmt(@RequestParam String fmFranchiseMasterId){
        dinersFranchiseMasterTableService.remove(fmFranchiseMasterId);
        return AnyTxnHttpResponse.success();
    }

    @Operation(summary = "FranchiseMasterTable masterId 列表")
    @GetMapping(value="/param/franchise/master/all")
    public AnyTxnHttpResponse<List<String>> getAllMasterId(){
        return AnyTxnHttpResponse.success(dinersFranchiseMasterTableService.getAllMasterId());
    }


    @Operation(summary = "国家码")
    @GetMapping(value="/param/country/code")
    public AnyTxnHttpResponse<List<ParmCountryCodeDTO>> getAllCounty(){
        List<ParmCountryCodeDTO> allCountry = dinersFranchiseMasterTableService.getAllCountry();
        return AnyTxnHttpResponse.success(allCountry);
    }

}
