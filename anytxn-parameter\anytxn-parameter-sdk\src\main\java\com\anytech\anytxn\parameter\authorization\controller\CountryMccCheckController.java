package com.anytech.anytxn.parameter.authorization.controller;

import com.anytech.anytxn.common.core.base.PageResultDTO;
import com.anytech.anytxn.common.core.base.AnyTxnHttpResponse;
import com.anytech.anytxn.common.core.base.BizBaseController;
import com.anytech.anytxn.parameter.base.authorization.service.ICountryMccCheckService;
import com.anytech.anytxn.parameter.base.authorization.service.IMccCodeService;
import com.anytech.anytxn.parameter.base.common.domain.dto.ParmCountryCodeDTO;
import com.anytech.anytxn.parameter.base.common.service.ICountryCodeService;
import com.anytech.anytxn.parameter.base.common.enums.ParameterRepDetailEnum;
import io.swagger.v3.oas.annotations.tags.Tag;
import io.swagger.v3.oas.annotations.Operation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import jakarta.validation.Valid;
import java.util.List;
import java.util.Map;



/**
 * <AUTHOR>
 */
@RestController
@Tag(name = "国家MCC检查服务")
public class CountryMccCheckController extends BizBaseController {

    @Autowired
    private ICountryCodeService countryCodeService;
    @Autowired
    private ICountryMccCheckService countryMccCheckService;
    @Autowired
    private IMccCodeService mccCodeService;

    /**
     * 分页查询国家码基本信息
     */
    @Operation(summary = "查询国家码基本信息")
    @GetMapping("/param/countryMccCheck/selectCountryCode")
    public AnyTxnHttpResponse<PageResultDTO<ParmCountryCodeDTO>> selectCountryCode(
            @RequestParam(value = "page", defaultValue = "1") Integer page,
            @RequestParam(value = "rows", defaultValue = "10") Integer rows) {
        PageResultDTO<ParmCountryCodeDTO> countryCode = countryCodeService.selectCountryCodeCheck(page,rows);
        return AnyTxnHttpResponse.success(countryCode);
    }


    /**
     * 获取所有国家码和国家名称
     */
    @Operation(summary = "获取所有国家码和国家名称")
    @GetMapping("/param/countryMccCheck/countryAndNames")
    public AnyTxnHttpResponse<List<Map<String,String>>> getCountryAndName() {
        List<Map<String,String>> countryAndNames = countryCodeService.getAllCountryAndName();
        return AnyTxnHttpResponse.success(countryAndNames);
    }


    /**
     * 根据主键查看国家MCC基本信息
     */
    @Operation(summary = "根据主键查看国家MCC基本信息")
    @GetMapping("/param/countryMccCheck/countryCodeById")
    public AnyTxnHttpResponse<ParmCountryCodeDTO> getByIndex(@RequestParam(value = "id") String id){
        ParmCountryCodeDTO countryCodeDTO = countryCodeService.findCountryCodeAndCountryMccChecks(id);
        return AnyTxnHttpResponse.success(countryCodeDTO);

    }

    /**
     * 根据国家码查询
     */
    @Operation(summary = "根据国家码查询")
    @GetMapping("/param/countryMccCheck/countryMccCheckByCountryCode")
    public AnyTxnHttpResponse<ParmCountryCodeDTO> getByCountryCode(@RequestParam String num) {
        ParmCountryCodeDTO parmCountryCodeDTO = countryCodeService.getCountryCodeByCde(num);
        return AnyTxnHttpResponse.success(parmCountryCodeDTO);
    }

    /**
     * 根据国家名称查询
     */
    @Operation(summary = "根据国家名称查询")
    @GetMapping("/param/countryMccCheck/countryMccCheckByCountryName")
    public AnyTxnHttpResponse<ParmCountryCodeDTO> getByCountryName(@RequestParam String countryName) {
        ParmCountryCodeDTO parmCountryCodeDTO = countryCodeService.getCountryCodeByName(countryName);
        return AnyTxnHttpResponse.success(parmCountryCodeDTO);
    }


    /**
     * 删除国家MCC信息
     * */
    @Operation(summary = "删除国家MCC信息")
    @DeleteMapping("/param/countryMccCheck/deleteCountryMccCheck/{id}")
    public AnyTxnHttpResponse cancelCountryMccCheck(@PathVariable Long id) {
        Boolean flag = countryCodeService.removeCountryMccCheckAndDtails(id);
        return AnyTxnHttpResponse.success(flag, ParameterRepDetailEnum.DEL.message());
    }

    /**
     * 新增国家MCC信息
     */
    @Operation(summary = "新增国家MCC信息")
    @PostMapping("/param/countryMccCheck/addCountryMccCheck")
    public AnyTxnHttpResponse create(@Valid @RequestBody ParmCountryCodeDTO parmCountryCodeDTO) {
        Boolean flag = countryCodeService.addCountryMccCheckAndDtails(parmCountryCodeDTO);
        return AnyTxnHttpResponse.success(flag,ParameterRepDetailEnum.CREATE.message());
    }

    /**
     * 国家MCC信息修改
     */
    @Operation(summary = "国家MCC信息修改")
    @PutMapping("/param/countryMccCheck/updateCountryMccCheck")
    public AnyTxnHttpResponse modify(@Valid @RequestBody ParmCountryCodeDTO parmCountryCodeDTO) {
        Boolean flag = countryCodeService.modifyCountryMccCheckAndUtils(parmCountryCodeDTO);
        return AnyTxnHttpResponse.success(flag,ParameterRepDetailEnum.UPDATE.message());
    }

    /**
     * 删除单个
     */
    @Operation(summary = "删除单个")
    @DeleteMapping("/param/countryMccCheck/deleteOneCountryMccCheck")
    public AnyTxnHttpResponse delectOne(@PathVariable Long id) {
        Boolean flag = countryMccCheckService.removeCountryMccCheck(id);
        return AnyTxnHttpResponse.success(flag,ParameterRepDetailEnum.UPDATE.message());
    }

    /**
     * 获取mcc和描述
     */
    @Operation(summary = "获取mcc和描述")
    @GetMapping("/param/countryMccCheck/mccCodes")
    public AnyTxnHttpResponse<List<Map<String,String>>> selectAllCode(String organizationNumber) {
        List<Map<String,String>> list = mccCodeService.getMccCodes(organizationNumber);
        return AnyTxnHttpResponse.success(list);
    }

}
