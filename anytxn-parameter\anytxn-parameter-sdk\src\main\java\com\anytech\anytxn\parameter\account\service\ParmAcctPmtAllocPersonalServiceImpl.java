package com.anytech.anytxn.parameter.account.service;

import com.alibaba.fastjson.JSON;
import com.github.pagehelper.Page;
import com.github.pagehelper.PageHelper;
import com.anytech.anytxn.common.core.annotation.db.DeleteParameterAnnotation;
import com.anytech.anytxn.common.core.annotation.db.InsertParameterAnnotation;
import com.anytech.anytxn.common.core.annotation.db.UpdateParameterAnnotation;
import com.anytech.anytxn.business.base.audit.AbstractParameterService;
import com.anytech.anytxn.common.core.base.ParameterCompare;
import com.anytech.anytxn.business.base.audit.ParmModificationRecord;
import com.anytech.anytxn.common.core.base.PageResultDTO;
import com.anytech.anytxn.common.core.utils.BeanMapping;
import com.anytech.anytxn.common.core.utils.OrgNumberUtils;
import com.anytech.anytxn.common.sequence.utils.Number16IdGen;
import com.anytech.anytxn.common.core.utils.TenantUtils;
import com.anytech.anytxn.parameter.base.account.domain.dto.AcctPmtAllocPersonalDefReqDTO;
import com.anytech.anytxn.parameter.base.account.domain.dto.AcctPmtAllocPersonalDefResDTO;
import com.anytech.anytxn.parameter.base.account.domain.dto.AcctPmtAllocPersonalReqDTO;
import com.anytech.anytxn.parameter.base.account.domain.dto.AcctPmtAllocPersonalResDTO;
import com.anytech.anytxn.parameter.base.account.service.IParmAcctPmtAllocPersonalService;
import com.anytech.anytxn.parameter.base.common.constants.Constants;
import com.anytech.anytxn.parameter.base.common.enums.AnyTxnParameterRespCodeEnum;
import com.anytech.anytxn.parameter.base.common.exception.AnyTxnParameterException;
import com.anytech.anytxn.parameter.account.mapper.ParmAcctPmtAllocPersonalDefMapper;
import com.anytech.anytxn.parameter.account.mapper.ParmAcctPmtAllocPersonalMapper;
import com.anytech.anytxn.parameter.base.account.domain.model.ParmAcctPmtAllocPersonal;
import com.anytech.anytxn.parameter.base.account.domain.model.ParmAcctPmtAllocPersonalDef;
import org.apache.commons.lang3.StringUtils;
//import org.apache.shardingsphere.transaction.annotation.ShardingSphereTransactionType;
//import org.apache.shardingsphere.transaction.core.TransactionType;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.HashSet;
import java.util.List;
import java.util.Set;

/**
 * <pre>
 * Description  账户间还款分配个性化参数
 * Dept:		江融信
 * Author:		lichao
 * Version:		1.0
 * Create at:	2020-08-09 18:01
 *
 * 修改历史:
 * 日期    作者    版本  修改描述
 * ------------------------------------------------------------------
 **/
@Service(value = "parm_acct_pmt_alloc_personal_def_serviceImpl")
public class ParmAcctPmtAllocPersonalServiceImpl extends AbstractParameterService implements IParmAcctPmtAllocPersonalService {

    @Autowired
    private ParmAcctPmtAllocPersonalMapper parmAcctPmtAllocPersonalMapper;
    @Autowired
    private ParmAcctPmtAllocPersonalDefMapper parmAcctPmtAllocPersonalDefMapper;
    @Autowired
    private Number16IdGen numberIdGenerator;

    /**
     * 添加账户间还款分配个性化参数
     * @param acctPmtAllocPersonalReqDTO 还款分配个性化入参对象
     * @return 还款分配个性化详情
     * @throws
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    @InsertParameterAnnotation(tableName = "parm_acct_pmt_alloc_personal_def", tableDesc = "Personalized Repayment Allocation", isJoinTable = true)
    public ParameterCompare add(AcctPmtAllocPersonalReqDTO acctPmtAllocPersonalReqDTO) {
        boolean isExists = parmAcctPmtAllocPersonalMapper.isExists(OrgNumberUtils.getOrg(acctPmtAllocPersonalReqDTO.getOrganizationNumber()),acctPmtAllocPersonalReqDTO.getTableId()) > 0;
        if(isExists){
            throw new AnyTxnParameterException(AnyTxnParameterRespCodeEnum.D_EXIST_ACCT_PMT_ALLOC_PERSONAL_FAULT);
        }
        //检查是否有重复的排序因子
        checkAcctPmt(acctPmtAllocPersonalReqDTO);
        acctPmtAllocPersonalReqDTO.setId(String.valueOf(numberIdGenerator.generateId(TenantUtils.getTenantId())));
        return ParameterCompare.getBuilder()
                .withMainParmId(acctPmtAllocPersonalReqDTO.getTableId())
                .withAfter(acctPmtAllocPersonalReqDTO)
                .build(AcctPmtAllocPersonalReqDTO.class);
    }


    /**
     * 修改账户间还款分配个性化参数
     * @param acctPmtAllocPersonalReqDTO 还款分配个性化入参对象
     * @return 还款分配个性化详情
     * @throws
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    @UpdateParameterAnnotation(tableName = "parm_acct_pmt_alloc_personal_def", tableDesc = "Personalized Repayment Allocation", isJoinTable = true)
    public ParameterCompare modify(AcctPmtAllocPersonalReqDTO acctPmtAllocPersonalReqDTO) {
        if (acctPmtAllocPersonalReqDTO.getId() == null) {
            throw new AnyTxnParameterException(AnyTxnParameterRespCodeEnum.P_PARAMETER_ID_EMPTY_FAULT);
        }
        //检查是否有重复的排序因子
        checkAcctPmt(acctPmtAllocPersonalReqDTO);

        AcctPmtAllocPersonalResDTO byId = findById(acctPmtAllocPersonalReqDTO.getId());
        if(byId == null){
            throw new AnyTxnParameterException(AnyTxnParameterRespCodeEnum.D_GET_ACCT_PMT_ALLOC_PERSONAL_BY_ID_FAULT);
        }
        AcctPmtAllocPersonalReqDTO pmtAllocPersonalReqDTO = BeanMapping.copy(byId, AcctPmtAllocPersonalReqDTO.class);
        pmtAllocPersonalReqDTO.setAcctPmtAllocPersonalDefReqList(BeanMapping.copyList(byId.getAcctPmtAllocPersonalDefResList(), AcctPmtAllocPersonalDefReqDTO.class));

        return ParameterCompare.getBuilder()
                .withMainParmId(acctPmtAllocPersonalReqDTO.getTableId())
                .withAfter(acctPmtAllocPersonalReqDTO)
                .withBefore(pmtAllocPersonalReqDTO)
                .build(AcctPmtAllocPersonalReqDTO.class);
    }


    /**
     * 分页查询数据
     * @params [pageNum,pageSize]
     * @return PageResultDTO
     * @throws
     */
    @Override
    public PageResultDTO<AcctPmtAllocPersonalResDTO> findByPage(Integer pageNum, Integer pageSize, AcctPmtAllocPersonalReqDTO acctPmtAllocPersonalReqDTO) {
        if(null == acctPmtAllocPersonalReqDTO){
            acctPmtAllocPersonalReqDTO = new AcctPmtAllocPersonalReqDTO();
        }
        acctPmtAllocPersonalReqDTO.setOrganizationNumber(StringUtils.isEmpty(acctPmtAllocPersonalReqDTO.getOrganizationNumber()) ? OrgNumberUtils.getOrg() : acctPmtAllocPersonalReqDTO.getOrganizationNumber());
        Page page = PageHelper.startPage(pageNum, pageSize);
        List<ParmAcctPmtAllocPersonal> acctPmtAllocPersonalList = parmAcctPmtAllocPersonalMapper.selectByCondition(acctPmtAllocPersonalReqDTO);
        List<AcctPmtAllocPersonalResDTO> acctPmtAllocPersonalResDTOList = BeanMapping.copyList(acctPmtAllocPersonalList,AcctPmtAllocPersonalResDTO.class);
        return new PageResultDTO<>(pageNum, pageSize, page.getTotal(), page.getPages(), acctPmtAllocPersonalResDTOList);
    }


    /**
     * 通过id查询账户间还款分配个性化参数
     * @param id
     * @return AcctPmtAllocPersonalResDTO
     * @throws
     */
    @Override
    public AcctPmtAllocPersonalResDTO findById(String id) {
        if (id == null) {
            throw new AnyTxnParameterException(AnyTxnParameterRespCodeEnum.P_PARAMETER_EMPTY_FAULT);
        }
        AcctPmtAllocPersonalResDTO res = new AcctPmtAllocPersonalResDTO();
        ParmAcctPmtAllocPersonal parmAcctPmtAllocPersonal = parmAcctPmtAllocPersonalMapper.selectByPrimaryKey(id);
        if(parmAcctPmtAllocPersonal == null){
            throw new AnyTxnParameterException(AnyTxnParameterRespCodeEnum.D_GET_ACCT_PMT_ALLOC_PERSONAL_BY_ID_FAULT);
        }
        BeanMapping.copy(parmAcctPmtAllocPersonal,res);
        List<ParmAcctPmtAllocPersonalDef> parmAcctPmtAllocBasicDefList = parmAcctPmtAllocPersonalDefMapper.selectByOrgNumAndTableId(parmAcctPmtAllocPersonal.getOrganizationNumber(),parmAcctPmtAllocPersonal.getTableId());
        List<AcctPmtAllocPersonalDefResDTO> acctPmtAllocPersonalDefResDTOList = new ArrayList<>();
        if(!parmAcctPmtAllocBasicDefList.isEmpty()){
            acctPmtAllocPersonalDefResDTOList = BeanMapping.copyList(parmAcctPmtAllocBasicDefList,AcctPmtAllocPersonalDefResDTO.class);
        }
        res.setAcctPmtAllocPersonalDefResList(acctPmtAllocPersonalDefResDTOList);
        return res;
    }

    /**
     * 通过id删除账户间还款分配个性化参数
     * @param id
     * @return Boolean
     * @throws
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    @DeleteParameterAnnotation(tableName = "parm_acct_pmt_alloc_personal_def", tableDesc = "Personalized Repayment Allocation", isJoinTable = true)
    public ParameterCompare remove(String id) {
        if (id == null) {
            throw new AnyTxnParameterException(AnyTxnParameterRespCodeEnum.P_PARAMETER_EMPTY_FAULT);
        }
        ParmAcctPmtAllocPersonal parmAcctPmtAllocPersonal = parmAcctPmtAllocPersonalMapper.selectByPrimaryKey(id);
        if(parmAcctPmtAllocPersonal == null){
            throw new AnyTxnParameterException(AnyTxnParameterRespCodeEnum.D_GET_ACCT_PMT_ALLOC_PERSONAL_BY_ID_FAULT);
        }

        return ParameterCompare.getBuilder()
                .withMainParmId(parmAcctPmtAllocPersonal.getTableId())
                .withBefore(parmAcctPmtAllocPersonal)
                .build(ParmAcctPmtAllocPersonal.class);
    }

    /**
     * 通过orgNumber、tableId查询还款分配个性化信息
     * @params [orgNumber,tableId]
     * @return AcctPmtAllocPersonalResDTO
     * @throws
     */
    @Override
    public AcctPmtAllocPersonalResDTO queryAcctPmtAllocPersonalByOrgTableId(String orgNumber, String tableId) {
        AcctPmtAllocPersonalResDTO res = new AcctPmtAllocPersonalResDTO();
        ParmAcctPmtAllocPersonal parmAcctPmtAllocPersonal = parmAcctPmtAllocPersonalMapper.selectByOrgNumAndTableId(orgNumber,tableId);
        if(parmAcctPmtAllocPersonal != null && StringUtils.isNotBlank(parmAcctPmtAllocPersonal.getOrganizationNumber())){
            BeanMapping.copy(parmAcctPmtAllocPersonal,res);
            List<AcctPmtAllocPersonalDefResDTO> acctPmtAllocPersonalDefResDTOList = new ArrayList<>();
            List<ParmAcctPmtAllocPersonalDef> parmAcctPmtAllocPersonalDefList = parmAcctPmtAllocPersonalDefMapper.selectByOrgNumAndTableId(orgNumber,tableId);
            if(!CollectionUtils.isEmpty(parmAcctPmtAllocPersonalDefList)){
                acctPmtAllocPersonalDefResDTOList = BeanMapping.copyList(parmAcctPmtAllocPersonalDefList,AcctPmtAllocPersonalDefResDTO.class);
            }
            res.setAcctPmtAllocPersonalDefResList(acctPmtAllocPersonalDefResDTOList);
        }
        return res;
    }


    /**
     * 检查还款分配个性化参数是否有重复的排序因子
     * @param acctPmtAllocPersonalReqDTO 还款分配个性化入参对象
     * @throws
     */
    private void checkAcctPmt(AcctPmtAllocPersonalReqDTO acctPmtAllocPersonalReqDTO){
        List<AcctPmtAllocPersonalDefReqDTO> acctPmtAllocPersonalDefList = acctPmtAllocPersonalReqDTO.getAcctPmtAllocPersonalDefReqList();
        Set<String> accountSet = new HashSet<>();
        int size = CollectionUtils.isEmpty(acctPmtAllocPersonalDefList)?0:acctPmtAllocPersonalDefList.size();
        for (AcctPmtAllocPersonalDefReqDTO acctPmtAllocPersonalDefReqDTO:acctPmtAllocPersonalDefList){
            if(!StringUtils.isEmpty(acctPmtAllocPersonalDefReqDTO.getSequenceItem())){
                accountSet.add(acctPmtAllocPersonalDefReqDTO.getSequenceItem());
            }else {
                size = size - 1;
            }
        }

        if (size > accountSet.size()) {
            throw new AnyTxnParameterException(AnyTxnParameterRespCodeEnum.D_REPEAT_ACCT_PMT_ALLOC_PERSONAL_FAULT);
        }
    }
    /**
     * 添加账户间还款分配个性化定义参数
     * @param acctPmtAllocPersonalReqDTO 还款分配个性化定义入参对象
     * @return
     * @throws
     */
    private void addAcctPmtAllocPersonalDef(AcctPmtAllocPersonalReqDTO acctPmtAllocPersonalReqDTO,ParmAcctPmtAllocPersonal parmAcctPmtAllocPersonal){
        acctPmtAllocPersonalReqDTO.getAcctPmtAllocPersonalDefReqList().stream().forEach(acctPmtAllocPersonalDefReqDTO ->{
            ParmAcctPmtAllocPersonalDef parmAcctPmtAllocPersonalDef = BeanMapping.copy(acctPmtAllocPersonalDefReqDTO,ParmAcctPmtAllocPersonalDef.class);
            parmAcctPmtAllocPersonalDef.setOrganizationNumber(parmAcctPmtAllocPersonal.getOrganizationNumber());
            parmAcctPmtAllocPersonalDef.setTableId(parmAcctPmtAllocPersonal.getTableId());
            parmAcctPmtAllocPersonalDef.setCreateTime(LocalDateTime.now());
            parmAcctPmtAllocPersonalDef.setUpdateTime(LocalDateTime.now());
            parmAcctPmtAllocPersonalDef.setUpdateBy(Constants.DEFAULT_USER);
            parmAcctPmtAllocPersonalDef.setVersionNumber(1L);
            parmAcctPmtAllocPersonalDef.setStatus(Constants.ENABLED);
            parmAcctPmtAllocPersonalDef.setId(numberIdGenerator.generateId(TenantUtils.getTenantId()));
            parmAcctPmtAllocPersonalDefMapper.insert(parmAcctPmtAllocPersonalDef);
        });
    }

    @Override
    @Transactional(rollbackFor = Exception.class, propagation = Propagation.REQUIRED)
    //@ShardingSphereTransactionType(TransactionType.XA)
    protected boolean updateDb(ParmModificationRecord parmModificationRecord) {
        AcctPmtAllocPersonalReqDTO acctPmtAllocPersonalReqDTO = JSON.parseObject(parmModificationRecord.getParmBody(), AcctPmtAllocPersonalReqDTO.class);
        ParmAcctPmtAllocPersonal parmAcctPmtAllocPersonal = BeanMapping.copy(acctPmtAllocPersonalReqDTO, ParmAcctPmtAllocPersonal.class);
        parmAcctPmtAllocPersonal.initUpdateDateTime();
        parmAcctPmtAllocPersonal.setUpdateBy(parmModificationRecord.getApplicationBy());
        parmAcctPmtAllocPersonalMapper.updateByPrimaryKeySelective(parmAcctPmtAllocPersonal);
        //先根据机构号、参数表ID删除还款分配个性化定义信息
        parmAcctPmtAllocPersonalDefMapper.deleteByOrgNumberAndTableId(parmAcctPmtAllocPersonal.getTableId(),parmAcctPmtAllocPersonal.getOrganizationNumber());
        //再添加还款分配个性化定义信息
        addAcctPmtAllocPersonalDef(acctPmtAllocPersonalReqDTO,parmAcctPmtAllocPersonal);
        return true;
    }

    @Override
    @Transactional(rollbackFor = Exception.class, propagation = Propagation.REQUIRED)
    //@ShardingSphereTransactionType(TransactionType.XA)
    protected boolean insertDb(ParmModificationRecord parmModificationRecord) {
        AcctPmtAllocPersonalReqDTO acctPmtAllocPersonalReqDTO = JSON.parseObject(parmModificationRecord.getParmBody(), AcctPmtAllocPersonalReqDTO.class);
        ParmAcctPmtAllocPersonal parmAcctPmtAllocPersonal = BeanMapping.copy(acctPmtAllocPersonalReqDTO,ParmAcctPmtAllocPersonal.class);
        parmAcctPmtAllocPersonal.initCreateDateTime();
        parmAcctPmtAllocPersonal.setVersionNumber(1L);
        parmAcctPmtAllocPersonalMapper.insertSelective(parmAcctPmtAllocPersonal);
        //添加还款分配个性化定义参数
        addAcctPmtAllocPersonalDef(acctPmtAllocPersonalReqDTO, parmAcctPmtAllocPersonal);
        return true;
    }

    @Override
    @Transactional(rollbackFor = Exception.class, propagation = Propagation.REQUIRED)
    //@ShardingSphereTransactionType(TransactionType.XA)
    protected boolean deleteDb(ParmModificationRecord parmModificationRecord) {
        ParmAcctPmtAllocPersonal parmAcctPmtAllocPersonal = JSON.parseObject(parmModificationRecord.getParmBody(), ParmAcctPmtAllocPersonal.class);
        //先通过机构号和参数表ID删定义表，在删除参数表
        try{
            parmAcctPmtAllocPersonalDefMapper.deleteByOrgNumberAndTableId(parmAcctPmtAllocPersonal.getTableId(),parmAcctPmtAllocPersonal.getOrganizationNumber());
            parmAcctPmtAllocPersonalMapper.deleteByPrimaryKey(parmAcctPmtAllocPersonal.getId());
        }catch (Exception e) {
            throw new AnyTxnParameterException(AnyTxnParameterRespCodeEnum.D_DELETE_ACCT_PMT_ALLOC_PERSONAL_FAULT);
        }
        return true;
    }
}
