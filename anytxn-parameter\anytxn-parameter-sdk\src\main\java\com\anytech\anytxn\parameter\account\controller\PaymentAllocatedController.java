package com.anytech.anytxn.parameter.account.controller;

import io.swagger.v3.oas.annotations.tags.Tag;
import io.swagger.v3.oas.annotations.Operation;
import com.anytech.anytxn.common.core.base.ParameterCompare;
import com.anytech.anytxn.common.core.base.PageResultDTO;
import com.anytech.anytxn.common.core.base.AnyTxnHttpResponse;
import com.anytech.anytxn.common.core.base.BizBaseController;
import com.anytech.anytxn.parameter.base.account.domain.dto.PaymentAllocatedReqDTO;
import com.anytech.anytxn.parameter.base.account.domain.dto.PaymentAllocatedResDTO;
import com.anytech.anytxn.parameter.base.account.service.IPaymentAllocatedService;
import com.anytech.anytxn.parameter.base.common.enums.ParameterRepDetailEnum;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.ArrayList;

/**
 * 还款分配定义参数
 * <AUTHOR> tingting
 * @date 2018/8/16
 */
@Tag(name = "还款分配定义参数")
@RestController
public class PaymentAllocatedController extends BizBaseController {

    @Autowired
    private IPaymentAllocatedService paymentAllocatedService;

    /**
     * 查询所有还款分配定义参数信息
     *
     * @return TxnTypeControlRes
     */
    @Operation(summary = "还款分配定义参数分页查询", description = "分页查询")
    @GetMapping(value = "/param/paymentAllocated/pageNum/{pageNum}/pageSize/{pageSize}")
    public AnyTxnHttpResponse<PageResultDTO<PaymentAllocatedResDTO>> getAllPage(@PathVariable(value = "pageNum") Integer pageNum,
                                                                             @PathVariable(value = "pageSize") Integer pageSize,
                                                                                PaymentAllocatedReqDTO paymentAllocatedReq) {
        PageResultDTO<PaymentAllocatedResDTO> response;
        response = paymentAllocatedService.findAll(pageNum, pageSize,paymentAllocatedReq);
        return AnyTxnHttpResponse.success(response);

    }

    /**
     * 查询还款分配定义详情
     *
     * @return
     */
    @Operation(summary = "通过id查询还款分配定义详情", description = "还款分配定义详情查询")
    @GetMapping(value = "/param/paymentAllocated/id/{id}")
    public AnyTxnHttpResponse<PaymentAllocatedResDTO> getDetail(@PathVariable String id) {
        PaymentAllocatedResDTO response;
        response = paymentAllocatedService.findDetail(id);
        return AnyTxnHttpResponse.success(response);
    }


    /**
     * 通过表状态查询还款分配定义参数
     *
     * @param status
     * @return
     */
    @Operation(summary = "通过表状态查询还款分配定义参数", description = "通过表状态查询还款分配定义参数")
    @GetMapping(value = "/param/paymentAllocated/status/{status}")
    public AnyTxnHttpResponse<ArrayList<PaymentAllocatedResDTO>> getByStatus(@PathVariable String status) {
        ArrayList<PaymentAllocatedResDTO> resList;
        resList = (ArrayList) paymentAllocatedService.findByStatus(status);
        return AnyTxnHttpResponse.success(resList);
    }

    /**
     * 新增还款分配定义参数
     *
     * @param paymentAllocatedReq
     * @return
     */
    @Operation(summary = "新增还款分配定义参数", description = "新增还款分配定义参数")
    @PostMapping(value = "/param/paymentAllocated")
    public AnyTxnHttpResponse<Object> create(@RequestBody PaymentAllocatedReqDTO paymentAllocatedReq) {
        if (paymentAllocatedReq.getControlReqList() == null) {
            paymentAllocatedReq.setControlReqList(new ArrayList<>());
        }
        ParameterCompare res = paymentAllocatedService.addPaymentAllocated(paymentAllocatedReq);
        return AnyTxnHttpResponse.success(res, ParameterRepDetailEnum.CREATE.message());

    }

    /**
     * 通过id删除还款分配定义参数
     *
     * @param id
     * @return
     */
    @Operation(summary = "删除还款分配定义参数", description = "通过id删除还款分配定义参数")
    @DeleteMapping(value = "/param/paymentAllocated/id/{id}")
    public AnyTxnHttpResponse<Object> remove(@PathVariable String id) {
        ParameterCompare flag = paymentAllocatedService.removePaymentAllocated(id);
        return AnyTxnHttpResponse.success(flag,ParameterRepDetailEnum.DEL.message());

    }

    /**
     * 更新还款分配定义参数
     *
     * @param paymentAllocatedReq
     * @return
     */
    @Operation(summary = "更新还款分配定义参数", description = "更新还款分配定义参数")
    @PutMapping(value = "/param/paymentAllocated")
    public AnyTxnHttpResponse<Object> modify(@RequestBody PaymentAllocatedReqDTO paymentAllocatedReq) {
        if (paymentAllocatedReq.getControlReqList() == null) {
            paymentAllocatedReq.setControlReqList(new ArrayList<>());
        }
        ParameterCompare res = paymentAllocatedService.modifyPaymentAllocated(paymentAllocatedReq);
        return AnyTxnHttpResponse.success(res,ParameterRepDetailEnum.UPDATE.message());
    }


    /**
     * 通过机构号和表Id查询还款分配定义
     *
     * @param organizationNumber 机构号
     * @param tableId 表Id
     * @return
     */
    @Operation(summary = "通过机构号和表Id查询还款分配定义", description = "通过机构号和表Id查询")
    @GetMapping(value = "/param/paymentAllocated/organizationNumber/{organizationNumber}/tableId/{tableId}")
    public AnyTxnHttpResponse<PaymentAllocatedResDTO> getPaymentAllocated(@PathVariable String organizationNumber, @PathVariable String tableId) {
        PaymentAllocatedResDTO res;
        res = paymentAllocatedService.findByOrgAndTableId(organizationNumber, tableId);
        return AnyTxnHttpResponse.success(res);

    }
}
