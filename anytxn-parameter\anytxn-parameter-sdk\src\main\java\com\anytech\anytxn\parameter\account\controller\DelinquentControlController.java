package com.anytech.anytxn.parameter.account.controller;

import io.swagger.v3.oas.annotations.tags.Tag;
import io.swagger.v3.oas.annotations.Operation;
import com.anytech.anytxn.common.core.base.PageResultDTO;
import com.anytech.anytxn.common.core.base.AnyTxnHttpResponse;
import com.anytech.anytxn.common.core.base.BizBaseController;
import com.anytech.anytxn.parameter.base.account.domain.dto.BlockCodeAccountResDTO;
import com.anytech.anytxn.parameter.base.account.domain.dto.DelinquentControlReqDTO;
import com.anytech.anytxn.parameter.base.account.domain.dto.DelinquentControlResDTO;
import com.anytech.anytxn.parameter.base.account.service.IBlockCodeAccountService;
import com.anytech.anytxn.parameter.base.account.service.IDelinquentControlService;
import com.anytech.anytxn.parameter.base.common.enums.ParameterRepDetailEnum;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.ArrayList;

/**
 * 延滞控制参数
 * <AUTHOR> tingting
 * @date 2018/8/21
 */
@Tag(name = "延滞控制参数")
@RestController
public class DelinquentControlController extends BizBaseController {
    @Autowired
    private IDelinquentControlService delinquentControlService;

    @Autowired
    private IBlockCodeAccountService blockCodeAccountService;
    /**
     * 新增延滞控制参数
     * @param delinquentControlReq
     * @return DelinquentControlRes
     *
     */
    @Operation(summary = "新增延滞控制参数",description = "新增延滞控制参数")
    @PostMapping(value = "/param/delinquentControl")
    public AnyTxnHttpResponse<DelinquentControlResDTO> create(@RequestBody DelinquentControlReqDTO delinquentControlReq) {
        DelinquentControlResDTO res = delinquentControlService.addDelinquentControl(delinquentControlReq);
        return AnyTxnHttpResponse.success(res, ParameterRepDetailEnum.CREATE.message());

    }

    /**
     * 查询所有延滞控制参数信息
     * @return DelinquentControlRes
     *
     */
    @Operation(summary = "延滞控制参数分页查询",description = "分页查询")
    @GetMapping(value = "/param/delinquentControl/pageNum/{pageNum}/pageSize/{pageSize}")
    public AnyTxnHttpResponse<PageResultDTO<DelinquentControlResDTO>> getAllPage(@PathVariable(value = "pageNum") Integer pageNum,
                                                                              @PathVariable(value = "pageSize") Integer pageSize) {
        PageResultDTO<DelinquentControlResDTO> response = delinquentControlService.findAll(pageNum,pageSize);
        return AnyTxnHttpResponse.success(response);

    }


    /**
     * 修改延滞控制参数信息
     * @param delinquentControlReq
     * @return DelinquentControlRes
     *
     */
    @Operation(summary = "延滞控制参数信息修改", description = "只更新需要更新的,其他不变")
    @PutMapping(value = "/param/delinquentControl")
    public AnyTxnHttpResponse<DelinquentControlResDTO> modify(@RequestBody DelinquentControlReqDTO delinquentControlReq) {
        DelinquentControlResDTO res = delinquentControlService.modifyDelinquentControl(delinquentControlReq);
        return AnyTxnHttpResponse.success(res,ParameterRepDetailEnum.UPDATE.message());


    }

    /**
     * 通过id删除延滞控制参数信息
     * @param id
     * @return Boolean
     *
     */
    @Operation(summary = "删除延滞控制参数信息", description = "通过id删除延滞控制参数信息")
    @DeleteMapping(value = "/param/delinquentControl/id/{id}")
    public AnyTxnHttpResponse<Boolean> remove(@PathVariable(value = "id") Long id) {
        Boolean flag = delinquentControlService.removeDelinquentControl(id);
        return AnyTxnHttpResponse.success(flag,ParameterRepDetailEnum.DEL.message());

    }

    /**
     * 通过id查询延滞控制参数信息
     * @param id
     * @return HttpApiResponse<DelinquentControlRes>
     *
     */
    @Operation(summary = "查询延滞控制参数信息", description = "通过id查询延滞控制参数信息")
    @GetMapping(value = "/param/delinquentControl/id/{id}")
    public AnyTxnHttpResponse<DelinquentControlResDTO> getById(@PathVariable (value = "id")Long id) {
        DelinquentControlResDTO res;
        res = delinquentControlService.findById(id);
        return AnyTxnHttpResponse.success(res);

    }

    /**
     * 通过机构号,tableId,延滞等级查询延滞控制参数信息
     * @param organizationNumber 机构号
     * @param tableId 表Id
     * @param cycleDue 延滞等级
     * @return
     *
     */
    @Operation(summary = "根据机构号,talbeId,延滞等级查询延滞控制参数信息", description = "根据talbeId,延滞等级可为空")
    @GetMapping(value = "/param/delinquentControl/organizationNumber/{organizationNumber}/tableId/{tableId}/cycleDue/{cycleDue}")
    public AnyTxnHttpResponse<DelinquentControlResDTO> getDelinquentControl(@PathVariable(value = "organizationNumber") String organizationNumber
            , @PathVariable(value = "tableId") String tableId
            , @PathVariable(value = "cycleDue") Integer cycleDue) {
        ArrayList<DelinquentControlResDTO> res = null;
        DelinquentControlResDTO controlRes = null;
        res = (ArrayList) delinquentControlService.findDelinquentControl(organizationNumber, tableId, cycleDue);
        if (!res.isEmpty()) {
            controlRes = res.get(0);
        }
        return AnyTxnHttpResponse.success(controlRes);

    }

    @Operation(summary = "账户层封锁码获取接口",description = "账户层封锁码获取接口")
    @GetMapping(value = "/param/delinquentControl/blockCode")
    public AnyTxnHttpResponse<ArrayList<BlockCodeAccountResDTO>> getBlockCode(@RequestParam String organizationNumber) {
        ArrayList<BlockCodeAccountResDTO> res = null;
        res = (ArrayList) blockCodeAccountService.findBlockCode(organizationNumber);
        return AnyTxnHttpResponse.success(res);
    }
}
