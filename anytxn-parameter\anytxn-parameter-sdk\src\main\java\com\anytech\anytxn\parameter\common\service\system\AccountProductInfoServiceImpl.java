package com.anytech.anytxn.parameter.common.service.system;


import com.alibaba.fastjson.JSON;
import com.github.pagehelper.Page;
import com.github.pagehelper.PageHelper;
import com.anytech.anytxn.common.core.annotation.db.DeleteParameterAnnotation;
import com.anytech.anytxn.common.core.annotation.db.InsertParameterAnnotation;
import com.anytech.anytxn.common.core.annotation.db.UpdateParameterAnnotation;
import com.anytech.anytxn.business.base.audit.AbstractParameterService;
import com.anytech.anytxn.common.core.base.ParameterCompare;
import com.anytech.anytxn.business.base.audit.ParmModificationRecord;
import com.anytech.anytxn.common.core.utils.OrgNumberUtils;
import com.anytech.anytxn.parameter.base.common.domain.dto.CommonSelectResDTO;
import com.anytech.anytxn.parameter.base.common.constants.Constants;
import com.anytech.anytxn.common.core.base.PageResultDTO;
import com.anytech.anytxn.common.core.utils.BeanMapping;
import com.anytech.anytxn.common.sequence.utils.Number16IdGen;
import com.anytech.anytxn.parameter.base.common.domain.dto.product.ProductInfoResDTO;
import com.anytech.anytxn.parameter.base.common.domain.dto.product.AccountProductInfoReqDTO;
import com.anytech.anytxn.parameter.base.common.domain.dto.product.AccountProductInfoResDTO;
import com.anytech.anytxn.parameter.base.common.domain.dto.system.SysCodeSimpleResDTO;
import com.anytech.anytxn.parameter.base.common.service.product.IAccountProductInfoService;
import com.anytech.anytxn.parameter.base.common.service.ICommonService;
import com.anytech.anytxn.parameter.base.common.service.product.IProductInfoService;
import com.anytech.anytxn.parameter.base.common.enums.AnyTxnParameterRespCodeEnum;
import com.anytech.anytxn.parameter.base.common.exception.AnyTxnParameterException;
import com.anytech.anytxn.parameter.common.mapper.broadcast.system.ParmAccountProductInfoMapper;
import com.anytech.anytxn.parameter.common.mapper.broadcast.system.ParmAccountProductInfoSelfMapper;
import com.anytech.anytxn.parameter.common.mapper.broadcast.product.ParmAcctProductMainInfoSelfMapper;
import com.anytech.anytxn.parameter.base.common.domain.model.system.ParmAccountProductInfo;
import com.anytech.anytxn.parameter.base.common.domain.model.ParmAcctProductMainInfo;
import org.apache.commons.lang3.StringUtils;
//import org.apache.shardingsphere.transaction.annotation.ShardingSphereTransactionType;
//import org.apache.shardingsphere.transaction.core.TransactionType;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;
import org.springframework.util.ObjectUtils;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;

/**
 * 账户产品信息表 业务实现
 *
 * <AUTHOR>
 * @date 2018-12-04 17:24
 **/
@Service("parm_account_product_info_serviceImpl")
public class AccountProductInfoServiceImpl extends AbstractParameterService implements IAccountProductInfoService {

    private Logger logger = LoggerFactory.getLogger(AccountProductInfoServiceImpl.class);

    @Autowired
    private ICommonService commonService;
    @Autowired
    private Number16IdGen numberIdGenerator;
    @Autowired
    private IProductInfoService productInfoService;
    @Autowired
    private ParmAccountProductInfoMapper parmAccountProductInfoMapper;
    @Autowired
    private ParmAccountProductInfoSelfMapper parmAccountProductInfoSelfMapper;
    @Autowired
    private ParmAcctProductMainInfoSelfMapper parmAcctProductMainInfoSelfMapper;

    /**
     * 根据机构号和卡产品账户产品编号读取信息
     *
     * @param orgNum        String
     * @param accountProNum String
     * @return
     */
    @Override
    public AccountProductInfoResDTO findByIndex(String orgNum, String accountProNum)  {
        if (orgNum == null ||accountProNum == null) {
            throw new AnyTxnParameterException(AnyTxnParameterRespCodeEnum.P_PARAMETER_NOT_EMPTY_FAULT);
        }

        ParmAccountProductInfo parmAccountProductInfoList = parmAccountProductInfoSelfMapper.selectByIndex(orgNum,accountProNum);
        if (parmAccountProductInfoList == null) {
            logger.error("查询账户产品信息表，通过机构号:{}、账户产品编号:{}未查到数据", orgNum, accountProNum);
            throw new AnyTxnParameterException(AnyTxnParameterRespCodeEnum.D_QUERY_PRODUCT_INFO_FAULT);
        }

        return BeanMapping.copy(parmAccountProductInfoList, AccountProductInfoResDTO.class);
    }

    /**
     * 根据id查询
     *
     * @param id id
     * @return CurrencyRateRes
     */
    @Override
    public AccountProductInfoResDTO findOne(String id) {
        if (id == null) {
            throw new AnyTxnParameterException(AnyTxnParameterRespCodeEnum.P_PRIMARY_KEY_NULL_FAULT);
        }

        ParmAccountProductInfo parmAccountProductInfo = parmAccountProductInfoMapper.selectByPrimaryKey(id);

        if (parmAccountProductInfo == null) {
            logger.error("查询账户产品信息，通过id:{}未查到数据", id);
            throw new AnyTxnParameterException(AnyTxnParameterRespCodeEnum.D_QUERY_ACCOUNT_PRODUCT_INFO_BY_ID_FAULT);
        }

        return BeanMapping.copy(parmAccountProductInfo, AccountProductInfoResDTO.class);
    }

    /**
     * 查询列表 分页
     *
     * @param pageNum  页码
     * @param pageSize 页面容量
     * @return PageResultDTO<CurrencyRateRes>
     */
    @Override
    public PageResultDTO<AccountProductInfoResDTO> findPage(Integer pageNum, Integer pageSize, String organizationNumber)  {
        logger.debug("分页查询账户产品信息");
        Page<ParmAccountProductInfo> page = PageHelper.startPage(pageNum, pageSize);
        List<ParmAccountProductInfo> parmAccountProductInfos = parmAccountProductInfoSelfMapper.selectAll(false, OrgNumberUtils.getOrg(organizationNumber));

        if (parmAccountProductInfos.isEmpty()) {
            logger.error("未查询到账户产品信息信息");
            throw new AnyTxnParameterException(AnyTxnParameterRespCodeEnum.D_PAGE_QUERY_ACCOUNT_PRODUCT_INFO_FAULT);
        }
        List<AccountProductInfoResDTO> currencyRateRes = BeanMapping.copyList(parmAccountProductInfos, AccountProductInfoResDTO.class);
        return new PageResultDTO<>(pageNum, pageSize, page.getTotal(), page.getPages(), currencyRateRes);
    }

    /**
     * 修改
     *
     * @param authorisationProcessingReq 账户产品信息传入
     * @return int
     */
    @Override
    @UpdateParameterAnnotation(tableName = "parm_account_product_info", tableDesc = "Account Product Info")
    public ParameterCompare modify(AccountProductInfoReqDTO authorisationProcessingReq) throws AnyTxnParameterException {
        if (authorisationProcessingReq.getId() == null) {
            throw new AnyTxnParameterException(AnyTxnParameterRespCodeEnum.P_PRIMARY_KEY_NULL_FAULT);
        }

        ParmAccountProductInfo parmAccountProductInfo1 = parmAccountProductInfoMapper.selectByPrimaryKey(String.valueOf(authorisationProcessingReq.getId()));

        if (parmAccountProductInfo1 == null) {
            logger.error("修改账户产品信息，通过id:{}未查到数据", authorisationProcessingReq.getId());
            throw new AnyTxnParameterException(AnyTxnParameterRespCodeEnum.D_MODIFY_ACCOUNT_PRODUCT_INFO_BY_ID_FAULT);
        }

        ParmAccountProductInfo parmAccountProductInfo = BeanMapping.copy(authorisationProcessingReq, ParmAccountProductInfo.class);
        parmAccountProductInfo.setUpdateTime(LocalDateTime.now());
        parmAccountProductInfo.setUpdateBy(Constants.DEFAULT_USER);
        parmAccountProductInfo.setId(String.valueOf(authorisationProcessingReq.getId()));

        ParmAccountProductInfo accountProductInfo = parmAccountProductInfoSelfMapper.selectByIndex(
                parmAccountProductInfo.getOrganizationNumber(),parmAccountProductInfo.getAccountProductNumber());

        if (accountProductInfo != null && !accountProductInfo.getId().equals(parmAccountProductInfo.getId())) {
            logger.warn("账户产品信息已存在，orgNum={},accountProductNumber={}",accountProductInfo.getOrganizationNumber(),accountProductInfo.getAccountProductNumber());
            throw new AnyTxnParameterException(AnyTxnParameterRespCodeEnum.D_EXITS_ACCOUNT_PRODUCT_INFO_FAULT);
        }

        return ParameterCompare.getBuilder()
                .withAfter(parmAccountProductInfo)
                .withBefore(parmAccountProductInfo1)
                .build(ParmAccountProductInfo.class);
    }

    /**
     * 删除
     *
     * @param id id
     * @return Boolean
     */
    @Override
    @DeleteParameterAnnotation(tableName = "parm_account_product_info", tableDesc = "Account Product Info")
    public ParameterCompare remove(String id)  {
        if (null == id) {
            throw new AnyTxnParameterException(AnyTxnParameterRespCodeEnum.P_PARAMETER_EMPTY_FAULT);
        }

        ParmAccountProductInfo parmAccountProductInfo = parmAccountProductInfoMapper.selectByPrimaryKey(id);

        if (parmAccountProductInfo == null) {
            logger.error("删除账户产品信息，通过id:{}未查到数据", id);
            throw new AnyTxnParameterException(AnyTxnParameterRespCodeEnum.D_DELETE_ACCOUNT_PRODUCT_INFO_BY_ID_FAULT);
        }

        return ParameterCompare.getBuilder().withBefore(parmAccountProductInfo)
                .build(ParmAccountProductInfo.class);
    }

    /**
     * 新增
     *
     * @param accountProductInfoReqDTO 账户产品信息传入
     * @return CurrencyRateRes
     */
    @Override
    @InsertParameterAnnotation(tableName = "parm_account_product_info", tableDesc = "Account Product Info")
    public ParameterCompare add(AccountProductInfoReqDTO accountProductInfoReqDTO) {
        boolean isExists = parmAccountProductInfoSelfMapper.isExists(
                accountProductInfoReqDTO.getOrganizationNumber(), accountProductInfoReqDTO.getAccountProductNumber())>0;
        if (isExists) {
            logger.warn("账户产品信息已存在，orgNum={},accountProductNumber={}",
                    accountProductInfoReqDTO.getOrganizationNumber(), accountProductInfoReqDTO.getAccountProductNumber());
            throw new AnyTxnParameterException(AnyTxnParameterRespCodeEnum.D_EXITS_ACCOUNT_PRODUCT_INFO_FAULT);
        }

        ParmAccountProductInfo parmAccountProductInfo = BeanMapping.copy(accountProductInfoReqDTO, ParmAccountProductInfo.class);

        return ParameterCompare.getBuilder().withAfter(parmAccountProductInfo)
                .build(ParmAccountProductInfo.class);
    }

    /**
     * 获取账产品编号下拉框数据
     * @return
     */
    @Override
    public HashMap<String, ArrayList<SysCodeSimpleResDTO>> findAcctProductTypeList(String organizationNumber) {
        HashMap<String, ArrayList<SysCodeSimpleResDTO>> codeMap = new HashMap<>(2);
        ArrayList<SysCodeSimpleResDTO> sysCodeSimpleResList = new ArrayList<>();
        ArrayList<CommonSelectResDTO> commonSelectResDtos = (ArrayList)commonService.findAllDuplicate("PARM_PRODUCT_INFO","description","product_number", organizationNumber);
        if (!commonSelectResDtos.isEmpty()) {
            commonSelectResDtos.forEach(commonSelectResDTO -> {
                SysCodeSimpleResDTO sysCodeSimpleRes = new SysCodeSimpleResDTO();
                sysCodeSimpleRes.setCodeId(commonSelectResDTO.getId());
                sysCodeSimpleRes.setCodeName(commonSelectResDTO.getName());
                sysCodeSimpleResList.add(sysCodeSimpleRes);
            });
        }
        codeMap.put("acctProductType", sysCodeSimpleResList);
        return codeMap;
    }

    @Override
    public HashMap<String, List<SysCodeSimpleResDTO>> findAcctProductTypeListByLimitTypeNumAnd(
            String limitTypeNum,String tableId,String customerId) {
        if (StringUtils.isBlank(limitTypeNum) || StringUtils.isBlank(tableId) || StringUtils.isBlank(customerId)) {
            logger.error("帐产品编号查询异常,limitTypeNum:{},tableId:{},customerId:{}", limitTypeNum,tableId,customerId);
            throw new AnyTxnParameterException(AnyTxnParameterRespCodeEnum.D_DELETE_ACCOUNT_PRODUCT_INFO_BY_ID_FAULT);
        }
        List<ProductInfoResDTO> productInfoResDtos = productInfoService
                .findProductInfoBylimitTypeAndTemIdAndCustomerId(limitTypeNum,tableId,customerId);
        if(CollectionUtils.isEmpty(productInfoResDtos)){
            logger.error("该节点没有帐产品关联额度信息");
            return null;
        }

        HashMap<String, List<SysCodeSimpleResDTO>> codeMap = new HashMap<>(2);
        List<SysCodeSimpleResDTO> sysCodeSimpleResList = new ArrayList<>();

        for(ProductInfoResDTO productInfoResDTO:productInfoResDtos){
            SysCodeSimpleResDTO sysCodeSimpleRes = new SysCodeSimpleResDTO();
            sysCodeSimpleRes.setCodeId(productInfoResDTO.getProductNumber());
            ParmAcctProductMainInfo acctProductMainInfo = parmAcctProductMainInfoSelfMapper.selectByOrgNumAndProNum(OrgNumberUtils.getOrg(), productInfoResDTO.getProductNumber());
            if (!ObjectUtils.isEmpty(acctProductMainInfo)){
                sysCodeSimpleRes.setCodeName(acctProductMainInfo.getDescription());
            }
            sysCodeSimpleResList.add(sysCodeSimpleRes);
        }
        codeMap.put("acctProductType", sysCodeSimpleResList);
        return codeMap;
    }

    @Override
    @Transactional(rollbackFor = Exception.class,propagation = Propagation.REQUIRED)
    //@ShardingSphereTransactionType(TransactionType.XA)
    protected boolean updateDb(ParmModificationRecord parmModificationRecord) {
        ParmAccountProductInfo parmAccountProductInfo = JSON.parseObject(parmModificationRecord.getParmBody(), ParmAccountProductInfo.class);
        parmAccountProductInfo.initUpdateDateTime();
        parmAccountProductInfo.setOrganizationNumber(OrgNumberUtils.getOrg());

        int i = parmAccountProductInfoMapper.updateByPrimaryKeySelective(parmAccountProductInfo);
        return i > 0;
    }

    @Override
    @Transactional(rollbackFor = Exception.class,propagation = Propagation.REQUIRED)
    //@ShardingSphereTransactionType(TransactionType.XA)
    protected boolean insertDb(ParmModificationRecord parmModificationRecord) {
        ParmAccountProductInfo parmAccountProductInfo = JSON.parseObject(parmModificationRecord.getParmBody(), ParmAccountProductInfo.class);
        parmAccountProductInfo.initUpdateDateTime();
        parmAccountProductInfo.initCreateDateTime();

        int i = parmAccountProductInfoMapper.insertSelective(parmAccountProductInfo);
        return i > 0;
    }

    @Override
    @Transactional(rollbackFor = Exception.class,propagation = Propagation.REQUIRED)
    //@ShardingSphereTransactionType(TransactionType.XA)
    protected boolean deleteDb(ParmModificationRecord parmModificationRecord) {
        ParmAccountProductInfo parmAccountProductInfo = JSON.parseObject(parmModificationRecord.getParmBody(), ParmAccountProductInfo.class);

        logger.warn("删除账户产品信息");
        int i = parmAccountProductInfoMapper.deleteByPrimaryKey(parmAccountProductInfo.getId());
        return i > 0;
    }
}
