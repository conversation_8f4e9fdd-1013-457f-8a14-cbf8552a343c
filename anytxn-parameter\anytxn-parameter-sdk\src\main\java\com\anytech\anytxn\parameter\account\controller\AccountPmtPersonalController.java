package com.anytech.anytxn.parameter.account.controller;

import io.swagger.v3.oas.annotations.tags.Tag;
import io.swagger.v3.oas.annotations.Operation;
import com.anytech.anytxn.common.core.base.ParameterCompare;
import com.anytech.anytxn.common.core.base.PageResultDTO;
import com.anytech.anytxn.common.core.base.AnyTxnHttpResponse;
import com.anytech.anytxn.common.core.base.BizBaseController;
import com.anytech.anytxn.parameter.base.account.domain.dto.AcctPmtAllocPersonalReqDTO;
import com.anytech.anytxn.parameter.base.account.domain.dto.AcctPmtAllocPersonalResDTO;
import com.anytech.anytxn.parameter.base.account.service.IParmAcctPmtAllocPersonalService;
import com.anytech.anytxn.parameter.base.common.enums.ParameterRepDetailEnum;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import jakarta.validation.Valid;

/**
 *
 *<pre>
 * Description:
 * Company:		江融信
 * Author:		lichao
 * Version:		1.0
 * Create at:	2020/8/9 6:15 下午
 * <p>
 * 修改历史:
 * 日期    作者    版本  修改描述
 * ------------------------------------------------------------------
 *
 *</pre>
 */
@Tag(name = "账户间还款分配个性化参数管理", description = "账户间还款分配个性化参数管理接口")
@RestController
public class AccountPmtPersonalController extends BizBaseController {

    @Autowired
    private IParmAcctPmtAllocPersonalService parmAcctPmtAllocPersonalService;

    /**
     * 分页查询数据
     * @params [pageNum,pageSize]
     * @return PageResultDTO
     * @throws
     */
    @Operation(summary = "分页查询，账户间还款分配个性化参数")
    @GetMapping(value = "/param/queryAcctPmtAllocPersonal/pageNum/{pageNum}/pageSize/{pageSize}")
    public AnyTxnHttpResponse<PageResultDTO<AcctPmtAllocPersonalResDTO>> getPage(@PathVariable(value = "pageNum") Integer pageNum,
                                                                                 @PathVariable(value = "pageSize") Integer pageSize,
                                                                                 AcctPmtAllocPersonalReqDTO acctPmtAllocPersonalReqDTO) {
        PageResultDTO<AcctPmtAllocPersonalResDTO> page = parmAcctPmtAllocPersonalService.findByPage(pageNum, pageSize,acctPmtAllocPersonalReqDTO);
        return AnyTxnHttpResponse.success(page);
    }

    /**
     * 通过id查询账户间还款分配个性化参数
     * @param id
     * @return AcctPmtAllocPersonalResDTO
     * @throws
     */
    @Operation(summary = "通过id获取账户间还款分配个性化参数")
    @GetMapping(value = "/param/queryAcctPmtAllocPersonalById/id/{id}")
    public AnyTxnHttpResponse<AcctPmtAllocPersonalResDTO> queryAcctPmtAllocPersonalById(@PathVariable String id) {
        AcctPmtAllocPersonalResDTO res = parmAcctPmtAllocPersonalService.findById(id);
        return AnyTxnHttpResponse.success(res);
    }

    /**
     * 新增账户间还款分配个性化参数
     * @param acctPmtAllocPersonalReqDTO 还款分配个性化入参对象
     * @return 还款分配个性化详情
     * @throws
     */
    @Operation(summary = "新增账户间还款分配个性化参数")
    @PostMapping(value = "/param/addAcctPmtAllocPersonal")
    public AnyTxnHttpResponse<Object> add(@Valid @RequestBody AcctPmtAllocPersonalReqDTO acctPmtAllocPersonalReqDTO){
        ParameterCompare res = parmAcctPmtAllocPersonalService.add(acctPmtAllocPersonalReqDTO);
        return AnyTxnHttpResponse.success(res, ParameterRepDetailEnum.CREATE.message());
    }

    /**
     * 修改账户间还款分配个性化参数
     * @param acctPmtAllocPersonalReqDTO 还款分配个性化入参对象
     * @return 还款分配个性化详情
     * @throws
     */
    @Operation(summary = "修改账户间还款分配个性化参数")
    @PutMapping(value = "/param/updateAcctPmtAllocPersonal")
    public AnyTxnHttpResponse<Object> modify(@Valid @RequestBody AcctPmtAllocPersonalReqDTO acctPmtAllocPersonalReqDTO){
        ParameterCompare res = parmAcctPmtAllocPersonalService.modify(acctPmtAllocPersonalReqDTO);
        return AnyTxnHttpResponse.success(res,ParameterRepDetailEnum.UPDATE.message());
    }

    /**
     * 通过id删除账户间还款分配个性化参数
     * @param id
     * @return Boolean
     * @throws
     */
    @Operation(summary = "删除账户间还款分配个性化参数")
    @DeleteMapping(value = "/param/deleteAcctPmtAllocPersonalById/id/{id}")
    public AnyTxnHttpResponse<Object> remove(@PathVariable String id){
        ParameterCompare res = parmAcctPmtAllocPersonalService.remove(id);
        return AnyTxnHttpResponse.success(res,ParameterRepDetailEnum.DEL.message());
    }

    /**
     * 通过orgNumber、tableId查询还款分配个性化信息
     * @params [orgNumber,tableId]
     * @return AcctPmtAllocPersonalResDTO
     * @throws
     */
    @Operation(summary = "通过orgNumber、tableId获取账户间还款分配个性化参数")
    @GetMapping(value = "/param/queryAcctPmtAllocPersonalByOrgAndTableId/orgNumber/{orgNumber}/tableId/{tableId}")
    public AnyTxnHttpResponse<AcctPmtAllocPersonalResDTO> findByOrgAndTableId(@PathVariable(value = "orgNumber") String orgNumber,
                                                                              @PathVariable(value = "tableId") String tableId){
        AcctPmtAllocPersonalResDTO res = parmAcctPmtAllocPersonalService.queryAcctPmtAllocPersonalByOrgTableId(orgNumber,tableId);
        return AnyTxnHttpResponse.success(res);
    }
}
