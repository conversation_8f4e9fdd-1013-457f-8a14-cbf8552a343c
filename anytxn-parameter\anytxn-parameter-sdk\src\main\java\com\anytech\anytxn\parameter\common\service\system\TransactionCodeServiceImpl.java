package com.anytech.anytxn.parameter.common.service.system;

import com.alibaba.fastjson.JSON;
import com.github.pagehelper.Page;
import com.github.pagehelper.PageHelper;
import com.anytech.anytxn.common.core.annotation.db.DeleteParameterAnnotation;
import com.anytech.anytxn.common.core.annotation.db.InsertParameterAnnotation;
import com.anytech.anytxn.common.core.annotation.db.UpdateParameterAnnotation;
import com.anytech.anytxn.business.base.audit.AbstractParameterService;
import com.anytech.anytxn.common.core.base.ParameterCompare;
import com.anytech.anytxn.business.base.audit.ParmModificationRecord;
import com.anytech.anytxn.common.core.base.PageResultDTO;
import com.anytech.anytxn.common.core.enums.DebitCreditIndicatorEnum;
import com.anytech.anytxn.common.core.enums.TransactionAttributeEnum;
import com.anytech.anytxn.common.core.utils.BeanMapping;
import com.anytech.anytxn.common.core.utils.OrgNumberUtils;
import com.anytech.anytxn.common.sequence.utils.Number16IdGen;
import com.anytech.anytxn.common.core.utils.TenantUtils;
import com.anytech.anytxn.parameter.base.common.domain.dto.ParmTransactionCodeResDTO;
import com.anytech.anytxn.parameter.base.common.domain.dto.TransactionCodeReqDTO;
import com.anytech.anytxn.parameter.base.common.domain.dto.TransactionCodeResDTO;
import com.anytech.anytxn.parameter.base.common.service.ITransactionCodeService;
import com.anytech.anytxn.parameter.base.common.constants.ParameterConstant;
import com.anytech.anytxn.parameter.base.common.enums.AnyTxnParameterRespCodeEnum;
import com.anytech.anytxn.parameter.base.common.exception.AnyTxnParameterException;
import com.anytech.anytxn.parameter.common.mapper.broadcast.system.ParmTransactionCodeMapper;
import com.anytech.anytxn.parameter.common.mapper.broadcast.system.ParmTransactionCodeSelfMapper;
import com.anytech.anytxn.parameter.base.common.domain.model.system.ParmTransactionCode;
import org.apache.commons.lang3.StringUtils;
//import org.apache.shardingsphere.transaction.annotation.ShardingSphereTransactionType;
//import org.apache.shardingsphere.transaction.core.TransactionType;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import java.util.*;

/**
 * 交易码 业务接口实现
 * <AUTHOR>
 * @date 2018-08-16
 **/
@Service("parm_transaction_code_serviceImpl")
public class TransactionCodeServiceImpl extends AbstractParameterService implements ITransactionCodeService {

    private Logger logger = LoggerFactory.getLogger(TransactionCodeServiceImpl.class);

    @Autowired
    private ParmTransactionCodeMapper transactionCodeMapper;
    @Autowired
    private ParmTransactionCodeSelfMapper transactionCodeSelfMapper;
    @Autowired
    private Number16IdGen numberIdGenerator;


    /**
     * 添加交易码
     *
     * @param req 交易码入参对象
     * @return 交易码详情
     * @throws AnyTxnParameterException
     */
    @Override
    @InsertParameterAnnotation(tableName = "parm_transaction_code",tableDesc = "Transaction code")
    public ParameterCompare add(TransactionCodeReqDTO req) throws AnyTxnParameterException {
        boolean isExists = transactionCodeSelfMapper.isExists(req.getOrganizationNumber(), req.getTransactionCode()) > 0;
        if (isExists) {
            logger.warn("交易码编号已存在, orgNumber={}",
                    OrgNumberUtils.getOrg());
            throw new AnyTxnParameterException(AnyTxnParameterRespCodeEnum.D_EXIST_TRANSACTION_CODE_FAULT);
        }
        // 构建交易码
        ParmTransactionCode transactionType = BeanMapping.copy(req, ParmTransactionCode.class);
        transactionType.setId(numberIdGenerator.generateId(TenantUtils.getTenantId())+"");
        return ParameterCompare
                .getBuilder()
                .withAfter(transactionType)
                .build(ParmTransactionCode.class);
    }

    /**
     * 修改交易码
     *
     * @param req 交易码入参对象
     * @return 交易码详情
     * @throws AnyTxnParameterException
     */
    @Override
    @UpdateParameterAnnotation(tableName = "parm_transaction_code",tableDesc = "Transaction code")
    public ParameterCompare modify(TransactionCodeReqDTO req) throws AnyTxnParameterException {
        if (req.getId() == null) {
            throw new AnyTxnParameterException(AnyTxnParameterRespCodeEnum.P_PARAMETER_ID_EMPTY_FAULT);
        }
        ParmTransactionCode transactionCode = transactionCodeMapper.selectByPrimaryKey(req.getId());
        if (transactionCode == null) {
            logger.error("修改交易码, 通过主键id({})未找到数据", req.getId());
            throw new AnyTxnParameterException(AnyTxnParameterRespCodeEnum.D_QUERY_TRANSACTION_CODE_BY_ID_FAULT);
        }

        // 如果交易码编码被修改，则验证新的交易码编码是否存在
        String oldTransactionCode = transactionCode.getTransactionCode();
        String newTransactionCode = req.getTransactionCode();
        if (!oldTransactionCode.equals(newTransactionCode)) {
            boolean isExists = false;
            isExists = transactionCodeSelfMapper.isExists(transactionCode.getOrganizationNumber(), req.getTransactionCode()) > 0;
            if (isExists) {
                logger.warn("交易码已存在, orgNumber={}, transactionCode={}",
                        transactionCode.getOrganizationNumber(), req.getTransactionCode());

                throw new AnyTxnParameterException(AnyTxnParameterRespCodeEnum.D_EXIST_TRANSACTION_CODE_FAULT);
            }
        }
        // 拷贝修改的数据并更新
        ParmTransactionCode modify = BeanMapping.copy(req, ParmTransactionCode.class);
        return ParameterCompare
                .getBuilder()
                .withAfter(modify)
                .withBefore(transactionCode)
                .build(ParmTransactionCode.class);
    }

    /**
     * 删除交易码，通过id
     *
     * @param id 交易码ID
     * @return true:成功|false:失败
     * @throws AnyTxnParameterException
     */
    @Override
    @DeleteParameterAnnotation(tableName = "parm_transaction_code",tableDesc = "Transaction code")
    public ParameterCompare remove(String id) throws AnyTxnParameterException {
        if (id == null) {
            throw new AnyTxnParameterException(AnyTxnParameterRespCodeEnum.P_PARAMETER_EMPTY_FAULT);
        }

        ParmTransactionCode transactionCode = transactionCodeMapper.selectByPrimaryKey(id);
        if (transactionCode == null) {
            logger.error("删除交易码, 通过主键id({})未找到数据", id);

            throw new AnyTxnParameterException(AnyTxnParameterRespCodeEnum.D_QUERY_TRANSACTION_CODE_BY_ID_FAULT);
        }
        return ParameterCompare
                .getBuilder()
                .withBefore(transactionCode)
                .build(ParmTransactionCode.class);
    }

    /**
     * 获取交易码详情，通过id
     *
     * @param id 交易码ID
     * @return 交易码详情
     * @throws AnyTxnParameterException
     */
    @Override
    public TransactionCodeResDTO find(String id) throws AnyTxnParameterException {
        if (id == null) {

            throw new AnyTxnParameterException(AnyTxnParameterRespCodeEnum.P_PARAMETER_EMPTY_FAULT);
        }

        ParmTransactionCode transactionCode = transactionCodeMapper.selectByPrimaryKey(id);
        if (transactionCode == null) {
            logger.error("获取交易码详情, 通过主键id({})未找到数据", id);

            throw new AnyTxnParameterException(AnyTxnParameterRespCodeEnum.D_QUERY_TRANSACTION_CODE_BY_ID_FAULT);
        }

        return BeanMapping.copy(transactionCode, TransactionCodeResDTO.class);
    }

    /**
     * 查询交易码集合，通过机构编号及启用状态
     *
     * @param orgNumber 机构编号
     * @param status    交易码启用状态，不传查询所有状态
     * @return 分页的交易码
     * @throws AnyTxnParameterException
     */
    @Override
    public List<TransactionCodeResDTO> findListByOrgNumber(String orgNumber, String status) throws AnyTxnParameterException {
        List<ParmTransactionCode> typeList = transactionCodeSelfMapper.selectListByOrgNumber(orgNumber, status, true);
        return BeanMapping.copyList(typeList, TransactionCodeResDTO.class);
    }

    /**
     * 分页查询交易码，通过机构编号及启用状态
     *
     * @param pageNum   页号
     * @param pageSize  每页大小
     * @return 分页的交易码
     * @throws AnyTxnParameterException
     */
    @Override
    public PageResultDTO<TransactionCodeResDTO> findPageByOrgNumber(Integer pageNum, Integer pageSize,
                                                                    TransactionCodeReqDTO transactionCodeReqDTO) throws AnyTxnParameterException {
        logger.info("分页查询交易码, pageNum={}, pageSize={}, orgNumber={}, status={}", pageNum, pageSize);

        Page<ParmTransactionCode> page = PageHelper.startPage(pageNum, pageSize);
        transactionCodeReqDTO.setOrganizationNumber(StringUtils.isEmpty(transactionCodeReqDTO.getOrganizationNumber()) ? OrgNumberUtils.getOrg() : transactionCodeReqDTO.getOrganizationNumber());
        List<ParmTransactionCode> transactionCodeList = transactionCodeSelfMapper.selectByCondition(transactionCodeReqDTO);
        List<TransactionCodeResDTO> listData = BeanMapping.copyList(transactionCodeList, TransactionCodeResDTO.class);

        return new PageResultDTO<>(pageNum, pageSize, page.getTotal(), page.getPages(), listData);
    }

    /**
     * 获取交易码详情，通过交易码,机构号
     *
     * @param orgNumber       机构号
     * @param transactionCode 交易码
     * @return 交易码详情
     * @throws AnyTxnParameterException
     */
    @Override
    public TransactionCodeResDTO findTransactionCode(String orgNumber, String transactionCode) throws AnyTxnParameterException {
        ParmTransactionCode parmTransactionCode = transactionCodeSelfMapper.selectByOrgNumberAndCode(orgNumber, transactionCode);
        if (parmTransactionCode == null) {
            logger.error("ParmTransactionCode not exists ,orgNumber={},transactionCode={}", orgNumber, transactionCode);

            throw new AnyTxnParameterException(AnyTxnParameterRespCodeEnum.D_QUERY_TRANSACTION_CODE_FAULT);
        }
        return BeanMapping.copy(parmTransactionCode, TransactionCodeResDTO.class);
    }

    /**
     * 查询所有交易码
     *
     * @return 所有交易码信息
     * @throws AnyTxnParameterException 异常
     */
    @Override
    public List<TransactionCodeResDTO> findAll(String organizationNumber) throws AnyTxnParameterException {
        List<ParmTransactionCode> typeList = transactionCodeSelfMapper.selectAll(OrgNumberUtils.getOrg(organizationNumber));
        return BeanMapping.copyList(typeList, TransactionCodeResDTO.class);
    }
    /**
     * 查询所有交易码
     *
     * @return 所有交易码信息
     * @throws AnyTxnParameterException 异常
     */
    @Override
    public List<TransactionCodeResDTO> findTransctionCode(String organizationNumber) throws AnyTxnParameterException {
        List<ParmTransactionCode> typeList = transactionCodeSelfMapper.selectTransactionCode(OrgNumberUtils.getOrg(organizationNumber));
        return BeanMapping.copyList(typeList, TransactionCodeResDTO.class);
    }


    @Override
    public Map<String, List<ParmTransactionCodeResDTO>> selectDataBydebitAttrAndtransactionAttr(String organizationNumber) {
        Map<String, List<ParmTransactionCodeResDTO>> map = new HashMap<>(10);
        /**
         * L - 专项分期交易(暂无)
         * D=借记；C=贷记；M=非金融（memo类）
         * 借贷记属性为借，交易属性为1消费
         */
        //R - 普通消费交易
        List<ParmTransactionCode> parmTransactionCodeList1 =
                transactionCodeSelfMapper.selectDataBydebitAttrAndtransactionAttr(DebitCreditIndicatorEnum.DEBIT_INDICATOR.getCode(), TransactionAttributeEnum.CONSUME.getCode(), organizationNumber);
        if (parmTransactionCodeList1 != null) {
            map.put(ParameterConstant.RD1, BeanMapping.copyList(parmTransactionCodeList1, ParmTransactionCodeResDTO.class));
        }
        //借贷记属性为贷，交易属性为1消费
        List<ParmTransactionCode> parmTransactionCodeList2 =
                transactionCodeSelfMapper.selectDataBydebitAttrAndtransactionAttr(DebitCreditIndicatorEnum.CREDIT_INDICATOR.getCode(), TransactionAttributeEnum.CONSUME.getCode(), organizationNumber);
        if (parmTransactionCodeList2 != null) {
            map.put(ParameterConstant.RC1, BeanMapping.copyList(parmTransactionCodeList2, ParmTransactionCodeResDTO.class));
        }

        //借贷记属性为借，交易属性为2现金
        //C - 取现交易
        List<ParmTransactionCode> parmTransactionCodeList3 =
                transactionCodeSelfMapper.selectDataBydebitAttrAndtransactionAttr(DebitCreditIndicatorEnum.DEBIT_INDICATOR.getCode(), TransactionAttributeEnum.CASH.getCode(), organizationNumber);
        if (parmTransactionCodeList3 != null) {
            map.put(ParameterConstant.CD2, BeanMapping.copyList(parmTransactionCodeList3, ParmTransactionCodeResDTO.class));

        }
        //借贷记属性为贷，交易属性为2消费
        List<ParmTransactionCode> parmTransactionCodeList4 =
                transactionCodeSelfMapper.selectDataBydebitAttrAndtransactionAttr(DebitCreditIndicatorEnum.CREDIT_INDICATOR.getCode(), TransactionAttributeEnum.CASH.getCode(), organizationNumber);
        if (parmTransactionCodeList4 != null) {
            map.put(ParameterConstant.CC2, BeanMapping.copyList(parmTransactionCodeList4, ParmTransactionCodeResDTO.class));
        }

        //借贷记属性为借，交易属性为3普通分期
        //I - 普通分期交易
        List<ParmTransactionCode> parmTransactionCodeList5 =
                transactionCodeSelfMapper.selectDataBydebitAttrAndtransactionAttr(DebitCreditIndicatorEnum.DEBIT_INDICATOR.getCode(), TransactionAttributeEnum.CONSUME_BY_STAGES.getCode(), organizationNumber);
        if (parmTransactionCodeList5 != null) {
            map.put(ParameterConstant.ID3, BeanMapping.copyList(parmTransactionCodeList5, ParmTransactionCodeResDTO.class));
        }

        //借贷记属性为贷，交易属性为3消费
        List<ParmTransactionCode> parmTransactionCodeList6 =
                transactionCodeSelfMapper.selectDataBydebitAttrAndtransactionAttr(DebitCreditIndicatorEnum.CREDIT_INDICATOR.getCode(), TransactionAttributeEnum.CONSUME_BY_STAGES.getCode(), organizationNumber);
        if (parmTransactionCodeList6 != null) {
            map.put(ParameterConstant.IC3, BeanMapping.copyList(parmTransactionCodeList6, ParmTransactionCodeResDTO.class));
        }

        //借贷记属性为借，交易属性为4现金分期
        //S - 现金分期交易
        List<ParmTransactionCode> parmTransactionCodeList7 =
                transactionCodeSelfMapper.selectDataBydebitAttrAndtransactionAttr(DebitCreditIndicatorEnum.DEBIT_INDICATOR.getCode(), TransactionAttributeEnum.CASH_BY_STAGES.getCode(), organizationNumber);
        if (parmTransactionCodeList7 != null) {
            map.put(ParameterConstant.SD4, BeanMapping.copyList(parmTransactionCodeList7, ParmTransactionCodeResDTO.class));
        }
        //借贷记属性为借，交易属性为4现金分期
        List<ParmTransactionCode> parmTransactionCodeList8 =
                transactionCodeSelfMapper.selectDataBydebitAttrAndtransactionAttr(DebitCreditIndicatorEnum.CREDIT_INDICATOR.getCode(), TransactionAttributeEnum.CASH_BY_STAGES.getCode(), organizationNumber);
        if (parmTransactionCodeList8 != null) {
            map.put(ParameterConstant.SC4, BeanMapping.copyList(parmTransactionCodeList8, ParmTransactionCodeResDTO.class));
        }
        //借贷记属性为贷，交易属性为7 还款
        //P - 还款交易
        List<ParmTransactionCode> parmTransactionCodeList9 =
                transactionCodeSelfMapper.selectDataBydebitAttrAndtransactionAttr(DebitCreditIndicatorEnum.DEBIT_INDICATOR.getCode(), TransactionAttributeEnum.REPAYMENT.getCode(), organizationNumber);
        if (parmTransactionCodeList9 != null) {
            map.put(ParameterConstant.PD7, BeanMapping.copyList(parmTransactionCodeList9, ParmTransactionCodeResDTO.class));
        }
        //借贷记属性为借，交易属性为4现金分期
        List<ParmTransactionCode> parmTransactionCodeList10 =
                transactionCodeSelfMapper.selectDataBydebitAttrAndtransactionAttr(DebitCreditIndicatorEnum.CREDIT_INDICATOR.getCode(), TransactionAttributeEnum.REPAYMENT.getCode(), organizationNumber);
        if (parmTransactionCodeList10 != null) {
            map.put(ParameterConstant.PC7, BeanMapping.copyList(parmTransactionCodeList10, ParmTransactionCodeResDTO.class));
        }
        //L - 专项分期
        //借贷记属性为借，交易属性为"E","F","G","H","I","J"
        String[] transactionAttributes = {"E","F","G","H","I","J"};
        List<String> attriList = Arrays.asList(transactionAttributes);
        List<ParmTransactionCode> parmTransactionCodeList11 =
                transactionCodeSelfMapper.selectDataBydebitAttrAndtransactionAttrs(DebitCreditIndicatorEnum.DEBIT_INDICATOR.getCode(), attriList, organizationNumber);
        if (parmTransactionCodeList11 != null) {
            map.put(ParameterConstant.LDE, BeanMapping.copyList(parmTransactionCodeList11, ParmTransactionCodeResDTO.class));
        }
        //借贷记属性为贷，交易属性为"E","F","G","H","I","J"
        List<ParmTransactionCode> parmTransactionCodeList12 =
                transactionCodeSelfMapper.selectDataBydebitAttrAndtransactionAttrs(DebitCreditIndicatorEnum.CREDIT_INDICATOR.getCode(), attriList, organizationNumber);
        if (parmTransactionCodeList12 != null) {
            map.put(ParameterConstant.LCE, BeanMapping.copyList(parmTransactionCodeList12, ParmTransactionCodeResDTO.class));
        }
        return map;
    }

    /**
     * 查询分期相关交易码
     * @return Map<String, List<ParmTransactionCodeResDTO>>
     */
    @Override
    public Map<String, List<ParmTransactionCodeResDTO>> findInstallCode(String organizationNumber) {
        Map<String, List<ParmTransactionCodeResDTO>> resultMap = new HashMap<>(10);
        String[] transactionAttributes = {"E","F","G","H","I","J"};
        List<ParmTransactionCodeResDTO> createTransactionCodes=new ArrayList<>();
        List<ParmTransactionCodeResDTO> installTransactionCodes=new ArrayList<>();
        List<ParmTransactionCodeResDTO> principalTransactionCodes=new ArrayList<>();
        List<ParmTransactionCodeResDTO> principalReversalTransCodes=new ArrayList<>();
        List<ParmTransactionCodeResDTO> feeTransactionCodes=new ArrayList<>();
        List<ParmTransactionCodeResDTO> feeReversalTransactionCodes=new ArrayList<>();
        List<ParmTransactionCodeResDTO> penatlyTransactionCodes=new ArrayList<>();
        List<ParmTransactionCodeResDTO> feeAmortizeTransactionCodes=new ArrayList<>();
        List<ParmTransactionCodeResDTO> installReturnCodeRevs=new ArrayList<>();
        List<ParmTransactionCodeResDTO> transferFeeTransactionCods=new ArrayList<>();
        List<String> attriList = Arrays.asList(transactionAttributes);
        List<ParmTransactionCode> installCodeCredits = transactionCodeSelfMapper.selectDataBydebitAttrAndtransactionAttrs("C", attriList, organizationNumber);
        List<ParmTransactionCode> installCodeDebits = transactionCodeSelfMapper.selectDataBydebitAttrAndtransactionAttrs("D",attriList, organizationNumber);
        List<ParmTransactionCode> parmTransactionCodes = transactionCodeSelfMapper.selectData(organizationNumber);
        List<ParmTransactionCodeResDTO> installCodeCreditList = BeanMapping.copyList(installCodeCredits, ParmTransactionCodeResDTO.class);
        List<ParmTransactionCodeResDTO> installCodeDebitList = BeanMapping.copyList(installCodeDebits, ParmTransactionCodeResDTO.class);
        List<ParmTransactionCodeResDTO> installCodeList = BeanMapping.copyList(parmTransactionCodes, ParmTransactionCodeResDTO.class);

        for (ParmTransactionCodeResDTO transactionCode : installCodeCreditList) {
            if("E".equals(transactionCode.getTransactionAttribute())){
                createTransactionCodes.add(transactionCode);
            }
            if("F".equals(transactionCode.getTransactionAttribute())){
                installReturnCodeRevs.add(transactionCode);
            }
            if("G".equals(transactionCode.getTransactionAttribute())){
                principalReversalTransCodes.add(transactionCode);
            }
            if("H".equals(transactionCode.getTransactionAttribute())){
                feeReversalTransactionCodes.add(transactionCode);
            }

        }
        for (ParmTransactionCodeResDTO transactionCode : installCodeDebitList) {
            if("F".equals(transactionCode.getTransactionAttribute())){
                installTransactionCodes.add(transactionCode);
            }
            if("G".equals(transactionCode.getTransactionAttribute())){
                principalTransactionCodes.add(transactionCode);
            }
            if("H".equals(transactionCode.getTransactionAttribute())){
                feeTransactionCodes.add(transactionCode);
            }
            if("I".equals(transactionCode.getTransactionAttribute())){
                penatlyTransactionCodes.add(transactionCode);
            }
            if("J".equals(transactionCode.getTransactionAttribute())){
                feeAmortizeTransactionCodes.add(transactionCode);
            }
        }

        for (ParmTransactionCodeResDTO transactionCode :installCodeList) {
            transferFeeTransactionCods.add(transactionCode);
        }
        resultMap.put("createTransactionCode",createTransactionCodes);
        resultMap.put("installTransactionCode",installTransactionCodes);
        resultMap.put("principalTransactionCode",principalTransactionCodes);
        resultMap.put("principalReversalTransCode",principalReversalTransCodes);
        resultMap.put("feeTransactionCode",feeTransactionCodes);
        resultMap.put("feeReversalTransactionCode",feeReversalTransactionCodes);
        resultMap.put("penatlyTransactionCode",penatlyTransactionCodes);
        resultMap.put("feeAmortizeTransactionCode",feeAmortizeTransactionCodes);
        resultMap.put("installReturnCodeRev",installReturnCodeRevs);
        resultMap.put("transferFeeCode",transferFeeTransactionCods);
        return resultMap;
    }

    /**
     * 根据交易属性、借贷记属性查询交易码参数表
     * @param transactionAttribute 交易属性
     * @param debitCreditIndicator 借贷记属性
     * @return List<TransactionCodeResDTO>
     */
    @Override
    public List<TransactionCodeResDTO> selectByTransAttrAndDebitCreditInd(String transactionAttribute, String debitCreditIndicator) {
        List<ParmTransactionCode> parmTransactionCodes = transactionCodeSelfMapper.selectDataBydebitAttrAndtransactionAttr(debitCreditIndicator, transactionAttribute, OrgNumberUtils.getOrg());
        if (null == parmTransactionCodes){
            logger.error("根据交易属性={}、借贷记属性={}查询交易码参数表，未查到数据", transactionAttribute, debitCreditIndicator);

            throw new AnyTxnParameterException(AnyTxnParameterRespCodeEnum.D_QUERY_TRANSACTION_CODE_BY_ATTRIBUTE_FAULT);
        }
        return BeanMapping.copyList(parmTransactionCodes, TransactionCodeResDTO.class);
    }

    /**
     * 获取交易属性是5、借贷记属性为D的所有交易码参数表
     * @return List<TransactionCodeResDTO>
     */
    @Override
    public List<TransactionCodeResDTO> getBySixAndDebitCreditInd(String organizationNumber) {
        List<ParmTransactionCode> typeList = transactionCodeSelfMapper.selectAll(organizationNumber);
        List<ParmTransactionCode> res = new ArrayList<>();
        for(ParmTransactionCode parmTransactionCode : typeList){
            String transactionAttribute = parmTransactionCode.getTransactionAttribute();
            String debitCreditIndicator = parmTransactionCode.getDebitCreditIndicator();
            if("5".equals(transactionAttribute) && "D".equals(debitCreditIndicator)){
                    res.add(parmTransactionCode);
            }
        }
        return BeanMapping.copyList(res,TransactionCodeResDTO.class);
    }

    @Override
    @Transactional(rollbackFor = Exception.class,propagation = Propagation.REQUIRED)
    //@ShardingSphereTransactionType(TransactionType.XA)
    protected boolean updateDb(ParmModificationRecord parmModificationRecord) {
        ParmTransactionCode parmTransactionCode = JSON.parseObject(parmModificationRecord.getParmBody(), ParmTransactionCode.class);
        parmTransactionCode.initUpdateDateTime();
        int res = transactionCodeMapper.updateByPrimaryKeySelective(parmTransactionCode);
        return res > 0;
    }

    @Override
    @Transactional(rollbackFor = Exception.class,propagation = Propagation.REQUIRED)
    //@ShardingSphereTransactionType(TransactionType.XA)
    protected boolean insertDb(ParmModificationRecord parmModificationRecord) {
        ParmTransactionCode parmTransactionCode = JSON.parseObject(parmModificationRecord.getParmBody(), ParmTransactionCode.class);
        parmTransactionCode.initUpdateDateTime();
        parmTransactionCode.initCreateDateTime();
        int res = transactionCodeMapper.insert(parmTransactionCode);
        return res > 0;
    }

    @Override
    @Transactional(rollbackFor = Exception.class,propagation = Propagation.REQUIRED)
    //@ShardingSphereTransactionType(TransactionType.XA)
    protected boolean deleteDb(ParmModificationRecord parmModificationRecord) {
        ParmTransactionCode parmTransactionCode = JSON.parseObject(parmModificationRecord.getParmBody(), ParmTransactionCode.class);
        int deleteRow = transactionCodeMapper.deleteByPrimaryKey(parmTransactionCode.getId());
        return deleteRow > 0;
    }
}
