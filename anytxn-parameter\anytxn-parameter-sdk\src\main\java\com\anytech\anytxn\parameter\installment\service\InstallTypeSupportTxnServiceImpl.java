package com.anytech.anytxn.parameter.installment.service;

import com.anytech.anytxn.common.core.utils.BeanMapping;
import com.anytech.anytxn.parameter.base.installment.domain.dto.InstallTypeSupportTxnResDTO;
import com.anytech.anytxn.parameter.base.installment.service.IInstallTypeSupportTxnService;
import com.anytech.anytxn.parameter.base.common.enums.AnyTxnParameterRespCodeEnum;
import com.anytech.anytxn.parameter.base.common.exception.AnyTxnParameterException;
import com.anytech.anytxn.parameter.installment.mapper.InstallTypeSupportTxnMapper;
import com.anytech.anytxn.parameter.installment.mapper.InstallTypeSupportTxnSelfMapper;
import com.anytech.anytxn.parameter.base.installment.domain.model.InstallTypeSupportTxn;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import java.util.Collections;
import java.util.List;

/**
 * @description: 分期类型交易表 业务逻辑实现
 * @author: heqi
 * @create: 2019-08-12
 */
@Service
public class InstallTypeSupportTxnServiceImpl implements IInstallTypeSupportTxnService {

    private Logger logger = LoggerFactory.getLogger(InstallTypeSupportTxnServiceImpl.class);

    @Autowired
    private InstallTypeSupportTxnMapper installTypeSupportTxnMapper;
    @Autowired
    private InstallTypeSupportTxnSelfMapper installTypeSupportTxnSelfMapper;

    /**
     * 根据分期类型、机构号查询分期交易类型表
     * @param  type 分期类型
     * @param  organizationNumber 机构号
     * @return List<InstallTypeSupportTxnResDTO>
     * */
    @Override
    public List<InstallTypeSupportTxnResDTO> getByTypeAndOrgNum(String type, String organizationNumber) {
        //必输项检查
        if (null == type || "".equals(type)) {

            throw new AnyTxnParameterException(AnyTxnParameterRespCodeEnum.D_INSTALL_TYPE_PARM_TYPE_FAULT);
        }
        if (null == organizationNumber || "".equals(organizationNumber)) {

            throw new AnyTxnParameterException(AnyTxnParameterRespCodeEnum.D_INSTALL_ACCOUNTING_TRANS_PARM_ORG_FAULT);
        }
        List<InstallTypeSupportTxn> installTypeSupportTxns = installTypeSupportTxnSelfMapper.selectByTypeAndOrgNum(type, organizationNumber);
        if(CollectionUtils.isEmpty(installTypeSupportTxns)){
            logger.info("查询分期类型交易参数信息，通过organizationNumber:{},type:{}未查到数据",organizationNumber,type);
            return Collections.emptyList();
        }
        return BeanMapping.copyList(installTypeSupportTxns, InstallTypeSupportTxnResDTO.class);
    }

    /**
     * 通过id主键删除分期交易类型参数
     *
     * @param id 主键
     * @return Boolean
     * @throws AnyTxnParameterException
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean removeInstallTypeSupportTxn(String id) throws AnyTxnParameterException {
        //参数校验
        if (id == null || id.equals("0")) {
            logger.warn("根据id删除分期交易类型参数，参数为空");

            throw new AnyTxnParameterException(AnyTxnParameterRespCodeEnum.P_PARAMETER_EMPTY_FAULT);
        }
        InstallTypeSupportTxn installTypeSupportTxn = installTypeSupportTxnMapper.selectByPrimaryKey(id);
        if(installTypeSupportTxn ==null){
            logger.warn("根据id删除分期交易类型参数,要删除的数据不存在,id{}", id);

            throw new AnyTxnParameterException(AnyTxnParameterRespCodeEnum.D_QUERY_TYPE_PARM_TYPE_BY_ID_FAULT);
        }
        try {
            return installTypeSupportTxnMapper.deleteByPrimaryKey(id) > 0;
        } catch (Exception e) {
            logger.warn("根据id删除分期交易类型参数 删除数据库失败,id{}", id);

            throw new AnyTxnParameterException(AnyTxnParameterRespCodeEnum.D_DELETE_TYPE_PARM_TYPE_BY_ID_FAULT);
        }
    }
    @Override
    public InstallTypeSupportTxnResDTO getByOrgTypeAndTransCode(String organizationNumber, String type, String transactionCode) {
        InstallTypeSupportTxn installTypeSupportTxn = installTypeSupportTxnSelfMapper.selectByOrgTypeAndTransCode(organizationNumber, type, transactionCode);
        if(null ==installTypeSupportTxn){
            return null;
        }
        return BeanMapping.copy(installTypeSupportTxn,InstallTypeSupportTxnResDTO.class);
    }
}
