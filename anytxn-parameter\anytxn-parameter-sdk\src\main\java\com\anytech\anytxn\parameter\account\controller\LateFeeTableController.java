package com.anytech.anytxn.parameter.account.controller;

import io.swagger.v3.oas.annotations.tags.Tag;
import io.swagger.v3.oas.annotations.Operation;
import com.anytech.anytxn.common.core.base.ParameterCompare;
import com.anytech.anytxn.common.core.base.PageResultDTO;
import com.anytech.anytxn.common.core.base.AnyTxnHttpResponse;
import com.anytech.anytxn.common.core.base.BizBaseController;
import com.anytech.anytxn.parameter.base.account.domain.dto.LateFeeTableReqDTO;
import com.anytech.anytxn.parameter.base.account.domain.dto.LateFeeTableResDTO;
import com.anytech.anytxn.parameter.base.account.domain.dto.LateFeeTableSearchDTO;
import com.anytech.anytxn.parameter.base.account.service.ILateFeeTableService;
import com.anytech.anytxn.parameter.base.common.enums.ParameterRepDetailEnum;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;


/**
 * 违约金参数参数
 * <AUTHOR>
 * @date 2020/3/26
 */
@Tag(name = "违约金参数")
@RestController
public class LateFeeTableController extends BizBaseController {

    @Autowired
    private ILateFeeTableService lateFeeTableService;

    /**
     * 新增违约金参数
     * @param feeTableReq
     * @return FeeTableRes
     *
     */
    @Operation(summary = "新增违约金参数",description = "新增违约金参数")
    @PostMapping(value = "/param/lateFee")
    public AnyTxnHttpResponse<Object> create(@RequestBody LateFeeTableReqDTO feeTableReq) {
        ParameterCompare res = lateFeeTableService.addParmFee(feeTableReq);
        return AnyTxnHttpResponse.success(res, ParameterRepDetailEnum.CREATE.message());
    }

    /**
     * 查询违约金参数信息
     * @return FeeTableRes
     *
     */
    @Operation(summary = "违约金参数分页查询",description = "分页查询")
    @GetMapping(value = "/param/lateFee/pageNum/{pageNum}/pageSize/{pageSize}")
    public AnyTxnHttpResponse<PageResultDTO<LateFeeTableResDTO>> getAllPage(@PathVariable(value = "pageNum") Integer pageNum,
                                                                            @PathVariable(value = "pageSize") Integer pageSize,
                                                                            LateFeeTableSearchDTO lateFeeTableSearchDTO) {
        PageResultDTO<LateFeeTableResDTO> lateFeePage = lateFeeTableService.findAll(pageNum,pageSize,lateFeeTableSearchDTO);
        return AnyTxnHttpResponse.success(lateFeePage);

    }


    /**
     * 修改违约金参数信息
     * @param feeTableReq
     * @return FeeTableRes
     *
     */
    @Operation(summary = "违约金参数信息修改", description = "只更新需要更新的,其他不变")
    @PutMapping(value = "/param/lateFee")
    public AnyTxnHttpResponse<Object> modify(@RequestBody LateFeeTableReqDTO feeTableReq) {
        ParameterCompare res = lateFeeTableService.modifyParmFee(feeTableReq);
        return AnyTxnHttpResponse.success(res,ParameterRepDetailEnum.UPDATE.message());
    }

    /**
     * 通过id删除违约金参数信息
     * @param id
     * @return Boolean
     *
     */
    @Operation(summary = "删除违约金参数信息", description = "通过id删除违约金参数信息")
    @DeleteMapping(value = "/param/lateFee/id/{id}")
    public AnyTxnHttpResponse<Object> remove(@PathVariable String id) {
        ParameterCompare flag = lateFeeTableService.removeParmFee(id);
        return AnyTxnHttpResponse.success(flag,ParameterRepDetailEnum.DEL.message());
    }

    /**
     * 通过id查询违约金参数信息
     * @param id
     * @return HttpApiResponse<FeeTableRes>
     *
     */
    @Operation(summary = "通过id查询违约金参数信息", description = "通过id查询违约金参数信息")
    @GetMapping(value = "/param/lateFee/id/{id}")
    public AnyTxnHttpResponse<LateFeeTableResDTO> getById(@PathVariable String id) {
        LateFeeTableResDTO res = lateFeeTableService.findById(id);
        return AnyTxnHttpResponse.success(res);

    }

    /**
     * 通过机构号,tableId查询违约金参数信息
     * @param organizationNumber 机构号
     * @param tableId 表Id
     * @return
     *
     */
    @Operation(summary = "通过机构号,tableId查询违约金参数信息", description = "通过tableId查询违约金参数信息")
    @GetMapping(value = "/param/lateFee/organizationNumber/{organizationNumber}/tableId/{tableId}")
    public AnyTxnHttpResponse<LateFeeTableResDTO> getFeeTable(@PathVariable String organizationNumber, @PathVariable String tableId) {
        LateFeeTableResDTO res = lateFeeTableService.findByOrgAndTableId(organizationNumber,tableId);
        return AnyTxnHttpResponse.success(res);

    }

}
