package com.anytech.anytxn.parameter.account.service;

import com.alibaba.fastjson.JSON;
import com.github.pagehelper.Page;
import com.github.pagehelper.PageHelper;
import com.anytech.anytxn.common.core.annotation.db.DeleteParameterAnnotation;
import com.anytech.anytxn.common.core.annotation.db.InsertParameterAnnotation;
import com.anytech.anytxn.common.core.annotation.db.UpdateParameterAnnotation;
import com.anytech.anytxn.business.base.audit.AbstractParameterService;
import com.anytech.anytxn.common.core.base.ParameterCompare;
import com.anytech.anytxn.business.base.audit.ParmModificationRecord;
import com.anytech.anytxn.common.core.base.PageResultDTO;
import com.anytech.anytxn.common.core.utils.BeanMapping;
import com.anytech.anytxn.common.core.utils.OrgNumberUtils;
import com.anytech.anytxn.common.sequence.utils.Number16IdGen;
import com.anytech.anytxn.common.core.utils.TenantUtils;
import com.anytech.anytxn.parameter.base.account.domain.dto.InterestBearingDTO;
import com.anytech.anytxn.parameter.base.account.service.IInterestBearingService;
import com.anytech.anytxn.parameter.base.common.enums.AnyTxnParameterRespCodeEnum;
import com.anytech.anytxn.parameter.base.common.exception.AnyTxnParameterException;
import com.anytech.anytxn.parameter.account.mapper.InterestBearingMapper;
import com.anytech.anytxn.parameter.account.mapper.InterestBearingSelfMapper;
import com.anytech.anytxn.parameter.base.account.domain.model.InterestBearing;
//import org.apache.shardingsphere.transaction.annotation.ShardingSphereTransactionType;
//import org.apache.shardingsphere.transaction.core.TransactionType;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.ObjectUtils;
import org.springframework.util.StringUtils;

import jakarta.annotation.Resource;
import java.util.List;

/**
 * Description:计息参数
 * date: 2021/5/12 11:28
 *
 * <AUTHOR>
 */
@Service("parm_interest_bearing_serviceImpl")
public class InterestBearingServiceImpl extends AbstractParameterService implements IInterestBearingService {
    @Resource
    private Number16IdGen numberIdGenerator;

    @Resource
    private InterestBearingMapper interestBearingMapper;
    @Resource
    private InterestBearingSelfMapper interestBearingSelfMapper;


    @Override
    @Transactional(rollbackFor = Exception.class,propagation = Propagation.REQUIRED)
    //@ShardingSphereTransactionType(TransactionType.XA)
    protected boolean updateDb(ParmModificationRecord parmModificationRecord) throws Exception {
        InterestBearing interestBearing = JSON.parseObject(parmModificationRecord.getParmBody(), InterestBearing.class);
        interestBearing.initUpdateDateTime();
        int res = interestBearingMapper.updateByPrimaryKeySelective(interestBearing);
        return res == 1;
    }

    @Override
    @Transactional(rollbackFor = Exception.class,propagation = Propagation.REQUIRED)
    //@ShardingSphereTransactionType(TransactionType.XA)
    protected boolean insertDb(ParmModificationRecord parmModificationRecord) throws Exception {
        InterestBearing interestBearing = JSON.parseObject(parmModificationRecord.getParmBody(), InterestBearing.class);
        interestBearing.initUpdateDateTime();
        interestBearing.initCreateDateTime();
        int res = interestBearingMapper.insertSelective(interestBearing);
        return res == 1;
    }

    @Override
    @Transactional(rollbackFor = Exception.class,propagation = Propagation.REQUIRED)
    //@ShardingSphereTransactionType(TransactionType.XA)
    protected boolean deleteDb(ParmModificationRecord parmModificationRecord) throws Exception {
        InterestBearing interestBearing = JSON.parseObject(parmModificationRecord.getParmBody(), InterestBearing.class);
        int res = interestBearingMapper.deleteByPrimaryKey(interestBearing.getId());
        return res == 1;
    }

    @Override
    @InsertParameterAnnotation(tableName = "parm_interest_bearing",tableDesc = "Interest calculation parameters")
    public ParameterCompare add(InterestBearingDTO dto) {
        if (ObjectUtils.isEmpty(dto)){
            throw new AnyTxnParameterException(AnyTxnParameterRespCodeEnum.P_PARAMETER_EMPTY_FAULT);
        }
        if (StringUtils.isEmpty(dto.getTableId())){
            throw new AnyTxnParameterException(AnyTxnParameterRespCodeEnum.P_PARAMETER_TABLE_ID_EMPTY_FAULT);
        }
        String organizationNumber = OrgNumberUtils.getOrg(dto.getOrganizationNumber());
        InterestBearing interestBearing = interestBearingMapper.selectByTableIdAndOrg(dto.getTableId(), organizationNumber);
        if (!ObjectUtils.isEmpty(interestBearing)){
            throw new AnyTxnParameterException(AnyTxnParameterRespCodeEnum.D_EXIST_PARM_PAYMENT_ALLOCATED_BY_ID_FAULT);
        }
        InterestBearing after = BeanMapping.copy(dto, InterestBearing.class);
        after.setId(String.valueOf(numberIdGenerator.generateId(TenantUtils.getTenantId())));
        after.setOrganizationNumber(organizationNumber);
        return ParameterCompare
                .getBuilder()
                .withAfter(after)
                .build(InterestBearing.class);
    }

    @Override
    @UpdateParameterAnnotation(tableName = "parm_interest_bearing",tableDesc = "Interest calculation parameters")
    public ParameterCompare modify(InterestBearingDTO dto) {
        if (ObjectUtils.isEmpty(dto)){
            throw new AnyTxnParameterException(AnyTxnParameterRespCodeEnum.P_PARAMETER_EMPTY_FAULT);
        }
        InterestBearing before = interestBearingMapper.selectByPrimaryKey(dto.getId());
        if (ObjectUtils.isEmpty(before)){
            throw new AnyTxnParameterException(AnyTxnParameterRespCodeEnum.P_PARAMETER_DATA_NOT_EXIST);
        }
        InterestBearing after = BeanMapping.copy(dto, InterestBearing.class);
        return ParameterCompare
                .getBuilder()
                .withAfter(after)
                .withBefore(before)
                .build(InterestBearing.class);
    }

    @Override
    @DeleteParameterAnnotation(tableName = "parm_interest_bearing",tableDesc = "Interest calculation parameters")
    public ParameterCompare remove(String id) {
        if (StringUtils.isEmpty(id)){
            throw new AnyTxnParameterException(AnyTxnParameterRespCodeEnum.P_PARAMETER_EMPTY_FAULT);
        }
        InterestBearing before = interestBearingMapper.selectByPrimaryKey(id);
        if (ObjectUtils.isEmpty(before)){
            throw new AnyTxnParameterException(AnyTxnParameterRespCodeEnum.P_PARAMETER_DATA_NOT_EXIST);
        }
        return ParameterCompare
                .getBuilder()
                .withBefore(before)
                .build(InterestBearing.class);
    }

    @Override
    public InterestBearingDTO findById(String id) {
        if (StringUtils.isEmpty(id)){
            throw new AnyTxnParameterException(AnyTxnParameterRespCodeEnum.P_PARAMETER_EMPTY_FAULT);
        }
        InterestBearing interestBearing = interestBearingMapper.selectByPrimaryKey(id);
        return BeanMapping.copy(interestBearing,InterestBearingDTO.class);
    }

    @Override
    public PageResultDTO<InterestBearingDTO> findPage(Integer pageNum, Integer pageSize, String organizationNumber, String tableId, String description) {
        Page<InterestBearing> page = PageHelper.startPage(pageNum,pageSize);
        List<InterestBearing> interestBearings = interestBearingMapper.selectByConditionAndPage(tableId, description, OrgNumberUtils.getOrg(organizationNumber));
        List<InterestBearingDTO> res = BeanMapping.copyList(interestBearings, InterestBearingDTO.class);
        return new PageResultDTO<>(pageNum,pageSize,page.getTotal(), page.getPages(),res);
    }

    @Override
    public InterestBearingDTO findByOrgAndTableId(String organizationNumber, String tableId) {
        InterestBearing interestBearing = interestBearingSelfMapper.selectByOrgAndTableId(organizationNumber, tableId);
        return null == interestBearing ? null : BeanMapping.copy(interestBearing,InterestBearingDTO.class);
    }
}
