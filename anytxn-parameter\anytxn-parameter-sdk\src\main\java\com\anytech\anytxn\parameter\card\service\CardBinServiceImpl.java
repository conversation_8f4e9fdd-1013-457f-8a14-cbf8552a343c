package com.anytech.anytxn.parameter.card.service;

import com.github.pagehelper.Page;
import com.github.pagehelper.PageHelper;
import com.anytech.anytxn.common.core.utils.OrgNumberUtils;
import com.anytech.anytxn.parameter.base.common.constants.Constants;
import com.anytech.anytxn.common.core.base.PageResultDTO;
import com.anytech.anytxn.common.core.utils.BeanMapping;
import com.anytech.anytxn.common.sequence.utils.Number16IdGen;
import com.anytech.anytxn.common.core.utils.TenantUtils;
import com.anytech.anytxn.parameter.base.card.domain.dto.CardBinControlResDTO;
import com.anytech.anytxn.parameter.base.card.domain.dto.CardBinReqDTO;
import com.anytech.anytxn.parameter.base.card.domain.dto.CardBinResDTO;
import com.anytech.anytxn.parameter.base.card.service.ICardBinService;
import com.anytech.anytxn.parameter.base.common.enums.AnyTxnParameterRespCodeEnum;
import com.anytech.anytxn.parameter.base.common.exception.AnyTxnParameterException;
import com.anytech.anytxn.parameter.card.mapper.broadcast.ParmCardBinControlSelfMapper;
import com.anytech.anytxn.parameter.card.mapper.broadcast.ParmCardBinMapper;
import com.anytech.anytxn.parameter.card.mapper.broadcast.ParmCardBinSelfMapper;
import com.anytech.anytxn.parameter.base.card.domain.model.ParmCardBin;
import com.anytech.anytxn.parameter.base.card.domain.model.ParmCardBinControl;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.util.List;

/**
 * 卡bin 业务实现
 *
 * <AUTHOR>
 * @date 2018-08-15 15:41
 **/
@Service
public class CardBinServiceImpl implements ICardBinService {

    private Logger logger = LoggerFactory.getLogger(CardBinServiceImpl.class);

    @Autowired
    private ParmCardBinMapper parmCardBinMapper;
    @Autowired
    private ParmCardBinSelfMapper parmCardBinSelfMapper;
    @Autowired
    private ParmCardBinControlSelfMapper parmCardBinControlSelfMapper;

    @Autowired
    private Number16IdGen numberIdGenerator;

    /**
     * 新增记录
     *
     * @param cardBinReq 卡bin请求对象
     * @return CardBinRes
     * @throws AnyTxnParameterException
     */
    @Override
    public CardBinResDTO add(CardBinReqDTO cardBinReq)  {
        boolean isExists = parmCardBinSelfMapper.isExists(cardBinReq.getProductId(), cardBinReq.getProductBin(), cardBinReq.getBinSequence(), OrgNumberUtils.getOrg())>0;
        if(isExists) {
            logger.warn("卡bin已存在, ProductId={}, ProductBin={}, BinSequence={} ",
                    cardBinReq.getProductId(), cardBinReq.getProductBin(), cardBinReq.getBinSequence());
            throw new AnyTxnParameterException(AnyTxnParameterRespCodeEnum.S_EXIST_CARD_BIN_FAULT);
        }

        //构建卡bin
        ParmCardBin parmCardBin = BeanMapping.copy(cardBinReq, ParmCardBin.class);
        parmCardBin.setCreateTime(LocalDateTime.now());
        parmCardBin.setVersionNumber(1000000L);
        parmCardBin.setUpdateTime(LocalDateTime.now());
        parmCardBin.setUpdateBy(Constants.DEFAULT_USER);
        parmCardBin.setId(numberIdGenerator.generateId(TenantUtils.getTenantId()));
        parmCardBinMapper.insertSelective(parmCardBin);

        return BeanMapping.copy(parmCardBin, CardBinResDTO.class);
    }

    /**
     * 通过id删除条目
     *
     * @param id 技术id
     * @return true:删除成功|false:删除失败
     * @throws AnyTxnParameterException
     */
    @Override
    public Boolean remove(Long id) {
        ParmCardBin parmCardBin = parmCardBinMapper.selectByPrimaryKey(id);

        if (parmCardBin == null) {
            logger.error("删除卡bin数据, 通过主键id({})未找到数据", id);
            throw new AnyTxnParameterException(AnyTxnParameterRespCodeEnum.D_PARM_CARD_BIN_BY_ID_FAULT);

        }

        logger.warn("通过id删除卡bin数据, {}", parmCardBin);
        int deleteRows = parmCardBinMapper.deleteByPrimaryKey(id);
        return deleteRows > 0;
    }

    /**
     * 通过id获取详情
     *
     * @param id 主键id
     * @return CardBinRes
     * @throws AnyTxnParameterException
     */
    @Override
    public CardBinResDTO find(Long id)  {
        if (id == null) {
            throw new AnyTxnParameterException(AnyTxnParameterRespCodeEnum.P_PARAMETER_EMPTY_FAULT);
        }

        ParmCardBin parmCardBin = parmCardBinMapper.selectByPrimaryKey(id);

        if (parmCardBin == null) {
            logger.error("查询卡bin数据, 通过主键id({})未找到数据", id);
            throw new AnyTxnParameterException(AnyTxnParameterRespCodeEnum.D_PARM_CARD_BIN_BY_ID_FAULT);
        }

        return BeanMapping.copy(parmCardBin, CardBinResDTO.class);
    }

    /**
     * 修改
     *
     * @param cardBinReq 卡bin入参对象
     * @return CardBinRes
     * @throws AnyTxnParameterException
     */
    @Override
    public CardBinResDTO modify(CardBinReqDTO cardBinReq)  {
        if (cardBinReq.getId() == null) {
            throw new AnyTxnParameterException(AnyTxnParameterRespCodeEnum.P_PARAMETER_ID_EMPTY_FAULT);
        }
        ParmCardBin parmCardBin = parmCardBinMapper.selectByPrimaryKey(cardBinReq.getId());

        if (parmCardBin == null) {
            logger.error("修改卡bin, 通过主键id({})未找到数据", cardBinReq.getId());
            throw new AnyTxnParameterException(AnyTxnParameterRespCodeEnum.D_PARM_CARD_BIN_BY_ID_FAULT);
        }

        // 拷贝修改的数据并更新
        BeanMapping.copy(cardBinReq, parmCardBin);
        parmCardBin.setUpdateTime(LocalDateTime.now());
        parmCardBin.setUpdateBy(Constants.DEFAULT_USER);

        parmCardBinMapper.updateByPrimaryKeySelective(parmCardBin);

        //历史表中添加记录

        return BeanMapping.copy(parmCardBin, CardBinResDTO.class);
    }

    /**
     * 分页查询卡bin信息
     *
     * @param pageNum  页号
     * @param pageSize 每页大小
     * @return 卡bin详情
     * @throws AnyTxnParameterException
     */
    @Override
    public PageResultDTO<CardBinResDTO> findPage(Integer pageNum, Integer pageSize)  {

        logger.debug("分页查询卡bin信息, pageNum={}, pageSize={}",pageNum, pageSize);
        Page<ParmCardBin> page = PageHelper.startPage(pageNum, pageSize);
        List<ParmCardBin> parmCardBinList = parmCardBinSelfMapper.selectAll(false, OrgNumberUtils.getOrg());
        if (parmCardBinList.isEmpty()) {
            logger.error("未查询到信息");
            throw new AnyTxnParameterException(AnyTxnParameterRespCodeEnum.D_QUERY_PARM_CARD_BIN_BY_ID_FAULT);
        }
        List<CardBinResDTO> cardBinResList = BeanMapping.copyList(parmCardBinList, CardBinResDTO.class);
        return new PageResultDTO<>(pageNum, pageSize, page.getTotal(), page.getPages(), cardBinResList);
    }

    /**
     * 根据机构号和tableId查询
     *
     * @param orgNum  机构号
     * @param tableId 表Id
     * @return 卡bin控制列表
     * @throws AnyTxnParameterException
     */
    @Override
    public List<CardBinControlResDTO> findByOrgAndTableId(String orgNum, String tableId)  {
        List<ParmCardBinControl> parmCardBinControlList = parmCardBinControlSelfMapper.selectAll(orgNum, tableId);
        if (parmCardBinControlList.isEmpty()) {
            throw new AnyTxnParameterException(AnyTxnParameterRespCodeEnum.D_QUERY_PARM_CARD_BIN_CONTROL_LIST_FAULT);
        }
        return BeanMapping.copyList(parmCardBinControlList, CardBinControlResDTO.class);
    }

    /**
     * 根据机构号和tableId查询
     *
     * @param orgNum      机构号
     * @param tableId     表Id
     * @param binSequence 卡bin顺序号
     * @return 卡bin控制详情
     * @throws AnyTxnParameterException
     */
    @Override
    public CardBinControlResDTO findByOrgAndTableIdAndSequence(String orgNum, String tableId, String binSequence) {
        ParmCardBinControl parmCardBinControl = parmCardBinControlSelfMapper.isExists(orgNum, tableId, binSequence);
        if (parmCardBinControl == null) {
            throw new AnyTxnParameterException(AnyTxnParameterRespCodeEnum.D_QUERY_PARM_CARD_BIN_CONTROL_FAULT);
        }
        return BeanMapping.copy(parmCardBinControl, CardBinControlResDTO.class);
    }
}
