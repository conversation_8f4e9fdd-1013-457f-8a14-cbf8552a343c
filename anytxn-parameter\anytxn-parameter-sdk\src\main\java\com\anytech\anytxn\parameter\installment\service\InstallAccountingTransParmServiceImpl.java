package com.anytech.anytxn.parameter.installment.service;

import com.alibaba.fastjson.JSON;
import com.github.pagehelper.Page;
import com.github.pagehelper.PageHelper;
import com.anytech.anytxn.common.core.annotation.db.DeleteParameterAnnotation;
import com.anytech.anytxn.common.core.annotation.db.InsertParameterAnnotation;
import com.anytech.anytxn.common.core.annotation.db.UpdateParameterAnnotation;
import com.anytech.anytxn.business.base.audit.AbstractParameterService;
import com.anytech.anytxn.common.core.base.ParameterCompare;
import com.anytech.anytxn.business.base.audit.ParmModificationRecord;
import com.anytech.anytxn.common.core.utils.OrgNumberUtils;
import com.anytech.anytxn.common.core.base.PageResultDTO;
import com.anytech.anytxn.common.core.utils.BeanMapping;
import com.anytech.anytxn.common.sequence.utils.Number16IdGen;
import com.anytech.anytxn.common.core.utils.TenantUtils;
import com.anytech.anytxn.parameter.base.common.constants.ParamConstant;
import com.anytech.anytxn.parameter.base.common.enums.AnyTxnParameterRespCodeEnum;
import com.anytech.anytxn.parameter.base.common.exception.AnyTxnParameterException;
import com.anytech.anytxn.parameter.base.installment.domain.dto.InstallAccountingTransParmReqDTO;
import com.anytech.anytxn.parameter.base.installment.domain.dto.InstallAccountingTransParmResDTO;
import com.anytech.anytxn.parameter.base.installment.service.IInstallAccountingTransParmService;
import com.anytech.anytxn.parameter.common.mapper.broadcast.system.ParmTransactionCodeSelfMapper;
import com.anytech.anytxn.parameter.installment.mapper.InstallAccountingTransParmMapper;
import com.anytech.anytxn.parameter.installment.mapper.InstallAccountingTransParmSelfMapper;
import com.anytech.anytxn.parameter.base.common.domain.model.system.ParmAccountProductInfo;
import com.anytech.anytxn.parameter.base.installment.domain.model.InstallAccountingTransParm;
import org.apache.commons.lang3.StringUtils;
//import org.apache.shardingsphere.transaction.annotation.ShardingSphereTransactionType;
//import org.apache.shardingsphere.transaction.core.TransactionType;
import org.assertj.core.util.Lists;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

/***
 * @Author: zy
 * @Description: 分期入账交易参数业务层
 * @Date: 2019/5/13 17:32
 **/
@Service(value = "parm_install_accounting_trans_serviceImpl")
public class InstallAccountingTransParmServiceImpl extends AbstractParameterService implements IInstallAccountingTransParmService {

    private Logger logger = LoggerFactory.getLogger(InstallAccountingTransParmServiceImpl.class);

    @Autowired
    private InstallAccountingTransParmMapper installAccountingTransParmMapper;
    @Autowired
    private InstallAccountingTransParmSelfMapper installAccountingTransParmSelfMapper;
    @Autowired
    private ParmTransactionCodeSelfMapper transactionCodeSelfMapper;

    @Autowired
    private Number16IdGen numberIdGenerator;

    /**
     * 新建分期入账交易参数表
     *
     * @param installAccountingTransParmReqDTO 分期入账交易参数
     * @return HttpApiResponse<InstallAccountingTransParmResDTO>
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    @InsertParameterAnnotation(tableName = "parm_install_accounting_trans", tableDesc = "Installment Posting")
    public ParameterCompare add(InstallAccountingTransParmReqDTO installAccountingTransParmReqDTO) {
        //判断参数是否为空或空字符串
        if (null != installAccountingTransParmReqDTO) {
            //检查必输字段是否为空
            checkRequiredInputs(installAccountingTransParmReqDTO);
            //判断是否重复
            int flag = installAccountingTransParmSelfMapper.isExists(installAccountingTransParmReqDTO.getOrganizationNumber(), installAccountingTransParmReqDTO.getTableId());
            if (flag > 0) {
                logger.warn("分期入账交易参数已存在，organizationNumber={},tableId={}",
                        installAccountingTransParmReqDTO.getOrganizationNumber(),
                        installAccountingTransParmReqDTO.getTableId());

                throw new AnyTxnParameterException(AnyTxnParameterRespCodeEnum.D_EXIST_INSTALL_ACCOUNTING_TRANS_PARM_FAULT);
            }
            //installAccountingTransParmReqDTO.getCreateTransactionCode(),            //贷记交易码  暂时注掉  后期优化
            //判断传过来的交易码是否存在
            List<String> codes = transactionCodeSelfMapper.isExistsTranCode(Lists.newArrayList(
                    installAccountingTransParmReqDTO.getInstallTransactionCode(),           //码分期整笔退货交易码
                    installAccountingTransParmReqDTO.getPrincipalTransactionCode(),         //分期交易码
                    installAccountingTransParmReqDTO.getPrincipalReversalTransCode(),       //分期撤销交易码
                    installAccountingTransParmReqDTO.getFeeTransactionCode(),               //费用入账交易码
                    installAccountingTransParmReqDTO.getFeeReversalTransactionCode(),       //费用入账撤销交易码
                    installAccountingTransParmReqDTO.getPenatlyTransactionCode(),           //违约金入账交易码
                    installAccountingTransParmReqDTO.getFeeAmortizeTransactionCode(),       //费用摊销交易码
                    installAccountingTransParmReqDTO.getInstallReturnCodeRev()              //分期整笔交易
            ), installAccountingTransParmReqDTO.getOrganizationNumber());
            //校验交易码
            checkTranCode(codes, installAccountingTransParmReqDTO);

            InstallAccountingTransParm transParm = BeanMapping.copy(installAccountingTransParmReqDTO, InstallAccountingTransParm.class);
            transParm.setId(String.valueOf(numberIdGenerator.generateId(TenantUtils.getTenantId())));

            return ParameterCompare.getBuilder().withAfter(transParm).build(InstallAccountingTransParm.class);


        } else {
            logger.warn("分期入账交易参数为空 installAccountingTransParmReqDTO={}", installAccountingTransParmReqDTO);

            throw new AnyTxnParameterException(AnyTxnParameterRespCodeEnum.P_PARAMETER_EMPTY_FAULT);
        }
    }

    /**
     * 修改分期入账交易参数表
     *
     * @param installAccountingTransParmReqDTO 分期入账交易参数
     * @return HttpApiResponse<InstallAccountingTransParmResDTO>
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    @UpdateParameterAnnotation(tableName = "parm_install_accounting_trans", tableDesc = "Installment Posting")
    public ParameterCompare modify(InstallAccountingTransParmReqDTO installAccountingTransParmReqDTO) {
        if (installAccountingTransParmReqDTO.getId() == null) {

            throw new AnyTxnParameterException(AnyTxnParameterRespCodeEnum.P_PARAMETER_ID_EMPTY_FAULT);
        }
        //检查必输字段是否为空
        checkRequiredInputs(installAccountingTransParmReqDTO);
        InstallAccountingTransParm installAccountingTransParm = installAccountingTransParmMapper.selectByPrimaryKey(installAccountingTransParmReqDTO.getId());

        if (installAccountingTransParm == null) {
            logger.error("修改分期入账交易参数表，通过id:{}未查到数据", installAccountingTransParmReqDTO.getId());

            throw new AnyTxnParameterException(AnyTxnParameterRespCodeEnum.D_QUERY_INSTALL_ACCOUNTING_TRANS_PARM_BY_ID_FAULT);
        }
        InstallAccountingTransParm copy = BeanMapping.copy(installAccountingTransParmReqDTO, InstallAccountingTransParm.class);


        InstallAccountingTransParm accountingTransParm = installAccountingTransParmSelfMapper.selectByIndex(
                installAccountingTransParm.getOrganizationNumber(), installAccountingTransParm.getTableId());

        if (accountingTransParm != null && !accountingTransParm.getId().equals(installAccountingTransParm.getId())) {
            logger.warn("分期入账交易参数已存在，organizationNumber={},tableId={}",
                    installAccountingTransParm.getOrganizationNumber(), installAccountingTransParm.getTableId());

            throw new AnyTxnParameterException(AnyTxnParameterRespCodeEnum.D_EXIST_INSTALL_ACCOUNTING_TRANS_PARM_FAULT);
        }
        //installAccountingTransParmReqDTO.getCreateTransactionCode(),            //贷记交易码
        //判断传过来的交易码是否存在
        List<String> codes = transactionCodeSelfMapper.isExistsTranCode(Lists.newArrayList(
                installAccountingTransParmReqDTO.getInstallTransactionCode(),           //分期整笔交易码
                installAccountingTransParmReqDTO.getPrincipalTransactionCode(),         //分期交易码
                installAccountingTransParmReqDTO.getPrincipalReversalTransCode(),       //分期撤销交易码
                installAccountingTransParmReqDTO.getFeeTransactionCode(),               //费用入账交易码
                installAccountingTransParmReqDTO.getFeeReversalTransactionCode(),       //费用入账撤销交易码
                installAccountingTransParmReqDTO.getPenatlyTransactionCode(),           //违约金入账交易码
                installAccountingTransParmReqDTO.getFeeAmortizeTransactionCode(),       //费用摊销交易码
                installAccountingTransParmReqDTO.getInstallReturnCodeRev()              //分期整笔退货交易码
        ), installAccountingTransParmReqDTO.getOrganizationNumber());
        //校验交易码
        checkTranCode(codes, installAccountingTransParmReqDTO);
        InstallAccountingTransParmReqDTO copy1 = BeanMapping.copy(installAccountingTransParm, InstallAccountingTransParmReqDTO.class);
        return ParameterCompare
                .getBuilder()
                .withAfter(installAccountingTransParmReqDTO)
                .withBefore(copy1)
                .build(InstallAccountingTransParmReqDTO.class);

    }

    /**
     * 删除分期入账交易参数表
     *
     * @param id 分期入账交易参数
     * @return
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    @DeleteParameterAnnotation(tableName = "parm_install_accounting_trans", tableDesc = "Installment Posting")
    public ParameterCompare remove(String id) {
        if (null != id) {
            InstallAccountingTransParm installAccountingTransParm = installAccountingTransParmMapper.selectByPrimaryKey(id);

            if (installAccountingTransParm == null) {
                logger.error("删除分期入账交易参数表，通过id:{}未查到数据", id);

                throw new AnyTxnParameterException(AnyTxnParameterRespCodeEnum.D_QUERY_INSTALL_ACCOUNTING_TRANS_PARM_BY_ID_FAULT);
            }
            return ParameterCompare.getBuilder().withBefore(installAccountingTransParm).build(InstallAccountingTransParm.class);

        } else {
            logger.warn("id不存在，id={}", id);

            throw new AnyTxnParameterException(AnyTxnParameterRespCodeEnum.P_PARAMETER_ID_EMPTY_FAULT);
        }
    }

    /**
     * 根据id 查询分期入账交易参数表
     *
     * @param id
     * @return HttpApiResponse<InstallAccountingTransParmResDTO>
     */
    @Override
    public InstallAccountingTransParmResDTO selectByPrimaryKey(String id) {
        if (null == id) {

            throw new AnyTxnParameterException(AnyTxnParameterRespCodeEnum.P_PRIMARY_KEY_NULL_FAULT);
        }
        InstallAccountingTransParm installAccountingTransParm = installAccountingTransParmMapper.selectByPrimaryKey(id);
        if (installAccountingTransParm == null) {
            logger.error("分期入账交易参数信息，通过id:{}未查到数据", id);

            throw new AnyTxnParameterException(AnyTxnParameterRespCodeEnum.D_QUERY_INSTALL_ACCOUNTING_TRANS_PARM_BY_ID_FAULT);
        }
        return BeanMapping.copy(installAccountingTransParm, InstallAccountingTransParmResDTO.class);
    }

    @Override
    public PageResultDTO<InstallAccountingTransParmResDTO> findPage(Integer pageNum, Integer pageSize, InstallAccountingTransParmReqDTO installAccountingTransParmReqDTO) {
        if(null == installAccountingTransParmReqDTO){
            installAccountingTransParmReqDTO = new InstallAccountingTransParmReqDTO();
        }
        installAccountingTransParmReqDTO.setOrganizationNumber(StringUtils.isEmpty(installAccountingTransParmReqDTO.getOrganizationNumber()) ? OrgNumberUtils.getOrg() : installAccountingTransParmReqDTO.getOrganizationNumber());
        logger.debug("分期入账交易参数信息");
        Page<ParmAccountProductInfo> page = PageHelper.startPage(pageNum, pageSize);
        List<InstallAccountingTransParm> installAccountingTransParms = installAccountingTransParmSelfMapper.selectByCondition(installAccountingTransParmReqDTO);
        List<InstallAccountingTransParmResDTO> listPage = BeanMapping.copyList(installAccountingTransParms, InstallAccountingTransParmResDTO.class);
        return new PageResultDTO<>(pageNum, pageSize, page.getTotal(), page.getPages(), listPage);
    }

    /**
     * 根据id 查询分期入账交易参数表
     *
     * @param organizationNumber tableId
     * @return InstallAccountingTransParmResDTO
     */
    @Override
    public InstallAccountingTransParmResDTO selectByIndex(String organizationNumber, String tableId) {
        if (null == organizationNumber || "".equals(organizationNumber)) {

            throw new AnyTxnParameterException(AnyTxnParameterRespCodeEnum.D_INSTALL_ACCOUNTING_TRANS_PARM_ORG_FAULT);
        }
        if (null == tableId || "".equals(tableId)) {

            throw new AnyTxnParameterException(AnyTxnParameterRespCodeEnum.D_INSTALL_ACCOUNTING_TRANS_PARM_TABLE_ID_FAULT);
        }
        InstallAccountingTransParm transParm = installAccountingTransParmSelfMapper.selectByIndex(organizationNumber, tableId);
        if (null == transParm) {
            logger.error("分期入账交易参数信息，通过:organizationNumber{},tableId{}未查到数据", organizationNumber, tableId);

            throw new AnyTxnParameterException(AnyTxnParameterRespCodeEnum.D_QUERY_INSTALL_ACCOUNTING_TRANS_PARM_FAULT);
        }
        return BeanMapping.copy(transParm, InstallAccountingTransParmResDTO.class);
    }

    //校验必输项
    public void checkRequiredInputs(InstallAccountingTransParmReqDTO installAccounting) {
        if (null == installAccounting.getOrganizationNumber() || "".equals(installAccounting.getOrganizationNumber())) {

            throw new AnyTxnParameterException(AnyTxnParameterRespCodeEnum.D_INSTALL_ACCOUNTING_TRANS_PARM_ORG_FAULT);
        }
        if (null == installAccounting.getTableId() || "".equals(installAccounting.getTableId())) {

            throw new AnyTxnParameterException(AnyTxnParameterRespCodeEnum.D_INSTALL_ACCOUNTING_TRANS_PARM_TABLE_ID_FAULT);
        }
        if (null == installAccounting.getStatus() || "".equals(installAccounting.getStatus())) {

            throw new AnyTxnParameterException(AnyTxnParameterRespCodeEnum.D_INSTALL_ACCOUNTING_TRANS_PARM_STATUS_FAULT);
        }
        if (null == installAccounting.getCreateCreditFlag() || "".equals(installAccounting.getCreateCreditFlag())) {

            throw new AnyTxnParameterException(AnyTxnParameterRespCodeEnum.D_INSTALL_ACCOUNTING_TRANS_PARM_FLAG_FAULT);
        }
        if (ParamConstant.CREDIT_FLAG_CREATE.equals(installAccounting.getCreateCreditFlag())) {
            if (null == installAccounting.getCreateTransactionCode() || "".equals(installAccounting.getCreateTransactionCode())) {

                throw new AnyTxnParameterException(AnyTxnParameterRespCodeEnum.D_INSTALL_ACCOUNTING_TRANS_PARM_TRAN_FAULT);
            }
        }
        if (null == installAccounting.getCreateInstallmentFlag() || "".equals(installAccounting.getCreateInstallmentFlag())) {

            throw new AnyTxnParameterException(AnyTxnParameterRespCodeEnum.D_INSTALL_ACCOUNTING_TRANS_PARM_INSTALL_FAULT);
        }
        if (null == installAccounting.getInstallTransactionCode() || "".equals(installAccounting.getInstallTransactionCode())) {

            throw new AnyTxnParameterException(AnyTxnParameterRespCodeEnum.D_INSTALL_ACCOUNTING_TRANS_PARM_SETTLE_FAULT);
        }

        if (null == installAccounting.getPrincipalTransactionCode() || "".equals(installAccounting.getPrincipalTransactionCode())) {

            throw new AnyTxnParameterException(AnyTxnParameterRespCodeEnum.D_INSTALL_ACCOUNTING_TRANS_PARM_SETTLE_ONE_FAULT);
        }
        if (null == installAccounting.getPrincipalReversalTransCode() || "".equals(installAccounting.getPrincipalReversalTransCode())) {

            throw new AnyTxnParameterException(AnyTxnParameterRespCodeEnum.D_INSTALL_ACCOUNTING_TRANS_PARM_REVERSE_FAULT);
        }
        if (null == installAccounting.getFeeTransactionCode() || "".equals(installAccounting.getFeeTransactionCode())) {

            throw new AnyTxnParameterException(AnyTxnParameterRespCodeEnum.D_INSTALL_ACCOUNTING_TRANS_PARM_FEE_FAULT);
        }
        if (null == installAccounting.getFeeReversalTransactionCode() || "".equals(installAccounting.getFeeReversalTransactionCode())) {

            throw new AnyTxnParameterException(AnyTxnParameterRespCodeEnum.D_INSTALL_ACCOUNTING_TRANS_PARM_FEE_REVERSE_FAULT);
        }
        if (null == installAccounting.getPenatlyTransactionCode() || "".equals(installAccounting.getPenatlyTransactionCode())) {

            throw new AnyTxnParameterException(AnyTxnParameterRespCodeEnum.D_INSTALL_ACCOUNTING_TRANS_PARM_PENALTY_FAULT);
        }
        if (null == installAccounting.getFeeAmortizeTransactionCode() || "".equals(installAccounting.getFeeAmortizeTransactionCode())) {

            throw new AnyTxnParameterException(AnyTxnParameterRespCodeEnum.D_INSTALL_ACCOUNTING_TRANS_PARM_AMORTIZE_FAULT);
        }
        if (null == installAccounting.getInstallReturnCodeRev() || "".equals(installAccounting.getInstallReturnCodeRev())) {

            throw new AnyTxnParameterException(AnyTxnParameterRespCodeEnum.D_INSTALL_ACCOUNTING_TRANS_PARM_RETURN_FAULT);
        }
    }

    //校验交易码是否存在
    public void checkTranCode(List<String> codes, InstallAccountingTransParmReqDTO installAccountingTransParmReqDTO) {
        //拼接不存在的交易码
        StringBuilder resMessages = new StringBuilder();
        //0 存在  1 不存在
        int flagCode = 0;
        if (null != codes) {
            if (!codes.contains(installAccountingTransParmReqDTO.getInstallTransactionCode())) {
                resMessages.append("installmentTransactionCode:分期整笔交易码不存在,");
                flagCode = 1;
            }
            if (!codes.contains(installAccountingTransParmReqDTO.getPrincipalTransactionCode())) {
                resMessages.append("principalTransactionCode:分期交易码不存在,");
                flagCode = 1;
            }
            if (!codes.contains(installAccountingTransParmReqDTO.getPrincipalReversalTransCode())) {
                resMessages.append("principalReversalTransCode:分期撤销交易码不存在,");
                flagCode = 1;
            }
            if (!codes.contains(installAccountingTransParmReqDTO.getFeeTransactionCode())) {
                resMessages.append("feeTransactionCode:费用入账交易码不存在");
                flagCode = 1;
            }
            if (!codes.contains(installAccountingTransParmReqDTO.getFeeReversalTransactionCode())) {
                resMessages.append("feeReversalTransactionCode:费用入账撤销交易码不存在,");
                flagCode = 1;
            }
            if (!codes.contains(installAccountingTransParmReqDTO.getPenatlyTransactionCode())) {
                resMessages.append("penatlyTransactionCode:违约金入账交易码,");
                flagCode = 1;
            }
            if (!codes.contains(installAccountingTransParmReqDTO.getFeeAmortizeTransactionCode())) {
                resMessages.append("feeAmortizeTransactionCode:费用摊销交易码,");
                flagCode = 1;
            }
            //有不存在的交易码
            if (flagCode == 1) {

                throw new AnyTxnParameterException(AnyTxnParameterRespCodeEnum.D_INSTALL_ACCOUNTING_TRANS_PARM_CHECK_CODE_FAULT);
            }
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class,propagation = Propagation.REQUIRED)
    //@ShardingSphereTransactionType(TransactionType.XA)
    protected boolean updateDb(ParmModificationRecord parmModificationRecord) {
        InstallAccountingTransParm installAccountingTransParm = JSON.parseObject(parmModificationRecord.getParmBody(), InstallAccountingTransParm.class);
        installAccountingTransParm.initUpdateDateTime();
        int i = installAccountingTransParmMapper.updateByPrimaryKeySelective(installAccountingTransParm);
        return i > 0;
    }

    @Override
    @Transactional(rollbackFor = Exception.class,propagation = Propagation.REQUIRED)
    //@ShardingSphereTransactionType(TransactionType.XA)
    protected boolean insertDb(ParmModificationRecord parmModificationRecord) {
        InstallAccountingTransParm installAccountingTransParm = JSON.parseObject(parmModificationRecord.getParmBody(), InstallAccountingTransParm.class);
        installAccountingTransParm.initCreateDateTime();
        installAccountingTransParm.initUpdateDateTime();
        int i = installAccountingTransParmMapper.insertSelective(installAccountingTransParm);
        return i > 0;    }

    @Override
    @Transactional(rollbackFor = Exception.class,propagation = Propagation.REQUIRED)
    //@ShardingSphereTransactionType(TransactionType.XA)
    protected boolean deleteDb(ParmModificationRecord parmModificationRecord) {
        InstallAccountingTransParm installAccountingTransParm = JSON.parseObject(parmModificationRecord.getParmBody(), InstallAccountingTransParm.class);
        int i = installAccountingTransParmMapper.deleteByPrimaryKey(installAccountingTransParm.getId());
        return i > 0;    }
}
