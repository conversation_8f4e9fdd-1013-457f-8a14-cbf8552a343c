package com.anytech.anytxn.parameter.account.controller;

import com.anytech.anytxn.common.core.base.ParameterCompare;
import com.anytech.anytxn.common.core.base.PageResultDTO;
import com.anytech.anytxn.common.core.base.AnyTxnHttpResponse;
import com.anytech.anytxn.common.core.base.BizBaseController;
import com.anytech.anytxn.parameter.base.account.domain.dto.GiroBackReqDTO;
import com.anytech.anytxn.parameter.base.account.domain.dto.GiroBackResDTO;
import com.anytech.anytxn.parameter.base.account.service.IParmGiroAutoPaymentService;
import com.anytech.anytxn.parameter.base.common.enums.ParameterRepDetailEnum;
import io.swagger.v3.oas.annotations.tags.Tag;
import io.swagger.v3.oas.annotations.Operation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import jakarta.validation.Valid;
import java.math.BigDecimal;
import java.util.ArrayList;

/**
 * Description    GIRO退票费用参数管理
 * Copyright:	Copyright (c) 2021
 * Company:		江融信
 * Author:		liurui
 * Version:		1.0
 * Create at:	2021/11/4 2:38 PM
 *
 * 修改历史:
 * 日期    作者    版本  修改描述
 * ------------------------------------------------------------------
 *
 **/
@Tag(name = "GIRO退票费用参数定义")
@RestController
public class GiroBackController extends BizBaseController {
    @Autowired
    private IParmGiroAutoPaymentService parmGiroAutoPaymentService;

    /**
     * 根据机构号（organization_number），
     * 费用请求参数表id（table_id），获得费用请求参数表信息
     *
     * @param organizationNumber 机构号
     * @param tableId 表Id
     * @return
     */
    @Operation(summary = "通过机构号和表Id查询退票费用参数信息", description = "通过机构号和表Id查询")
    @GetMapping(value = "/param/giroBack/organizationNumber/{organizationNumber}/tableId/{tableId}")
    public AnyTxnHttpResponse<GiroBackResDTO> getChequeBackInfo(@PathVariable String organizationNumber, @PathVariable String tableId) {
        GiroBackResDTO res = parmGiroAutoPaymentService.findByTableIdAndOrgNo(tableId,organizationNumber);
        return AnyTxnHttpResponse.success(res);
    }

    /**
     * 创建退票费用信息
     * @param giroBackReqDTO 退票费用请求数据
     * @return HttpApiResponse<GiroBackReqDTO>
     */
    @PostMapping(value = "/param/giroBack")
    @Operation(summary = "创建退票费用信息")
    public AnyTxnHttpResponse<Object> create(@Valid @RequestBody GiroBackReqDTO giroBackReqDTO) {
        ParameterCompare giroBackResDTO = parmGiroAutoPaymentService.add(giroBackReqDTO);
        return AnyTxnHttpResponse.success(giroBackResDTO, ParameterRepDetailEnum.CREATE.message());
    }

    /**
     * 更新退票费用信息
     * @param giroBackReqDTO 退票费用请求数据
     * @return HttpApiResponse<GiroBackReqDTO>
     */
    @PutMapping(value = "/param/giroBack")
    @Operation(summary="根据id更新退票费用信息", description = "")
    public AnyTxnHttpResponse<Object> modify(@Valid @RequestBody GiroBackReqDTO giroBackReqDTO) {
        ParameterCompare giroBackResDTO = parmGiroAutoPaymentService.modify(giroBackReqDTO);
        return AnyTxnHttpResponse.success(giroBackResDTO,ParameterRepDetailEnum.UPDATE.message());
    }

    /**
     * 删除退票费用信息，通过id
     * @param id 技术id
     * @return HttpApiResponse<Boolean>
     */
    @DeleteMapping(value = "/param/giroBack/id/{id}")
    @Operation(summary="根据id删除", description = "需传入id")
    public AnyTxnHttpResponse<Object> remove(@PathVariable String id) {
        ParameterCompare bool = parmGiroAutoPaymentService.remove(id);
        return AnyTxnHttpResponse.success(bool,ParameterRepDetailEnum.DEL.message());
    }
//加一个根据tableID和机构号删除的方法
    /**
     * 删除退票费用信息，通过参数表id和机构号
     * @param tableId 参数表
     * @param orgNum 机构号
     * @return HttpApiResponse<Boolean>
     */
    @DeleteMapping(value = "/param/giroBack/tableId/{tableId}/organizationNumber/{orgNum}")
    @Operation(summary="根据参数表id和机构号删除", description = "需传入tableId和机构号")
    public AnyTxnHttpResponse<Object> remove(@PathVariable String tableId, @PathVariable String orgNum) {
        ParameterCompare bool = parmGiroAutoPaymentService.remove(tableId,orgNum);
        return AnyTxnHttpResponse.success(bool,ParameterRepDetailEnum.DEL.message());
    }

    /**
     * 根据根据id获取详情，进行编辑
     * @param id
     * @return HttpApiResponse<GiroBackResDTO>
     */
    @Operation(summary = "获取退票费用详情，通过id", description = "")
    @GetMapping(value = "/param/giroBack/id/{id}")
    public AnyTxnHttpResponse<GiroBackResDTO> getByIndex(@PathVariable String id){
        GiroBackResDTO giroBackResDTO = parmGiroAutoPaymentService.find(id);
        return AnyTxnHttpResponse.success(giroBackResDTO);
    }
// 加一个根据tableID和机构号联合查询的方法

    /**
     * 根据根据tableId和机构号获取详情，进行编辑
     * @param tableId 参数表id
     * @param orgNum 机构号
     * @return HttpApiResponse<GiroBackResDTO>
     */
    @Operation(summary = "获取退票费用详情，通过tableId和orgNum", description = "")
    @GetMapping(value = "/param/giroBack/tableId/{tableId}/organizationNumber/{orgNum}")
    public AnyTxnHttpResponse<GiroBackResDTO> getByIndex(@PathVariable String tableId,@PathVariable String orgNum){
        GiroBackResDTO giroBackResDTO = parmGiroAutoPaymentService.find(tableId,orgNum);
        return AnyTxnHttpResponse.success(giroBackResDTO);
    }

    /**
     * 查询所有退票分配定义参数信息
     *
     * @return TxnTypeControlRes
     */
    @Operation(summary = "GIRO自扣退票参数分页查询", description = "分页查询")
    @GetMapping(value = "/param/giroBack/pageNum/{pageNum}/pageSize/{pageSize}")
    public AnyTxnHttpResponse<PageResultDTO<GiroBackResDTO>> getAllPage(@PathVariable(value = "pageNum") Integer pageNum,
                                                                          @PathVariable(value = "pageSize") Integer pageSize,
                                                                          @RequestParam(value = "tableId",required = false) String tableId,
                                                                          @RequestParam(value = "description",required = false) String description,
                                                                          @RequestParam(value = "feeIndicator",required = false) String feeIndicator,
                                                                          @RequestParam(value = "status",required = false) String status,
                                                                          @RequestParam(value = "transactionCode",required = false) String transactionCode,
                                                                          @RequestParam(value = "interestIndicator",required = false) String interestIndicator,
                                                                          @RequestParam(value = "fixedFee",required = false) BigDecimal fixedFee,
                                                                          @RequestParam(required = false) String organizationNumber) {
        PageResultDTO<GiroBackResDTO> response;
        response = parmGiroAutoPaymentService.findAll(pageNum, pageSize, tableId, description, feeIndicator, status, transactionCode, interestIndicator, fixedFee, organizationNumber);
        return AnyTxnHttpResponse.success(response);
    }

    /**
     * 通过表状态查询退票费用参数
     *
     * @param status 参数表状态
     * @return
     */
    @Operation(summary = "通过表状态查询GIRO退票费用参数", description = "通过表状态查询退票费用参数")
    @GetMapping(value = "/param/giroBack/status/{status}")
    public AnyTxnHttpResponse<ArrayList<GiroBackResDTO>> getByStatus(@PathVariable String status) {
        ArrayList<GiroBackResDTO> resList = (ArrayList)parmGiroAutoPaymentService.findByStatus(status);
        return AnyTxnHttpResponse.success(resList);
    }

}
