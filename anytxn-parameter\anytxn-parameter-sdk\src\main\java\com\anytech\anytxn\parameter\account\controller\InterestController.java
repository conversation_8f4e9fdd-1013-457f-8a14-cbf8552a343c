package com.anytech.anytxn.parameter.account.controller;

import io.swagger.v3.oas.annotations.tags.Tag;
import io.swagger.v3.oas.annotations.Operation;
import com.anytech.anytxn.common.core.base.ParameterCompare;
import com.anytech.anytxn.common.core.base.PageResultDTO;
import com.anytech.anytxn.common.core.base.AnyTxnHttpResponse;
import com.anytech.anytxn.common.core.base.BizBaseController;
import com.anytech.anytxn.parameter.base.account.domain.dto.InterestReqDTO;
import com.anytech.anytxn.parameter.base.account.domain.dto.InterestResDTO;
import com.anytech.anytxn.parameter.base.account.domain.dto.InterestSearchDTO;
import com.anytech.anytxn.parameter.base.account.service.IInterestService;
import com.anytech.anytxn.parameter.base.common.enums.ParameterRepDetailEnum;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.ArrayList;

/**
 * 利率参数
 * <AUTHOR> tingting
 * @date 2018/8/16
 */
@Tag(name = "利率参数")
@RestController
@Slf4j
public class InterestController extends BizBaseController {

    @Autowired
    private IInterestService interestService;

    /**
     * 查询所有利率参数信息
     * @return InterestRes
     *
     */
    @Operation(summary = "利率参数分页查询",description = "分页查询")
    @GetMapping(value = "/param/interest/pageNum/{pageNum}/pageSize/{pageSize}")
    public AnyTxnHttpResponse<PageResultDTO<InterestResDTO>> getAllPage(@PathVariable(value = "pageNum") Integer pageNum,
                                                                        @PathVariable(value = "pageSize") Integer pageSize,
                                                                        InterestSearchDTO interestSearch) {
        PageResultDTO<InterestResDTO> response;
        response = interestService.findByCondition(pageNum,pageSize,interestSearch);

        return AnyTxnHttpResponse.success(response);

    }

    /**
     * 通过机构号,tableId查询所有利率参数信息
     * @return InterestRes
     *
     */
    @Operation(summary = "通过机构号,tableId查询")
    @GetMapping(value = "/param/interest/organizationNumber/{organizationNumber}/tableId/{tableId}")
    public AnyTxnHttpResponse<InterestResDTO> getInterest(@PathVariable String organizationNumber,
                                                       @PathVariable String tableId) {
        InterestResDTO res;
        res = interestService.findByOrgAndTableId(organizationNumber,tableId);
        return AnyTxnHttpResponse.success(res);

    }


    /**
     * 通过id查询利率参数信息
     * @param id
     * @return
     *
     */
    @Operation(summary = "通过id查询利率参数信息", description = "通过id查询利率参数信息")
    @GetMapping(value = "/param/interest/id/{id}")
    public AnyTxnHttpResponse<InterestResDTO> getById(@PathVariable String id) {
        InterestResDTO res;
        res = interestService.findById(id);
        return AnyTxnHttpResponse.success(res);

    }

    /**
     * 添加利率参数
     * @param interestReq
     * @return 利率参数响应参数
     */
    @Operation(summary = "新增利率参数",description = "新增利率参数")
    @PostMapping(value = "/param/interest")
    public AnyTxnHttpResponse<Object> create(@RequestBody InterestReqDTO interestReq) {
        ParameterCompare res;
        res = interestService.addParmInterest(interestReq);
        return AnyTxnHttpResponse.success(res, ParameterRepDetailEnum.CREATE.message());

    }

    /**
     * 通过表状态查询利率参数信息
     *
     */
    @Operation(summary = "根据表状态查询利率参数信息",description = "通过表状态查询利率参数信息")
    @GetMapping(value = "/param/interest/status/{status}")
    public AnyTxnHttpResponse<ArrayList<InterestResDTO>> getByStatus(@PathVariable String status) {
        ArrayList<InterestResDTO> response;
        response = (ArrayList)interestService.findByStatus(status);
        return AnyTxnHttpResponse.success(response);

    }

    /**
     * 更新利率参数
     * @param  interestReq 利率参数入参对象
     * @return 利率参数响应参数
     */
    @Operation(summary = "利率参数信息修改", description = "只更新需要更新的,其他不变")
    @PutMapping(value = "/param/interest")
    public AnyTxnHttpResponse<Object> modify(@RequestBody InterestReqDTO interestReq) {
        ParameterCompare res = interestService.modifyParmInterest(interestReq);
        return AnyTxnHttpResponse.success(res,ParameterRepDetailEnum.UPDATE.message());

    }

    /**
     * 通过Id主键删除费用参数
     * @param  id 主键
     * @return Boolean
     */
    @Operation(summary = "删除利率参数信息", description = "通过id删除利率参数信息")
    @DeleteMapping(value = "/param/interest/id/{id}")
    public AnyTxnHttpResponse<Object> delete(@PathVariable String id) {
        ParameterCompare flag = interestService.removeParmInterest(id);
        return AnyTxnHttpResponse.success(flag,ParameterRepDetailEnum.DEL.message());
    }
}
