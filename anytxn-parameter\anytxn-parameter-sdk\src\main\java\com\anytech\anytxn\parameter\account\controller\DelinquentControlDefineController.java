package com.anytech.anytxn.parameter.account.controller;

import io.swagger.v3.oas.annotations.tags.Tag;
import io.swagger.v3.oas.annotations.Operation;
import com.anytech.anytxn.common.core.base.ParameterCompare;
import com.anytech.anytxn.common.core.base.PageResultDTO;
import com.anytech.anytxn.common.core.base.AnyTxnHttpResponse;
import com.anytech.anytxn.common.core.base.BizBaseController;
import com.anytech.anytxn.parameter.base.account.domain.dto.DelinquentControlDefineReqDTO;
import com.anytech.anytxn.parameter.base.account.domain.dto.DelinquentControlDefineResDTO;
import com.anytech.anytxn.parameter.base.account.service.IDelinquentControlDefineService;
import com.anytech.anytxn.parameter.base.common.enums.ParameterRepDetailEnum;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.ArrayList;

/**
 * Description   延滞控制定义表
 * Copyright:	Copyright (c) 2018
 * Company:		江融信
 * Author:		lichao
 * Version:		1.0
 * Create at:	2018/9/21 上午10:41
 *
 * 修改历史:
 * 日期    作者    版本  修改描述
 * ------------------------------------------------------------------
 *
 **/
@Tag(name = "延滞控制定义参数")
@RestController
public class DelinquentControlDefineController extends BizBaseController {

    @Autowired
    private IDelinquentControlDefineService delinquentControlDefineService;

    /**
     * 通过机构号,tableId查询延滞控制参数定义信息
     * @param organizationNumber 机构号
     * @param tableId 表Id
     * @return
     *
     */
    @Operation(summary = "根据机构号,tableId查询延滞控制参数信息", description = "根据tableId")
    @GetMapping(value = "/param/delinquentControlDefine/organizationNumber/{organizationNumber}/tableId/{tableId}")
    public AnyTxnHttpResponse<DelinquentControlDefineResDTO> getDelinquentControlDefine(@PathVariable String organizationNumber, @PathVariable String tableId) {
        DelinquentControlDefineResDTO res = null;
        res = delinquentControlDefineService.findDelinquentControlDefine(organizationNumber,tableId);
        return AnyTxnHttpResponse.success(res);
    }

    /**
     * 查询所有延滞控制定义参数信息
     *
     * @return DelinquentControlDefineRes
     */
    @Operation(summary = "延滞控制定义参数分页查询", description = "分页查询")
    @GetMapping(value = "/param/delinquentControlDefine/pageNum/{pageNum}/pageSize/{pageSize}")
    public AnyTxnHttpResponse<PageResultDTO<DelinquentControlDefineResDTO>> getAllPage(@PathVariable(value = "pageNum") Integer pageNum,
                                                                                       @PathVariable(value = "pageSize") Integer pageSize,
                                                                                       DelinquentControlDefineReqDTO delinquentControlDefineReqDTO) {
        PageResultDTO<DelinquentControlDefineResDTO> response = delinquentControlDefineService.findAll(pageNum, pageSize,delinquentControlDefineReqDTO);
        return AnyTxnHttpResponse.success(response);



    }

    /**
     * 查询延滞控制定义详情
     *
     * @return
     */
    @Operation(summary = "通过id查询延滞控制定义详情", description = "延滞控制定义详情查询")
    @GetMapping(value = "/param/delinquentControlDefine/id/{id}")
    public AnyTxnHttpResponse<DelinquentControlDefineResDTO> getDetail(@PathVariable String id) {
        DelinquentControlDefineResDTO response;
        response = delinquentControlDefineService.findById(id);
        return AnyTxnHttpResponse.success(response);
    }

    /**
     * 新增延滞控制定义参数
     *
     * @param delinquentControlDefineReq
     * @return
     */
    @Operation(summary = "新增延滞控制定义参数", description = "新增延滞控制定义参数")
    @PostMapping("/param/delinquentControlDefine")
    public AnyTxnHttpResponse<Object> create(@RequestBody DelinquentControlDefineReqDTO delinquentControlDefineReq) {
        if (delinquentControlDefineReq.getControlReqList() == null) {
            delinquentControlDefineReq.setControlReqList(new ArrayList<>());
        }
        ParameterCompare res = delinquentControlDefineService.addDelinquentControlDefine(delinquentControlDefineReq);
        return AnyTxnHttpResponse.success(res, ParameterRepDetailEnum.CREATE.message());
    }

    /**
     * 通过id删除延滞控制定义参数
     *
     * @param id
     * @return
     */
    @Operation(summary = "删除延滞控制定义参数", description = "通过id删除延滞控制定义参数")
    @DeleteMapping(value = "/param/delinquentControlDefine/id/{id}")
    public AnyTxnHttpResponse<Object> delete(@PathVariable String id) {
        ParameterCompare flag = delinquentControlDefineService.removeDelinquentControlDefine(id);
        return AnyTxnHttpResponse.success(flag,ParameterRepDetailEnum.DEL.message());
    }

    /**
     * 更新延滞控制定义参数
     *
     * @param delinquentControlDefineReq
     * @return
     */
    @Operation(summary = "更新延滞控制定义参数", description = "更新延滞控制定义参数")
    @PutMapping(value = "/param/delinquentControlDefine")
    public AnyTxnHttpResponse<Object> update(@RequestBody DelinquentControlDefineReqDTO delinquentControlDefineReq) {
        if (delinquentControlDefineReq.getControlReqList() == null) {
            delinquentControlDefineReq.setControlReqList(new ArrayList<>());
        }
        ParameterCompare res = delinquentControlDefineService.modifyDelinquentControlDefine(delinquentControlDefineReq);
        return AnyTxnHttpResponse.success(res,ParameterRepDetailEnum.UPDATE.message());

    }

}
