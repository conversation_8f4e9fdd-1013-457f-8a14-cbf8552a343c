package com.anytech.anytxn.parameter.common.service.system;

import com.alibaba.fastjson.JSON;
import com.anytech.anytxn.common.core.annotation.db.DeleteParameterAnnotation;
import com.anytech.anytxn.common.core.annotation.db.InsertParameterAnnotation;
import com.anytech.anytxn.common.core.annotation.db.UpdateParameterAnnotation;
import com.anytech.anytxn.business.base.audit.AbstractParameterService;
import com.anytech.anytxn.common.core.base.ParameterCompare;
import com.anytech.anytxn.business.base.audit.ParmModificationRecord;
import com.anytech.anytxn.common.core.base.PageResultDTO;
import com.anytech.anytxn.common.core.utils.BeanMapping;
import com.anytech.anytxn.common.core.utils.OrgNumberUtils;
import com.anytech.anytxn.common.sequence.utils.Number16IdGen;
import com.anytech.anytxn.common.core.utils.TenantUtils;
import com.anytech.anytxn.parameter.base.common.enums.AnyTxnParameterRespCodeEnum;
import com.anytech.anytxn.parameter.base.common.exception.AnyTxnParameterException;
import com.anytech.anytxn.parameter.common.mapper.broadcast.system.ParmLabelPricingMapper;
import com.anytech.anytxn.parameter.base.common.domain.model.system.ParmLabelPricing;
import com.anytech.anytxn.parameter.base.common.domain.dto.system.ParamLabelPricingDTO;
import com.anytech.anytxn.parameter.base.common.service.system.IParamLabelPricingService;
import com.github.pagehelper.Page;
import com.github.pagehelper.PageHelper;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
//import org.apache.shardingsphere.transaction.annotation.ShardingSphereTransactionType;
//import org.apache.shardingsphere.transaction.core.TransactionType;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 标签定价页面相关
 *
 * <AUTHOR>
 */
@Service("parm_label_pricing_serviceImpl")
@Slf4j
public class ParamLabelPricingServiceImpl extends AbstractParameterService implements IParamLabelPricingService {
    @Autowired
    private Number16IdGen numberIdGenerator;
    @Autowired
    private ParmLabelPricingMapper parmLabelPricingMapper;

    @Override
    @Transactional(rollbackFor = Exception.class, propagation = Propagation.REQUIRED)
    //@ShardingSphereTransactionType(TransactionType.XA)
    public boolean updateDb(ParmModificationRecord parmModificationRecord) throws Exception {
        ParmLabelPricing parmLabelPricing = getPricing(parmModificationRecord);
        parmLabelPricing.initUpdateValue(parmLabelPricing.getVersionNumber());

        // 此处业务上面--前端将标签值进行了置灰处理 所以在更新的时候是没有： 不存在的数据进行新增的业务逻辑的
        int res = parmLabelPricingMapper.updateByPrimaryKeySelective(parmLabelPricing);
        return res > 0;
    }


    @Override
    @Transactional(rollbackFor = Exception.class, propagation = Propagation.REQUIRED)
    //@ShardingSphereTransactionType(TransactionType.XA)
    public boolean insertDb(ParmModificationRecord parmModificationRecord) throws Exception {
        ParmLabelPricing parmLabelPricing = JSON.parseObject(parmModificationRecord.getParmBody(), ParmLabelPricing.class);
        parmLabelPricing.initCreateValue();
        int res = parmLabelPricingMapper.insertSelective(parmLabelPricing);
        return res > 0;

    }

    @Override
    @Transactional(rollbackFor = Exception.class, propagation = Propagation.REQUIRED)
    //@ShardingSphereTransactionType(TransactionType.XA)
    public boolean deleteDb(ParmModificationRecord parmModificationRecord) throws Exception {
        ParmLabelPricing parmLabelPricing = JSON.parseObject(parmModificationRecord.getParmBody(), ParmLabelPricing.class);
        int res = parmLabelPricingMapper.deleteByPrimaryKey(parmLabelPricing.getId());
        return res > 0;
    }

//=========================================================================================================================================

    @Override
    @InsertParameterAnnotation(tableName = "parm_label_pricing", tableDesc = "Label pricing parameter")
    public ParameterCompare add(ParamLabelPricingDTO paramLabelPricingDTO) {
        if (paramLabelPricingDTO == null || org.apache.commons.lang3.StringUtils.isBlank(paramLabelPricingDTO.getLabelType())) {
            throw new AnyTxnParameterException(AnyTxnParameterRespCodeEnum.P_PARAMETER_EMPTY_FAULT);
        }
        String organizationNumber = OrgNumberUtils.getOrg(paramLabelPricingDTO.getOrganizationNumber());

        // 根据标签类型 值 状态 查询 是否 数据已经存在
        List<ParmLabelPricing> parmLabelPricingList = parmLabelPricingMapper.selByLabelTypeAndValue(paramLabelPricingDTO.getLabelType(), paramLabelPricingDTO.getLabelValue(), organizationNumber);

        if (CollectionUtils.isNotEmpty(parmLabelPricingList)) {
            throw new AnyTxnParameterException(AnyTxnParameterRespCodeEnum.D_DATA_ALREADY_EXISTS_FAULT);
        }


        String result = null;
        if (CollectionUtils.isNotEmpty(paramLabelPricingDTO.getLabelCardProductNumberList())) {
            result = String.join(",", paramLabelPricingDTO.getLabelCardProductNumberList());
        }
        ParmLabelPricing parmLabelPricing = BeanMapping.copy(paramLabelPricingDTO, ParmLabelPricing.class);
        BigDecimal labelTopupMarkupRate = paramLabelPricingDTO.getLabelTopupMarkupRate();
        parmLabelPricing.setLabelFxFee(labelTopupMarkupRate);
        parmLabelPricing.setLabelCardProductNumberList(result);

        parmLabelPricing.setId(String.valueOf(numberIdGenerator.generateId(TenantUtils.getTenantId())));
        parmLabelPricing.setOrganizationNumber(organizationNumber);

        return ParameterCompare.getBuilder().withAfter(parmLabelPricing).build(ParamLabelPricingDTO.class);
    }


    @Override
    @UpdateParameterAnnotation(tableName = "parm_label_pricing", tableDesc = "Label pricing parameter")
    public ParameterCompare modify(ParamLabelPricingDTO paramLabelPricingDTO) {
        if (paramLabelPricingDTO == null) {
            throw new AnyTxnParameterException(AnyTxnParameterRespCodeEnum.P_PARAMETER_EMPTY_FAULT);
        }
        ParmLabelPricing parmLabelPricing = parmLabelPricingMapper.selectByPrimaryKey(paramLabelPricingDTO.getId());
        if (parmLabelPricing == null) {
            throw new AnyTxnParameterException(AnyTxnParameterRespCodeEnum.P_PARAMETER_DATA_NOT_EXIST);
        }

        BigDecimal labelFxFee = parmLabelPricing.getLabelFxFee();
        ParamLabelPricingDTO labelPricingDTO = BeanMapping.copy(parmLabelPricing, ParamLabelPricingDTO.class);

        getProductNum(Collections.singletonList(labelPricingDTO), parmLabelPricing);

        labelPricingDTO.setLabelTopupMarkupRate(labelFxFee);
        return ParameterCompare
                .getBuilder()
                .withAfter(paramLabelPricingDTO)
                .withBefore(labelPricingDTO)
                .build(ParamLabelPricingDTO.class);
    }

    @Override
    public PageResultDTO<ParamLabelPricingDTO> findPage(ParamLabelPricingDTO labelPricingDTO) {
        Integer pageIndex = labelPricingDTO.getPageIndex();
        Integer pageSize = labelPricingDTO.getPageSize();
        String organizationNumber = labelPricingDTO.getOrganizationNumber();

        String labelType = labelPricingDTO.getLabelType() == null ? null : labelPricingDTO.getLabelType().trim();
        String labelValue = labelPricingDTO.getLabelValue() == null ? null : labelPricingDTO.getLabelValue().trim();
        organizationNumber = organizationNumber == null ? OrgNumberUtils.getOrg() : organizationNumber;

        Page<Object> page = PageHelper.startPage(pageIndex, pageSize);
        List<ParmLabelPricing> labelPricingList = parmLabelPricingMapper.selByLabelTypeAndValue(labelType, labelValue, organizationNumber);
        // 分组后返回
        Map<String, List<ParmLabelPricing>> groupByLabelMap = labelPricingList.stream().collect(Collectors.groupingBy(ParmLabelPricing::getLabelType));

        List<ParmLabelPricing> resultList = new ArrayList<>();
        for (Map.Entry<String, List<ParmLabelPricing>> listEntry : groupByLabelMap.entrySet()) {
            List<ParmLabelPricing> entryValue = listEntry.getValue();
            resultList.addAll(entryValue);
        }

        List<ParamLabelPricingDTO> labelPricingDTOList = BeanMapping.copyList(resultList, ParamLabelPricingDTO.class);
        for (ParmLabelPricing parmLabelPricing : resultList) {
            BigDecimal labelFxFee = parmLabelPricing.getLabelFxFee();
            for (ParamLabelPricingDTO paramLabelPricingDTO : labelPricingDTOList) {
                if (parmLabelPricing.getId().equals(paramLabelPricingDTO.getId())) {
                    paramLabelPricingDTO.setLabelTopupMarkupRate(labelFxFee);
                    break;
                }
            }

            getProductNum(labelPricingDTOList, parmLabelPricing);
        }

        return new PageResultDTO<>(pageIndex, pageSize, page.getTotal(), page.getPages(), labelPricingDTOList);
    }


    @Override
    @DeleteParameterAnnotation(tableName = "parm_label_pricing", tableDesc = "Label pricing parameter")
    public ParameterCompare remove(String id) {
        if (StringUtils.isBlank(id)) {
            throw new AnyTxnParameterException(AnyTxnParameterRespCodeEnum.P_PARAMETER_EMPTY_FAULT);
        }
        ParmLabelPricing before = parmLabelPricingMapper.selectByPrimaryKey(id);
        if (before == null) {
            throw new AnyTxnParameterException(AnyTxnParameterRespCodeEnum.P_PARAMETER_DATA_NOT_EXIST);
        }
        return ParameterCompare
                .getBuilder()
                .withBefore(before)
                .build(ParmLabelPricing.class);
    }

    @Override
    public ParamLabelPricingDTO findById(String id) {
        if (StringUtils.isBlank(id)) {
            throw new AnyTxnParameterException(AnyTxnParameterRespCodeEnum.P_PARAMETER_EMPTY_FAULT);
        }
        ParmLabelPricing parmLabelPricing = parmLabelPricingMapper.selectByPrimaryKey(id);
        if (parmLabelPricing == null) {
            return new ParamLabelPricingDTO();
        }

        List<ParmLabelPricing> resultList = Collections.singletonList(parmLabelPricing);
        List<ParamLabelPricingDTO> labelPricingDTOList = BeanMapping.copyList(resultList, ParamLabelPricingDTO.class);

        for (ParmLabelPricing tempParmLabelPricing : resultList) {
            BigDecimal labelFxFee = tempParmLabelPricing.getLabelFxFee();
            for (ParamLabelPricingDTO paramLabelPricingDTO : labelPricingDTOList) {
                if (tempParmLabelPricing.getId().equals(paramLabelPricingDTO.getId())) {
                    paramLabelPricingDTO.setLabelTopupMarkupRate(labelFxFee);
                    break;
                }
            }

            getProductNum(labelPricingDTOList, parmLabelPricing);
        }

        return labelPricingDTOList.get(0);
    }


    @Override
    public boolean change(ParamLabelPricingDTO paramLabelPricingDTO) {
        if (paramLabelPricingDTO == null || org.apache.commons.lang3.StringUtils.isBlank(paramLabelPricingDTO.getLabelType())) {
            throw new AnyTxnParameterException(AnyTxnParameterRespCodeEnum.P_PARAMETER_EMPTY_FAULT);
        }
        String organizationNumber = OrgNumberUtils.getOrg(paramLabelPricingDTO.getOrganizationNumber());

        String result = null;
        if (CollectionUtils.isNotEmpty(paramLabelPricingDTO.getLabelCardProductNumberList())) {
            result = String.join(",", paramLabelPricingDTO.getLabelCardProductNumberList());
        }
        ParmLabelPricing parmLabelPricing = BeanMapping.copy(paramLabelPricingDTO, ParmLabelPricing.class);
        BigDecimal labelTopupMarkupRate = paramLabelPricingDTO.getLabelTopupMarkupRate();
        parmLabelPricing.setLabelFxFee(labelTopupMarkupRate);
        parmLabelPricing.setLabelCardProductNumberList(result);

        // 根据标签类型 值 状态 查询 是否 数据已经存在
        List<ParmLabelPricing> parmLabelPricingList = parmLabelPricingMapper.selByLabelTypeAndValue(paramLabelPricingDTO.getLabelType(),
                paramLabelPricingDTO.getLabelValue(), organizationNumber);

        // 空则新增 不空则更新
        if (CollectionUtils.isEmpty(parmLabelPricingList)) {
            parmLabelPricing.setId(String.valueOf(numberIdGenerator.generateId(TenantUtils.getTenantId())));
            parmLabelPricing.initCreateValue();
            parmLabelPricingMapper.insertSelective(parmLabelPricing);
        } else {
            parmLabelPricing.setId(parmLabelPricingList.get(0).getId());
            parmLabelPricing.initUpdateValue(parmLabelPricing.getVersionNumber());
            parmLabelPricingMapper.updateByPrimaryKeySelective(parmLabelPricing);
        }
        return true;
    }


    private static void getProductNum(List<ParamLabelPricingDTO> labelPricingDTOList, ParmLabelPricing parmLabelPricing) {
        String labelCardProductNumberList = parmLabelPricing.getLabelCardProductNumberList();
        if (StringUtils.isNotBlank(labelCardProductNumberList)) {
            List<String> cardProductList = Arrays.asList(labelCardProductNumberList.split(","));
            for (ParamLabelPricingDTO paramLabelPricingDTO : labelPricingDTOList) {
                if (paramLabelPricingDTO.getId().equals(parmLabelPricing.getId())) {
                    paramLabelPricingDTO.setLabelCardProductNumberList(cardProductList);
                    break;
                }
            }
        }
    }

    private static ParmLabelPricing getPricing(ParmModificationRecord parmModificationRecord) {
        String parmBody = parmModificationRecord.getParmBody();
        ParamLabelPricingDTO paramLabelPricingDTO = JSON.parseObject(parmBody, ParamLabelPricingDTO.class);
        String result = null;
        if (CollectionUtils.isNotEmpty(paramLabelPricingDTO.getLabelCardProductNumberList())) {
            result = String.join(",", paramLabelPricingDTO.getLabelCardProductNumberList());
        }
        ParmLabelPricing parmLabelPricing = BeanMapping.copy(paramLabelPricingDTO, ParmLabelPricing.class);
        BigDecimal labelTopupMarkupRate = paramLabelPricingDTO.getLabelTopupMarkupRate();
        parmLabelPricing.setLabelFxFee(labelTopupMarkupRate);
        parmLabelPricing.setLabelCardProductNumberList(result);
        return parmLabelPricing;
    }


}
    
    

