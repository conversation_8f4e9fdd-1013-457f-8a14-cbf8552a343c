package com.anytech.anytxn.parameter.account.controller;

import io.swagger.v3.oas.annotations.tags.Tag;
import com.anytech.anytxn.common.core.base.PageResultDTO;
import com.anytech.anytxn.common.core.utils.OrgNumberUtils;
import com.anytech.anytxn.common.core.base.AnyTxnHttpResponse;
import com.anytech.anytxn.common.core.base.BizBaseController;
import com.anytech.anytxn.parameter.base.account.domain.dto.InterestBearingDTO;
import com.anytech.anytxn.parameter.base.account.service.IInterestBearingService;
import com.anytech.anytxn.parameter.base.common.enums.ParameterRepDetailEnum;
import org.springframework.web.bind.annotation.*;

import jakarta.annotation.Resource;

/**
 * Description:计息参数表
 * date: 2021/5/12 11:23
 *
 * <AUTHOR>
 */
@Tag(name = "计息参数表")
@RestController
public class InterestBearingController extends BizBaseController {

    @Resource
    private IInterestBearingService interestBearingService;

    /**
     * 添加
     * @param  dto 计息参数表
     * @return Object
     */
    @PostMapping(value = "/param/addInterestBearing")
    public AnyTxnHttpResponse<Object> add(@RequestBody InterestBearingDTO dto){
        return AnyTxnHttpResponse.success(interestBearingService.add(dto), ParameterRepDetailEnum.CREATE.message());
    }

    /**
     * 修改
     * @param  dto 计息参数表
     * @return Object
     */
    @PutMapping(value = "/param/modifyInterestBearing")
    public AnyTxnHttpResponse<Object> modify(@RequestBody InterestBearingDTO dto){
        return AnyTxnHttpResponse.success(interestBearingService.modify(dto), ParameterRepDetailEnum.UPDATE.message());
    }

    /**
     * 分页查询
     * @param  pageNum pageSize  organizationNumber
     * @return InterestBearingDTO
     */
    @GetMapping(value = "/param/interestBearing/pageNum/{pageNum}/pageSize/{pageSize}")
    public AnyTxnHttpResponse<PageResultDTO<InterestBearingDTO>> findByPage(@PathVariable(value = "pageNum") Integer pageNum,
                                                                                          @PathVariable(value = "pageSize") Integer pageSize,
                                                                                          @RequestParam(value = "tableId",required = false) String tableId,
                                                                                          @RequestParam(value = "description",required = false) String description,
                                                                                          @RequestParam(value = "organizationNumber",required = false) String organizationNumber){
        return AnyTxnHttpResponse.success(interestBearingService.findPage(pageNum,pageSize, OrgNumberUtils.getOrg(organizationNumber),tableId, description));
    }

    /**
     * 删除
     * @param  id 计息参数表
     * @return Object
     */
    @DeleteMapping(value = "/param/removeInterestBearing/id/{id}")
    public AnyTxnHttpResponse<Object> remove(@PathVariable String id){
        return AnyTxnHttpResponse.success(interestBearingService.remove(id), ParameterRepDetailEnum.DEL.message());
    }

    /**
     * 通过ID查看
     * @param  id 计息参数表ID
     * @return InterestBearingDTO
     */
    @GetMapping(value = "/param/interestBearing/id/{id}")
    public AnyTxnHttpResponse<InterestBearingDTO> findById(@PathVariable String id){
        return AnyTxnHttpResponse.success(interestBearingService.findById(id));
    }
}
