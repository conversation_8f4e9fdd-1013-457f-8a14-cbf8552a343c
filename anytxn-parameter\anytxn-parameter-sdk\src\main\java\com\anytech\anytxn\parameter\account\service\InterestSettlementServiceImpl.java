package com.anytech.anytxn.parameter.account.service;

import com.alibaba.fastjson.JSON;
import com.github.pagehelper.Page;
import com.github.pagehelper.PageHelper;
import com.anytech.anytxn.common.core.annotation.db.DeleteParameterAnnotation;
import com.anytech.anytxn.common.core.annotation.db.InsertParameterAnnotation;
import com.anytech.anytxn.common.core.annotation.db.UpdateParameterAnnotation;
import com.anytech.anytxn.business.base.audit.AbstractParameterService;
import com.anytech.anytxn.common.core.base.ParameterCompare;
import com.anytech.anytxn.business.base.audit.ParmModificationRecord;
import com.anytech.anytxn.common.core.base.PageResultDTO;
import com.anytech.anytxn.common.core.utils.BeanMapping;
import com.anytech.anytxn.common.core.utils.OrgNumberUtils;
import com.anytech.anytxn.common.sequence.utils.Number16IdGen;
import com.anytech.anytxn.common.core.utils.TenantUtils;
import com.anytech.anytxn.parameter.base.account.domain.dto.InterestSettlementDTO;
import com.anytech.anytxn.parameter.base.account.service.IInterestSettlementService;
import com.anytech.anytxn.parameter.base.common.enums.AnyTxnParameterRespCodeEnum;
import com.anytech.anytxn.parameter.base.common.exception.AnyTxnParameterException;
import com.anytech.anytxn.parameter.account.mapper.InterestSettlementMapper;
import com.anytech.anytxn.parameter.base.account.domain.model.InterestSettlement;
//import org.apache.shardingsphere.transaction.annotation.ShardingSphereTransactionType;
//import org.apache.shardingsphere.transaction.core.TransactionType;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.ObjectUtils;
import org.springframework.util.StringUtils;

import jakarta.annotation.Resource;
import java.util.List;

/**
 * Description: 结息参数表
 * date: 2021/5/12 14:02
 *
 * <AUTHOR>
 */
@Service("parm_interest_settlement_serviceImpl")
public class InterestSettlementServiceImpl  extends AbstractParameterService implements IInterestSettlementService {

    @Resource
    private Number16IdGen numberIdGenerator;

    @Resource
    private InterestSettlementMapper interestSettlementMapper;


    @Override
    @Transactional(rollbackFor = Exception.class,propagation = Propagation.REQUIRED)
    //@ShardingSphereTransactionType(TransactionType.XA)
    protected boolean updateDb(ParmModificationRecord parmModificationRecord) throws Exception {
        InterestSettlement interestSettlement = JSON.parseObject(parmModificationRecord.getParmBody(), InterestSettlement.class);
        interestSettlement.initUpdateDateTime();
        int res = interestSettlementMapper.updateByPrimaryKeySelective(interestSettlement);
        return res == 1;
    }

    @Override
    @Transactional(rollbackFor = Exception.class,propagation = Propagation.REQUIRED)
    //@ShardingSphereTransactionType(TransactionType.XA)
    protected boolean insertDb(ParmModificationRecord parmModificationRecord) throws Exception {
        InterestSettlement interestSettlement = JSON.parseObject(parmModificationRecord.getParmBody(), InterestSettlement.class);
        interestSettlement.initUpdateDateTime();
        interestSettlement.initCreateDateTime();
        int res = interestSettlementMapper.insertSelective(interestSettlement);
        return res == 1;
    }

    @Override
    @Transactional(rollbackFor = Exception.class,propagation = Propagation.REQUIRED)
    //@ShardingSphereTransactionType(TransactionType.XA)
    protected boolean deleteDb(ParmModificationRecord parmModificationRecord) throws Exception {
        InterestSettlement interestSettlement = JSON.parseObject(parmModificationRecord.getParmBody(), InterestSettlement.class);
        int res = interestSettlementMapper.deleteByPrimaryKey(interestSettlement.getId());
        return res == 1;
    }

    @Override
    @InsertParameterAnnotation(tableName = "parm_interest_settlement",tableDesc = "Settlement parameters")
    public ParameterCompare add(InterestSettlementDTO dto) {
        if (ObjectUtils.isEmpty(dto)){
            throw new AnyTxnParameterException(AnyTxnParameterRespCodeEnum.P_PARAMETER_EMPTY_FAULT);
        }
        if (StringUtils.isEmpty(dto.getTableId())){
            throw new AnyTxnParameterException(AnyTxnParameterRespCodeEnum.P_PARAMETER_TABLE_ID_EMPTY_FAULT);
        }
        String organizationNumber = OrgNumberUtils.getOrg(dto.getOrganizationNumber());
        InterestSettlement interestSettlement = interestSettlementMapper.selectByTableIdAndOrg(dto.getTableId(), organizationNumber);
        if (!ObjectUtils.isEmpty(interestSettlement)){
            throw new AnyTxnParameterException(AnyTxnParameterRespCodeEnum.D_EXIST_PARM_PAYMENT_ALLOCATED_BY_ID_FAULT);
        }
        InterestSettlement after = BeanMapping.copy(dto, InterestSettlement.class);
        after.setId(String.valueOf(numberIdGenerator.generateId(TenantUtils.getTenantId())));
        after.setOrganizationNumber(organizationNumber);
        return ParameterCompare
                .getBuilder()
                .withAfter(after)
                .build(InterestSettlement.class);
    }

    @Override
    @UpdateParameterAnnotation(tableName = "parm_interest_settlement",tableDesc = "Settlement parameters")
    public ParameterCompare modify(InterestSettlementDTO dto) {
        if (ObjectUtils.isEmpty(dto)){
            throw new AnyTxnParameterException(AnyTxnParameterRespCodeEnum.P_PARAMETER_EMPTY_FAULT);
        }
        InterestSettlement before = interestSettlementMapper.selectByPrimaryKey(dto.getId());
        if (ObjectUtils.isEmpty(before)){
            throw new AnyTxnParameterException(AnyTxnParameterRespCodeEnum.P_PARAMETER_DATA_NOT_EXIST);
        }
        InterestSettlement after = BeanMapping.copy(dto, InterestSettlement.class);
        return ParameterCompare
                .getBuilder()
                .withAfter(after)
                .withBefore(before)
                .build(InterestSettlement.class);
    }

    @Override
    @DeleteParameterAnnotation(tableName = "parm_interest_settlement",tableDesc = "Settlement parameters")
    public ParameterCompare remove(String id) {
        if (StringUtils.isEmpty(id)){
            throw new AnyTxnParameterException(AnyTxnParameterRespCodeEnum.P_PARAMETER_EMPTY_FAULT);
        }
        InterestSettlement before = interestSettlementMapper.selectByPrimaryKey(id);
        if (ObjectUtils.isEmpty(before)){
            throw new AnyTxnParameterException(AnyTxnParameterRespCodeEnum.P_PARAMETER_DATA_NOT_EXIST);
        }
        return ParameterCompare
                .getBuilder()
                .withBefore(before)
                .build(InterestSettlement.class);
    }

    @Override
    public InterestSettlementDTO findById(String id) {
        if (StringUtils.isEmpty(id)){
            throw new AnyTxnParameterException(AnyTxnParameterRespCodeEnum.P_PARAMETER_EMPTY_FAULT);
        }
        InterestSettlement interestSettlement = interestSettlementMapper.selectByPrimaryKey(id);
        return BeanMapping.copy(interestSettlement,InterestSettlementDTO.class);
    }

    @Override
    public PageResultDTO<InterestSettlementDTO> findPage(Integer pageNum, Integer pageSize, String organizationNumber, String tableId, String description) {
        Page<InterestSettlement> page = PageHelper.startPage(pageNum,pageSize);
        List<InterestSettlement> interestSettlements = interestSettlementMapper.selectByConditionAndPage(tableId, description, OrgNumberUtils.getOrg(organizationNumber));
        List<InterestSettlementDTO> res = BeanMapping.copyList(interestSettlements, InterestSettlementDTO.class);
        return new PageResultDTO<>(pageNum,pageSize,page.getTotal(), page.getPages(),res);
    }

    @Override
    public InterestSettlementDTO findByOrgAndTableId(String organizationNumber, String tableId) {
        InterestSettlement interestSettlement = interestSettlementMapper.selectByTableIdAndOrg(tableId, organizationNumber);
        return null == interestSettlement ? null : BeanMapping.copy(interestSettlement,InterestSettlementDTO.class);
    }
}
