package com.anytech.anytxn.parameter.card.controller;

import com.anytech.anytxn.common.core.utils.OrgNumberUtils;
import io.swagger.v3.oas.annotations.tags.Tag;
import io.swagger.v3.oas.annotations.Operation;
import com.anytech.anytxn.common.core.base.PageResultDTO;
import com.anytech.anytxn.common.core.base.AnyTxnHttpResponse;
import com.anytech.anytxn.common.core.base.BizBaseController;
import com.anytech.anytxn.parameter.base.card.domain.dto.CardCustomReqDTO;
import com.anytech.anytxn.parameter.base.card.domain.dto.CardCustomResDTO;
import com.anytech.anytxn.parameter.base.card.service.ICardCustomService;
import com.anytech.anytxn.parameter.base.common.enums.ParameterRepDetailEnum;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import jakarta.validation.Valid;

/**
 * 卡号个性化管理信息
 *
 * <AUTHOR>
 * @date 2018-12-04
 **/
@RestController
@Tag(name = "卡号个性化信息")
public class CardCustomController extends BizBaseController {

    @Autowired
    private ICardCustomService cardCustomService;


    /**
     * 创建卡号个性化信息条目
     * @param cardCustomReqDTO 卡号个性化信息请求数据
     * @return AnyTxnHttpResponse<CardProductInfoRes>
     */
    @PostMapping(value = "/param/cardCustom")
    @Operation(summary = "创建卡号个性化信息")
    public AnyTxnHttpResponse<CardCustomResDTO> create(@Valid @RequestBody CardCustomReqDTO cardCustomReqDTO) {
        CardCustomResDTO cardCustomResDTO = cardCustomService.add(cardCustomReqDTO);
        return AnyTxnHttpResponse.success(cardCustomResDTO, ParameterRepDetailEnum.CREATE.message());
    }

    /**
     * 删除卡号个性化信息条目，通过id
     * @param id 技术id
     * @return HttpApiResponse<Boolean>
     */
    @DeleteMapping(value = "/param/cardCustom/id/{id}")
    @Operation(summary="根据id删除卡号个性化信息条目", description = "需传入id")
    public AnyTxnHttpResponse<Boolean> remove(@PathVariable Long id) {
        Boolean bool = cardCustomService.remove(id);
        return AnyTxnHttpResponse.success(bool,ParameterRepDetailEnum.DEL.message());
    }

    /**
     * 更新卡号个性化信息
     * @param cardCustomReqDTO 卡号个性化信息请求数据
     * @return AnyTxnHttpResponse<CardProductInfoRes>
     */
    @PutMapping(value = "/param/cardCustom")
    @Operation(summary="根据id更新卡号个性化信息", description = "")
    public AnyTxnHttpResponse<CardCustomResDTO> modify(@Valid @RequestBody CardCustomReqDTO cardCustomReqDTO) {
        CardCustomResDTO cardCustomResDTO = cardCustomService.modify(cardCustomReqDTO);
        return AnyTxnHttpResponse.success(cardCustomResDTO,ParameterRepDetailEnum.UPDATE.message());
    }

    /**
     * 根据根据id获取详情，进行编辑
     * @param id 卡号个性化信息id
     * @return HttpApiResponse<ProductInfoRes>
     */
    @Operation(summary="获取卡号个性化详情，通过id", description = "")
    @GetMapping(value = "/param/cardCustom/id/{id}")
    public AnyTxnHttpResponse<CardCustomResDTO> getByIndex(@PathVariable Long id){
        CardCustomResDTO cardCustomResDTO = cardCustomService.find(id);
        return AnyTxnHttpResponse.success(cardCustomResDTO);
    }

    /**
     * 查询所有卡号个性化参数信息
     *
     * @return TxnTypeControlRes
     */
    @Operation(summary = "号卡号个性化参数分页查询", description = "分页查询")
    @GetMapping(value = "/param/cardCustom/pageNum/{pageNum}/pageSize/{pageSize}")
    public AnyTxnHttpResponse<PageResultDTO<CardCustomResDTO>> getAllPage(@PathVariable(value = "pageNum") Integer pageNum,
                                                                       @PathVariable(value = "pageSize") Integer pageSize) {
        PageResultDTO<CardCustomResDTO> response = cardCustomService.findPageByOrgNumber(pageNum, pageSize, OrgNumberUtils.getOrg());
        return AnyTxnHttpResponse.success(response);
    }

}
