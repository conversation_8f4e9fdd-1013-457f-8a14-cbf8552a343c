package com.anytech.anytxn.parameter.common.service;

import com.alibaba.fastjson.JSON;
import com.github.pagehelper.Page;
import com.github.pagehelper.PageHelper;
import com.anytech.anytxn.common.core.annotation.db.DeleteParameterAnnotation;
import com.anytech.anytxn.common.core.annotation.db.InsertParameterAnnotation;
import com.anytech.anytxn.common.core.annotation.db.UpdateParameterAnnotation;
import com.anytech.anytxn.business.base.audit.AbstractParameterService;
import com.anytech.anytxn.common.core.base.ParameterCompare;
import com.anytech.anytxn.business.base.audit.ParmModificationRecord;
import com.anytech.anytxn.common.core.base.PageResultDTO;
import com.anytech.anytxn.common.core.utils.BeanMapping;
import com.anytech.anytxn.common.core.utils.OrgNumberUtils;
import com.anytech.anytxn.common.sequence.utils.Number16IdGen;
import com.anytech.anytxn.common.core.utils.TenantUtils;
import com.anytech.anytxn.parameter.base.common.service.ISmsTemplateService;
import com.anytech.anytxn.parameter.base.common.enums.AnyTxnParameterRespCodeEnum;
import com.anytech.anytxn.parameter.base.common.exception.AnyTxnParameterException;
import com.anytech.anytxn.parameter.common.mapper.broadcast.ParmSmsTemplateMapper;
import com.anytech.anytxn.parameter.base.common.domain.model.ParmSmsTemplate;
import com.anytech.anytxn.parameter.base.common.domain.dto.SmsTemplateDTO;
import org.apache.commons.lang3.ObjectUtils;
//import org.apache.shardingsphere.transaction.annotation.ShardingSphereTransactionType;
//import org.apache.shardingsphere.transaction.core.TransactionType;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import jakarta.annotation.Resource;
import java.util.List;

/**
 * Description:
 * date: 2021/3/26 11:26
 *
 * <AUTHOR>
 */
@Service(value = "parm_sms_template_serviceImpl")
public class SmsTemplateServiceImpl extends AbstractParameterService implements ISmsTemplateService {

    @Resource
    private ParmSmsTemplateMapper parmSmsTemplateMapper;

    @Autowired
    private Number16IdGen numberIdGenerator;


    @Override
    @Transactional(rollbackFor = Exception.class,propagation = Propagation.REQUIRED)
    //@ShardingSphereTransactionType(TransactionType.XA)
    protected boolean updateDb(ParmModificationRecord parmModificationRecord) {

        //获取PARM_BODY json对象
        ParmSmsTemplate parmSmsTemplate = JSON.parseObject(parmModificationRecord.getParmBody(), ParmSmsTemplate.class);
        parmSmsTemplate.initUpdateDateTime();

        //转换成实体操作数据库
        int i = parmSmsTemplateMapper.updateByPrimaryKeySelective(parmSmsTemplate);

        return i == 1;
    }

    @Override
    @Transactional(rollbackFor = Exception.class,propagation = Propagation.REQUIRED)
    //@ShardingSphereTransactionType(TransactionType.XA)
    protected boolean insertDb(ParmModificationRecord parmModificationRecord) {
        ParmSmsTemplate parmSmsTemplate = JSON.parseObject(parmModificationRecord.getParmBody(), ParmSmsTemplate.class);
        parmSmsTemplate.initUpdateDateTime();
        parmSmsTemplate.initCreateDateTime();

        int i = parmSmsTemplateMapper.insertSelective(parmSmsTemplate);

        return i == 1;

    }

    @Override
    @Transactional(rollbackFor = Exception.class,propagation = Propagation.REQUIRED)
    //@ShardingSphereTransactionType(TransactionType.XA)
    protected boolean deleteDb(ParmModificationRecord parmModificationRecord) {

        ParmSmsTemplate parmSmsTemplate = JSON.parseObject(parmModificationRecord.getParmBody(), ParmSmsTemplate.class);
        int i = parmSmsTemplateMapper.deleteByPrimaryKey(parmSmsTemplate.getId());
        return i == 1;

    }










    @Override
    @InsertParameterAnnotation(tableName = "parm_sms_template", tableDesc = "Message Notification Template")
    public ParameterCompare add(SmsTemplateDTO smsTemplateDTO) {
        ParmSmsTemplate isExistCode = parmSmsTemplateMapper.selectByTemplateCode(smsTemplateDTO.getTemplateCode(), OrgNumberUtils.getOrg(smsTemplateDTO.getOrganizationNumber()));
        if(ObjectUtils.isNotEmpty(isExistCode)){
            throw new AnyTxnParameterException(AnyTxnParameterRespCodeEnum.D_EXIST_SMS_TEMPLATE_CODE_EXIST);
        }

        //校验通过返回实体对象
        ParmSmsTemplate parmSmsTemplate = BeanMapping.copy(smsTemplateDTO,ParmSmsTemplate.class);

        //实体 和dto 中的 id 都换成string类型
        parmSmsTemplate.setId(numberIdGenerator.generateId(TenantUtils.getTenantId())+"");
        return ParameterCompare
                .getBuilder()
                .withAfter(parmSmsTemplate)
                .build(ParmSmsTemplate.class);
    }




    @Override
    @UpdateParameterAnnotation(tableName = "parm_sms_template", tableDesc = "Message Notification Template")
    public ParameterCompare modify(SmsTemplateDTO smsTemplateDTO) {
        if(ObjectUtils.isEmpty(smsTemplateDTO)){
            throw new AnyTxnParameterException(AnyTxnParameterRespCodeEnum.P_PARAMETER_EMPTY_FAULT);
        }
        ParmSmsTemplate parmSmsTemplate = parmSmsTemplateMapper.selectByPrimaryKey(smsTemplateDTO.getId());
        if(ObjectUtils.isEmpty(parmSmsTemplate)){
            throw new AnyTxnParameterException(AnyTxnParameterRespCodeEnum.D_EXIST_SMS_TEMPLATE_ID_EXIST);
        }
        
        ParmSmsTemplate modify = BeanMapping.copy(smsTemplateDTO,ParmSmsTemplate.class);


        return ParameterCompare
                .getBuilder()
                .withAfter(modify)
                .withBefore(parmSmsTemplate)
                .build(ParmSmsTemplate.class);
    }

    @Override
    @DeleteParameterAnnotation(tableName = "parm_sms_template",tableDesc = "Message Notification Template")
    public ParameterCompare remove(String id) {
        if (null == id) {
            throw new AnyTxnParameterException(AnyTxnParameterRespCodeEnum.P_PARAMETER_EMPTY_FAULT);
        }
        ParmSmsTemplate isExistCode = parmSmsTemplateMapper.selectByPrimaryKey(id);
        if(ObjectUtils.isEmpty(isExistCode)){
            throw new AnyTxnParameterException(AnyTxnParameterRespCodeEnum.D_EXIST_SMS_TEMPLATE_ID_EXIST);
        }

        return ParameterCompare
                .getBuilder()
                .withBefore(isExistCode)
                .build(ParmSmsTemplate.class);
    }











    @Override
    public SmsTemplateDTO findById(String id) {
        if (null == id) {
            throw new AnyTxnParameterException(AnyTxnParameterRespCodeEnum.P_PARAMETER_EMPTY_FAULT);
        }
        ParmSmsTemplate parmSmsTemplate = parmSmsTemplateMapper.selectByPrimaryKey(id);
        if(ObjectUtils.isEmpty(parmSmsTemplate)){
            throw new AnyTxnParameterException(AnyTxnParameterRespCodeEnum.D_EXIST_SMS_TEMPLATE_ID_EXIST);
        }
        return BeanMapping.copy(parmSmsTemplate,SmsTemplateDTO.class);
    }



    @Override
    public PageResultDTO<SmsTemplateDTO> findByPage(Integer pageNum, Integer pageSize, String templateCode, String templateTitle, String channelCode,String organizationNumber) {
        Page<SmsTemplateDTO> page = PageHelper.startPage(pageNum, pageSize);
        List<ParmSmsTemplate> parmSmsTemplates = parmSmsTemplateMapper.selectAll(templateCode,templateTitle,channelCode,OrgNumberUtils.getOrg(organizationNumber));
        List<SmsTemplateDTO> smsTemplateDtos = BeanMapping.copyList(parmSmsTemplates,SmsTemplateDTO.class);
        return new PageResultDTO<>(pageNum, pageSize, page.getTotal(), page.getPages(), smsTemplateDtos);
    }



}
