package com.anytech.anytxn.parameter.account.controller;

import com.anytech.anytxn.parameter.base.common.domain.dto.system.HolidayListResDTO;
import io.swagger.v3.oas.annotations.tags.Tag;
import io.swagger.v3.oas.annotations.Operation;
import com.anytech.anytxn.common.core.base.ParameterCompare;
import com.anytech.anytxn.common.core.base.PageResultDTO;
import com.anytech.anytxn.common.core.base.AnyTxnHttpResponse;
import com.anytech.anytxn.common.core.base.BizBaseController;
import com.anytech.anytxn.parameter.base.account.domain.dto.AutoPaymentTableReqDTO;
import com.anytech.anytxn.parameter.base.account.domain.dto.AutoPaymentTableResDTO;
import com.anytech.anytxn.parameter.base.account.service.IAutoPaymentTableService;
import com.anytech.anytxn.parameter.base.common.enums.ParameterRepDetailEnum;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import jakarta.validation.Valid;
import java.util.ArrayList;


/**
 * Description    约定扣款参数管理
 * Copyright:	Copyright (c) 2018
 * Company:		江融信
 * Author:		lichao
 * Version:		1.0
 * Create at:	2018/11/7 2:38 PM
 *
 * 修改历史:
 * 日期    作者    版本  修改描述
 * ------------------------------------------------------------------
 *
 **/
@Tag(name = "约定扣款参数定义")
@RestController
public class AutoPaymentTableController extends BizBaseController {

    @Autowired
    private IAutoPaymentTableService autoPaymentTableService;

    /**
     * 根据机构号（organization_number），
     * 约定扣款参数表id（auto_payment_table_id），获得约定扣款参数表信息
     *
     * @param organizationNumber 机构号
     * @param tableId 表Id
     * @return
     */
    @Operation(summary = "通过机构号和表Id查询约定扣款参数信息", description = "通过机构号和表Id查询")
    @GetMapping(value = "/param/autoPayment/organizationNumber/{organizationNumber}/tableId/{tableId}")
    public AnyTxnHttpResponse<AutoPaymentTableResDTO> getAutoPaymentTableInfo(@PathVariable String organizationNumber, @PathVariable String tableId) {
        AutoPaymentTableResDTO res = autoPaymentTableService.findByAutoPaymentTableIdAndOrgNo(tableId,organizationNumber);
        return AnyTxnHttpResponse.success(res);
    }

    /**
     * 创建约定扣款信息
     * @param autoPaymentTableReqDTO 约定扣款请求数据
     * @return HttpApiResponse<AutoPaymentTableRes>
     */
    @PostMapping(value = "/param/autoPayment")
    @Operation(summary = "创建约定扣款信息")
    public AnyTxnHttpResponse<Object> create(@Valid @RequestBody AutoPaymentTableReqDTO autoPaymentTableReqDTO) {
        ParameterCompare autoPaymentTableResDTO = autoPaymentTableService.add(autoPaymentTableReqDTO);
        return AnyTxnHttpResponse.success(autoPaymentTableResDTO, ParameterRepDetailEnum.CREATE.message());
    }


    /**
     * 更新约定扣款信息
     * @param autoPaymentTableReqDTO 约定扣款请求数据
     * @return HttpApiResponse<AutoPaymentTableRes>
     */
    @PutMapping(value = "/param/autoPayment")
    @Operation(summary="根据id更新约定扣款信息", description = "")
    public AnyTxnHttpResponse<Object> modify(@Valid @RequestBody AutoPaymentTableReqDTO autoPaymentTableReqDTO) {
        ParameterCompare autoPaymentTableResDTO = autoPaymentTableService.modify(autoPaymentTableReqDTO);
        return AnyTxnHttpResponse.success(autoPaymentTableResDTO,ParameterRepDetailEnum.UPDATE.message());
    }

    /**
     * 删除约定扣款信息，通过id
     * @param id 技术id
     * @return HttpApiResponse<Boolean>
     */
    @DeleteMapping(value = "/param/autoPayment/id/{id}")
    @Operation(summary="根据id删除", description = "需传入id")
    public AnyTxnHttpResponse<Object> remove(@PathVariable String id) {
        ParameterCompare bool = autoPaymentTableService.remove(id);
        return AnyTxnHttpResponse.success(bool,ParameterRepDetailEnum.DEL.message());
    }

    /**
     * 根据根据id获取详情，进行编辑
     * @param id 产品信息id
     * @return HttpApiResponse<ProductInfoRes>
     */
    @Operation(summary = "获取约定扣款详情，通过id", description = "")
    @GetMapping(value = "/param/autoPayment/id/{id}")
    public AnyTxnHttpResponse<AutoPaymentTableResDTO> getByIndex(@PathVariable String id){
        AutoPaymentTableResDTO autoPaymentTableResDTO = autoPaymentTableService.find(id);
        return AnyTxnHttpResponse.success(autoPaymentTableResDTO);
    }

    /**
     * 查询所有还款分配定义参数信息
     *
     * @return TxnTypeControlRes
     */
    @Operation(summary = "约定扣款参数分页查询", description = "分页查询")
    @GetMapping(value = "/param/autoPayment/pageNum/{pageNum}/pageSize/{pageSize}")
    public AnyTxnHttpResponse<PageResultDTO<AutoPaymentTableResDTO>> getAllPage(@PathVariable(value = "pageNum") Integer pageNum,
                                                                             @PathVariable(value = "pageSize") Integer pageSize,
                                                                                @RequestParam(value = "tableId",required = false) String tableId,
                                                                                @RequestParam(value = "description",required = false) String description,
                                                                                @RequestParam(required = false) String organizationNumber) {
        PageResultDTO<AutoPaymentTableResDTO> response;
        response = autoPaymentTableService.findAll(pageNum, pageSize,tableId,description,organizationNumber);

        return AnyTxnHttpResponse.success(response);
    }

    /**
     * 通过表状态查询约定扣款参数
     *
     * @param status 参数表状态
     * @return
     */
    @Operation(summary = "通过表状态查询约定扣款参数", description = "通过表状态查询约定扣款参数")
    @GetMapping(value = "/param/autoPayment/status/{status}")
    public AnyTxnHttpResponse<ArrayList<AutoPaymentTableResDTO>> getByStatus(@PathVariable String status) {
        ArrayList<AutoPaymentTableResDTO> resList = (ArrayList)autoPaymentTableService.findByStatus(status);
        return AnyTxnHttpResponse.success(resList);
    }

    /**
     * @return AnyTxnHttpResponse<HolidayListRes>
     * @Description: 根据自扣表中提取日期参数表ID（holiday_list_id）读取自扣假日列表表数据
     * @Param holidayListId 假日列表id
     **/
    @Operation(summary = "根据自扣参数表的假日列表ID查询假日列表数据信息", description = "根据holidayListId")
    @GetMapping(value = "/param/autoPayment/holidayListId/{holidayListId}")
    public AnyTxnHttpResponse<HolidayListResDTO> getByHoliayInfo(@PathVariable String holidayListId, @RequestParam String organizationNumber) {
        HolidayListResDTO res;
        res = autoPaymentTableService.findByHolidayListId(holidayListId, organizationNumber);
        return AnyTxnHttpResponse.success(res);
    }


}
