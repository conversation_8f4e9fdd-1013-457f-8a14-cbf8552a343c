package com.anytech.anytxn.parameter.authorization.controller;

import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import com.anytech.anytxn.common.core.base.PageResultDTO;
import com.anytech.anytxn.common.core.base.AnyTxnHttpResponse;
import com.anytech.anytxn.common.core.base.BizBaseController;
import com.anytech.anytxn.parameter.base.authorization.domain.dto.MccGroupDetailDTO;
import com.anytech.anytxn.parameter.base.authorization.service.IMccGroupDetailService;
import com.anytech.anytxn.parameter.base.common.enums.ParameterRepDetailEnum;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import jakarta.validation.Valid;

/**
 * @description 商户类别群详细api
 * <AUTHOR>
 * @date 2019/4/4
 */

@RestController
@Tag(name = "商户类别群详细服务")
public class MccGroupDetailController extends BizBaseController {

    @Autowired
    private IMccGroupDetailService mccGroupDetailService;

    /**
     * @description 分页查询商户类别群详细列表
     * <AUTHOR>
     * @date 2019/4/4
     * @param page, rows 
     * @return jrx.anytxn.biz.common.web.AnyTxnHttpResponse<jrx.anytxn.biz.common.dto.PageResultDTO<jrx.anytxn.auth.dto.MccGroupDetailDTO>>
     */
    @Operation(summary = "分页查询商户类别群详细信息")
    @GetMapping("/param/mccGroupDetails/{page}/{rows}")
    public AnyTxnHttpResponse<PageResultDTO<MccGroupDetailDTO>> getMccGroupDetailList(@Parameter(name = "page", description = "当前页", example = "1")@PathVariable("page") int page ,
                                                                                      @Parameter(name = "rows", description = "每页大小", example = "8")@PathVariable("rows") int rows) {
        PageResultDTO<MccGroupDetailDTO> result = mccGroupDetailService.findListMccGroupDetail(page, rows);
        return AnyTxnHttpResponse.success(result);
    }

    
    /**
     * @description 根据主键查询商户类别群信息
     * <AUTHOR>
     * @date 2019/4/4
     * @param id 
     * @return jrx.anytxn.biz.common.web.AnyTxnHttpResponse<jrx.anytxn.auth.dto.MccGroupDetailDTO>
     */
    
    @Operation(summary = "根据主键查询商户类别群详细信息")
    @GetMapping("/param/mccGroupDetail/{id}")
    @Parameter(name = "id", description = "技术主键")
    public AnyTxnHttpResponse<MccGroupDetailDTO> getMccGroupDetail(@PathVariable(value = "id") Long id) {
        MccGroupDetailDTO mccGroupDetailDTO = mccGroupDetailService.findMccGroupDetail(id);
        return AnyTxnHttpResponse.success(mccGroupDetailDTO);
    }


    /**
     * @description 删除商户类别群详细信息
     * <AUTHOR>
     * @date 2019/4/4
     * @param id 
     * @return jrx.anytxn.biz.common.web.AnyTxnHttpResponse
     */
    
    @Operation(summary = "删除商户类别群详细信息")
    @DeleteMapping("/param/mccGroupDetail/{id}")
    public AnyTxnHttpResponse cancelMccGroupDetail(@PathVariable Long id) {
        Boolean flag = mccGroupDetailService.removeMccGroupDetail(id);
        return AnyTxnHttpResponse.success(flag, ParameterRepDetailEnum.DEL.message());
    }

    /**
     * @description 新增商户类别群详细信息
     * <AUTHOR>
     * @date 2019/4/4
     * @param mccGroupDetailDTO
     * @return jrx.anytxn.biz.common.web.AnyTxnHttpResponse
     */
    
    @Operation(summary = "新增商户类别群详细信息")
    @PostMapping("/param/mccGroupDetail")
    public AnyTxnHttpResponse create(@Valid @RequestBody MccGroupDetailDTO mccGroupDetailDTO) {
        Boolean flag = mccGroupDetailService.addMccGroupDetail(mccGroupDetailDTO);
        return AnyTxnHttpResponse.success(flag,ParameterRepDetailEnum.CREATE.message());
    }

    /**
     * @description 商户类别群详细信息修改
     * <AUTHOR>
     * @date 2019/4/4
     * @param mccGroupDetailDTO
     * @return jrx.anytxn.biz.common.web.AnyTxnHttpResponse
     */
    
    @Operation(summary = "商户类别群详细信息修改")
    @PutMapping("/param/mccGroupDetail")
    public AnyTxnHttpResponse modify(@Valid @RequestBody MccGroupDetailDTO mccGroupDetailDTO) {
        mccGroupDetailService.modifyMccGroupDetail(mccGroupDetailDTO);
        return AnyTxnHttpResponse.success();
    }

}
