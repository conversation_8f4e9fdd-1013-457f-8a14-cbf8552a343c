package com.anytech.anytxn.parameter.account.controller;

import io.swagger.v3.oas.annotations.tags.Tag;
import io.swagger.v3.oas.annotations.Operation;
import com.anytech.anytxn.common.core.utils.OrgNumberUtils;
import com.anytech.anytxn.common.core.base.PageResultDTO;
import com.anytech.anytxn.common.core.base.AnyTxnHttpResponse;
import com.anytech.anytxn.common.core.base.BizBaseController;
import com.anytech.anytxn.parameter.base.account.domain.dto.BlockCodeAccountReqDTO;
import com.anytech.anytxn.parameter.base.account.domain.dto.BlockCodeAccountResDTO;
import com.anytech.anytxn.parameter.base.account.service.IBlockCodeAccountService;
import com.anytech.anytxn.parameter.base.common.enums.ParameterRepDetailEnum;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import jakarta.validation.Valid;


/**
 * 账户层封锁码 api
 * <AUTHOR>
 * @date 2018-08-15
 **/
@Tag(name = "账户层封锁码")
@RestController
public class BlockCodeAccountController extends BizBaseController {

    @Autowired
    private IBlockCodeAccountService blockCodeAccountService;

    /**
     * business需要的接口
     * 获取账户层封锁码参数表
     * @param organizationNumber      机构号
     * @param tableId                 参数表ID
     * @param blockCode               卡片封锁码
     * @return HttpApiResponse<BlockCodeAccountRes>
     */
    @Operation(summary = "获取账户层封锁码参数")
    @GetMapping(value = "/param/blockCodeAccount/organizationNumber/{organizationNumber}/tableId/{tableId}/blockCode/{blockCode}")
    public AnyTxnHttpResponse<BlockCodeAccountResDTO> getBlockCodeAccount(@PathVariable String organizationNumber,
                                                                          @PathVariable String tableId,
                                                                          @PathVariable String blockCode) {
        BlockCodeAccountResDTO blockCodeAccountRes = blockCodeAccountService.findBlockCodeAccount(organizationNumber, tableId, blockCode);
        return AnyTxnHttpResponse.success(blockCodeAccountRes);
    }

    /**
     * business需要的接口
     * 获取账户层封锁码参数表
     * @param organizationNumber      机构号
     * @param tableId                 参数表ID
     * @return HttpApiResponse<BlockCodeAccountRes>
     */
    @Operation(summary = "根据机构号和tableId获取账户层封锁码参数")
    @GetMapping(value = "/param/blockCodeAccount/organizationNumber/{organizationNumber}/tableId/{tableId}")
    public AnyTxnHttpResponse<BlockCodeAccountResDTO> getBlockCodeAccount(@PathVariable String organizationNumber,
                                                                       @PathVariable String tableId) {
        BlockCodeAccountResDTO blockCodeAccountRes = blockCodeAccountService.findBlockCodeAccount(organizationNumber, tableId, null);
        return AnyTxnHttpResponse.success(blockCodeAccountRes);
    }

    /**
     * 创建账户层封锁码
     * @param blockCodeAccountReqDTO 账户层封锁码请求数据
     * @return 账户层封锁码详情
     */
    @Operation(summary = "创建账户层封锁码")
    @PostMapping(value = "/param/blockCodeAccount")
    public AnyTxnHttpResponse<BlockCodeAccountResDTO> create(@Valid @RequestBody BlockCodeAccountReqDTO blockCodeAccountReqDTO) {
        BlockCodeAccountResDTO blockCodeAccountRes = blockCodeAccountService.add(blockCodeAccountReqDTO);
        return AnyTxnHttpResponse.success(blockCodeAccountRes, ParameterRepDetailEnum.CREATE.message());
    }

    /**
     * 更新账户层封锁码
     * @param blockCodeAccountReqDTO 账户层封锁码请求数据
     * @return 账户层封锁码详情
     */
    @Operation(summary = "更新账户层封锁码")
    @PutMapping(value = "/param/blockCodeAccount")
    public AnyTxnHttpResponse<BlockCodeAccountResDTO> modify(@Valid @RequestBody BlockCodeAccountReqDTO blockCodeAccountReqDTO) {
        BlockCodeAccountResDTO blockCodeAccountRes = blockCodeAccountService.modify(blockCodeAccountReqDTO);
        return AnyTxnHttpResponse.success(blockCodeAccountRes,ParameterRepDetailEnum.UPDATE.message());
    }

    /**
     * 删除账户层封锁码，通过id
     * @param id 账户层封锁码id
     * @return
     */
    @Operation(summary = "删除账户层封锁码，通过id")
    @DeleteMapping(value = "/param/blockCodeAccount/id/{id}")
    public AnyTxnHttpResponse<Boolean> remove(@PathVariable Long id) {
        Boolean deleted = blockCodeAccountService.remove(id);
        return AnyTxnHttpResponse.success(deleted,ParameterRepDetailEnum.DEL.message());
    }

    /**
     * 通过id获取账户层封锁码详情
     * @param id 账户层封锁码id
     * @return 账户层封锁码详情
     */
    @Operation(summary = "通过id获取账户层封锁码详情")
    @GetMapping(value = "/param/blockCodeAccount/id/{id}")
    public AnyTxnHttpResponse<BlockCodeAccountResDTO> get(@PathVariable Long id) {
        BlockCodeAccountResDTO blockCodeAccountRes = blockCodeAccountService.find(id);
        return AnyTxnHttpResponse.success(blockCodeAccountRes);
    }


    /**
     * 分页查询，当前机构下账户层封锁码
     *
     * @param pageNum  页码
     * @param pageSize 每页大小
     * @param status   启用状态，不传查询所有状态，取值0:禁用|1:启用
     * @return
     */
    @Operation(summary = "分页查询，当前机构下账户层封锁码")
    @GetMapping(value = "/param/blockCodeAccount/pageNum/{pageNum}/pageSize/{pageSize}/status/{status}")
    public AnyTxnHttpResponse<PageResultDTO<BlockCodeAccountResDTO>> getPageByOrgNumber(@PathVariable(value = "pageNum") Integer pageNum,
                                                                                     @PathVariable(value = "pageSize") Integer pageSize,
                                                                                     @PathVariable(value = "status") String status) {

        PageResultDTO<BlockCodeAccountResDTO> page = blockCodeAccountService.findPageByOrgNumber(pageNum, pageSize, OrgNumberUtils.getOrg(), status);
        return AnyTxnHttpResponse.success(page);
    }
}
