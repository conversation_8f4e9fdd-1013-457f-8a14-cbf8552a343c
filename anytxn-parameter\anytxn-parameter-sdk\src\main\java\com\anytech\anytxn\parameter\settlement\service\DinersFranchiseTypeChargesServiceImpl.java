package com.anytech.anytxn.parameter.settlement.service;

import com.anytech.anytxn.parameter.base.transaction.domain.model.ParmTxnCodeConvert;
import com.anytech.anytxn.parameter.base.settlement.domain.bo.InfonetFileBO;
import com.anytech.anytxn.parameter.transaction.mapper.unicast.ParmTxnCodeConvertMapper;
import com.anytech.anytxn.parameter.transaction.mapper.unicast.ParmTxnCodeConvertSelfMapper;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.HashSet;
import java.util.List;

@Slf4j
@Service
public class DinersFranchiseTypeChargesServiceImpl {

    @Autowired
    private ParmTxnCodeConvertMapper parmTxnCodeConvertMapper;
    @Autowired
    private ParmTxnCodeConvertSelfMapper parmTxnCodeConvertSelfMapper;

    public void updateXmlTypeCharges(InfonetFileBO.TypeCharges typeCharges){
        List<ParmTxnCodeConvert> codeConvertList = new ArrayList<>();
        StringBuffer codeDe = new StringBuffer();
        StringBuffer codeCr = new StringBuffer();
        HashSet<String> set = new HashSet<>();
        HashSet<String> set2 = new HashSet<>();
        List<ParmTxnCodeConvert> parmTxnCodeConverts = parmTxnCodeConvertSelfMapper.selectByDebitCreditIndicator("D");
        List<ParmTxnCodeConvert> parmTxnCodeConverts2 = parmTxnCodeConvertSelfMapper.selectByDebitCreditIndicator("C");
        //
        if (!"0".equals(parmTxnCodeConverts.size())){
            for (ParmTxnCodeConvert parm : parmTxnCodeConverts) {
                String[] g = parm.getTypch().split(",");
                for (String d : g) {
                    set2.add(d);
                }
            }
        }else {
            log.info("没有debit类型的数据");
        }
        if (!"0".equals(parmTxnCodeConverts2.size())){
            for (ParmTxnCodeConvert parm : parmTxnCodeConverts2) {
                String[] f = parm.getTypch().split(",");
                for (String d : f) {
                    set.add(d);
                }
            }
        }else {
            log.info("没有credit类型的数据");
        }


        for (InfonetFileBO.TypeCharge typeCharge: typeCharges.getTypeCharge()){

            if (StringUtils.isNotEmpty(typeCharge.getDrCrInd()) && StringUtils.isNotEmpty(typeCharge.getTypeCharge())){
            if ("D".equals(typeCharge.getDrCrInd())){
                if (!set2.contains(typeCharge.getTypeCharge())){
                    codeDe.append(typeCharge.getTypeCharge()+",");
                }else {
                    log.info("该Debit类型已存在：{}",typeCharge.getTypeCharge());
                }
            }else if ("C".equals(typeCharge.getDrCrInd())){
                if (!set.contains(typeCharge.getTypeCharge())){
                    codeCr.append(typeCharge.getTypeCharge()+",");
                }else {
                    log.info("该Credit类型已存在：{}",typeCharge.getTypeCharge());
                }
            }
            }else {
                log.info("本次没有更新，因为{}或{}为空", typeCharge.getDrCrInd(),typeCharge.getTypeCharge());
            }
            }
               for (ParmTxnCodeConvert parm : parmTxnCodeConverts){
                    ParmTxnCodeConvert parmTxnCodeConvert = new ParmTxnCodeConvert();
                    parmTxnCodeConvert.setTypch(codeDe.toString()+parm.getTypch());
                    parmTxnCodeConvert.setId(parm.getId());
                    parmTxnCodeConvert.setDebitCreditIndicator(parm.getDebitCreditIndicator());
                    codeConvertList.add(parmTxnCodeConvert);
                }

                for (ParmTxnCodeConvert parm : parmTxnCodeConverts2){
                    ParmTxnCodeConvert parmTxnCodeConvert = new ParmTxnCodeConvert();
                    parmTxnCodeConvert.setTypch(codeCr.toString()+parm.getTypch());
                    parmTxnCodeConvert.setId(parm.getId());
                    parmTxnCodeConvert.setDebitCreditIndicator(parm.getDebitCreditIndicator());
                    codeConvertList.add(parmTxnCodeConvert);
                }

            if (!codeConvertList.isEmpty()){
                for (int i = 0; i<codeConvertList.size(); i++){
                    parmTxnCodeConvertMapper.updateByPrimaryKeySelective(codeConvertList.get(i));
                    log.info("其中一种类型更新成功，是{}和{}", codeConvertList.get(i).getDebitCreditIndicator(),codeConvertList.get(i).getTypch());
                }

            }

    }

}
