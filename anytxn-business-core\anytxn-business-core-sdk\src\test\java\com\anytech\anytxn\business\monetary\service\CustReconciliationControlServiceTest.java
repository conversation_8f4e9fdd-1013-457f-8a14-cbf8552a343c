package com.anytech.anytxn.business.monetary.service;

import com.anytech.anytxn.business.base.monetary.domain.dto.CustReconciliationControlDTO;
import com.anytech.anytxn.business.base.monetary.enums.AnyTxnCustAccountLockRespCodeEnum;
import com.anytech.anytxn.business.base.monetary.enums.AnyTxnCustAccountRespCodeEnum;
import com.anytech.anytxn.business.base.monetary.exception.AnyTxnCustAccountException;
import com.anytech.anytxn.business.base.monetary.exception.AnyTxnCustAccountLockException;
import com.anytech.anytxn.business.dao.monetary.mapper.CustReconciliationControlSelfMapper;
import com.anytech.anytxn.business.dao.monetary.model.CustReconciliationControl;
import com.anytech.anytxn.common.core.utils.BeanMapping;
import com.anytech.anytxn.common.core.utils.OrgNumberUtils;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockedStatic;
import org.mockito.junit.jupiter.MockitoExtension;

import java.time.LocalDate;
import java.time.LocalDateTime;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.*;

/**
 * CustReconciliationControlServiceImpl 测试类
 * 
 * <AUTHOR>
 * @date 2024-01-01
 */
@ExtendWith(MockitoExtension.class)
class CustReconciliationControlServiceTest {

    @Mock
    private CustReconciliationControlSelfMapper custReconciliationControlSelfMapper;

    @InjectMocks
    private CustReconciliationControlServiceImpl custReconciliationControlService;

    private CustReconciliationControlDTO mockControlDTO;

    @BeforeEach
    void setUp() {
        try (MockedStatic<OrgNumberUtils> orgUtilsMock = mockStatic(OrgNumberUtils.class)) {
            // Mock OrgNumberUtils for BaseEntity constructor
            orgUtilsMock.when(OrgNumberUtils::getOrg).thenReturn("TEST_ORG");
            
            // 准备测试数据
            mockControlDTO = new CustReconciliationControlDTO();
            mockControlDTO.setId("CUST001");
            mockControlDTO.setCustomerId("CUSTOMER001");
            mockControlDTO.setVersionNumber(1L);
            mockControlDTO.setOptmisticLockCount(0L);
            mockControlDTO.setReconciliationDate(LocalDate.of(2024, 1, 1));
            mockControlDTO.setBatchStatus(1L);
            mockControlDTO.setOrganizationNumber("ORG001");
        }
    }

    /**
     * 测试方法：shouldCommitLock_whenValidParametersProvided
     * 用来测试 CustReconciliationControlServiceImpl 方法 commitLock
     * 验证当提供有效参数时，能够成功提交锁定
     */
    @Test
    void shouldCommitLock_whenValidParametersProvided() {
        try (MockedStatic<OrgNumberUtils> orgUtilsMock = mockStatic(OrgNumberUtils.class)) {
            // Mock OrgNumberUtils for BaseEntity constructor
            orgUtilsMock.when(OrgNumberUtils::getOrg).thenReturn("TEST_ORG");
            
            // Arrange - 准备测试数据和环境
            when(custReconciliationControlSelfMapper.updateLock(
                    eq(mockControlDTO.getId()),
                    eq(mockControlDTO.getVersionNumber()),
                    eq(mockControlDTO.getReconciliationDate()),
                    eq(mockControlDTO.getOptmisticLockCount()),
                    eq(mockControlDTO.getOptmisticLockCount() + 1),
                    any(LocalDateTime.class)
            )).thenReturn(1);

            // Act - 执行被测方法
            custReconciliationControlService.commitLock(mockControlDTO);

            // Assert - 验证结果
            verify(custReconciliationControlSelfMapper).updateLock(
                    eq("CUST001"),
                    eq(1L), // 使用原始版本号
                    eq(LocalDate.of(2024, 1, 1)),
                    eq(0L), // 使用原始乐观锁计数
                    eq(1L), // 新的乐观锁计数是原始值+1
                    any(LocalDateTime.class)
            );

            // 验证乐观锁计数和版本号是否更新
            assertEquals(1L, mockControlDTO.getOptmisticLockCount());
            assertEquals(2L, mockControlDTO.getVersionNumber());
        }
    }

    /**
     * 测试方法：shouldThrowException_whenUpdateLockFails
     * 用来测试 CustReconciliationControlServiceImpl 方法 commitLock
     * 验证当更新失败时，应抛出异常
     */
    @Test
    void shouldThrowException_whenUpdateLockFails() {
        try (MockedStatic<OrgNumberUtils> orgUtilsMock = mockStatic(OrgNumberUtils.class)) {
            // Mock OrgNumberUtils for BaseEntity constructor
            orgUtilsMock.when(OrgNumberUtils::getOrg).thenReturn("TEST_ORG");
            
            // Arrange - 准备测试数据和环境
            when(custReconciliationControlSelfMapper.updateLock(
                    anyString(), anyLong(), any(LocalDate.class), anyLong(), anyLong(), any(LocalDateTime.class)
            )).thenReturn(0); // 返回0表示更新失败

            // Act & Assert - 执行并验证异常
            AnyTxnCustAccountLockException exception = assertThrows(
                    AnyTxnCustAccountLockException.class,
                    () -> custReconciliationControlService.commitLock(mockControlDTO)
            );

            // 验证异常信息
            assertNotNull(exception);
            assertEquals(AnyTxnCustAccountLockRespCodeEnum.D_LOCK_ERROR.getCode(), exception.getErrCode());
        }
    }

    /**
     * 测试方法：shouldCommitLockAndReStatus_whenValidParametersProvided
     * 用来测试 CustReconciliationControlServiceImpl 方法 commitLockAndReStatus
     * 验证当提供有效参数时，能够成功提交锁定并重置状态
     */
    @Test
    void shouldCommitLockAndReStatus_whenValidParametersProvided() {
        try (MockedStatic<OrgNumberUtils> orgUtilsMock = mockStatic(OrgNumberUtils.class)) {
            // Mock OrgNumberUtils for BaseEntity constructor
            orgUtilsMock.when(OrgNumberUtils::getOrg).thenReturn("TEST_ORG");
            
            // Arrange - 准备测试数据和环境
            Long originalBatchStatus = mockControlDTO.getBatchStatus();
            when(custReconciliationControlSelfMapper.updateLock(
                    anyString(), anyLong(), any(LocalDate.class), anyLong(), anyLong(), any(LocalDateTime.class)
            )).thenReturn(1);

            // Act - 执行被测方法
            custReconciliationControlService.commitLockAndReStatus(mockControlDTO);

            // Assert - 验证结果
            assertEquals(0L, mockControlDTO.getBatchStatus());
            verify(custReconciliationControlSelfMapper).updateLock(
                    anyString(), anyLong(), any(LocalDate.class), anyLong(), anyLong(), any(LocalDateTime.class)
            );
        }
    }

    /**
     * 测试方法：shouldRestoreStatus_whenCommitLockAndReStatusFails
     * 用来测试 CustReconciliationControlServiceImpl 方法 commitLockAndReStatus
     * 验证当提交失败时，能够恢复原始状态
     */
    @Test
    void shouldRestoreStatus_whenCommitLockAndReStatusFails() {
        try (MockedStatic<OrgNumberUtils> orgUtilsMock = mockStatic(OrgNumberUtils.class)) {
            // Mock OrgNumberUtils for BaseEntity constructor
            orgUtilsMock.when(OrgNumberUtils::getOrg).thenReturn("TEST_ORG");
            
            // Arrange - 准备测试数据和环境
            Long originalBatchStatus = mockControlDTO.getBatchStatus();
            when(custReconciliationControlSelfMapper.updateLock(
                    anyString(), anyLong(), any(LocalDate.class), anyLong(), anyLong(), any(LocalDateTime.class)
            )).thenReturn(0); // 模拟更新失败

            // Act & Assert - 执行并验证异常
            assertThrows(AnyTxnCustAccountLockException.class, () -> {
                custReconciliationControlService.commitLockAndReStatus(mockControlDTO);
            });

            // 验证状态恢复
            assertEquals(originalBatchStatus, mockControlDTO.getBatchStatus());
        }
    }

    /**
     * 测试方法：shouldUpdateStatus_whenValidParametersProvided
     * 用来测试 CustReconciliationControlServiceImpl 方法 updateStatus
     * 验证当提供有效参数时，能够成功更新状态
     */
    @Test
    void shouldUpdateStatus_whenValidParametersProvided() {
        // Arrange - 准备测试数据和环境
        String id = "CUST001";
        Long oldStatus = 1L;

        // Act - 执行被测方法
        custReconciliationControlService.updateStatus(id, oldStatus);

        // Assert - 验证结果
        verify(custReconciliationControlSelfMapper).updateByStatus(eq(id), eq(oldStatus));
    }

    /**
     * 测试方法：shouldGetBillingDate_whenValidParametersProvided
     * 用来测试 CustReconciliationControlServiceImpl 方法 getBillingDate
     * 验证当提供有效参数时，能够正确获取账单日期
     */
    @Test
    void shouldGetBillingDate_whenValidParametersProvided() {
        try (MockedStatic<OrgNumberUtils> orgUtilsMock = mockStatic(OrgNumberUtils.class)) {
            // Mock OrgNumberUtils for BaseEntity constructor
            orgUtilsMock.when(OrgNumberUtils::getOrg).thenReturn("TEST_ORG");
            
            // Arrange - 准备测试数据和环境
            LocalDate accruedThruDay = LocalDate.of(2024, 1, 1);
            LocalDate today = LocalDate.of(2024, 1, 2);
            LocalDate nextProcessingDay = LocalDate.of(2024, 1, 3);
            
            // 设置业务日期等于累计日期
            mockControlDTO.setReconciliationDate(accruedThruDay);

            // Act - 执行被测方法
            LocalDate result = custReconciliationControlService.getBillingDate(
                    mockControlDTO, accruedThruDay, today, nextProcessingDay);

            // Assert - 验证结果
            assertEquals(nextProcessingDay, result);
        }
    }

    /**
     * 测试方法：shouldReturnToday_whenReconciliationDateBeforeAccruedThruDay
     * 用来测试 CustReconciliationControlServiceImpl 方法 getBillingDate
     * 验证当对账日期小于累计日期时，返回今天日期
     */
    @Test
    void shouldReturnToday_whenReconciliationDateBeforeAccruedThruDay() {
        try (MockedStatic<OrgNumberUtils> orgUtilsMock = mockStatic(OrgNumberUtils.class)) {
            // Mock OrgNumberUtils for BaseEntity constructor
            orgUtilsMock.when(OrgNumberUtils::getOrg).thenReturn("TEST_ORG");
            
            // Arrange - 准备测试数据和环境
            LocalDate accruedThruDay = LocalDate.of(2024, 1, 2);
            LocalDate today = LocalDate.of(2024, 1, 3);
            LocalDate nextProcessingDay = LocalDate.of(2024, 1, 4);
            
            // 设置业务日期小于累计日期
            mockControlDTO.setReconciliationDate(LocalDate.of(2024, 1, 1));

            // Act - 执行被测方法
            LocalDate result = custReconciliationControlService.getBillingDate(
                    mockControlDTO, accruedThruDay, today, nextProcessingDay);

            // Assert - 验证结果
            assertEquals(today, result);
        }
    }

    /**
     * 测试方法：shouldThrowException_whenReconciliationDateAfterAccruedThruDay
     * 用来测试 CustReconciliationControlServiceImpl 方法 getBillingDate
     * 验证当对账日期大于累计日期时，应抛出异常
     */
    @Test
    void shouldThrowException_whenReconciliationDateAfterAccruedThruDay() {
        try (MockedStatic<OrgNumberUtils> orgUtilsMock = mockStatic(OrgNumberUtils.class)) {
            // Mock OrgNumberUtils for BaseEntity constructor
            orgUtilsMock.when(OrgNumberUtils::getOrg).thenReturn("TEST_ORG");
            
            // Arrange - 准备测试数据和环境
            LocalDate accruedThruDay = LocalDate.of(2024, 1, 1);
            LocalDate today = LocalDate.of(2024, 1, 2);
            LocalDate nextProcessingDay = LocalDate.of(2024, 1, 3);
            
            // 设置业务日期大于累计日期
            mockControlDTO.setReconciliationDate(LocalDate.of(2024, 1, 2));

            // Act & Assert - 执行并验证异常
            AnyTxnCustAccountException exception = assertThrows(
                    AnyTxnCustAccountException.class,
                    () -> custReconciliationControlService.getBillingDate(
                            mockControlDTO, accruedThruDay, today, nextProcessingDay)
            );

            // 验证异常信息
            assertNotNull(exception);
            assertEquals(AnyTxnCustAccountRespCodeEnum.P_DATE_ERROR.getCode(), exception.getErrCode());
        }
    }

    /**
     * 测试方法：shouldReturnNextProcessingDay_whenControlDTOIsNull
     * 用来测试 CustReconciliationControlServiceImpl 方法 getBillingDate
     * 验证当控制DTO为空时，返回下一处理日
     */
    @Test
    void shouldReturnNextProcessingDay_whenControlDTOIsNull() {
        // Arrange - 准备测试数据和环境
        LocalDate accruedThruDay = LocalDate.of(2024, 1, 1);
        LocalDate today = LocalDate.of(2024, 1, 2);
        LocalDate nextProcessingDay = LocalDate.of(2024, 1, 3);

        // Act - 执行被测方法
        LocalDate result = custReconciliationControlService.getBillingDate(
                null, accruedThruDay, today, nextProcessingDay);

        // Assert - 验证结果
        assertEquals(nextProcessingDay, result);
    }

    /**
     * 测试方法：shouldGetControl_whenValidParametersProvided
     * 用来测试 CustReconciliationControlServiceImpl 方法 getControl
     * 验证当提供有效参数时，能够成功获取控制信息
     */
    @Test
    void shouldGetControl_whenValidParametersProvided() {
        try (MockedStatic<OrgNumberUtils> orgUtilsMock = mockStatic(OrgNumberUtils.class);
             MockedStatic<BeanMapping> beanMappingMock = mockStatic(BeanMapping.class)) {
            
            // Mock OrgNumberUtils for BaseEntity constructor
            orgUtilsMock.when(OrgNumberUtils::getOrg).thenReturn("TEST_ORG");
            
            // Arrange - 准备测试数据和环境
            String customerId = "CUSTOMER001";
            String orgNum = "ORG001";
            
            CustReconciliationControl mockControl = new CustReconciliationControl();
            mockControl.setId("CUST001");
            mockControl.setCustomerId(customerId);
            
            when(custReconciliationControlSelfMapper.selectByCustAndOrg(eq(customerId), eq(orgNum)))
                    .thenReturn(mockControl);
            
            CustReconciliationControlDTO expectedDTO = new CustReconciliationControlDTO();
            expectedDTO.setId("CUST001");
            expectedDTO.setCustomerId(customerId);
            
            beanMappingMock.when(() -> BeanMapping.copy(eq(mockControl), eq(CustReconciliationControlDTO.class)))
                    .thenReturn(expectedDTO);

            // Act - 执行被测方法
            CustReconciliationControlDTO result = custReconciliationControlService.getControl(customerId, orgNum);

            // Assert - 验证结果
            assertNotNull(result);
            assertEquals(expectedDTO.getId(), result.getId());
            assertEquals(expectedDTO.getCustomerId(), result.getCustomerId());
            
            verify(custReconciliationControlSelfMapper).selectByCustAndOrg(eq(customerId), eq(orgNum));
        }
    }

    /**
     * 测试方法：shouldReturnNull_whenControlNotFound
     * 用来测试 CustReconciliationControlServiceImpl 方法 getControl
     * 验证当未找到控制信息时，返回null
     */
    @Test
    void shouldReturnNull_whenControlNotFound() {
        // Arrange - 准备测试数据和环境
        String customerId = "CUSTOMER001";
        String orgNum = "ORG001";
        
        when(custReconciliationControlSelfMapper.selectByCustAndOrg(eq(customerId), eq(orgNum)))
                .thenReturn(null);

        // Act - 执行被测方法
        CustReconciliationControlDTO result = custReconciliationControlService.getControl(customerId, orgNum);

        // Assert - 验证结果
        assertNull(result);
        verify(custReconciliationControlSelfMapper).selectByCustAndOrg(eq(customerId), eq(orgNum));
    }

    /**
     * 测试方法：shouldLogWarning_whenCommitLockWithNullParameter
     * 用来测试 CustReconciliationControlServiceImpl 方法 commitLock
     * 验证当参数为null时，应记录警告日志
     */
    @Test
    void shouldLogWarning_whenCommitLockWithNullParameter() {
        // Act - 执行被测方法
        custReconciliationControlService.commitLock(null);

        // Assert - 验证不会调用mapper
        verify(custReconciliationControlSelfMapper, never()).updateLock(
                anyString(), anyLong(), any(LocalDate.class), anyLong(), anyLong(), any(LocalDateTime.class));
    }
} 