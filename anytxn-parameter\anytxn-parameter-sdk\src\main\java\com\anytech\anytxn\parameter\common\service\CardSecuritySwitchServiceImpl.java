package com.anytech.anytxn.parameter.common.service;

import com.github.pagehelper.Page;
import com.github.pagehelper.PageHelper;
import com.anytech.anytxn.common.core.utils.OrgNumberUtils;
import com.anytech.anytxn.parameter.base.common.constants.Constants;
import com.anytech.anytxn.common.core.base.PageResultDTO;
import com.anytech.anytxn.common.core.utils.BeanMapping;
import com.anytech.anytxn.common.sequence.utils.Number16IdGen;
import com.anytech.anytxn.common.core.utils.TenantUtils;
import com.anytech.anytxn.parameter.base.common.domain.dto.CardSecuritySwitchReqDTO;
import com.anytech.anytxn.parameter.base.common.domain.dto.CardSecuritySwitchResDTO;
import com.anytech.anytxn.parameter.base.common.service.ICardSecuritySwitchService;
import com.anytech.anytxn.parameter.base.common.enums.AnyTxnParameterRespCodeEnum;
import com.anytech.anytxn.parameter.base.common.exception.AnyTxnParameterException;
import com.anytech.anytxn.parameter.common.mapper.broadcast.ParmCardSecuritySwitchMapper;
import com.anytech.anytxn.parameter.common.mapper.broadcast.ParmCardSecuritySwitchSelfMapper;
import com.anytech.anytxn.parameter.base.common.domain.model.ParmCardSecuritySwitch;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.util.List;

/**
 * 持卡人安全开关特别检查表
 *
 * <AUTHOR>
 * @date 2019-04-10
 **/
@Service
public class CardSecuritySwitchServiceImpl implements ICardSecuritySwitchService {

    private static Logger log = LoggerFactory.getLogger(CardSecuritySwitchServiceImpl.class);

    @Autowired
    private ParmCardSecuritySwitchSelfMapper parmCardSecuritySwitchSelfMapper;
    @Autowired
    private ParmCardSecuritySwitchMapper parmCardSecuritySwitchMapper;

    @Autowired
    private Number16IdGen numberIdGenerator;

    /**
     * 根据卡号查询持卡人安全开关特别检查表
     * @param cardNumber 卡号
     * @return List<CardSecuritySwitchResDTO>
     */
    @Override
    public List<CardSecuritySwitchResDTO> findListByCardNum(String cardNumber) {
        if(StringUtils.isBlank(cardNumber)){
            throw new AnyTxnParameterException(AnyTxnParameterRespCodeEnum.P_CARD_NUMBER_EMPTY_FAULT);
        }
        List<ParmCardSecuritySwitch> parmCardSecuritySwitches = parmCardSecuritySwitchSelfMapper.selectByCardNumber(cardNumber, OrgNumberUtils.getOrg());
        if(parmCardSecuritySwitches==null||parmCardSecuritySwitches.isEmpty()){
            log.error("持卡人安全开关特别检查表，通过卡号:{}未查到数据", cardNumber);
            return null;
        }
        return BeanMapping.copyList(parmCardSecuritySwitches, CardSecuritySwitchResDTO.class);
    }

    /**
     * 新增号查询持卡人安全开关特别检查参数
     * @param cardSecuritySwitchReq 持卡人安全开关特别检查参数
     * @return CardSecuritySwitchResDTO
     */
    @Override
    //@AfterInsertProcess(cacheObject = CardSecuritySwitchResDTO.class)
    public CardSecuritySwitchResDTO add(CardSecuritySwitchReqDTO cardSecuritySwitchReq) {
        ParmCardSecuritySwitch parmCardSecuritySwitch = BeanMapping.copy(cardSecuritySwitchReq, ParmCardSecuritySwitch.class);
        parmCardSecuritySwitch.setCreateTime(LocalDateTime.now());
        parmCardSecuritySwitch.setUpdateTime(LocalDateTime.now());
        parmCardSecuritySwitch.setUpdateBy(Constants.DEFAULT_USER);
        parmCardSecuritySwitch.setVersionNumber(1);
        parmCardSecuritySwitch.setId(numberIdGenerator.generateId(TenantUtils.getTenantId()));
        parmCardSecuritySwitchMapper.insertSelective(parmCardSecuritySwitch);
        cardSecuritySwitchReq.setId(parmCardSecuritySwitch.getId());
        return BeanMapping.copy(parmCardSecuritySwitch, CardSecuritySwitchResDTO.class);
    }

    /**
     * 根据id查询
     * @param id id
     * @return CardSecuritySwitchResDTO
     */
    @Override
    //@PreGetProcess(byPrimaryKey = true)
    public CardSecuritySwitchResDTO findOne(Long id) {
        if (id == null) {
            throw new AnyTxnParameterException(AnyTxnParameterRespCodeEnum.P_PARAMETER_EMPTY_FAULT);
        }

        ParmCardSecuritySwitch parmCardSecuritySwitch = parmCardSecuritySwitchMapper.selectByPrimaryKey(id);

        if (parmCardSecuritySwitch == null) {
            log.error("查询持卡人安全开关特别检查参数，通过id:{}未查到数据", id);
            throw new AnyTxnParameterException(AnyTxnParameterRespCodeEnum.D_QUERY_CARD_SECURITY_SWITCH_FAULT);
        }

        return BeanMapping.copy(parmCardSecuritySwitch, CardSecuritySwitchResDTO.class);
    }

    /**
     * 查询列表 分页
     * @param pageNum 页码
     * @param pageSize 页面容量
     * @return PageResultDTO<CardSecuritySwitchResDTO>
     */
    @Override
    public PageResultDTO<CardSecuritySwitchResDTO> findPage(Integer pageNum, Integer pageSize) {
        log.debug("分页查询持卡人安全开关特别检查参数");
        Page<ParmCardSecuritySwitch> page = PageHelper.startPage(pageNum, pageSize);
        List<ParmCardSecuritySwitch> cardSecuritySwitches = parmCardSecuritySwitchSelfMapper.selectAll(false,OrgNumberUtils.getOrg());
        List<CardSecuritySwitchResDTO> cardSecuritySwitchResDtos = BeanMapping.copyList(cardSecuritySwitches, CardSecuritySwitchResDTO.class);
        return new PageResultDTO<>(pageNum, pageSize, page.getTotal(), page.getPages(), cardSecuritySwitchResDtos);
    }

    /**
     * 修改
     * @param cardSecuritySwitchReq 持卡人安全开关特别检查参数传入
     * @return int
     */
    @Override
    public CardSecuritySwitchResDTO modify(CardSecuritySwitchReqDTO cardSecuritySwitchReq) {
        if (cardSecuritySwitchReq.getId() == null) {
            throw new AnyTxnParameterException(AnyTxnParameterRespCodeEnum.P_PARAMETER_ID_EMPTY_FAULT);
        }
        ParmCardSecuritySwitch parmCardSecuritySwitch = parmCardSecuritySwitchMapper.selectByPrimaryKey(cardSecuritySwitchReq.getId());

        if (parmCardSecuritySwitch == null) {
            log.error("修改持卡人安全开关特别检查参数，通过id:{}未查到数据", cardSecuritySwitchReq.getId());
            throw new AnyTxnParameterException(AnyTxnParameterRespCodeEnum.D_QUERY_CARD_SECURITY_SWITCH_FAULT);
        }
        BeanMapping.copy(cardSecuritySwitchReq, parmCardSecuritySwitch);
        parmCardSecuritySwitch.setUpdateTime(LocalDateTime.now());
        parmCardSecuritySwitch.setUpdateBy(Constants.DEFAULT_USER);

        parmCardSecuritySwitchMapper.updateByPrimaryKeySelective(parmCardSecuritySwitch);

        return BeanMapping.copy(parmCardSecuritySwitch, CardSecuritySwitchResDTO.class);
    }

    /**
     * 删除
     * @param id id
     * @return Boolean
     */
    @Override
    //@AfterDeleteProcess(cacheObjectClass = CardSecuritySwitchResDTO.class)
    public Boolean remove(Long id) {
        ParmCardSecuritySwitch parmCardSecuritySwitch = parmCardSecuritySwitchMapper.selectByPrimaryKey(id);
        if (parmCardSecuritySwitch == null) {
            log.error("删除持卡人安全开关特别检查参数，通过id:{}未查到数据", id);
            throw new AnyTxnParameterException(AnyTxnParameterRespCodeEnum.D_QUERY_CARD_SECURITY_SWITCH_FAULT);
        }

        log.error("删除持卡人安全开关特别检查参数");
        int deleteRow = parmCardSecuritySwitchMapper.deleteByPrimaryKey(id);
        return deleteRow > 0;
    }

    /*
    @Override
    //@Cachekey(findKeys = {"cardNumber"})
    public List<CardSecuritySwitchResDTO> selectAllCacheObject() {
        List<ParmCardSecuritySwitch> cardSecuritySwitches = parmCardSecuritySwitchSelfMapper.selectAll();
        return BeanMapping.copyList(cardSecuritySwitches,CardSecuritySwitchResDTO.class);
    }
    */
}
