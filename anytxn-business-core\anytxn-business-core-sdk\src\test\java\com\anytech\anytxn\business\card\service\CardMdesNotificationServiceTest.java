package com.anytech.anytxn.business.card.service;

import com.anytech.anytxn.business.base.card.enums.CardMdesCustomerServiceCodeEnum;
import com.anytech.anytxn.business.base.card.enums.CardMdesCustomerServiceReasonCodeEnum;
import com.anytech.anytxn.business.base.card.enums.CardMdesTokenStatusEnum;
import com.anytech.anytxn.business.dao.card.mapper.CardMdesTokenHistoryInfoMapper;
import com.anytech.anytxn.business.dao.card.mapper.CardMdesTokenInfoMapper;
import com.anytech.anytxn.business.dao.card.model.CardMdesTokenHistoryInfo;
import com.anytech.anytxn.business.dao.card.model.CardMdesTokenInfo;
import com.anytech.anytxn.common.core.enums.AnyTxnCommonRespCodeEnum;
import com.anytech.anytxn.common.core.exception.AnyTxnCommonException;
import com.anytech.anytxn.common.core.utils.BeanMapping;
import com.anytech.anytxn.common.core.utils.TenantUtils;
import com.anytech.anytxn.common.sequence.utils.SequenceIdGen;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockedStatic;
import org.mockito.junit.jupiter.MockitoExtension;

import java.time.LocalDateTime;
import java.util.Arrays;
import java.util.Collections;
import java.util.List;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.*;

/**
 * CardMdesNotificationServiceImpl测试类
 *
 * <AUTHOR>
 * @date 2025-01-24
 */
@ExtendWith(MockitoExtension.class)
class CardMdesNotificationServiceTest {

    @Mock
    private CardMdesTokenInfoMapper cardMdesTokenInfoMapper;

    @Mock
    private CardMdesTokenHistoryInfoMapper cardMdesTokenHistoryInfoMapper;

    @Mock
    private SequenceIdGen sequenceIdGen;

    @InjectMocks
    private CardMdesNotificationServiceImpl cardMdesNotificationService;

    private CardMdesTokenInfo testTokenInfo;
    private String testCardNumber;
    private String testTenantId;

    @BeforeEach
    void setUp() {
        testCardNumber = "****************";
        testTenantId = "TENANT001";

        testTokenInfo = new CardMdesTokenInfo();
        testTokenInfo.setTokenUniqueRef("TUR001");
        testTokenInfo.setCardNumber(testCardNumber);
        testTokenInfo.setTokenStatus(CardMdesTokenStatusEnum.ACTIVE.getCode());
        testTokenInfo.setCsCode("02");
        testTokenInfo.setCsReasonCode("Z");
        testTokenInfo.setCreateTime(LocalDateTime.now());
        testTokenInfo.setUpdateTime(LocalDateTime.now());
        testTokenInfo.setUpdateBy("Test");
    }

    @Test
    void testNotificationByCardUpdate_NoServiceRequired() {
        // 执行 - NO_SERVICE_REQUIRED 场景
        assertDoesNotThrow(() -> cardMdesNotificationService.notificationByCardUpdate(
                testCardNumber, 
                CardMdesCustomerServiceCodeEnum.NO_SERVICE_REQUIRED.getCode(), 
                CardMdesCustomerServiceReasonCodeEnum.Z
        ));

        // 验证 - 由于NO_SERVICE_REQUIRED，不应该有任何数据库操作
        verify(cardMdesTokenInfoMapper, never()).selectByCardNumberAndStatus(anyString(), anyString());
        verify(cardMdesTokenInfoMapper, never()).updateByPrimaryKeySelective(any());
        verify(cardMdesTokenHistoryInfoMapper, never()).batchInsert(anyList());
    }

    @Test
    void testNotificationByCardUpdate_Suspend_Success() {
        String csCode = CardMdesCustomerServiceCodeEnum.SUSPEND.getCode();
        List<CardMdesTokenInfo> activeTokens = Arrays.asList(testTokenInfo);

        // Mock
        when(cardMdesTokenInfoMapper.selectByCardNumberAndStatus(testCardNumber, CardMdesTokenStatusEnum.ACTIVE.getCode()))
                .thenReturn(activeTokens);
        when(cardMdesTokenInfoMapper.updateByPrimaryKeySelective(any(CardMdesTokenInfo.class)))
                .thenReturn(1);
        when(cardMdesTokenHistoryInfoMapper.batchInsert(anyList()))
                .thenReturn(1);

        try (MockedStatic<TenantUtils> tenantUtilsMock = mockStatic(TenantUtils.class);
             MockedStatic<BeanMapping> beanMappingMock = mockStatic(BeanMapping.class)) {
            
            tenantUtilsMock.when(TenantUtils::getTenantId).thenReturn(testTenantId);
            when(sequenceIdGen.generateId(testTenantId)).thenReturn("HIST001");
            
            CardMdesTokenHistoryInfo historyInfo = new CardMdesTokenHistoryInfo();
            historyInfo.setId("HIST001");
            beanMappingMock.when(() -> BeanMapping.copy(any(CardMdesTokenInfo.class), eq(CardMdesTokenHistoryInfo.class)))
                    .thenReturn(historyInfo);

            // 执行
            assertDoesNotThrow(() -> cardMdesNotificationService.notificationByCardUpdate(
                    testCardNumber, csCode, CardMdesCustomerServiceReasonCodeEnum.L
            ));

            // 验证
            verify(cardMdesTokenInfoMapper).selectByCardNumberAndStatus(testCardNumber, CardMdesTokenStatusEnum.ACTIVE.getCode());
            verify(cardMdesTokenInfoMapper).updateByPrimaryKeySelective(argThat(token -> 
                token.getCsCode().equals(csCode) && 
                token.getTokenStatus().equals(CardMdesTokenStatusEnum.SUSPENDED.getCode()) &&
                token.getCsReasonCode().equals(CardMdesCustomerServiceReasonCodeEnum.L.getCode())
            ));
            verify(cardMdesTokenHistoryInfoMapper).batchInsert(anyList());
        }
    }

    @Test
    void testNotificationByCardUpdate_Unsuspend_Success() {
        String csCode = CardMdesCustomerServiceCodeEnum.UNSUSPEND.getCode();
        testTokenInfo.setTokenStatus(CardMdesTokenStatusEnum.SUSPENDED.getCode());
        List<CardMdesTokenInfo> suspendedTokens = Arrays.asList(testTokenInfo);

        // Mock
        when(cardMdesTokenInfoMapper.selectByCardNumberAndStatus(testCardNumber, CardMdesTokenStatusEnum.SUSPENDED.getCode()))
                .thenReturn(suspendedTokens);
        when(cardMdesTokenInfoMapper.updateByPrimaryKeySelective(any(CardMdesTokenInfo.class)))
                .thenReturn(1);
        when(cardMdesTokenHistoryInfoMapper.batchInsert(anyList()))
                .thenReturn(1);

        try (MockedStatic<TenantUtils> tenantUtilsMock = mockStatic(TenantUtils.class);
             MockedStatic<BeanMapping> beanMappingMock = mockStatic(BeanMapping.class)) {
            
            tenantUtilsMock.when(TenantUtils::getTenantId).thenReturn(testTenantId);
            when(sequenceIdGen.generateId(testTenantId)).thenReturn("HIST002");
            
            CardMdesTokenHistoryInfo historyInfo = new CardMdesTokenHistoryInfo();
            historyInfo.setId("HIST002");
            beanMappingMock.when(() -> BeanMapping.copy(any(CardMdesTokenInfo.class), eq(CardMdesTokenHistoryInfo.class)))
                    .thenReturn(historyInfo);

            // 执行
            assertDoesNotThrow(() -> cardMdesNotificationService.notificationByCardUpdate(
                    testCardNumber, csCode, CardMdesCustomerServiceReasonCodeEnum.S
            ));

            // 验证
            verify(cardMdesTokenInfoMapper).selectByCardNumberAndStatus(testCardNumber, CardMdesTokenStatusEnum.SUSPENDED.getCode());
            verify(cardMdesTokenInfoMapper).updateByPrimaryKeySelective(argThat(token -> 
                token.getCsCode().equals(csCode) && 
                token.getTokenStatus().equals(CardMdesTokenStatusEnum.ACTIVE.getCode()) &&
                token.getCsReasonCode().equals(CardMdesCustomerServiceReasonCodeEnum.S.getCode())
            ));
            verify(cardMdesTokenHistoryInfoMapper).batchInsert(anyList());
        }
    }

    @Test
    void testNotificationByCardUpdate_Delete_Success() {
        String csCode = CardMdesCustomerServiceCodeEnum.DELETE.getCode();
        
        // 创建多个不同状态的Token
        CardMdesTokenInfo activeToken = new CardMdesTokenInfo();
        activeToken.setTokenUniqueRef("TUR001");
        activeToken.setTokenStatus(CardMdesTokenStatusEnum.ACTIVE.getCode());
        
        CardMdesTokenInfo suspendedToken = new CardMdesTokenInfo();
        suspendedToken.setTokenUniqueRef("TUR002");
        suspendedToken.setTokenStatus(CardMdesTokenStatusEnum.SUSPENDED.getCode());
        
        CardMdesTokenInfo deactivatedToken = new CardMdesTokenInfo();
        deactivatedToken.setTokenUniqueRef("TUR003");
        deactivatedToken.setTokenStatus(CardMdesTokenStatusEnum.DEACTIVATED.getCode());
        
        List<CardMdesTokenInfo> allTokens = Arrays.asList(activeToken, suspendedToken, deactivatedToken);

        // Mock
        when(cardMdesTokenInfoMapper.selectByCardNumberAndStatus(testCardNumber, null))
                .thenReturn(allTokens);
        when(cardMdesTokenInfoMapper.updateByPrimaryKeySelective(any(CardMdesTokenInfo.class)))
                .thenReturn(1);
        when(cardMdesTokenHistoryInfoMapper.batchInsert(anyList()))
                .thenReturn(2); // 只有非DEACTIVATED的token被更新

        try (MockedStatic<TenantUtils> tenantUtilsMock = mockStatic(TenantUtils.class);
             MockedStatic<BeanMapping> beanMappingMock = mockStatic(BeanMapping.class)) {
            
            tenantUtilsMock.when(TenantUtils::getTenantId).thenReturn(testTenantId);
            when(sequenceIdGen.generateId(testTenantId)).thenReturn("HIST003", "HIST004");
            
            CardMdesTokenHistoryInfo historyInfo = new CardMdesTokenHistoryInfo();
            beanMappingMock.when(() -> BeanMapping.copy(any(CardMdesTokenInfo.class), eq(CardMdesTokenHistoryInfo.class)))
                    .thenReturn(historyInfo);

            // 执行
            assertDoesNotThrow(() -> cardMdesNotificationService.notificationByCardUpdate(
                    testCardNumber, csCode, CardMdesCustomerServiceReasonCodeEnum.C
            ));

            // 验证
            verify(cardMdesTokenInfoMapper).selectByCardNumberAndStatus(testCardNumber, null);
            verify(cardMdesTokenInfoMapper, times(2)).updateByPrimaryKeySelective(any()); // 只有2个非DEACTIVATED的token
            verify(cardMdesTokenHistoryInfoMapper).batchInsert(anyList());
        }
    }

    @Test
    void testNotificationByCardUpdate_UpdateTokenFailed() {
        String csCode = CardMdesCustomerServiceCodeEnum.SUSPEND.getCode();
        List<CardMdesTokenInfo> activeTokens = Arrays.asList(testTokenInfo);

        // Mock - updateByPrimaryKeySelective 返回0表示更新失败
        when(cardMdesTokenInfoMapper.selectByCardNumberAndStatus(testCardNumber, CardMdesTokenStatusEnum.ACTIVE.getCode()))
                .thenReturn(activeTokens);
        when(cardMdesTokenInfoMapper.updateByPrimaryKeySelective(any(CardMdesTokenInfo.class)))
                .thenReturn(0);
        lenient().when(sequenceIdGen.generateId(testTenantId)).thenReturn("HIST005");

        try (MockedStatic<TenantUtils> tenantUtilsMock = mockStatic(TenantUtils.class);
             MockedStatic<BeanMapping> beanMappingMock = mockStatic(BeanMapping.class)) {
            
            tenantUtilsMock.when(TenantUtils::getTenantId).thenReturn(testTenantId);
            
            CardMdesTokenHistoryInfo historyInfo = new CardMdesTokenHistoryInfo();
            beanMappingMock.when(() -> BeanMapping.copy(any(CardMdesTokenInfo.class), eq(CardMdesTokenHistoryInfo.class)))
                    .thenReturn(historyInfo);

            // 执行并验证异常
            AnyTxnCommonException exception = assertThrows(AnyTxnCommonException.class,
                    () -> cardMdesNotificationService.notificationByCardUpdate(
                            testCardNumber, csCode, CardMdesCustomerServiceReasonCodeEnum.F
                    ));

            assertEquals(AnyTxnCommonRespCodeEnum.D_ERR.getCode(), exception.getErrCode());
        }
    }

    @Test
    void testNotificationByCardUpdate_HistoryInsertFailed() {
        String csCode = CardMdesCustomerServiceCodeEnum.SUSPEND.getCode();
        List<CardMdesTokenInfo> activeTokens = Arrays.asList(testTokenInfo);

        // Mock - batchInsert 返回的数量与预期不符
        when(cardMdesTokenInfoMapper.selectByCardNumberAndStatus(testCardNumber, CardMdesTokenStatusEnum.ACTIVE.getCode()))
                .thenReturn(activeTokens);
        when(cardMdesTokenInfoMapper.updateByPrimaryKeySelective(any(CardMdesTokenInfo.class)))
                .thenReturn(1);
        when(cardMdesTokenHistoryInfoMapper.batchInsert(anyList()))
                .thenReturn(0); // 返回0表示插入失败

        try (MockedStatic<TenantUtils> tenantUtilsMock = mockStatic(TenantUtils.class);
             MockedStatic<BeanMapping> beanMappingMock = mockStatic(BeanMapping.class)) {
            
            tenantUtilsMock.when(TenantUtils::getTenantId).thenReturn(testTenantId);
            when(sequenceIdGen.generateId(testTenantId)).thenReturn("HIST006");
            
            CardMdesTokenHistoryInfo historyInfo = new CardMdesTokenHistoryInfo();
            beanMappingMock.when(() -> BeanMapping.copy(any(CardMdesTokenInfo.class), eq(CardMdesTokenHistoryInfo.class)))
                    .thenReturn(historyInfo);

            // 执行并验证异常
            AnyTxnCommonException exception = assertThrows(AnyTxnCommonException.class,
                    () -> cardMdesNotificationService.notificationByCardUpdate(
                            testCardNumber, csCode, CardMdesCustomerServiceReasonCodeEnum.T
                    ));

            assertEquals(AnyTxnCommonRespCodeEnum.D_ERR.getCode(), exception.getErrCode());
        }
    }

    @Test
    void testNotificationByCardUpdate_NoTokensFound() {
        String csCode = CardMdesCustomerServiceCodeEnum.SUSPEND.getCode();

        // Mock - 没有找到符合条件的token
        when(cardMdesTokenInfoMapper.selectByCardNumberAndStatus(testCardNumber, CardMdesTokenStatusEnum.ACTIVE.getCode()))
                .thenReturn(Collections.emptyList());

        // 执行
        assertDoesNotThrow(() -> cardMdesNotificationService.notificationByCardUpdate(
                testCardNumber, csCode, CardMdesCustomerServiceReasonCodeEnum.D
        ));

        // 验证 - 没有token需要更新，所以不应该有更新和历史插入操作
        verify(cardMdesTokenInfoMapper).selectByCardNumberAndStatus(testCardNumber, CardMdesTokenStatusEnum.ACTIVE.getCode());
        verify(cardMdesTokenInfoMapper, never()).updateByPrimaryKeySelective(any());
        verify(cardMdesTokenHistoryInfoMapper, never()).batchInsert(anyList());
    }

    @Test
    void testNotificationByCardUpdate_WithDefaultReasonCode() {
        String csCode = CardMdesCustomerServiceCodeEnum.SUSPEND.getCode();
        List<CardMdesTokenInfo> activeTokens = Arrays.asList(testTokenInfo);

        // Mock
        when(cardMdesTokenInfoMapper.selectByCardNumberAndStatus(testCardNumber, CardMdesTokenStatusEnum.ACTIVE.getCode()))
                .thenReturn(activeTokens);
        when(cardMdesTokenInfoMapper.updateByPrimaryKeySelective(any(CardMdesTokenInfo.class)))
                .thenReturn(1);
        when(cardMdesTokenHistoryInfoMapper.batchInsert(anyList()))
                .thenReturn(1);

        try (MockedStatic<TenantUtils> tenantUtilsMock = mockStatic(TenantUtils.class);
             MockedStatic<BeanMapping> beanMappingMock = mockStatic(BeanMapping.class)) {
            
            tenantUtilsMock.when(TenantUtils::getTenantId).thenReturn(testTenantId);
            when(sequenceIdGen.generateId(testTenantId)).thenReturn("HIST007");
            
            CardMdesTokenHistoryInfo historyInfo = new CardMdesTokenHistoryInfo();
            historyInfo.setId("HIST007");
            beanMappingMock.when(() -> BeanMapping.copy(any(CardMdesTokenInfo.class), eq(CardMdesTokenHistoryInfo.class)))
                    .thenReturn(historyInfo);

            // 执行 - 使用默认原因码的重载方法
            assertDoesNotThrow(() -> cardMdesNotificationService.notificationByCardUpdate(
                    testCardNumber, csCode
            ));

            // 验证 - 应该使用默认的Z原因码
            verify(cardMdesTokenInfoMapper).updateByPrimaryKeySelective(argThat(token -> 
                token.getCsReasonCode().equals(CardMdesCustomerServiceReasonCodeEnum.Z.getCode())
            ));
        }
    }

    @Test
    void testNotificationByCardUpdate_InvalidServiceCode() {
        String invalidCsCode = "99"; // 无效的服务码

        // 执行 - 无效的服务码不匹配任何if条件，所以token列表为空，不会有任何更新操作
        assertDoesNotThrow(() -> cardMdesNotificationService.notificationByCardUpdate(
                testCardNumber, invalidCsCode, CardMdesCustomerServiceReasonCodeEnum.Z
        ));

        // 验证 - 由于不匹配任何if条件，token列表为空，所以不应该有任何数据库操作
        verify(cardMdesTokenInfoMapper, never()).selectByCardNumberAndStatus(anyString(), anyString());
        verify(cardMdesTokenInfoMapper, never()).updateByPrimaryKeySelective(any());
        verify(cardMdesTokenHistoryInfoMapper, never()).batchInsert(anyList());
    }
}