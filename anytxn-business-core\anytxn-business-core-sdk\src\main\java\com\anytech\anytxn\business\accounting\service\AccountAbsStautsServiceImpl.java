package com.anytech.anytxn.business.accounting.service;

import com.github.pagehelper.Page;
import com.github.pagehelper.PageHelper;
import com.anytech.anytxn.common.core.base.PageResultDTO;
import com.anytech.anytxn.common.core.utils.BeanMapping;
import com.anytech.anytxn.business.base.accounting.domain.dto.AccountAbsstatusDTO;
import com.anytech.anytxn.business.dao.accounting.mapper.AccountAbsstatusMapper;
import com.anytech.anytxn.business.dao.accounting.mapper.AccountAbsstatusSelfMapper;
import com.anytech.anytxn.business.dao.accounting.model.AccountAbsstatus;
import com.anytech.anytxn.common.core.utils.OrgNumberUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.util.List;

/**
 * abs状态
 * <AUTHOR>
 * @date 2020-08-06
 */
@Service
public class AccountAbsStautsServiceImpl{

    private static final Logger log = LoggerFactory.getLogger(AccountAbsStautsServiceImpl.class);

    @Autowired
    private AccountAbsstatusMapper accountAbsstatusMapper;
    @Autowired
    private AccountAbsstatusSelfMapper accountAbsstatusSelfMapper;

    /**
     * 分页查询
     * @param pageNum
     * @param pageSize
     * @param accountManagementId
     * @param assetNo
     * @return
     */
    public PageResultDTO<AccountAbsstatusDTO> page(Integer pageNum, Integer pageSize, String accountManagementId, String assetNo) {
        Page<AccountAbsstatus> page = PageHelper.startPage(pageNum, pageSize);

        List<AccountAbsstatus> accountAbsstatus = accountAbsstatusSelfMapper.selectByCondition(accountManagementId, OrgNumberUtils.getOrg(), assetNo, null);

        if (!CollectionUtils.isEmpty(accountAbsstatus)) {
            return new PageResultDTO(pageNum, pageSize, page.getTotal(), page.getPages(), BeanMapping.copyList(accountAbsstatus, AccountAbsstatusDTO.class));
        } else {
            return null;
        }
    }

    /**
     * 基于id查询
     * @param id
     * @return
     */
    public AccountAbsstatusDTO findById(String id){
        AccountAbsstatus accountAbsstatus = accountAbsstatusMapper.selectByPrimaryKey(id);

        return accountAbsstatus == null ? null : BeanMapping.copy(accountAbsstatus, AccountAbsstatusDTO.class);
    }
}
