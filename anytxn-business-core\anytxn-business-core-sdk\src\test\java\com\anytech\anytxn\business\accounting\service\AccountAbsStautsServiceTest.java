package com.anytech.anytxn.business.accounting.service;

import com.github.pagehelper.Page;
import com.github.pagehelper.PageHelper;
import com.anytech.anytxn.common.core.base.PageResultDTO;
import com.anytech.anytxn.common.core.utils.BeanMapping;
import com.anytech.anytxn.business.base.accounting.domain.dto.AccountAbsstatusDTO;
import com.anytech.anytxn.business.dao.accounting.mapper.AccountAbsstatusMapper;
import com.anytech.anytxn.business.dao.accounting.mapper.AccountAbsstatusSelfMapper;
import com.anytech.anytxn.business.dao.accounting.model.AccountAbsstatus;
import com.anytech.anytxn.common.core.utils.OrgNumberUtils;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockedStatic;
import org.mockito.junit.jupiter.MockitoExtension;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.Arrays;
import java.util.Collections;
import java.util.List;

import static org.assertj.core.api.Assertions.assertThat;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.*;

/**
 * AccountAbsStautsServiceImpl单元测试
 * 
 * <AUTHOR>
 * @date 2025-06-20
 */
@ExtendWith(MockitoExtension.class)
class AccountAbsStautsServiceTest {

    @Mock
    private AccountAbsstatusMapper accountAbsstatusMapper;

    @Mock
    private AccountAbsstatusSelfMapper accountAbsstatusSelfMapper;

    @InjectMocks
    private AccountAbsStautsServiceImpl accountAbsStautsService;

    // 测试常量
    private static final String TEST_ORG = "ORG001";
    private static final String TEST_ACCOUNT_MANAGEMENT_ID = "ACCT123456";
    private static final String TEST_ASSET_NO = "ASSET001";
    private static final String TEST_ID = "1";
    private static final Integer TEST_PAGE_NUM = 1;
    private static final Integer TEST_PAGE_SIZE = 10;

    @BeforeEach
    void setUp() {
        // 初始化设置
    }

    @Test
    void shouldReturnPageResult_whenValidParametersProvidedForPage() {
        try (MockedStatic<OrgNumberUtils> mockOrgUtils = mockStatic(OrgNumberUtils.class);
             MockedStatic<BeanMapping> mockBeanMapping = mockStatic(BeanMapping.class)) {
            
            // Given
            mockOrgUtils.when(OrgNumberUtils::getOrg).thenReturn(TEST_ORG);
            
            List<AccountAbsstatus> accountAbsstatusList = createTestAccountAbsstatusList();
            when(accountAbsstatusSelfMapper.selectByCondition(TEST_ACCOUNT_MANAGEMENT_ID, TEST_ORG, TEST_ASSET_NO, null))
                .thenReturn(accountAbsstatusList);
            
            List<AccountAbsstatusDTO> dtoList = createTestAccountAbsstatusDTOList();
            mockBeanMapping.when(() -> BeanMapping.copyList(accountAbsstatusList, AccountAbsstatusDTO.class))
                .thenReturn(dtoList);

            // When
            PageResultDTO<AccountAbsstatusDTO> result = accountAbsStautsService.page(
                TEST_PAGE_NUM, TEST_PAGE_SIZE, TEST_ACCOUNT_MANAGEMENT_ID, TEST_ASSET_NO);

            // Then
            assertThat(result).isNotNull();
            assertThat(result.getPage()).isEqualTo(TEST_PAGE_NUM);
            assertThat(result.getRows()).isEqualTo(TEST_PAGE_SIZE);
            assertThat(result.getData()).isEqualTo(dtoList);
            
            verify(accountAbsstatusSelfMapper).selectByCondition(TEST_ACCOUNT_MANAGEMENT_ID, TEST_ORG, TEST_ASSET_NO, null);
        }
    }

    @Test
    void shouldReturnNull_whenNoDataFoundForPage() {
        try (MockedStatic<OrgNumberUtils> mockOrgUtils = mockStatic(OrgNumberUtils.class)) {
            
            // Given
            mockOrgUtils.when(OrgNumberUtils::getOrg).thenReturn(TEST_ORG);
            
            when(accountAbsstatusSelfMapper.selectByCondition(TEST_ACCOUNT_MANAGEMENT_ID, TEST_ORG, TEST_ASSET_NO, null))
                .thenReturn(Collections.emptyList());

            // When
            PageResultDTO<AccountAbsstatusDTO> result = accountAbsStautsService.page(
                TEST_PAGE_NUM, TEST_PAGE_SIZE, TEST_ACCOUNT_MANAGEMENT_ID, TEST_ASSET_NO);

            // Then
            assertThat(result).isNull();
            
            verify(accountAbsstatusSelfMapper).selectByCondition(TEST_ACCOUNT_MANAGEMENT_ID, TEST_ORG, TEST_ASSET_NO, null);
        }
    }

    @Test
    void shouldReturnNull_whenNullDataFoundForPage() {
        try (MockedStatic<OrgNumberUtils> mockOrgUtils = mockStatic(OrgNumberUtils.class)) {
            
            // Given
            mockOrgUtils.when(OrgNumberUtils::getOrg).thenReturn(TEST_ORG);
            
            when(accountAbsstatusSelfMapper.selectByCondition(TEST_ACCOUNT_MANAGEMENT_ID, TEST_ORG, TEST_ASSET_NO, null))
                .thenReturn(null);

            // When
            PageResultDTO<AccountAbsstatusDTO> result = accountAbsStautsService.page(
                TEST_PAGE_NUM, TEST_PAGE_SIZE, TEST_ACCOUNT_MANAGEMENT_ID, TEST_ASSET_NO);

            // Then
            assertThat(result).isNull();
            
            verify(accountAbsstatusSelfMapper).selectByCondition(TEST_ACCOUNT_MANAGEMENT_ID, TEST_ORG, TEST_ASSET_NO, null);
        }
    }

    @Test
    void shouldReturnAccountAbsstatusDTO_whenValidIdProvidedForFindById() {
        try (MockedStatic<BeanMapping> mockBeanMapping = mockStatic(BeanMapping.class);
             MockedStatic<OrgNumberUtils> mockOrgUtils = mockStatic(OrgNumberUtils.class)) {
            
            // Given
            mockOrgUtils.when(OrgNumberUtils::getOrg).thenReturn(TEST_ORG);
            
            AccountAbsstatus accountAbsstatus = createTestAccountAbsstatus();
            when(accountAbsstatusMapper.selectByPrimaryKey(TEST_ID))
                .thenReturn(accountAbsstatus);
            
            AccountAbsstatusDTO expectedDTO = createTestAccountAbsstatusDTO();
            mockBeanMapping.when(() -> BeanMapping.copy(accountAbsstatus, AccountAbsstatusDTO.class))
                .thenReturn(expectedDTO);

            // When
            AccountAbsstatusDTO result = accountAbsStautsService.findById(TEST_ID);

            // Then
            assertThat(result).isNotNull();
            assertThat(result).isEqualTo(expectedDTO);
            
            verify(accountAbsstatusMapper).selectByPrimaryKey(TEST_ID);
        }
    }

    @Test
    void shouldReturnNull_whenNoDataFoundForFindById() {
        // Given
        when(accountAbsstatusMapper.selectByPrimaryKey(TEST_ID))
            .thenReturn(null);

        // When
        AccountAbsstatusDTO result = accountAbsStautsService.findById(TEST_ID);

        // Then
        assertThat(result).isNull();
        
        verify(accountAbsstatusMapper).selectByPrimaryKey(TEST_ID);
    }

    @Test
    void shouldHandleNullParameters_whenNullParametersProvidedForPage() {
        try (MockedStatic<OrgNumberUtils> mockOrgUtils = mockStatic(OrgNumberUtils.class)) {
            
            // Given
            mockOrgUtils.when(OrgNumberUtils::getOrg).thenReturn(TEST_ORG);
            
            when(accountAbsstatusSelfMapper.selectByCondition(null, TEST_ORG, null, null))
                .thenReturn(Collections.emptyList());

            // When
            PageResultDTO<AccountAbsstatusDTO> result = accountAbsStautsService.page(
                TEST_PAGE_NUM, TEST_PAGE_SIZE, null, null);

            // Then
            assertThat(result).isNull();
            
            verify(accountAbsstatusSelfMapper).selectByCondition(null, TEST_ORG, null, null);
        }
    }

    @Test
    void shouldHandleNullId_whenNullIdProvidedForFindById() {
        // Given
        when(accountAbsstatusMapper.selectByPrimaryKey(null))
            .thenReturn(null);

        // When
        AccountAbsstatusDTO result = accountAbsStautsService.findById(null);

        // Then
        assertThat(result).isNull();
        
        verify(accountAbsstatusMapper).selectByPrimaryKey(null);
    }

    // Helper methods to create test data
    private List<AccountAbsstatus> createTestAccountAbsstatusList() {
        AccountAbsstatus abs1 = createTestAccountAbsstatus();
        abs1.setId(1L);
        abs1.setAssetNo("ASSET001");
        
        AccountAbsstatus abs2 = createTestAccountAbsstatus();
        abs2.setId(2L);
        abs2.setAssetNo("ASSET002");
        
        return Arrays.asList(abs1, abs2);
    }

    private AccountAbsstatus createTestAccountAbsstatus() {
        AccountAbsstatus accountAbsstatus = new AccountAbsstatus();
        accountAbsstatus.setOrganizationNumber(TEST_ORG); // Set before other properties to avoid BaseEntity constructor issues
        accountAbsstatus.setId(1L);
        accountAbsstatus.setAccountManagementId(TEST_ACCOUNT_MANAGEMENT_ID);
        accountAbsstatus.setBranchid("001");
        accountAbsstatus.setAbsType("0");
        accountAbsstatus.setAssetNo(TEST_ASSET_NO);
        accountAbsstatus.setAbsStatus("A");
        accountAbsstatus.setOrderId("ORDER001");
        accountAbsstatus.setPreabsDate(LocalDate.now());
        accountAbsstatus.setOutabsDate(LocalDate.now().plusDays(30));
        accountAbsstatus.setEndabsDate(LocalDate.now().plusDays(60));
        accountAbsstatus.setStatus("1");
        accountAbsstatus.setAcctAbsStage("F");
        accountAbsstatus.setCreateTime(LocalDateTime.now());
        accountAbsstatus.setUpdateTime(LocalDateTime.now());
        accountAbsstatus.setUpdateBy("TEST_USER");
        accountAbsstatus.setVersionNumber(1L);
        return accountAbsstatus;
    }

    private List<AccountAbsstatusDTO> createTestAccountAbsstatusDTOList() {
        AccountAbsstatusDTO dto1 = createTestAccountAbsstatusDTO();
        dto1.setId(1L);
        dto1.setAssetNo("ASSET001");
        
        AccountAbsstatusDTO dto2 = createTestAccountAbsstatusDTO();
        dto2.setId(2L);
        dto2.setAssetNo("ASSET002");
        
        return Arrays.asList(dto1, dto2);
    }

    private AccountAbsstatusDTO createTestAccountAbsstatusDTO() {
        AccountAbsstatusDTO dto = new AccountAbsstatusDTO();
        dto.setOrganizationNumber(TEST_ORG); // Set before other properties to avoid BaseEntity constructor issues
        dto.setId(1L);
        dto.setAccountManagementId(TEST_ACCOUNT_MANAGEMENT_ID);
        dto.setBranchid("001");
        dto.setAbsType("0");
        dto.setAssetNo(TEST_ASSET_NO);
        dto.setAbsStatus("A");
        dto.setOrderId("ORDER001");
        dto.setPreabsDate(LocalDate.now());
        dto.setOutabsDate(LocalDate.now().plusDays(30));
        dto.setEndabsDate(LocalDate.now().plusDays(60));
        dto.setStatus("1");
        dto.setAcctAbsStage("F");
        dto.setCreateTime(LocalDateTime.now());
        dto.setUpdateTime(LocalDateTime.now());
        dto.setUpdateBy("TEST_USER");
        dto.setVersionNumber(1L);
        return dto;
    }
} 