package com.anytech.anytxn.parameter.common.service;

import com.github.pagehelper.Page;
import com.github.pagehelper.PageHelper;
import com.anytech.anytxn.common.core.utils.OrgNumberUtils;
import com.anytech.anytxn.parameter.base.common.constants.Constants;
import com.anytech.anytxn.common.core.base.PageResultDTO;
import com.anytech.anytxn.common.core.utils.BeanMapping;
import com.anytech.anytxn.common.sequence.utils.Number16IdGen;
import com.anytech.anytxn.common.core.utils.TenantUtils;
import com.anytech.anytxn.parameter.base.common.domain.dto.LockCardNumberReqDTO;
import com.anytech.anytxn.parameter.base.common.domain.dto.LockCardNumberResDTO;
import com.anytech.anytxn.parameter.base.common.service.ILockCardNumberService;
import com.anytech.anytxn.parameter.base.common.enums.AnyTxnParameterRespCodeEnum;
import com.anytech.anytxn.parameter.base.common.exception.AnyTxnParameterException;
import com.anytech.anytxn.parameter.common.mapper.deprecated.ParmLockCardNumberMapper;
import com.anytech.anytxn.parameter.common.mapper.deprecated.ParmLockCardNumberSelfMapper;
import com.anytech.anytxn.parameter.base.common.domain.model.ParmLockCardNumber;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.util.List;

/**
 * 产品参数 业务实现
 *
 * <AUTHOR>
 * @date 2018-08-16 11:37
 **/
@Service
public class LockCardNumberServiceImpl implements ILockCardNumberService {

    private Logger logger = LoggerFactory.getLogger(LockCardNumberServiceImpl.class);

    @Autowired
    private ParmLockCardNumberSelfMapper parmLockCardNumberSelfMapper;
    @Autowired
    private ParmLockCardNumberMapper parmLockCardNumberMapper;

    @Autowired
    private Number16IdGen numberIdGenerator;


    /**
     * 新增记录
     *
     * @param lockCardNumberReq
     * @return 产品信息详情
     * @throws AnyTxnParameterException
     */
    @Override
    public LockCardNumberResDTO add(LockCardNumberReqDTO lockCardNumberReq){
        if(StringUtils.isEmpty(lockCardNumberReq.getOrganizationNumber())){
            lockCardNumberReq.setOrganizationNumber(OrgNumberUtils.getOrg());
        }
        int count = parmLockCardNumberSelfMapper.isExists(lockCardNumberReq.getOrganizationNumber(), lockCardNumberReq.getLockCardNumber());
        if (count>0) {
            logger.warn("卡号已存在, Organization ={} cardNumber={}",
                    lockCardNumberReq.getOrganizationNumber(), lockCardNumberReq.getLockCardNumber());

            throw new AnyTxnParameterException(AnyTxnParameterRespCodeEnum.P_EXIST_CARD_NUMBER_EMPTY_FAULT);
        }

        //构建产品详情
        ParmLockCardNumber parmLockCardNumber = BeanMapping.copy(lockCardNumberReq, ParmLockCardNumber.class);
        parmLockCardNumber.setCreateTime(LocalDateTime.now());
        parmLockCardNumber.setVersionNumber(1L);
        parmLockCardNumber.setUpdateTime(LocalDateTime.now());
        parmLockCardNumber.setUpdateBy(Constants.DEFAULT_USER);
        parmLockCardNumber.setId(numberIdGenerator.generateId(TenantUtils.getTenantId()));
        parmLockCardNumberMapper.insertSelective(parmLockCardNumber);

        lockCardNumberReq.setId(parmLockCardNumber.getId());

        return BeanMapping.copy(parmLockCardNumber, LockCardNumberResDTO.class);
    }

    /**
     * 通过id删除条目
     *
     * @param id 技术主键
     * @return true:删除成功|false:删除失败
     * @throws AnyTxnParameterException
     */
    @Override
    public Boolean remove(Long id){
        ParmLockCardNumber parmLockCardNumber = parmLockCardNumberMapper.selectByPrimaryKey(id);

        if (parmLockCardNumber == null) {
            logger.error("删除锁定卡号信息, 通过主键id({})未找到数据", id);

            throw new AnyTxnParameterException(AnyTxnParameterRespCodeEnum.D_QUERY_PARM_LOCK_CARD_NUMBER_BY_ID_FAULT);
        }

        logger.warn("通过id删除锁定卡号信息, {}", parmLockCardNumber);

        int deleteRows = parmLockCardNumberMapper.deleteByPrimaryKey(id);
        return deleteRows > 0;
    }

    /**
     * 通过id获取详情
     *
     * @param id 主键id
     * @return 产品信息详情
     * @throws AnyTxnParameterException
     */
    @Override
    public LockCardNumberResDTO find(Long id){
        if (id == null) {

            throw new AnyTxnParameterException(AnyTxnParameterRespCodeEnum.P_PARAMETER_EMPTY_FAULT);
        }

        ParmLockCardNumber parmLockCardNumber = parmLockCardNumberMapper.selectByPrimaryKey(id);

        if (parmLockCardNumber == null) {
            logger.error("查询锁定卡号信息, 通过主键id({})未找到数据", id);

            throw new AnyTxnParameterException(AnyTxnParameterRespCodeEnum.D_QUERY_PARM_LOCK_CARD_NUMBER_BY_ID_FAULT);
        }

        return BeanMapping.copy(parmLockCardNumber, LockCardNumberResDTO.class);
    }

    /**
     * 修改产品
     *
     * @param lockCardNumberReq 产品入参对象
     * @return 产品信息详情
     * @throws AnyTxnParameterException
     */
    @Override
    public LockCardNumberResDTO modify(LockCardNumberReqDTO lockCardNumberReq){
        if (lockCardNumberReq.getId() == null) {

            throw new AnyTxnParameterException(AnyTxnParameterRespCodeEnum.P_PARAMETER_ID_EMPTY_FAULT);
        }
        if(StringUtils.isEmpty(lockCardNumberReq.getOrganizationNumber())){
            lockCardNumberReq.setOrganizationNumber(OrgNumberUtils.getOrg());
        }
        ParmLockCardNumber parmLockCardNumber = parmLockCardNumberMapper.selectByPrimaryKey(lockCardNumberReq.getId());

        if (parmLockCardNumber == null) {
            logger.error("修改产品信息, 通过主键id({})未找到数据", lockCardNumberReq.getId());

            throw new AnyTxnParameterException(AnyTxnParameterRespCodeEnum.D_QUERY_PARM_LOCK_CARD_NUMBER_BY_ID_FAULT);
        }

        // 拷贝修改的数据并更新
        BeanMapping.copy(lockCardNumberReq, parmLockCardNumber);
        parmLockCardNumber.setUpdateTime(LocalDateTime.now());
        parmLockCardNumber.setUpdateBy(Constants.DEFAULT_USER);
        parmLockCardNumberMapper.updateByPrimaryKeySelective(parmLockCardNumber);

        //历史表中添加记录

        return BeanMapping.copy(parmLockCardNumber, LockCardNumberResDTO.class);
    }

    /**
     * 分页查询产品信息 根据机构号
     *
     * @param pageNum  页号
     * @param pageSize 每页大小
     * @return 产品详情分页
     * @throws AnyTxnParameterException
     */
    @Override
    public PageResultDTO<LockCardNumberResDTO> findPageByOrgNumber(Integer pageNum, Integer pageSize, String organizationNumber){

        logger.debug("分页查询锁定卡号信息, pageNum={}, pageSize={}",pageNum, pageSize);
        Page<ParmLockCardNumber> page = PageHelper.startPage(pageNum, pageSize);
        List<ParmLockCardNumber> dataList = parmLockCardNumberSelfMapper.selectByOrganizationNumber(organizationNumber, false);
        if (dataList.isEmpty()) {
            logger.error("未查询到信息");

            throw new AnyTxnParameterException(AnyTxnParameterRespCodeEnum.D_GET_IS_NULL_FAULT);
        }
        List<LockCardNumberResDTO> lockCardNumberResList = BeanMapping.copyList(dataList, LockCardNumberResDTO.class);

        return new PageResultDTO<>(pageNum, pageSize, page.getTotal(), page.getPages(), lockCardNumberResList);
    }

}
