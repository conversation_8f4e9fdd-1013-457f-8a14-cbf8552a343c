package com.anytech.anytxn.parameter.accounting.service;

import com.alibaba.fastjson.JSON;
import com.anytech.anytxn.business.base.audit.AbstractParameterService;
import com.anytech.anytxn.business.base.audit.ParmModificationRecord;
import com.anytech.anytxn.parameter.base.accounting.domain.dto.TPmsGlamsControlDTO;
import com.anytech.anytxn.parameter.base.accounting.domain.dto.TPmsGlamsDefinitionDTO;
import com.anytech.anytxn.parameter.base.accounting.service.ITPmsGlamsControlService;
import com.anytech.anytxn.parameter.base.accounting.service.ITPmsGlamsDefinitionService;
import com.anytech.anytxn.parameter.base.common.enums.AnyTxnParameterRespCodeEnum;
import com.anytech.anytxn.parameter.base.common.exception.AnyTxnParameterException;
import com.anytech.anytxn.parameter.accounting.mapper.TPmsGlamsControlMapper;
import com.anytech.anytxn.parameter.accounting.mapper.TPmsGlamsControlSelfMapper;
import com.anytech.anytxn.parameter.base.accounting.domain.model.TPmsGlamsControl;
import com.github.pagehelper.Page;
import com.github.pagehelper.PageHelper;
import com.anytech.anytxn.common.core.annotation.db.DeleteParameterAnnotation;
import com.anytech.anytxn.common.core.annotation.db.InsertParameterAnnotation;
import com.anytech.anytxn.common.core.annotation.db.UpdateParameterAnnotation;
import com.anytech.anytxn.common.core.base.ParameterCompare;
import com.anytech.anytxn.common.core.base.PageResultDTO;
import com.anytech.anytxn.common.core.utils.OrgNumberUtils;
import com.anytech.anytxn.common.core.utils.TenantUtils;
import com.anytech.anytxn.common.sequence.utils.Number16IdGen;
import org.apache.commons.lang3.ObjectUtils;
//import org.apache.shardingsphere.transaction.annotation.ShardingSphereTransactionType;
//import org.apache.shardingsphere.transaction.core.TransactionType;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.cglib.beans.BeanCopier;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

/**
 * @description: 会计核算控制表参数
 * @author: ZXL
 * @create: 2019-10-09 14:26
 */

@Service("parm_pms_glams_control_serviceImpl")
public class TPmsGlamsControlServiceImpl extends AbstractParameterService implements ITPmsGlamsControlService {

    private static final Logger logger = LoggerFactory.getLogger(TPmsGlamsControlServiceImpl.class);

    @Autowired
    private TPmsGlamsControlSelfMapper tPmsGlamsControlSelfMapper;
    @Autowired
    private TPmsGlamsControlMapper tPmsGlamsControlMapper;
    @Autowired
    private Number16IdGen numberIdGenerator;

    @Autowired
    private ITPmsGlamsDefinitionService pmsGlamsDefinitionService;

    @Override
    public List<TPmsGlamsControlDTO> findByTableId(String tableId) {
        List<TPmsGlamsControl> tPmsGlamsControls = tPmsGlamsControlSelfMapper.selectTableId(tableId, OrgNumberUtils.getOrg());
        if (tPmsGlamsControls != null) {
            BeanCopier beanCopier = BeanCopier.create(TPmsGlamsControl.class, TPmsGlamsControlDTO.class, false);
            return tPmsGlamsControls.stream().map(tPmsGlamsControl -> {
                TPmsGlamsControlDTO tPmsGlamsControlDTO = new TPmsGlamsControlDTO();
                beanCopier.copy(tPmsGlamsControl, tPmsGlamsControlDTO, null);
                return tPmsGlamsControlDTO;
            }).collect(Collectors.toList());
        }
        return null;
    }

    @Override
    public List<TPmsGlamsControlDTO> findByDefine(String organizationNumber, String branchid, String tableId) {
        List<TPmsGlamsControl> tPmsGlamsControls = tPmsGlamsControlSelfMapper.selectByDefine(organizationNumber,
         branchid, tableId);
        if (tPmsGlamsControls != null) {

            BeanCopier beanCopier = BeanCopier.create(TPmsGlamsControl.class, TPmsGlamsControlDTO.class, false);
            return tPmsGlamsControls.stream().map(tPmsGlamsControl -> {
                TPmsGlamsControlDTO tPmsGlamsControlDTO = new TPmsGlamsControlDTO();
                beanCopier.copy(tPmsGlamsControl, tPmsGlamsControlDTO, null);
                return tPmsGlamsControlDTO;
            }).collect(Collectors.toList());
        }
        return null;
    }


    @Override
    public PageResultDTO<TPmsGlamsControlDTO> page(int page, int rows) {
        Page pageInfo = PageHelper.startPage(page, rows);
        List<TPmsGlamsControlDTO> pmsGlamsControlDTOList = null;
        try {
            List<TPmsGlamsControl> tPmsGlamsControlList = tPmsGlamsControlSelfMapper.selectAll(false,OrgNumberUtils.getOrg());
            if (!CollectionUtils.isEmpty(tPmsGlamsControlList)) {
                pmsGlamsControlDTOList = new ArrayList<>();
                BeanCopier beanCopier = BeanCopier.create(TPmsGlamsControl.class, TPmsGlamsControlDTO.class, false);
                for (TPmsGlamsControl tPmsGlamsControl : tPmsGlamsControlList) {
                    TPmsGlamsControlDTO tPmsGlamsControlDTO = new TPmsGlamsControlDTO();
                    beanCopier.copy(tPmsGlamsControl, tPmsGlamsControlDTO, null);
                    pmsGlamsControlDTOList.add(tPmsGlamsControlDTO);
                }
            }
            return new PageResultDTO<>(page, rows, pageInfo.getTotal(), pageInfo.getPages(), pmsGlamsControlDTOList);
        } catch (Exception e) {
            logger.error("分页查询流水拆分分录参数失败，当前页：{}，每页展示条数：{},错误信息：{}", page, rows, e.getMessage());
            /*throw new AnyTXNBusRuntimeException(TransactionEnum.DATABASE_ERROR.getCode(),
                    TransactionEnum.DATABASE_ERROR.getReason());*/
            throw new AnyTxnParameterException(AnyTxnParameterRespCodeEnum.D_PAGE_TPMS_GLAMS_CONTROL_FAULT);
        }
    }

    @Override
    public TPmsGlamsControlDTO detail(String id) {
        TPmsGlamsControl tPmsGlamsControl = tPmsGlamsControlMapper.selectByPrimaryKey(id);
        if (tPmsGlamsControl != null) {
            TPmsGlamsControlDTO tPmsGlamsControlDTO = new TPmsGlamsControlDTO();
            BeanCopier beanCopier = BeanCopier.create(TPmsGlamsControl.class, TPmsGlamsControlDTO.class, false);
            beanCopier.copy(tPmsGlamsControl, tPmsGlamsControlDTO, null);
            return tPmsGlamsControlDTO;
        }
        return null;
    }

    @Override
    @DeleteParameterAnnotation(tableName = "parm_pms_glams_control", tableDesc = "Accounting Rules")
    public ParameterCompare remove(String id) {
        if (null == id) {
            throw new AnyTxnParameterException(AnyTxnParameterRespCodeEnum.P_PARAMETER_EMPTY_FAULT);
        }
        TPmsGlamsControl tPmsGlamsControl = tPmsGlamsControlMapper.selectByPrimaryKey(id);
        if(ObjectUtils.isEmpty(tPmsGlamsControl)){
            throw new AnyTxnParameterException(AnyTxnParameterRespCodeEnum.D_DATA_NOT_EXISTS_TEMPLATE_FAULT);
        }

        return ParameterCompare
                .getBuilder()
                .withBefore(tPmsGlamsControl)
                .build(TPmsGlamsControl.class);
    }

    @Override
    @InsertParameterAnnotation(tableName = "parm_pms_glams_control", tableDesc = "Accounting Rules")
    public ParameterCompare add(TPmsGlamsControlDTO data) {
        if(ObjectUtils.isEmpty(data)){
            throw new AnyTxnParameterException(AnyTxnParameterRespCodeEnum.P_PARAMETER_DATA_NOT_EXIST);
        }
        TPmsGlamsControl tPmsGlamsControl = new TPmsGlamsControl();
        BeanCopier beanCopier = BeanCopier.create(TPmsGlamsControlDTO.class, TPmsGlamsControl.class, false);
        beanCopier.copy(data, tPmsGlamsControl, null);

        tPmsGlamsControl.setId(numberIdGenerator.generateId(TenantUtils.getTenantId()) + "");

        return ParameterCompare.getBuilder().withAfter(tPmsGlamsControl)
                .build(TPmsGlamsControl.class);
    }

    @Override
    @UpdateParameterAnnotation(tableName = "parm_pms_glams_control", tableDesc = "Accounting Rules")
    public ParameterCompare update(TPmsGlamsControlDTO data) {
        if(ObjectUtils.isEmpty(data)){
            throw new AnyTxnParameterException(AnyTxnParameterRespCodeEnum.P_PARAMETER_EMPTY_FAULT);
        }
        TPmsGlamsControl tPmsGlamsControl1 = tPmsGlamsControlMapper.selectByPrimaryKey(String.valueOf(data.getId()));

        if(ObjectUtils.isEmpty(tPmsGlamsControl1)){
            throw new AnyTxnParameterException(AnyTxnParameterRespCodeEnum.D_DATA_NOT_EXISTS_TEMPLATE_FAULT);
        }
        TPmsGlamsControl tPmsGlamsControl = new TPmsGlamsControl();
        BeanCopier beanCopier = BeanCopier.create(TPmsGlamsControlDTO.class, TPmsGlamsControl.class, false);
        beanCopier.copy(data, tPmsGlamsControl, null);
        tPmsGlamsControl.setId(String.valueOf(data.getId()));

        return  ParameterCompare.getBuilder().withAfter(tPmsGlamsControl)
                .withBefore(tPmsGlamsControl1).build(TPmsGlamsControl.class);
    }


    @Override
    @Transactional(rollbackFor = Exception.class,propagation = Propagation.REQUIRED)
    //@ShardingSphereTransactionType(TransactionType.XA)
    protected boolean updateDb(ParmModificationRecord parmModificationRecord) {
        TPmsGlamsControl tPmsGlamsControl = JSON.parseObject(parmModificationRecord.getParmBody(), TPmsGlamsControl.class);
        tPmsGlamsControl.initUpdateDateTime();

        try {
            tPmsGlamsControlMapper.updateByPrimaryKeySelective(tPmsGlamsControl);
        } catch (Exception e) {
            logger.error("更新会计核算控制表参数表失败", e);
            throw new AnyTxnParameterException(AnyTxnParameterRespCodeEnum.D_INSERT_TPMS_GLAMS_CONTROL_FAULT);
        }
        return true;
    }

    @Override
    @Transactional(rollbackFor = Exception.class,propagation = Propagation.REQUIRED)
    //@ShardingSphereTransactionType(TransactionType.XA)
    protected boolean insertDb(ParmModificationRecord parmModificationRecord) {
        TPmsGlamsControl tPmsGlamsControl = JSON.parseObject(parmModificationRecord.getParmBody(), TPmsGlamsControl.class);
        tPmsGlamsControl.initUpdateDateTime();
        tPmsGlamsControl.initCreateDateTime();
        tPmsGlamsControl.setVersionNumber(1L);

        try {
            tPmsGlamsControlMapper.insert(tPmsGlamsControl);
        } catch (Exception e) {
            logger.error("新增会计核算控制表参数表失败", e);
            throw new AnyTxnParameterException(AnyTxnParameterRespCodeEnum.D_INSERT_TPMS_GLAMS_CONTROL_FAULT);
        }
        return true;
    }

    @Override
    @Transactional(rollbackFor = Exception.class,propagation = Propagation.REQUIRED)
    //@ShardingSphereTransactionType(TransactionType.XA)
    protected boolean deleteDb(ParmModificationRecord parmModificationRecord) {
        TPmsGlamsControl tPmsGlamsControl = JSON.parseObject(parmModificationRecord.getParmBody(), TPmsGlamsControl.class);

        int i1 = tPmsGlamsControlMapper.deleteByPrimaryKey(tPmsGlamsControl.getId());

        TPmsGlamsDefinitionDTO detail = pmsGlamsDefinitionService.detail(tPmsGlamsControl.getId());
        int i = tPmsGlamsControlSelfMapper.deleteByDefine(detail.getOrganizationNumber(), detail.getBranchid(), detail.getTableId());
        return i > 0 && i1 > 0;
    }
}
