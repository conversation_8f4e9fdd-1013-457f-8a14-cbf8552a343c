package com.anytech.anytxn.parameter.common.service;

import com.alibaba.fastjson.JSON;
import com.github.pagehelper.Page;
import com.github.pagehelper.PageHelper;
import com.anytech.anytxn.common.core.annotation.db.DeleteParameterAnnotation;
import com.anytech.anytxn.common.core.annotation.db.InsertParameterAnnotation;
import com.anytech.anytxn.common.core.annotation.db.UpdateParameterAnnotation;
import com.anytech.anytxn.business.base.audit.AbstractParameterService;
import com.anytech.anytxn.common.core.base.ParameterCompare;
import com.anytech.anytxn.business.base.audit.ParmModificationRecord;
import com.anytech.anytxn.common.core.base.PageResultDTO;
import com.anytech.anytxn.common.core.utils.BeanMapping;
import com.anytech.anytxn.common.core.utils.OrgNumberUtils;
import com.anytech.anytxn.common.sequence.utils.Number16IdGen;
import com.anytech.anytxn.common.core.utils.TenantUtils;
import com.anytech.anytxn.parameter.base.common.domain.dto.TransactionCodeConfigReqDTO;
import com.anytech.anytxn.parameter.base.common.domain.dto.TransactionCodeConfigResDTO;
import com.anytech.anytxn.parameter.base.common.service.ITransactionCodeConfigService;
import com.anytech.anytxn.parameter.base.common.enums.AnyTxnParameterRespCodeEnum;
import com.anytech.anytxn.parameter.base.common.exception.AnyTxnParameterException;
import com.anytech.anytxn.parameter.common.mapper.broadcast.ParmTransactionCodeConfigMapper;
import com.anytech.anytxn.parameter.common.mapper.broadcast.ParmTransactionCodeConfigSelfMapper;
import com.anytech.anytxn.parameter.base.common.domain.model.ParmTransactionCodeConfig;
import org.apache.commons.lang3.ObjectUtils;
//import org.apache.shardingsphere.transaction.annotation.ShardingSphereTransactionType;
//import org.apache.shardingsphere.transaction.core.TransactionType;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.StringUtils;

import java.util.ArrayList;
import java.util.List;

/**
 * 内部交易码配置表实现类
 * <AUTHOR> tingting
 * @date 2018/10/19
 */
@Service(value = "parm_transaction_code_config_serviceImpl")
public class TransactionCodeConfigServiceImpl extends AbstractParameterService implements ITransactionCodeConfigService {
    private Logger logger = LoggerFactory.getLogger(TransactionCodeConfigServiceImpl.class);

    @Autowired
    private ParmTransactionCodeConfigMapper parmTransactionCodeConfigMapper;

    @Autowired
    private ParmTransactionCodeConfigSelfMapper parmTransactionCodeConfigSelfMapper;

    @Autowired
    private Number16IdGen numberIdGenerator;

    /*
    @Override
    //@Cachekey(findKeys = {"organizationNumber","type"})
    public List<TransactionCodeConfigResDTO> selectAllCacheObject() {
        List<ParmTransactionCodeConfig> parm = parmTransactionCodeConfigSelfMapper.selectAll();
        return BeanMapping.copyList(parm, TransactionCodeConfigResDTO.class);
    }
    */

    /**
     * 根据机构号与类型查询
     *
     * @param orgNumber 机构号
     * @param type      类型
     * @return TransactionCodeConfigRes 内部交易码配置表返回参数
     */
    @Override
    //@PreGetProcess(args = {"#0","#1"})
    public TransactionCodeConfigResDTO findByOrgAndType(String orgNumber, String type) throws AnyTxnParameterException {
        ParmTransactionCodeConfig parmTransactionCodeConfig = parmTransactionCodeConfigSelfMapper.selectByOrgAndType(orgNumber, type);
        if (parmTransactionCodeConfig == null) {

            throw new AnyTxnParameterException(AnyTxnParameterRespCodeEnum.D_GET_IS_NULL_FAULT);
        }
        return BeanMapping.copy(parmTransactionCodeConfig, TransactionCodeConfigResDTO.class);
    }

    @Override
    @InsertParameterAnnotation(tableName = "parm_transaction_code_config", tableDesc = "Inner Transaction code")
    public ParameterCompare add(TransactionCodeConfigReqDTO transactionCodeConfigReqDTO) {
        //通过机构号   交易码  类型查询
        ParmTransactionCodeConfig parmTransactionCodeConfig = parmTransactionCodeConfigSelfMapper.selectByOrgAndTypeAndTransCode(transactionCodeConfigReqDTO.getOrganizationNumber(),transactionCodeConfigReqDTO.getTransactionCode(), transactionCodeConfigReqDTO.getType());

        if (ObjectUtils.isNotEmpty(parmTransactionCodeConfig)){
            throw new AnyTxnParameterException(AnyTxnParameterRespCodeEnum.D_GET_IS_NOT_NULL_FAULT);
        }
        ParmTransactionCodeConfig copy = BeanMapping.copy(transactionCodeConfigReqDTO, ParmTransactionCodeConfig.class);
        copy.setId(numberIdGenerator.generateId(TenantUtils.getTenantId())+"");
        return ParameterCompare
                .getBuilder()
                .withMainParmId(transactionCodeConfigReqDTO.getTransactionCode())
                .withAfter(copy)
                .build(ParmTransactionCodeConfig.class);
    }

    @Override
    @UpdateParameterAnnotation(tableName = "parm_transaction_code_config", tableDesc = "Inner Transaction code")
    public ParameterCompare modify(TransactionCodeConfigReqDTO transactionCodeConfigReqDTO) {
        if (ObjectUtils.isEmpty(transactionCodeConfigReqDTO)){
            throw new AnyTxnParameterException(AnyTxnParameterRespCodeEnum.P_PARAMETER_EMPTY_FAULT);
        }
        ParmTransactionCodeConfig parmTransactionCodeConfig = parmTransactionCodeConfigMapper.selectByPrimaryKey(transactionCodeConfigReqDTO.getId());
        if (ObjectUtils.isEmpty(parmTransactionCodeConfig)){
            throw new AnyTxnParameterException(AnyTxnParameterRespCodeEnum.D_EXIST_TRANS_CODE_ID_EXIST);
        }
        ParmTransactionCodeConfig copy = BeanMapping.copy(transactionCodeConfigReqDTO, ParmTransactionCodeConfig.class);
        return ParameterCompare
                .getBuilder()
                .withMainParmId(transactionCodeConfigReqDTO.getTransactionCode())
                .withAfter(copy)
                .withBefore(parmTransactionCodeConfig)
                .build(ParmTransactionCodeConfig.class);
    }

    @Override
    public TransactionCodeConfigResDTO findById(String id) {
        if (null == id){
            throw new AnyTxnParameterException(AnyTxnParameterRespCodeEnum.P_PARAMETER_EMPTY_FAULT);
        }
        ParmTransactionCodeConfig parmTransactionCodeConfig = parmTransactionCodeConfigMapper.selectByPrimaryKey(id);
        if (ObjectUtils.isEmpty(parmTransactionCodeConfig)){
            throw new AnyTxnParameterException(AnyTxnParameterRespCodeEnum.D_EXIST_TRANS_CODE_ID_EXIST);
        }

        return BeanMapping.copy(parmTransactionCodeConfig,TransactionCodeConfigResDTO.class);
    }

    @Override
    @DeleteParameterAnnotation(tableName = "parm_transaction_code_config", tableDesc = "Inner Transaction code")
    public ParameterCompare remove(String id) {
        if (null == id){
            throw new AnyTxnParameterException(AnyTxnParameterRespCodeEnum.P_PARAMETER_EMPTY_FAULT);
        }
        ParmTransactionCodeConfig parmTransactionCodeConfig = parmTransactionCodeConfigMapper.selectByPrimaryKey(id);
        if (ObjectUtils.isEmpty(parmTransactionCodeConfig)){
            throw new AnyTxnParameterException(AnyTxnParameterRespCodeEnum.D_EXIST_TRANS_CODE_ID_EXIST);
        }
        return ParameterCompare
                .getBuilder()
                .withMainParmId(parmTransactionCodeConfig.getTransactionCode())
                .withBefore(parmTransactionCodeConfig)
                .build(ParmTransactionCodeConfig.class);
    }

    @Override
    public PageResultDTO<TransactionCodeConfigResDTO> findByPage(Integer pageNum, Integer pageSize, TransactionCodeConfigReqDTO transactionCodeConfigReqDTO) {
        logger.info("分页查询内部交易码配置参数信息");
        if (null == transactionCodeConfigReqDTO){
            transactionCodeConfigReqDTO = new TransactionCodeConfigReqDTO();
        }
        transactionCodeConfigReqDTO.setOrganizationNumber(StringUtils.isEmpty(transactionCodeConfigReqDTO.getOrganizationNumber())? OrgNumberUtils.getOrg() : transactionCodeConfigReqDTO.getOrganizationNumber());
        Page<TransactionCodeConfigResDTO> page = PageHelper.startPage(pageNum, pageSize);
        List<ParmTransactionCodeConfig> parmTransactionCodeConfigs = parmTransactionCodeConfigSelfMapper.selectByCondition(transactionCodeConfigReqDTO);

        if (parmTransactionCodeConfigs.isEmpty()) {
            logger.error("未查内部交易码配置参数");
            parmTransactionCodeConfigs = new ArrayList<>();
        }
        List<TransactionCodeConfigResDTO> currencyRateRes = BeanMapping.copyList(parmTransactionCodeConfigs, TransactionCodeConfigResDTO.class);
        return new PageResultDTO<>(pageNum, pageSize, page.getTotal(), page.getPages(), currencyRateRes);
    }

    @Override
    public List<TransactionCodeConfigResDTO> findByOrgan(String organizationNumber) {
        if (null == organizationNumber){
            throw new AnyTxnParameterException(AnyTxnParameterRespCodeEnum.P_PARAMETER_EMPTY_FAULT);
        }
        List<ParmTransactionCodeConfig> parmTransactionCodeConfigs = parmTransactionCodeConfigSelfMapper.selectAll(organizationNumber);
        if (null == parmTransactionCodeConfigs){
            throw new AnyTxnParameterException(AnyTxnParameterRespCodeEnum.QUERY_TRANS_CODE_FAULT);
        }
        return BeanMapping.copyList(parmTransactionCodeConfigs, TransactionCodeConfigResDTO.class);
    }

    @Override
    @Transactional(rollbackFor = Exception.class,propagation = Propagation.REQUIRED)
    //@ShardingSphereTransactionType(TransactionType.XA)
    protected boolean updateDb(ParmModificationRecord parmModificationRecord) throws Exception {
        ParmTransactionCodeConfig parmTransactionCodeConfig = JSON.parseObject(parmModificationRecord.getParmBody(), ParmTransactionCodeConfig.class);
        parmTransactionCodeConfig.initUpdateDateTime();
        parmTransactionCodeConfig.setUpdateBy(parmModificationRecord.getApplicationBy());

        int i = parmTransactionCodeConfigMapper.updateByPrimaryKey(parmTransactionCodeConfig);
        return i == 1;
    }

    @Override
    @Transactional(rollbackFor = Exception.class,propagation = Propagation.REQUIRED)
    //@ShardingSphereTransactionType(TransactionType.XA)
    protected boolean insertDb(ParmModificationRecord parmModificationRecord) throws Exception {
        ParmTransactionCodeConfig parmTransactionCodeConfig = JSON.parseObject(parmModificationRecord.getParmBody(), ParmTransactionCodeConfig.class);
        parmTransactionCodeConfig.initUpdateDateTime();
        parmTransactionCodeConfig.initCreateDateTime();
        parmTransactionCodeConfig.setUpdateBy(parmModificationRecord.getApplicationBy());

        int i = parmTransactionCodeConfigMapper.insertSelective(parmTransactionCodeConfig);

        return i == 1;
    }

    @Override
    @Transactional(rollbackFor = Exception.class,propagation = Propagation.REQUIRED)
    //@ShardingSphereTransactionType(TransactionType.XA)
    protected boolean deleteDb(ParmModificationRecord parmModificationRecord) throws Exception {
        ParmTransactionCodeConfig parmTransactionCodeConfig = JSON.parseObject(parmModificationRecord.getParmBody(), ParmTransactionCodeConfig.class);
        int i = parmTransactionCodeConfigMapper.deleteByPrimaryKey(parmTransactionCodeConfig.getId());
        return i == 1;
    }
}
