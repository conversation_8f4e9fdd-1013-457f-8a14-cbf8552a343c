package com.anytech.anytxn.parameter.common.controller;

import com.anytech.anytxn.common.core.base.ParameterCompare;
import com.anytech.anytxn.common.core.base.PageResultDTO;
import com.anytech.anytxn.common.core.base.AnyTxnHttpResponse;
import com.anytech.anytxn.common.core.base.BizBaseController;
import com.anytech.anytxn.parameter.base.common.domain.dto.ParmStatementMessageDTO;
import com.anytech.anytxn.parameter.base.common.domain.dto.ParmStatementMessageReqBodyDTO;
import com.anytech.anytxn.parameter.base.common.service.IParamStatementMessageService;
import org.springframework.web.bind.annotation.*;

import jakarta.annotation.Resource;

/**
@Author: sukang
@Date: 2023/3/28 18:36
@Description:
*/
@RestController
public class StatementMessageController extends BizBaseController {

    @Resource
    private IParamStatementMessageService paramStatementMessageService;

    @GetMapping("/param/statementMsg/pageNum/{pageNum}/pageSize/{pageSize}")
    public AnyTxnHttpResponse<PageResultDTO<ParmStatementMessageDTO>> getByPage(
            @PathVariable(value = "pageNum")Integer pageNum, @PathVariable(value = "pageSize")Integer pageSize,
            ParmStatementMessageDTO parmStatementMessageDTO){

        return AnyTxnHttpResponse.success(paramStatementMessageService.selectByPage(pageSize, pageNum, parmStatementMessageDTO));
    }


    @GetMapping("/param/statementMsg/{id}")
    public AnyTxnHttpResponse<ParmStatementMessageReqBodyDTO> getByPage(
            @PathVariable(value = "id") String id){
        return AnyTxnHttpResponse.success(paramStatementMessageService.selectDetailById(id));
    }

    @PostMapping("/param/statementMsg")
    public AnyTxnHttpResponse<ParameterCompare> addStatementMsg(@RequestBody ParmStatementMessageReqBodyDTO parmStatementMessageReqBodyDTO){
        return AnyTxnHttpResponse.success(paramStatementMessageService.add(parmStatementMessageReqBodyDTO));
    }

    @PutMapping("/param/statementMsg")
    public AnyTxnHttpResponse<ParameterCompare> updateStatementMsg(@RequestBody ParmStatementMessageReqBodyDTO parmStatementMessageReqBodyDTO){
        return AnyTxnHttpResponse.success(paramStatementMessageService.update(parmStatementMessageReqBodyDTO));
    }


    @DeleteMapping("/param/statementMsg/{id}")
    public AnyTxnHttpResponse<ParameterCompare> deleteStatementMsg( @PathVariable(value = "id") String id){
        return AnyTxnHttpResponse.success(paramStatementMessageService.delete(id));
    }


}
