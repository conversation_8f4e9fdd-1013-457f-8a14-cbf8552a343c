package com.anytech.anytxn.parameter.accounting.controller;

import com.anytech.anytxn.common.core.base.ParameterCompare;
import com.anytech.anytxn.common.core.base.PageResultDTO;
import com.anytech.anytxn.common.core.base.AnyTxnHttpResponse;
import com.anytech.anytxn.common.core.base.BizBaseController;
import com.anytech.anytxn.parameter.base.accounting.domain.dto.ParamAccountantDicMappingReqDTO;
import com.anytech.anytxn.parameter.base.accounting.service.IParamAccountantDictService;
import jakarta.annotation.Resource;
import org.springframework.web.bind.annotation.*;


/**
 * @Author: sukang
 * @Date: 2022/12/5 21:12
 */
@RestController
public class ParamAccountantDictController extends BizBaseController {


    @Resource
    private IParamAccountantDictService paramAccountantDictService;



    @GetMapping("/param/accountantDict/pageNum/{pageNum}/pageSize/{pageSize}")
    public AnyTxnHttpResponse<PageResultDTO<ParamAccountantDicMappingReqDTO>> getByPage(@PathVariable(value = "pageNum")Integer pageNum,
                                                                                        @PathVariable(value = "pageSize")Integer pageSize,
                                                                                        ParamAccountantDicMappingReqDTO paramAccountantDicMappingReqDTO){
        return AnyTxnHttpResponse.success(paramAccountantDictService.getByPage(pageNum, pageSize,paramAccountantDicMappingReqDTO));

    }


    @GetMapping("/param/accountantDict/{id}")
    public AnyTxnHttpResponse<ParamAccountantDicMappingReqDTO> getDetail(
            @PathVariable(value = "id") String id){
        return AnyTxnHttpResponse.success(paramAccountantDictService.getDetails(id));
    }


    @PostMapping("/param/accountantDict")
    public AnyTxnHttpResponse<ParameterCompare> insert(@RequestBody ParamAccountantDicMappingReqDTO paramAccountantDicMappingReqDTO) {
        return AnyTxnHttpResponse.success(paramAccountantDictService.addParam(paramAccountantDicMappingReqDTO));
    }


    @PutMapping("/param/accountantDict")
    public AnyTxnHttpResponse<ParameterCompare> updateByPrimaryKeySelective(@RequestBody ParamAccountantDicMappingReqDTO paramAccountantDicMappingReqDTO) {
        return AnyTxnHttpResponse.success(paramAccountantDictService.updateById(paramAccountantDicMappingReqDTO));
    }

    @DeleteMapping("/param/accountantDict/{id}")
    public AnyTxnHttpResponse<Object> deleteById(@PathVariable(value = "id") String id) {
        return AnyTxnHttpResponse.success(paramAccountantDictService.deleteById(id));
    }








}
