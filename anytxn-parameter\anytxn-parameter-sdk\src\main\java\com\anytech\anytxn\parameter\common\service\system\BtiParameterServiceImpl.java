package com.anytech.anytxn.parameter.common.service.system;

import com.alibaba.fastjson.JSON;
import com.anytech.anytxn.common.core.annotation.db.DeleteParameterAnnotation;
import com.anytech.anytxn.common.core.annotation.db.InsertParameterAnnotation;
import com.anytech.anytxn.common.core.annotation.db.UpdateParameterAnnotation;
import com.anytech.anytxn.business.base.audit.AbstractParameterService;
import com.anytech.anytxn.common.core.base.ParameterCompare;
import com.anytech.anytxn.business.base.audit.ParmModificationRecord;
import com.anytech.anytxn.common.core.base.PageResultDTO;
import com.anytech.anytxn.common.core.utils.BeanMapping;
import com.anytech.anytxn.common.core.utils.OrgNumberUtils;
import com.anytech.anytxn.common.sequence.utils.Number16IdGen;
import com.anytech.anytxn.common.core.utils.TenantUtils;
import com.anytech.anytxn.parameter.base.common.enums.AnyTxnParameterRespCodeEnum;
import com.anytech.anytxn.parameter.base.common.exception.AnyTxnParameterException;
import com.anytech.anytxn.parameter.common.mapper.broadcast.system.ParmBtiGroupMapper;
import com.anytech.anytxn.parameter.common.mapper.broadcast.system.ParmBtiGroupSelfMapper;
import com.anytech.anytxn.parameter.base.account.domain.model.ParmBtiGroup;
import com.anytech.anytxn.parameter.base.common.domain.dto.ParmBtiReqDTO;
import com.anytech.anytxn.parameter.base.common.domain.dto.ParmBtiResDTO;
import com.anytech.anytxn.parameter.base.common.service.IBtiParameterService;
import com.github.pagehelper.Page;
import com.github.pagehelper.PageHelper;
//import org.apache.shardingsphere.transaction.annotation.ShardingSphereTransactionType;
//import org.apache.shardingsphere.transaction.core.TransactionType;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.StringUtils;

import java.util.List;

/**
 * BTI参数码
 *
 * <AUTHOR>
 * @date 2022-01-20
 **/
@Service("parm_bti_serviceImpl")
public class BtiParameterServiceImpl extends AbstractParameterService implements IBtiParameterService {

    private final Logger logger = LoggerFactory.getLogger(BtiParameterServiceImpl.class);
    @Autowired
    private ParmBtiGroupMapper parmBtiGroupMapper;
    @Autowired
    private Number16IdGen numberIdGenerator;
    @Autowired
    private ParmBtiGroupSelfMapper parmBtiGroupSelfMapper;


    /**
     * 添加BTI参数码
     *
     * @param parmBtiReqDTO BTI参数码入参对象
     * @return BTI参数码详情
     * @throws AnyTxnParameterException
     */
    @Override
    @InsertParameterAnnotation(tableName = "parm_bti", tableDesc = "param bti code")
    public ParameterCompare add(ParmBtiReqDTO parmBtiReqDTO) {
        ParmBtiGroup parmBtiGroup = parmBtiGroupSelfMapper.selectParmBtiCodeByOrgAndAgeCodeStartCalculateAndStartAtAgeCode(parmBtiReqDTO.getOrganizationNumber(), parmBtiReqDTO.getBtiAgeCodeStartCalculate(), parmBtiReqDTO.getBtiBlockStartAtAgeCode());

        if (null != parmBtiGroup) {
            logger.warn("参数表ID和封锁码已存在, orgNumber={}, blockCode={}",
                    OrgNumberUtils.getOrg(), parmBtiReqDTO.getBtiBlockStartAtAgeCode(), parmBtiGroup.getBtiAgeCodeStartCalculate());
            throw new AnyTxnParameterException(AnyTxnParameterRespCodeEnum.P_EXIST_BLOCK_CODE_AND_ID_FAULT);
        }

        // 构建BTI参数DO
        ParmBtiGroup parmBti = BeanMapping.copy(parmBtiReqDTO, ParmBtiGroup.class);
        parmBti.setId(numberIdGenerator.generateId(TenantUtils.getTenantId()) + "");

        return ParameterCompare
                .getBuilder()
                .withAfter(parmBti)
                .build(ParmBtiGroup.class);
    }

    /**
     * 删除bti参数，通过tableId 和 Indicator
     *
     * @param id tableId
     * @return true:成功|false:失败
     * @throws AnyTxnParameterException
     */
    @Override
    @DeleteParameterAnnotation(tableName = "parm_bti", tableDesc = "parm bti code")
    public ParameterCompare remove(String id) {
        if (id == null || StringUtils.isEmpty(id)) {
            throw new AnyTxnParameterException(AnyTxnParameterRespCodeEnum.P_PARAMETER_EMPTY_FAULT);
        }

        ParmBtiGroup parmBtiGroup = parmBtiGroupMapper.selectByPrimaryKey(id);
        if (parmBtiGroup == null) {
            logger.error("删除BTI参数码, tableId={}, ", id);
            throw new AnyTxnParameterException(AnyTxnParameterRespCodeEnum.P_BLOCK_CODE_CUSTOMER_BY_ID_FAULT);
        }

        return ParameterCompare
                .getBuilder()
                .withBefore(parmBtiGroup)
                .build(ParmBtiGroup.class);
    }

    /**
     * 修改BTI参数锁码
     *
     * @param parmBtiReqDTO BTI参数码入参对象
     * @return BTI参数码码详情
     * @throws AnyTxnParameterException
     */
    @Override
    @UpdateParameterAnnotation(tableName = "parm_bti", tableDesc = "param bti code")
    public ParameterCompare modify(ParmBtiReqDTO parmBtiReqDTO) {
        if (parmBtiReqDTO.getId() == null) {
            throw new AnyTxnParameterException(AnyTxnParameterRespCodeEnum.P_BLOCK_CODE_CUSTOMER_REQ_ID_IS_NULL_FAULT);
        }

        ParmBtiGroup parmBtiGroup = parmBtiGroupMapper.selectByPrimaryKey(parmBtiReqDTO.getId());
        if (parmBtiGroup == null) {
            logger.error("修改客户层封锁码, 通过主键id({})未找到数据", parmBtiReqDTO.getId());
            throw new AnyTxnParameterException(AnyTxnParameterRespCodeEnum.P_BLOCK_CODE_CUSTOMER_BY_ID_FAULT);
        }

        if ("".equals(parmBtiReqDTO.getBtiBlockStartAtAgeCode()) || "".equals(parmBtiReqDTO.getBtiAgeCodeStartCalculate())) {
            throw new AnyTxnParameterException(AnyTxnParameterRespCodeEnum.P_BLOCK_CODE_CUSTOMER_BY_ID_FAULT);
        }

        if (!parmBtiGroup.getBtiBlockStartAtAgeCode().equals(parmBtiReqDTO.getBtiBlockStartAtAgeCode()) || !parmBtiGroup.getBtiAgeCodeStartCalculate().equals(parmBtiReqDTO.getBtiAgeCodeStartCalculate())) {
            List<ParmBtiGroup> dataList = parmBtiGroupSelfMapper.selectListByOrgNumber(parmBtiGroup.getOrganizationNumber(), true);
            dataList.forEach(element -> {
                boolean b1 = element.getBtiAgeCodeStartCalculate() != null && element.getBtiAgeCodeStartCalculate().equals(parmBtiReqDTO.getBtiAgeCodeStartCalculate());
                boolean b2 = element.getBtiBlockStartAtAgeCode() != null && element.getBtiBlockStartAtAgeCode().equals(parmBtiReqDTO.getBtiBlockStartAtAgeCode());

                if (b1 && b2) {
                    logger.error("修改客户层封锁码, 封锁码已存在({})", parmBtiReqDTO.getBtiAgeCodeStartCalculate(), parmBtiReqDTO.getBtiAgeCodeStartCalculate());
                    throw new AnyTxnParameterException(AnyTxnParameterRespCodeEnum.D_EXIST_PARM_BLOCK_CODE_CUSTOMER_FAULT);
                }
            });
        }

        ParmBtiGroup modify = BeanMapping.copy(parmBtiReqDTO, ParmBtiGroup.class);
        return ParameterCompare
                .getBuilder()
                .withAfter(modify)
                .withBefore(parmBtiReqDTO)
                .build(ParmBtiGroup.class);
    }

    /**
     * 通过id获取BTI参数码
     * @param id BTI参数码ID
     * @return BTI参数码详情
     * @throws AnyTxnParameterException
     */
    @Override
    public ParmBtiResDTO find(String id) {
        if (id == null) {
            throw new AnyTxnParameterException(AnyTxnParameterRespCodeEnum.P_PARAMETER_EMPTY_FAULT);
        }

        ParmBtiGroup parmBtiGroup = parmBtiGroupMapper.selectByPrimaryKey(id);
        if (parmBtiGroup == null) {
            logger.error("获取BTI参数码详情, 通过主键id({})未找到数据", id);
            throw new AnyTxnParameterException(AnyTxnParameterRespCodeEnum.P_BLOCK_CODE_CUSTOMER_BY_ID_FAULT);
        }
        return BeanMapping.copy(parmBtiGroup, ParmBtiResDTO.class);
    }

    /**
     * 查询BTI参数码集合
     *
     * @param tableId BTI参数对应的tableId，如果该参数为空则查询全部数据
     * @return BTI参数码的集合
     * @throws AnyTxnParameterException
     */
    @Override
    public PageResultDTO<ParmBtiResDTO> findListByTableId(String tableId, Integer pageNum, Integer pageSize) {
        Page page = PageHelper.startPage(pageNum, pageSize);
        List<ParmBtiGroup> blockCodeList = parmBtiGroupSelfMapper.findListByTableId(tableId);
        BeanMapping.copyList(blockCodeList, ParmBtiResDTO.class);
        return new PageResultDTO(pageNum, pageSize, page.getTotal(), page.getPages(), blockCodeList);

    }


    @Override
    @Transactional(rollbackFor = Exception.class, propagation = Propagation.REQUIRED)
    //@ShardingSphereTransactionType(TransactionType.XA)
    protected boolean insertDb(ParmModificationRecord parmModificationRecord) throws Exception {
        ParmBtiGroup parmBtiGroup = JSON.parseObject(parmModificationRecord.getParmBody(), ParmBtiGroup.class);
        parmBtiGroup.initUpdateDateTime();
        parmBtiGroup.initCreateDateTime();
        if ("".equals(parmBtiGroup.getBtiAgeCodeStartCalculate()) || "".equals(parmBtiGroup.getBtiBlockStartAtAgeCode())) {
            parmBtiGroup.setBtiBlockStartAtAgeCode(null);
            parmBtiGroup.setBtiAgeCodeStartCalculate(null);
        }
        int res = parmBtiGroupMapper.insert(parmBtiGroup);
        return res > 0;
    }

    @Override
    @Transactional(rollbackFor = Exception.class, propagation = Propagation.REQUIRED)
    //@ShardingSphereTransactionType(TransactionType.XA)
    protected boolean deleteDb(ParmModificationRecord parmModificationRecord) throws Exception {
        ParmBtiGroup parmBtiGroup = JSON.parseObject(parmModificationRecord.getParmBody(), ParmBtiGroup.class);
        int deleteRow = parmBtiGroupMapper.deleteByPrimaryKey(parmBtiGroup.getId());
        return deleteRow > 0;
    }

    @Override
    @Transactional(rollbackFor = Exception.class,propagation = Propagation.REQUIRED)
    //@ShardingSphereTransactionType(TransactionType.XA)
    protected boolean updateDb(ParmModificationRecord parmModificationRecord) throws Exception {
        ParmBtiGroup parmBtiGroup = JSON.parseObject(parmModificationRecord.getParmBody(), ParmBtiGroup.class);
        parmBtiGroup.initUpdateDateTime();
        if ("".equals(parmBtiGroup.getBtiAgeCodeStartCalculate()) || "".equals(parmBtiGroup.getBtiBlockStartAtAgeCode())) {
            parmBtiGroup.setBtiBlockStartAtAgeCode(null);
            parmBtiGroup.setBtiAgeCodeStartCalculate(null);
        }
        int res = parmBtiGroupMapper.updateByPrimaryKey(parmBtiGroup);
        return res > 0;
    }

}
