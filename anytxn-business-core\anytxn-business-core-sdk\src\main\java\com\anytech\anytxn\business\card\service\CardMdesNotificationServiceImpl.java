package com.anytech.anytxn.business.card.service;

import com.anytech.anytxn.common.core.enums.AnyTxnCommonRespCodeEnum;
import com.anytech.anytxn.common.core.exception.AnyTxnCommonException;
import com.anytech.anytxn.common.core.utils.BeanMapping;
import com.anytech.anytxn.business.base.card.enums.CardMdesCustomerServiceCodeEnum;
import com.anytech.anytxn.business.base.card.enums.CardMdesCustomerServiceReasonCodeEnum;
import com.anytech.anytxn.business.base.card.enums.CardMdesTokenStatusEnum;
import com.anytech.anytxn.business.dao.card.mapper.CardMdesTokenHistoryInfoMapper;
import com.anytech.anytxn.business.dao.card.mapper.CardMdesTokenInfoMapper;
import com.anytech.anytxn.business.dao.card.model.CardMdesTokenHistoryInfo;
import com.anytech.anytxn.business.dao.card.model.CardMdesTokenInfo;
import com.anytech.anytxn.business.base.card.service.ICardMdesNotificationService;
import com.anytech.anytxn.common.core.utils.TenantUtils;
import com.anytech.anytxn.common.sequence.utils.SequenceIdGen;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 卡片MDES状态更新通知类
 *
 * <AUTHOR>
 * @time 2024/4/29
 */
@Service
@Slf4j
public class CardMdesNotificationServiceImpl implements ICardMdesNotificationService {

    @Autowired
    private CardMdesTokenInfoMapper cardMdesTokenInfoMapper;

    @Autowired
    private CardMdesTokenHistoryInfoMapper cardMdesTokenHistoryInfoMapper;

    @Autowired
    private SequenceIdGen sequenceIdGen;



    @Override
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = Exception.class)
    public void notificationByCardUpdate(String cardNumber, String csCode, CardMdesCustomerServiceReasonCodeEnum csReasonCode) {
        if (StringUtils.equals(CardMdesCustomerServiceCodeEnum.NO_SERVICE_REQUIRED.getCode(), csCode)) {
            log.info("内部卡片封锁码或状态更新，MDES Token信息无需更新！cardNumber：{}, csCode：{}, csReasonCode：{}", cardNumber, csCode, csReasonCode.getCode());
            return;
        }
        List<CardMdesTokenInfo> cardMdesTokenInfoList = new ArrayList<>();
        if (StringUtils.equals(CardMdesCustomerServiceCodeEnum.UNSUSPEND.getCode(), csCode)) {
            cardMdesTokenInfoList = cardMdesTokenInfoMapper.selectByCardNumberAndStatus(cardNumber, CardMdesTokenStatusEnum.SUSPENDED.getCode());
        } else if (StringUtils.equals(CardMdesCustomerServiceCodeEnum.SUSPEND.getCode(), csCode)) {
            cardMdesTokenInfoList = cardMdesTokenInfoMapper.selectByCardNumberAndStatus(cardNumber, CardMdesTokenStatusEnum.ACTIVE.getCode());
        } else if (StringUtils.equals(CardMdesCustomerServiceCodeEnum.DELETE.getCode(), csCode)) {
            List<CardMdesTokenInfo> originCardMdesTokenInfoList = cardMdesTokenInfoMapper.selectByCardNumberAndStatus(cardNumber, null);
            cardMdesTokenInfoList = originCardMdesTokenInfoList.stream().filter(o -> !StringUtils.equals(CardMdesTokenStatusEnum.DEACTIVATED.getCode(), o.getTokenStatus())).collect(Collectors.toList());
        }
        List<CardMdesTokenHistoryInfo> dbHistoryUpdateList = new ArrayList<>();
        for (CardMdesTokenInfo cardMdesTokenInfo : cardMdesTokenInfoList) {
            CardMdesTokenHistoryInfo tokenHistoryInfo = BeanMapping.copy(cardMdesTokenInfo, CardMdesTokenHistoryInfo.class);
            cardMdesTokenInfo.setCsCode(csCode);
            switch (csCode) {
                case "01" :
                    cardMdesTokenInfo.setTokenStatus(CardMdesTokenStatusEnum.SUSPENDED.getCode());
                    break;
                case "02" :
                    cardMdesTokenInfo.setTokenStatus(CardMdesTokenStatusEnum.ACTIVE.getCode());
                    break;
                case "03" :
                    cardMdesTokenInfo.setTokenStatus(CardMdesTokenStatusEnum.DEACTIVATED.getCode());
                    break;
                default:
            }
            cardMdesTokenInfo.setCsReasonCode(csReasonCode.getCode());
            cardMdesTokenInfo.setUpdateTime(LocalDateTime.now());
            cardMdesTokenInfo.setUpdateBy("System Status Update");
            int i = cardMdesTokenInfoMapper.updateByPrimaryKeySelective(cardMdesTokenInfo);
            if (i == 0) {
                log.info("内部卡片封锁码或状态更新，MDES Token信息更新失败！TUR：{}，csCode：{}", cardMdesTokenInfo.getTokenUniqueRef(), csCode);
                throw new AnyTxnCommonException(AnyTxnCommonRespCodeEnum.D_ERR);
            }

            tokenHistoryInfo.setId(sequenceIdGen.generateId(TenantUtils.getTenantId()));
            dbHistoryUpdateList.add(tokenHistoryInfo);
        }
        if (!dbHistoryUpdateList.isEmpty()) {
            //插入更新历史
            int i = cardMdesTokenHistoryInfoMapper.batchInsert(dbHistoryUpdateList);
            if (i != dbHistoryUpdateList.size()) {
                log.info("插入MDES Token更新历史失败！应更新数：{}，实际更新数：{}", dbHistoryUpdateList.size(), i);
                throw new AnyTxnCommonException(AnyTxnCommonRespCodeEnum.D_ERR);
            }
        }
    }

    @Override
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = Exception.class)
    public void notificationByCardUpdate(String cardNumber, String csCode) {
        notificationByCardUpdate(cardNumber, csCode, CardMdesCustomerServiceReasonCodeEnum.Z);
    }
}
