package com.anytech.anytxn.parameter.common.service.system;

import com.alibaba.fastjson.JSON;
import com.github.pagehelper.Page;
import com.github.pagehelper.PageHelper;
import com.anytech.anytxn.common.core.annotation.db.DeleteParameterAnnotation;
import com.anytech.anytxn.common.core.annotation.db.InsertParameterAnnotation;
import com.anytech.anytxn.common.core.annotation.db.UpdateParameterAnnotation;
import com.anytech.anytxn.business.base.audit.AbstractParameterService;
import com.anytech.anytxn.common.core.base.ParameterCompare;
import com.anytech.anytxn.business.base.audit.ParmModificationRecord;
import com.anytech.anytxn.common.core.utils.OrgNumberUtils;
import com.anytech.anytxn.parameter.base.common.constants.Constants;
import com.anytech.anytxn.common.core.base.PageResultDTO;
import com.anytech.anytxn.common.core.utils.BeanMapping;
import com.anytech.anytxn.common.sequence.utils.Number16IdGen;
import com.anytech.anytxn.common.core.utils.TenantUtils;
import com.anytech.anytxn.parameter.base.common.enums.ParameterRepDetailEnum;
import com.anytech.anytxn.parameter.base.common.domain.dto.system.HolidayListReqDTO;
import com.anytech.anytxn.parameter.base.common.domain.dto.system.HolidayListResDTO;
import com.anytech.anytxn.parameter.base.common.service.system.IHoliDayListService;
import com.anytech.anytxn.parameter.base.common.enums.AnyTxnParameterRespCodeEnum;
import com.anytech.anytxn.parameter.base.common.exception.AnyTxnParameterException;
import com.anytech.anytxn.parameter.common.mapper.broadcast.system.ParmHolidayListMapper;
import com.anytech.anytxn.parameter.common.mapper.broadcast.system.ParmHolidayListSelfMapper;
import com.anytech.anytxn.parameter.base.common.domain.model.system.ParmHolidayList;
import com.anytech.anytxn.parameter.base.common.utils.DateTimeUtils;
import lombok.extern.slf4j.Slf4j;
//import org.apache.shardingsphere.transaction.annotation.ShardingSphereTransactionType;
//import org.apache.shardingsphere.transaction.core.TransactionType;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;
import org.springframework.util.ObjectUtils;
import org.springframework.util.StringUtils;

import java.time.LocalDate;
import java.util.*;


/**
 * Description   假日 业务接口实现
 * Copyright:	Copyright (c)
 * Company:		江融信
 * Author:		lichao
 * Version:		1.0
 * Create at:	2018/9/21 下午5:41
 * <p>
 * 修改历史:
 * 日期    作者    版本  修改描述
 * ------------------------------------------------------------------
 **/
@Slf4j
@Service("parm_holiday_list_serviceImpl")
public class HoliDayListServiceImpl extends AbstractParameterService implements IHoliDayListService {

    @Autowired
    private ParmHolidayListMapper holidayListMapper;
    @Autowired
    private ParmHolidayListSelfMapper holidayListSelfMapper;
    @Autowired
    private Number16IdGen numberIdGenerator;

    /**
     * @Description: 根据参数表的假日列表ID（holiday_list_id）+ 假日（holiday_day）读取假日列表表数据
     * @Param holidayListId 假日列表id
     * @Param holidayDay 假日
     * @return: HolidayListRes
     * @Date: 2018/9/21
     **/
    @Override
    public HolidayListResDTO findByHolidayAndId(String holidayListId, String holidayDay, String organizationNumber){

        if (holidayListId == null || StringUtils.isEmpty(holidayDay)) {
            throw new AnyTxnParameterException(AnyTxnParameterRespCodeEnum.P_PARAMETER_EMPTY_FAULT);
        }

        organizationNumber = StringUtils.isEmpty(organizationNumber) ? OrgNumberUtils.getOrg() : organizationNumber;
        ParmHolidayList parmHolidayList = holidayListSelfMapper.selectByHolidayAndId(holidayListId, DateTimeUtils.parse(holidayDay), organizationNumber);
        if(parmHolidayList==null){
            throw new AnyTxnParameterException(AnyTxnParameterRespCodeEnum.D_QUERY_PARM_HOLIDAY_LIST_FAULT);

        }
        return BeanMapping.copy(parmHolidayList, HolidayListResDTO.class);
    }

    /**
     * 分页查询假日参数表信息
     *
     * @param pageNum
     * @param pageSize
     * @return PageResultDTO<HolidayListRes> 假日参数响应参数
     * @throws AnyTxnParameterException
     */
    @Override
    public PageResultDTO<HolidayListResDTO> findAll(Integer pageNum, Integer pageSize, String organizationNumber,String holidayListId,String description){
        Page<ParmHolidayList> page = PageHelper.startPage(pageNum, pageSize);
        organizationNumber = StringUtils.isEmpty(organizationNumber) ? OrgNumberUtils.getOrg() : organizationNumber;
        // 基于listId和机构分组查询
        List<ParmHolidayList> holidayListIds = holidayListSelfMapper.selectAllHolidayListId(organizationNumber,holidayListId,description);
        for(ParmHolidayList parmHolidayList:holidayListIds){
            //根据假日列表ID查询
            List<ParmHolidayList> parmHolidayLists = holidayListSelfMapper.selectByHolidayListId(parmHolidayList.getHolidayListId(), parmHolidayList.getOrganizationNumber());
            parmHolidayList.setDescription(parmHolidayLists.get(0).getDescription());
            parmHolidayList.setStatus(parmHolidayLists.get(0).getStatus());
        }
        List<HolidayListResDTO> res = BeanMapping.copyList(holidayListIds, HolidayListResDTO.class);
        return new PageResultDTO<>(pageNum,pageSize,page.getTotal(), page.getPages(),res);
    }

    @Override
    @InsertParameterAnnotation(tableName = "parm_holiday_list",tableDesc = "Holiday",isJoinTable = true)
    public ParameterCompare addParmHolidayList(HolidayListReqDTO holidayListReq){
        if(ObjectUtils.isEmpty(holidayListReq)){
            throw new AnyTxnParameterException(AnyTxnParameterRespCodeEnum.P_PARAMETER_EMPTY_FAULT);
        }
        List<ParmHolidayList> isExist = holidayListSelfMapper.selectByHolidayListId(holidayListReq.getHolidayListId(), holidayListReq.getOrganizationNumber());
        if(!CollectionUtils.isEmpty(isExist)){
            throw new AnyTxnParameterException(AnyTxnParameterRespCodeEnum.P_PARAMETER_DATA_EXIST, ParameterRepDetailEnum.HOLIDAY_ID_EXIST);
        }

        /*List<LocalDate> holidayDayList = holidayListReq.getHolidayDayList();
        if(CollectionUtils.isEmpty(holidayDayList)){
            throw new AnyTxnParameterException(AnyTxnParameterRespCodeEnum.P_PARAMETER_EMPTY_FAULT, ParameterRepDetailEnum.HOLIDAY_PARAM_NULL);
        }*/
        return ParameterCompare
                .getBuilder()
                .withAfter(holidayListReq)
                .withMainParmId(holidayListReq.getHolidayListId())
                .build(HolidayListReqDTO.class);
    }

    /**
     * 更新假日参数
     * @param holidayListReq 假日参数入参对象
     * @return HolidayListRes 假日参数响应参数
     * @throws AnyTxnParameterException
     */
    @Override
    @UpdateParameterAnnotation(tableName = "parm_holiday_list",tableDesc = "Holiday",isJoinTable = true)
    public ParameterCompare modifyParmHolidayList(HolidayListReqDTO holidayListReq){
        if(ObjectUtils.isEmpty(holidayListReq)){
            throw new AnyTxnParameterException(AnyTxnParameterRespCodeEnum.P_PARAMETER_EMPTY_FAULT);
        }
        HolidayListResDTO beforeRes = findByHolidayListId(holidayListReq.getHolidayListId(), holidayListReq.getOrganizationNumber());
        if (ObjectUtils.isEmpty(beforeRes)) {
            throw new AnyTxnParameterException(AnyTxnParameterRespCodeEnum.P_PARAMETER_DATA_NOT_EXIST);
        }
        /*if(CollectionUtils.isEmpty(holidayListReq.getHolidayDayList())){
            throw new AnyTxnParameterException(AnyTxnParameterRespCodeEnum.P_PARAMETER_EMPTY_FAULT, ParameterRepDetailEnum.HOLIDAY_PARAM_NULL);
        }*/
        HolidayListReqDTO before = BeanMapping.copy(beforeRes, HolidayListReqDTO.class);
        return ParameterCompare
                .getBuilder()
                .withBefore(before)
                .withAfter(holidayListReq)
                .withMainParmId(holidayListReq.getHolidayListId())
                .build(HolidayListReqDTO.class);
    }

    /**
     * 通过Id主键删除假日参数
     *
     * @param id 主键
     * @return Boolean
     * @throws AnyTxnParameterException
     */
    @Override
    public Boolean removeParmHolidayList(String id){
        if (id == null) {
            throw new AnyTxnParameterException(AnyTxnParameterRespCodeEnum.P_PARAMETER_EMPTY_FAULT);
        }
        ParmHolidayList parmHolidayList = holidayListMapper.selectByPrimaryKey(id);
        // 如果该记录不存在，抛出异常，否则就删除该记录
        if (null == parmHolidayList) {
            throw new AnyTxnParameterException(AnyTxnParameterRespCodeEnum.D_QUERY_PARM_HOLIDAY_LIST_BY_ID_FAULT);
        }
        return holidayListMapper.deleteByPrimaryKey(id) > 0;
    }

    /**
     * 通过Id主键查询假日参数
     *
     * @param id 主键
     * @return HolidayListRes 假日参数响应参数
     * @throws AnyTxnParameterException
     */
    @Override
    public HolidayListResDTO findById(String id){
        if (id == null) {
            throw new AnyTxnParameterException(AnyTxnParameterRespCodeEnum.P_PARAMETER_EMPTY_FAULT);
        }
        ParmHolidayList parmHolidayList = holidayListMapper.selectByPrimaryKey(id);
        if (parmHolidayList == null) {
            throw new AnyTxnParameterException(AnyTxnParameterRespCodeEnum.D_QUERY_PARM_HOLIDAY_LIST_BY_ID_FAULT);
        }
        return BeanMapping.copy(parmHolidayList, HolidayListResDTO.class);
    }

    @Override
    public List<Map<String,String>> findHoliDayListId(String organizationNumber) {
        List<Map<String,String>> resultList=new ArrayList<>();

        organizationNumber = StringUtils.isEmpty(organizationNumber) ? OrgNumberUtils.getOrg() : organizationNumber;
        List<ParmHolidayList> holidayIdList = holidayListSelfMapper.selectHolidayListId(organizationNumber);
        for(ParmHolidayList parmHolidayList:holidayIdList){
            Map<String,String> result = new HashMap<>(4);
            result.put("id",parmHolidayList.getHolidayListId());
            result.put("name",parmHolidayList.getDescription());
            resultList.add(result);
        }
        return resultList;
    }

    /**
     * 通过假日列表Id删除假日参数
     *
     * @param  holidayListId 假日列表id
     * @return Boolean
     * @throws AnyTxnParameterException
     */
    @Override
    @DeleteParameterAnnotation(tableName = "parm_holiday_list",tableDesc = "Holiday",isJoinTable = true)
    public ParameterCompare removeByHolidayListId(String holidayListId, String organizationNumber){
        if (null == holidayListId) {
            throw new AnyTxnParameterException(AnyTxnParameterRespCodeEnum.P_PARAMETER_EMPTY_FAULT);
        }
        HolidayListResDTO byHolidayListId = findByHolidayListId(holidayListId, organizationNumber);
        if(ObjectUtils.isEmpty(byHolidayListId)){
            throw new AnyTxnParameterException(AnyTxnParameterRespCodeEnum.P_PARAMETER_DATA_NOT_EXIST, ParameterRepDetailEnum.HOLIDAY_NOT_EXIST);
        }
        return ParameterCompare
                .getBuilder()
                .withBefore(byHolidayListId)
                .withMainParmId(byHolidayListId.getHolidayListId())
                .build(HolidayListResDTO.class);
    }

    /**
     * @Description: 根据参数表的假日列表ID（holiday_list_id）读取假日列表表数据
     * @Param holidayListId 假日列表id
     * @return: HolidayListRes
     **/
    @Override
    public HolidayListResDTO findByHolidayListId(String holidayListId, String organizationNumber){
        if (StringUtils.isEmpty(holidayListId)) {
            throw new AnyTxnParameterException(AnyTxnParameterRespCodeEnum.P_PARAMETER_EMPTY_FAULT);
        }

        organizationNumber = StringUtils.isEmpty(organizationNumber) ? OrgNumberUtils.getOrg() : organizationNumber;
        List<ParmHolidayList> ParmHolidayListByHolidayListId = holidayListSelfMapper.selectByHolidayListId(holidayListId, organizationNumber);
        if(CollectionUtils.isEmpty(ParmHolidayListByHolidayListId)){
            throw new AnyTxnParameterException(AnyTxnParameterRespCodeEnum.D_DATA_NOT_EXISTS_FAULT, ParameterRepDetailEnum.HOLIDAY_NOT_EXIST);
        }

        HolidayListResDTO res = new HolidayListResDTO();
        res.setCreateTime(ParmHolidayListByHolidayListId.get(0).getCreateTime());
        res.setDescription(ParmHolidayListByHolidayListId.get(0).getDescription());
        res.setHolidayListId(holidayListId);
        res.setStatus(ParmHolidayListByHolidayListId.get(0).getStatus());
        res.setUpdateTime(ParmHolidayListByHolidayListId.get(0).getUpdateTime());
        res.setUpdateBy(ParmHolidayListByHolidayListId.get(0).getUpdateBy());
        res.setVersionNumber(ParmHolidayListByHolidayListId.get(0).getVersionNumber());
        res.setOrganizationNumber(ParmHolidayListByHolidayListId.get(0).getOrganizationNumber());
        res.setYear(ParmHolidayListByHolidayListId.get(0).getYear());

        List<LocalDate> holidayDayList = new ArrayList<>();
        res.setHolidayDayList(holidayDayList);
        for(ParmHolidayList hl:ParmHolidayListByHolidayListId){
            if(ObjectUtils.isEmpty(hl.getHolidayDay())){
                return res;
            }
            res.getHolidayDayList().add(hl.getHolidayDay());
        }


        return res;
    }

    @Override
    @Transactional(rollbackFor = Exception.class,propagation = Propagation.REQUIRED)
    //@ShardingSphereTransactionType(TransactionType.XA)
    protected boolean updateDb(ParmModificationRecord parmModificationRecord) {
        HolidayListReqDTO after = JSON.parseObject(parmModificationRecord.getParmBody(), HolidayListReqDTO.class);
        int deleteRes = holidayListSelfMapper.deleteByHolidayListId(after.getHolidayListId(), after.getOrganizationNumber());
        int addRes = batchAddHoliday(after);
        return deleteRes >= 0 && addRes >= 0;
    }

    @Override
    @Transactional(rollbackFor = Exception.class,propagation = Propagation.REQUIRED)
    //@ShardingSphereTransactionType(TransactionType.XA)
    protected boolean insertDb(ParmModificationRecord parmModificationRecord) {
        HolidayListReqDTO holidayListReq = JSON.parseObject(parmModificationRecord.getParmBody(), HolidayListReqDTO.class);

        // 兼容全年无假日的场景

        int count = batchAddHoliday(holidayListReq)  ;

        boolean flag = count == 1 && (holidayListReq.getHolidayDayList() == null || holidayListReq.getHolidayDayList().size() == 0);

        return  count == holidayListReq.getHolidayDayList().size() || flag;
    }

    @Override
    @Transactional(rollbackFor = Exception.class,propagation = Propagation.REQUIRED)
    //@ShardingSphereTransactionType(TransactionType.XA)
    protected boolean deleteDb(ParmModificationRecord parmModificationRecord) {
        HolidayListReqDTO holidayListReqDTO = JSON.parseObject(parmModificationRecord.getParmBody(), HolidayListReqDTO.class);
        return holidayListSelfMapper.deleteByHolidayListId(holidayListReqDTO.getHolidayListId(), holidayListReqDTO.getOrganizationNumber()) > 0;
    }

    private int batchAddHoliday(HolidayListReqDTO holidayListReq){
        List<LocalDate> holidayDayList = holidayListReq.getHolidayDayList();
        List<ParmHolidayList> addBatch = new ArrayList<>();


        // 兼容全年无假日的场景
        if(CollectionUtils.isEmpty(holidayDayList)){
            ParmHolidayList parmHolidayList = new ParmHolidayList();
            parmHolidayList.setId(numberIdGenerator.generateId(TenantUtils.getTenantId())+"");
            parmHolidayList.setHolidayListId(holidayListReq.getHolidayListId());
            //parmHolidayList.setHolidayDay(holidayDay);
            parmHolidayList.setDescription(holidayListReq.getDescription());
            parmHolidayList.setStatus(holidayListReq.getStatus());
            parmHolidayList.initCreateDateTime();
            parmHolidayList.initUpdateDateTime();
            parmHolidayList.setVersionNumber(1L);
            parmHolidayList.setUpdateBy(Constants.DEFAULT_UPDATE_BY);
            parmHolidayList.setOrganizationNumber(holidayListReq.getOrganizationNumber());
            parmHolidayList.setYear(holidayListReq.getYear());
            addBatch.add(parmHolidayList);
            return holidayListSelfMapper.insertBatch(addBatch);

        }
        for(LocalDate holidayDay : holidayDayList){
            ParmHolidayList parmHolidayList = new ParmHolidayList();
            parmHolidayList.setId(numberIdGenerator.generateId(TenantUtils.getTenantId())+"");
            parmHolidayList.setHolidayListId(holidayListReq.getHolidayListId());
            parmHolidayList.setHolidayDay(holidayDay);
            parmHolidayList.setDescription(holidayListReq.getDescription());
            parmHolidayList.setStatus(holidayListReq.getStatus());
            parmHolidayList.initCreateDateTime();
            parmHolidayList.initUpdateDateTime();
            parmHolidayList.setVersionNumber(1L);
            parmHolidayList.setUpdateBy(Constants.DEFAULT_UPDATE_BY);
            parmHolidayList.setOrganizationNumber(holidayListReq.getOrganizationNumber());
            parmHolidayList.setYear(holidayListReq.getYear());
            addBatch.add(parmHolidayList);
        }
        return holidayListSelfMapper.insertBatch(addBatch);
    }
}
