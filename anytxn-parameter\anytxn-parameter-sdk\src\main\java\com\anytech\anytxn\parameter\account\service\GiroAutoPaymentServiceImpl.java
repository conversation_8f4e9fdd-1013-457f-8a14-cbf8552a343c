package com.anytech.anytxn.parameter.account.service;

import com.alibaba.fastjson.JSON;
import com.anytech.anytxn.common.core.annotation.db.DeleteParameterAnnotation;
import com.anytech.anytxn.common.core.annotation.db.InsertParameterAnnotation;
import com.anytech.anytxn.common.core.annotation.db.UpdateParameterAnnotation;
import com.anytech.anytxn.business.base.audit.AbstractParameterService;
import com.anytech.anytxn.common.core.base.ParameterCompare;
import com.anytech.anytxn.business.base.audit.ParmModificationRecord;
import com.anytech.anytxn.common.core.base.PageResultDTO;
import com.anytech.anytxn.common.core.utils.BeanMapping;
import com.anytech.anytxn.common.core.utils.OrgNumberUtils;
import com.anytech.anytxn.common.sequence.utils.Number16IdGen;
import com.anytech.anytxn.common.core.utils.TenantUtils;
import com.anytech.anytxn.parameter.base.account.domain.dto.GiroBackReqDTO;
import com.anytech.anytxn.parameter.base.account.domain.dto.GiroBackResDTO;
import com.anytech.anytxn.parameter.base.account.service.IParmGiroAutoPaymentService;
import com.anytech.anytxn.parameter.base.common.enums.AnyTxnParameterRespCodeEnum;
import com.anytech.anytxn.parameter.base.common.exception.AnyTxnParameterException;
import com.anytech.anytxn.parameter.account.mapper.ParmGiroAutoPaymentMapper;
import com.anytech.anytxn.parameter.account.mapper.ParmGiroAutoPaymentSelfMapper;
import com.anytech.anytxn.parameter.base.account.domain.model.ParmGiroAutoPayment;
import com.github.pagehelper.Page;
import com.github.pagehelper.PageHelper;
//import org.apache.shardingsphere.transaction.annotation.ShardingSphereTransactionType;
//import org.apache.shardingsphere.transaction.core.TransactionType;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.StringUtils;

import java.math.BigDecimal;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * Description   GIRO退票费用实现类
 * Copyright:	Copyright (c) 2021
 * Company:		江融信
 * Author:		liurui
 * Version:		1.0
 * Create at:	2021/11/5 2:42 PM
 *
 * 修改历史:
 * 日期    作者    版本  修改描述
 * ------------------------------------------------------------------
 *
 **/
@Service(value = "parm_giro_refund_transaction_fee_serviceImpl")
public class GiroAutoPaymentServiceImpl extends AbstractParameterService implements IParmGiroAutoPaymentService {
    private final Logger logger = LoggerFactory.getLogger(ChequeBackServiceImpl.class);

    @Autowired
    private ParmGiroAutoPaymentMapper parmGiroAutoPaymentMapper;
    @Autowired
    private ParmGiroAutoPaymentSelfMapper parmGiroAutoPaymentSelfMapper;
    @Autowired
    private Number16IdGen numberIdGenerator;

    @Override
    public GiroBackResDTO findByTableIdAndOrgNo(String tableId, String organizationNumber) {
        if (tableId == null || StringUtils.isEmpty(organizationNumber)) {
            throw new AnyTxnParameterException(AnyTxnParameterRespCodeEnum.P_PARAMETER_EMPTY_FAULT);
        }
        ParmGiroAutoPayment parmGiroAutoPayment = parmGiroAutoPaymentSelfMapper.queryByGiroBackTableIdAndOrgNo(tableId,organizationNumber );
        if(parmGiroAutoPayment==null){
            throw new AnyTxnParameterException(AnyTxnParameterRespCodeEnum.D_QUERY_PARA_AUTO_PAYMENT_TABLE_FAULT);

        }
        return BeanMapping.copy(parmGiroAutoPayment, GiroBackResDTO.class);
    }

    @Override
    @InsertParameterAnnotation(tableName = "parm_giro_refund_transaction_fee", tableDesc = "GITO Refund Transaction Fee")
    public ParameterCompare add(GiroBackReqDTO giroBackReqDTO) {
        ParmGiroAutoPayment parmGiroAutoPayment = parmGiroAutoPaymentSelfMapper.queryByGiroBackTableIdAndOrgNo(giroBackReqDTO.getTableId(), OrgNumberUtils.getOrg(giroBackReqDTO.getOrganizationNumber()));
        if (parmGiroAutoPayment!=null) {
            logger.warn("GIRO退票费用信息已存在, tableId={} Organization ={}",
                    giroBackReqDTO.getTableId(), OrgNumberUtils.getOrg());
            throw new AnyTxnParameterException(AnyTxnParameterRespCodeEnum.D_EXIST_PARA_AUTO_PAYMENT_TABLE_FAULT);
        }

        //构建退票费用信息
        ParmGiroAutoPayment giroAutoPayment = BeanMapping.copy(giroBackReqDTO, ParmGiroAutoPayment.class);
        giroAutoPayment.setId(String.valueOf(numberIdGenerator.generateId(TenantUtils.getTenantId())));

        return ParameterCompare.getBuilder().withMainParmId(giroAutoPayment.getTableId()).withAfter(giroAutoPayment).build(ParmGiroAutoPayment.class);

    }

    @Override
    @UpdateParameterAnnotation(tableName = "parm_giro_refund_transaction_fee", tableDesc = "Update GITO Refund Transaction Fee")
    public ParameterCompare modify(GiroBackReqDTO giroBackReqDTO) {
        if (giroBackReqDTO.getId() == null) {
            throw new AnyTxnParameterException(AnyTxnParameterRespCodeEnum.P_PARAMETER_EMPTY_PARA_AUTO_PAYMENT_TABLE_FAULT);
        }
        ParmGiroAutoPayment parmGiroAutoPayment = parmGiroAutoPaymentMapper.selectByPrimaryKey(giroBackReqDTO.getId());

        if (parmGiroAutoPayment == null) {
            logger.error("修改退票费用数据, 通过主键id({})未找到数据", giroBackReqDTO.getId());
            throw new AnyTxnParameterException(AnyTxnParameterRespCodeEnum.D_MODIFY_PARA_AUTO_PAYMENT_TABLE_BY_ID_FAULT);
        }
        // 拷贝修改的数据并更新
        ParmGiroAutoPayment giroAutoPayment = BeanMapping.copy(giroBackReqDTO, ParmGiroAutoPayment.class);

        return ParameterCompare.getBuilder().withAfter(giroAutoPayment).withBefore(parmGiroAutoPayment).build(ParmGiroAutoPayment.class);

    }

    @Override
    @DeleteParameterAnnotation(tableName = "parm_giro_refund_transaction_fee", tableDesc = "Delete GITO Refund Transaction Fee")
    public ParameterCompare remove(String id) {
        if (id == null) {
            throw new AnyTxnParameterException(AnyTxnParameterRespCodeEnum.P_DELETE_EMPTY_PARA_AUTO_PAYMENT_TABLE_FAULT);
        }

        ParmGiroAutoPayment giroAutoPayment = parmGiroAutoPaymentMapper.selectByPrimaryKey(id);
        if (giroAutoPayment == null) {
            logger.error("删除GIRO退票费用数据, 通过主键id({})未找到数据", id);
            throw new AnyTxnParameterException(AnyTxnParameterRespCodeEnum.D_DELETE_PARA_AUTO_PAYMENT_TABLE_BY_ID_FAULT);
        }

        return ParameterCompare.getBuilder().withBefore(giroAutoPayment).build(ParmGiroAutoPayment.class);

    }

    @Override
    @DeleteParameterAnnotation(tableName = "parm_giro_refund_transaction_fee", tableDesc = "Delete GITO Refund Transaction Fee")
    public ParameterCompare remove(String tableId, String orgNum) {
        if (tableId == null || orgNum == null) {
            throw new AnyTxnParameterException(AnyTxnParameterRespCodeEnum.P_DELETE_EMPTY_PARA_AUTO_PAYMENT_TABLE_FAULT);
        }

        ParmGiroAutoPayment giroAutoPayment = parmGiroAutoPaymentMapper.selectByTableIdAndOrgNum(tableId,orgNum);
        if (giroAutoPayment == null) {
            logger.error("删除GIRO退票费用数据, 通过tableId({})和orgNum({})未找到数据", tableId,orgNum);
            throw new AnyTxnParameterException(AnyTxnParameterRespCodeEnum.D_DELETE_PARA_AUTO_PAYMENT_TABLE_BY_ID_FAULT);
        }

        return ParameterCompare.getBuilder().withBefore(giroAutoPayment).build(ParmGiroAutoPayment.class);

    }

    @Override
    public GiroBackResDTO find(String id) {
        if (id == null) {
            throw new AnyTxnParameterException(AnyTxnParameterRespCodeEnum.P_PRIMARY_KEY_NULL_FAULT);
        }

        ParmGiroAutoPayment giroAutoPayment = parmGiroAutoPaymentMapper.selectByPrimaryKey(id);

        if (giroAutoPayment == null) {
            logger.error("查询Giro退票费用信息, 通过主键id:{} 未找到数据", id);
            throw new AnyTxnParameterException(AnyTxnParameterRespCodeEnum.D_QUERY_PARA_AUTO_PAYMENT_TABLE_FAULT);
        }

        return BeanMapping.copy(giroAutoPayment, GiroBackResDTO.class);

    }

    @Override
    public GiroBackResDTO find(String tableId, String orgNum) {
        if (tableId == null || orgNum == null) {
            throw new AnyTxnParameterException(AnyTxnParameterRespCodeEnum.P_PRIMARY_KEY_NULL_FAULT);
        }

        ParmGiroAutoPayment giroAutoPayment = parmGiroAutoPaymentMapper.selectByTableIdAndOrgNum(tableId,orgNum);

        if (giroAutoPayment == null) {
            logger.error("查询giro退票费用信息, 通过tableId:{} 和orgNum:{} 未找到数据", tableId,orgNum);
            throw new AnyTxnParameterException(AnyTxnParameterRespCodeEnum.D_QUERY_PARA_AUTO_PAYMENT_TABLE_FAULT);
        }

        return BeanMapping.copy(giroAutoPayment, GiroBackResDTO.class);

    }

    @Override
    public PageResultDTO<GiroBackResDTO> findAll(Integer pageNum, Integer pageSize, String tableId, String description, String feeIndicator, String status, String transactionCode, String interestIndicator, BigDecimal fixedFee, String organizationNumber) {
        Map<String,Object> map = new HashMap<>(9);
        map.put("tableId",tableId);
        map.put("description",description);
        map.put("feeIndicator",feeIndicator);
        map.put("status",status);
        map.put("transactionCode",transactionCode);
        map.put("interestIndicator",interestIndicator);
        map.put("fixedFee",fixedFee);
        map.put("organizationNumber", StringUtils.isEmpty(organizationNumber) ? OrgNumberUtils.getOrg() : organizationNumber);
        Page<ParmGiroAutoPayment> page = PageHelper.startPage(pageNum, pageSize);
        List<ParmGiroAutoPayment> allocatedList = parmGiroAutoPaymentSelfMapper.selectAll(map);
        List<GiroBackResDTO> res = BeanMapping.copyList(allocatedList, GiroBackResDTO.class);
        return new PageResultDTO<>(pageNum,pageSize,page.getTotal(),page.getPages(),res);

    }

    @Override
    public List<GiroBackResDTO> findByStatus(String status) {
        List<GiroBackResDTO> paymentAllocatedResList = null;
        if (!StringUtils.isEmpty(status)) {
            List<ParmGiroAutoPayment> giroBacksList = parmGiroAutoPaymentSelfMapper.selectByStatus(status, OrgNumberUtils.getOrg());
            paymentAllocatedResList = BeanMapping.copyList(giroBacksList, GiroBackResDTO.class);
        }

        return paymentAllocatedResList;
    }

    @Override
    @Transactional(rollbackFor = Exception.class,propagation = Propagation.REQUIRED)
    //@ShardingSphereTransactionType(TransactionType.XA)
    protected boolean updateDb(ParmModificationRecord parmModificationRecord) throws Exception {
        ParmGiroAutoPayment parmGiroAutoPayment = JSON.parseObject(parmModificationRecord.getParmBody(), ParmGiroAutoPayment.class);
        parmGiroAutoPayment.initUpdateDateTime();
        int i = parmGiroAutoPaymentMapper.updateByPrimaryKeySelective(parmGiroAutoPayment);
        return i > 0;
    }

    @Override
    @Transactional(rollbackFor = Exception.class,propagation = Propagation.REQUIRED)
    //@ShardingSphereTransactionType(TransactionType.XA)
    protected boolean insertDb(ParmModificationRecord parmModificationRecord) throws Exception {
        ParmGiroAutoPayment parmGiroAutoPayment = JSON.parseObject(parmModificationRecord.getParmBody(), ParmGiroAutoPayment.class);
        parmGiroAutoPayment.initCreateDateTime();
        parmGiroAutoPayment.initUpdateDateTime();
        parmGiroAutoPayment.setUpdateBy(parmModificationRecord.getApplicationBy());
        int i = parmGiroAutoPaymentMapper.insertSelective(parmGiroAutoPayment);
        return i > 0;
    }

    @Override
    @Transactional(rollbackFor = Exception.class,propagation = Propagation.REQUIRED)
    //@ShardingSphereTransactionType(TransactionType.XA)
    protected boolean deleteDb(ParmModificationRecord parmModificationRecord) throws Exception {
        ParmGiroAutoPayment parmGiroAutoPayment = JSON.parseObject(parmModificationRecord.getParmBody(), ParmGiroAutoPayment.class);
        int i = parmGiroAutoPaymentMapper.deleteByPrimaryKey(parmGiroAutoPayment.getId());
        return i > 0;
    }
}
