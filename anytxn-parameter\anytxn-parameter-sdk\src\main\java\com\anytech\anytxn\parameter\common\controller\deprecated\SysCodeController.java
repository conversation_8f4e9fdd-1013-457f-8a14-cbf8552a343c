package com.anytech.anytxn.parameter.common.controller.deprecated;

import io.swagger.v3.oas.annotations.tags.Tag;
import io.swagger.v3.oas.annotations.Operation;
import com.anytech.anytxn.common.core.base.AnyTxnHttpResponse;
import com.anytech.anytxn.common.core.base.BizBaseController;
import com.anytech.anytxn.parameter.base.common.enums.ParameterRepDetailEnum;
import com.anytech.anytxn.parameter.base.common.domain.dto.system.SysCodeReqDTO;
import com.anytech.anytxn.parameter.base.common.domain.dto.system.SysCodeResDTO;
import com.anytech.anytxn.parameter.base.common.domain.dto.system.SysCodeSimpleResDTO;
import com.anytech.anytxn.parameter.base.common.service.system.ISysCodeService;
import com.anytech.anytxn.parameter.base.common.utils.AnyTxnHttpResponseHelper;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.HashMap;
import java.util.List;

/**
 * 系统字典 api
 * <AUTHOR>
 * @date 2018-08-15
 **/
@Deprecated
@Tag(name = "系统字典")
@RestController
public class SysCodeController extends BizBaseController {

    @Autowired
    private ISysCodeService sysCodeService;

    /**
     * 通过字典类型ID获取字典列表，字典类型ID分组
     * @param typeIds
     * @return
     */
    @Operation(summary = "通过字典类型ID获取字典列表，按分类ID分组")
    @GetMapping(value = "/param/sysCode/getMap")
    public AnyTxnHttpResponse<HashMap<String, List<SysCodeSimpleResDTO>>> getByTypeIds(@RequestParam List<String> typeIds) {
        HashMap<String, List<SysCodeSimpleResDTO>> sysCodeMap = sysCodeService.findMapByTypeIds(typeIds);
        return AnyTxnHttpResponse.success(sysCodeMap);

    }

    /**
     * 根据父id获取字典表数据
     * @param pid
     * @return
     */
    @Operation(summary = "根据父id获取字典表数据")
    @GetMapping(value = "/param/sysCode/pid/{pid}")
    public AnyTxnHttpResponse<List<SysCodeResDTO>> getByPid(@PathVariable(value = "pid") Long pid) {
        List<SysCodeResDTO> resultList = sysCodeService.getByPid(pid);
        return AnyTxnHttpResponse.success(resultList, ParameterRepDetailEnum.QUERY.message());

    }

    /**
     * 根据字典类型id获取字典表数据
     * @param typeId
     * @return
     */
    @Operation(summary = "根据字典类型id获取字典表数据")
    @GetMapping(value = "/param/sysCode/typeId/{typeId}")
    public AnyTxnHttpResponse<List<SysCodeResDTO>> getByPid(@PathVariable(value = "typeId") String typeId) {
        List<SysCodeResDTO> resultList = sysCodeService.getByTypeId(typeId);
        return AnyTxnHttpResponse.success(resultList,ParameterRepDetailEnum.QUERY.message());

    }

    /**
     * 根据字典数据表id获取字典表数据
     * @param id
     * @return
     */
    @Operation(summary = "根据字典数据表id获取字典表数据")
    @GetMapping(value = "/param/sysCode/id/{id}")
    public AnyTxnHttpResponse<SysCodeResDTO> getByPrimaryId(@PathVariable(value = "id") Long id) {
        SysCodeResDTO sysCode = sysCodeService.getByPrimaryId(id);
        return AnyTxnHttpResponse.success(sysCode,ParameterRepDetailEnum.QUERY.message());

    }

    /**
     * 根据字典数据表id删除字典表数据
     * @param id
     * @return
     */
    @Operation(summary = "根据字典数据表id删除字典表数据")
    @DeleteMapping(value = "/param/sysCode/id/{id}")
    public AnyTxnHttpResponse deleteSysCode(@PathVariable(value = "id") Long id) {
        sysCodeService.deleteSysCode(id);
        return AnyTxnHttpResponseHelper.succ(ParameterRepDetailEnum.DEL.message());

    }

    /**
     * 修改字典表数据
     * @param sysCodeReqDTO
     * @return
     */
    @Operation(summary = "修改字典表数据")
    @PutMapping(value = "/param/sysCode")
    public AnyTxnHttpResponse<SysCodeResDTO> modifySysCode(@RequestBody SysCodeReqDTO sysCodeReqDTO) {
        SysCodeResDTO sysCode = sysCodeService.modifySysCode(sysCodeReqDTO);
        return AnyTxnHttpResponse.success(sysCode,ParameterRepDetailEnum.UPDATE.message());

    }

    /**
     * 新增字典表数据
     * @param sysCodeReqDTO
     * @return
     */
    @Operation(summary = "新增字典表数据")
    @PostMapping(value = "/param/sysCode")
    public AnyTxnHttpResponse<SysCodeResDTO> addSysCode(@RequestBody SysCodeReqDTO sysCodeReqDTO) {
        SysCodeResDTO sysCode = sysCodeService.addSysCode(sysCodeReqDTO);
        return AnyTxnHttpResponse.success(sysCode,ParameterRepDetailEnum.CREATE.message());

    }
}
