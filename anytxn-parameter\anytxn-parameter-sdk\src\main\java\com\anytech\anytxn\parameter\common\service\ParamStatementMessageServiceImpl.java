package com.anytech.anytxn.parameter.common.service;

import com.alibaba.fastjson.JSON;
import com.anytech.anytxn.common.core.annotation.db.DeleteParameterAnnotation;
import com.anytech.anytxn.common.core.annotation.db.InsertParameterAnnotation;
import com.anytech.anytxn.common.core.annotation.db.UpdateParameterAnnotation;
import com.anytech.anytxn.business.base.audit.AbstractParameterService;
import com.anytech.anytxn.common.core.base.ParameterCompare;
import com.anytech.anytxn.business.base.audit.ParmModificationRecord;
import com.anytech.anytxn.common.core.base.PageResultDTO;
import com.anytech.anytxn.common.core.utils.BeanMapping;
import com.anytech.anytxn.common.sequence.utils.Number16IdGen;
import com.anytech.anytxn.common.core.utils.TenantUtils;
import com.anytech.anytxn.parameter.base.common.domain.dto.ParmStatementMessageDTO;
import com.anytech.anytxn.parameter.base.common.domain.dto.ParmStatementMessageReqBodyDTO;
import com.anytech.anytxn.parameter.base.common.service.IParamStatementMessageService;
import com.anytech.anytxn.parameter.base.common.enums.AnyTxnParameterRespCodeEnum;
import com.anytech.anytxn.parameter.base.common.enums.ParmStatusEnum;
import com.anytech.anytxn.parameter.base.common.exception.AnyTxnParameterException;
import com.anytech.anytxn.parameter.common.mapper.broadcast.ParamStatementMessageMapper;
import com.anytech.anytxn.parameter.base.common.domain.model.ParmStatementMessage;
import com.github.pagehelper.Page;
import com.github.pagehelper.PageHelper;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
//import org.apache.shardingsphere.transaction.annotation.ShardingSphereTransactionType;
//import org.apache.shardingsphere.transaction.core.TransactionType;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import jakarta.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

/**
@Author: sukang
@Date: 2023/3/28 17:02
@Description:
*/
@Service(value = "parm_statement_message_serviceImpl")
@Slf4j
public class ParamStatementMessageServiceImpl extends AbstractParameterService implements IParamStatementMessageService {

    @Resource
    private ParamStatementMessageMapper paramStatementMessageMapper;

    @Autowired
    private Number16IdGen numberIdGenerator;



    @Override
    public Map<String, Map<String, String>> getAllStatementMsg() {
        ParmStatementMessageDTO statementMessageDTO = new ParmStatementMessageDTO();
        statementMessageDTO.setStatus(ParmStatusEnum.EFFECTIVE.getCode());
        List<ParmStatementMessage> statementMessages = paramStatementMessageMapper.selectByCondition(statementMessageDTO);

        return statementMessages.stream().collect(Collectors.toMap(
                ParmStatementMessage::getAccountProductCodes,
                (e) -> {
                    List<ParmStatementMessage> parmStatementMessages = paramStatementMessageMapper.selectSeriMessageDetail(e.getId());
                    return parmStatementMessages
                            .stream()
                            .collect(Collectors.toMap(
                                    ParmStatementMessage::getSerialNumber,
                                    ParmStatementMessage::getStatementMessage,
                                    (k1, k2) -> k1,
                                    HashMap::new
                            ));
                },
                (k1, k2) -> k1,
                HashMap::new

        ));
    }





    @Override
    public PageResultDTO<ParmStatementMessageDTO> selectByPage(int pageSize,
                                                               int pageNum,
                                                               ParmStatementMessageDTO parmStatementMessageDTO) {

        if (parmStatementMessageDTO == null){
            parmStatementMessageDTO = new ParmStatementMessageDTO();
        }


        Page<ParmStatementMessageReqBodyDTO> messageReqBodyDtoPage = PageHelper.startPage(pageNum, pageSize);

        List<ParmStatementMessage> statementMessageList = paramStatementMessageMapper.selectByCondition(parmStatementMessageDTO);

        List<ParmStatementMessageDTO> statementMessageDtoS = BeanMapping.copyList(statementMessageList, ParmStatementMessageDTO.class);

        return new PageResultDTO<>(pageNum, pageSize, messageReqBodyDtoPage.getTotal(),
                messageReqBodyDtoPage.getPages(), statementMessageDtoS);
    }





    @Override
    public ParmStatementMessageReqBodyDTO selectDetailById(String id) {

        ParmStatementMessage parmStatementMessage = paramStatementMessageMapper.selectById(id);

        if (parmStatementMessage == null) {return null;}


        List<ParmStatementMessage> parmStatementMessageList =  paramStatementMessageMapper.selectSeriMessageDetail(parmStatementMessage.getId());

        parmStatementMessageList.sort( (e1,e2) -> {
            if (Objects.equals(e1.getSerialNumber(), "CAA") || Objects.equals(e2.getSerialNumber(), "CAA")){
                return 1;
            }
            return e1.getSerialNumber().compareTo(e2.getSerialNumber());
        });


        ParmStatementMessageReqBodyDTO messageReqBodyDTO = BeanMapping.copy(parmStatementMessage, ParmStatementMessageReqBodyDTO.class);

        messageReqBodyDTO.setParmStatementMessages(BeanMapping.copyList(parmStatementMessageList, ParmStatementMessageDTO.class));

        if (StringUtils.isNotBlank(parmStatementMessage.getAccountProductCodes())){
            List<String> asList = Arrays.asList(parmStatementMessage.getAccountProductCodes().split(","));
            asList.sort(Comparator.naturalOrder());
            messageReqBodyDTO.setAccountProductCodes(asList);
        }

        return messageReqBodyDTO;
    }


    @Override
    @InsertParameterAnnotation(tableName = "parm_statement_message", tableDesc = "statement message ", isJoinTable = true)
    public ParameterCompare add(ParmStatementMessageReqBodyDTO parmStatementMessageReqBodyDTO) {

        checkReqParam(parmStatementMessageReqBodyDTO);
        checkExitTableId(parmStatementMessageReqBodyDTO);

        parmStatementMessageReqBodyDTO.setId(String.valueOf(numberIdGenerator.generateId(TenantUtils.getTenantId())));
        parmStatementMessageReqBodyDTO.setVersionNumber(1L);

        return ParameterCompare.getBuilder()
                .withAfter(parmStatementMessageReqBodyDTO)
                .withMainParmId(parmStatementMessageReqBodyDTO.getTableId())
                .build(ParmStatementMessageReqBodyDTO.class);
    }


    @Override
    @UpdateParameterAnnotation(tableName = "parm_statement_message", tableDesc = "statement message ", isJoinTable = true)
    public ParameterCompare update(ParmStatementMessageReqBodyDTO parmStatementMessageReqBodyDTO) {

        checkReqParam(parmStatementMessageReqBodyDTO);

        ParmStatementMessageReqBodyDTO reqBodyDTO = selectDetailById(parmStatementMessageReqBodyDTO.getId());

        return ParameterCompare.getBuilder()
                .withMainParmId(parmStatementMessageReqBodyDTO.getTableId())
                .withBefore(reqBodyDTO)
                .withAfter(parmStatementMessageReqBodyDTO)
                .build(ParmStatementMessageReqBodyDTO.class);
    }



    @Override
    @DeleteParameterAnnotation(tableName = "parm_statement_message", tableDesc = "statement message ", isJoinTable = true)
    public ParameterCompare delete(String id) {

        ParmStatementMessageReqBodyDTO parmStatementMessageReqBodyDTO = selectDetailById(id);

        if (parmStatementMessageReqBodyDTO == null){
            throw new AnyTxnParameterException(AnyTxnParameterRespCodeEnum.P_PARAMETER_DATA_NOT_EXIST);
        }

        return ParameterCompare.getBuilder()
                .withBefore(parmStatementMessageReqBodyDTO)
                .build(ParmStatementMessageReqBodyDTO.class);
    }




    @Override
    @Transactional(rollbackFor = Exception.class,propagation = Propagation.REQUIRED)
    //@ShardingSphereTransactionType(TransactionType.XA)
    protected boolean updateDb(ParmModificationRecord parmModificationRecord) throws Exception {

        ParmStatementMessageReqBodyDTO statementMessageReqBodyDTO = JSON.parseObject(parmModificationRecord.getParmBody(), ParmStatementMessageReqBodyDTO.class);


        ParmStatementMessage statementMessage = BeanMapping.copy(statementMessageReqBodyDTO, ParmStatementMessage.class);
        statementMessage.setUpdateBy(parmModificationRecord.getApplicationBy());
        statementMessage.initUpdateDateTime();
        statementMessage.setAccountProductCodes(String.join(",", statementMessageReqBodyDTO.getAccountProductCodes()));

        int updateSelective = paramStatementMessageMapper.updateSelective(statementMessage);

        paramStatementMessageMapper.deleteGropuId(statementMessage.getId());


        List<ParmStatementMessageDTO> parmStatementMessages = statementMessageReqBodyDTO.getParmStatementMessages();

        List<ParmStatementMessage> statementMessageList = parmStatementMessages.stream().map(e -> {
            ParmStatementMessage copy = BeanMapping.copy(e, ParmStatementMessage.class);
            copy.setId(String.valueOf(numberIdGenerator.generateId(TenantUtils.getTenantId())));
            copy.setGroupParentId(statementMessage.getId());
            copy.setUpdateBy(parmModificationRecord.getApplicationBy());
            copy.initUpdateDateTime();
            copy.initCreateDateTime();
            copy.setVersionNumber(1L);
            return copy;
        }).collect(Collectors.toList());

        int batchUpdate = paramStatementMessageMapper.batchInsert(statementMessageList);


        return updateSelective == 1 && batchUpdate == statementMessageList.size();
    }




    @Override
    @Transactional(rollbackFor = Exception.class,propagation = Propagation.REQUIRED)
    //@ShardingSphereTransactionType(TransactionType.XA)
    protected boolean insertDb(ParmModificationRecord parmModificationRecord) throws Exception {

        ParmStatementMessageReqBodyDTO parmStatementMessageReqBodyDTO = JSON.parseObject(parmModificationRecord.getParmBody(), ParmStatementMessageReqBodyDTO.class);

        checkReqParam(parmStatementMessageReqBodyDTO);
        checkExitTableId(parmStatementMessageReqBodyDTO);


        ParmStatementMessage statementMessage = BeanMapping.copy(parmStatementMessageReqBodyDTO, ParmStatementMessage.class);


        statementMessage.setAccountProductCodes(String.join(
                ",",parmStatementMessageReqBodyDTO.getAccountProductCodes()));


        statementMessage.setUpdateBy(parmModificationRecord.getApplicationBy());
        statementMessage.initCreateDateTime();
        statementMessage.initUpdateDateTime();


        List<ParmStatementMessageDTO> parmStatementMessages = parmStatementMessageReqBodyDTO.getParmStatementMessages();

        List<ParmStatementMessage> statementMessages = parmStatementMessages.stream().map(e -> {
            ParmStatementMessage copy = BeanMapping.copy(e, ParmStatementMessage.class);
            copy.setId(String.valueOf(numberIdGenerator.generateId(TenantUtils.getTenantId())));
            copy.setGroupParentId(statementMessage.getId());
            copy.setUpdateBy(parmModificationRecord.getApplicationBy());
            copy.initUpdateDateTime();
            copy.initCreateDateTime();
            copy.setVersionNumber(1L);
            return copy;
        }).collect(Collectors.toList());

        int insertSelective = paramStatementMessageMapper.insertSelective(statementMessage);

        int batchInsert = paramStatementMessageMapper.batchInsert(statementMessages);


        return insertSelective == 1 && batchInsert == statementMessages.size();
    }





    @Override
    @Transactional(rollbackFor = Exception.class,propagation = Propagation.REQUIRED)
    //@ShardingSphereTransactionType(TransactionType.XA)
    protected boolean deleteDb(ParmModificationRecord parmModificationRecord) throws Exception {
        ParmStatementMessageReqBodyDTO parmStatementMessageReqBodyDTO = JSON.parseObject(parmModificationRecord.getParmBody(), ParmStatementMessageReqBodyDTO.class);

        paramStatementMessageMapper.deleteByPrimaryKey(parmStatementMessageReqBodyDTO.getId());
        paramStatementMessageMapper.deleteGropuId(parmStatementMessageReqBodyDTO.getId());

        return true;
    }


    private void checkExitTableId(ParmStatementMessageReqBodyDTO parmStatementMessageReqBodyDTO){
        ParmStatementMessage parmStatementMessage = paramStatementMessageMapper.selectGroupTableId(parmStatementMessageReqBodyDTO.getOrganizationNumber(),
                parmStatementMessageReqBodyDTO.getTableId());


        if (parmStatementMessage != null){
            throw new AnyTxnParameterException(AnyTxnParameterRespCodeEnum.P_PARAMETER_DATA_EXIST);
        }
    }



    private void checkReqParam(ParmStatementMessageReqBodyDTO parmStatementMessageReqBodyDTO) {

        if (StringUtils.isBlank(parmStatementMessageReqBodyDTO.getTableId())
                || StringUtils.isBlank(parmStatementMessageReqBodyDTO.getTableIdDesc())){
            throw new AnyTxnParameterException(AnyTxnParameterRespCodeEnum.P_PARAMETER_EMPTY_FAULT);
        }


        if (CollectionUtils.isEmpty(parmStatementMessageReqBodyDTO.getParmStatementMessages())){
            throw new AnyTxnParameterException(AnyTxnParameterRespCodeEnum.P_PARAMETER_EMPTY_FAULT);
        }


        List<String> productCodes = parmStatementMessageReqBodyDTO.getAccountProductCodes();


        if (CollectionUtils.isNotEmpty(productCodes)) {
            String selectGroupPatentIdIsNull = paramStatementMessageMapper.selectGroupPatentIdIsNull(
                    parmStatementMessageReqBodyDTO.getOrganizationNumber(),
                    Optional.ofNullable(parmStatementMessageReqBodyDTO.getId()).orElse(""));

            productCodes.forEach(e -> {

                if (StringUtils.isBlank(e)){
                    throw new AnyTxnParameterException(AnyTxnParameterRespCodeEnum.P_PARAMETER_EMPTY_FAULT);
                }

                if (Optional.ofNullable(selectGroupPatentIdIsNull).orElse("").contains(e)){
                    throw new AnyTxnParameterException(AnyTxnParameterRespCodeEnum.P_PARAMETER_DATA_EXIST);
                }
            });
        }

        parmStatementMessageReqBodyDTO.getAccountProductCodes().sort(Comparator.naturalOrder());
    }


}
