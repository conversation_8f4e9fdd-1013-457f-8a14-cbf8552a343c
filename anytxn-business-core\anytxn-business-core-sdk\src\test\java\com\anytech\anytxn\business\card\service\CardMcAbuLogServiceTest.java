package com.anytech.anytxn.business.card.service;

import com.anytech.anytxn.business.base.card.enums.CardMcAbuStatusEnum;
import com.anytech.anytxn.business.dao.card.mapper.CardMcAbuLogMapper;
import com.anytech.anytxn.business.dao.card.model.CardMcAbuLog;
import com.anytech.anytxn.common.core.enums.AnyTxnCommonRespCodeEnum;
import com.anytech.anytxn.common.core.exception.AnyTxnCommonException;
import com.anytech.anytxn.common.core.utils.LoginUserUtils;
import com.anytech.anytxn.common.core.utils.TenantUtils;
import com.anytech.anytxn.common.sequence.utils.SequenceIdGen;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockedStatic;
import org.mockito.junit.jupiter.MockitoExtension;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.Arrays;
import java.util.Collections;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.Mockito.*;

/**
 * CardMcAbuLogServiceImpl 测试类
 * 
 * <AUTHOR>
 * @date 2024-01-01
 */
@ExtendWith(MockitoExtension.class)
class CardMcAbuLogServiceTest {

    @InjectMocks
    private CardMcAbuLogServiceImpl cardMcAbuLogService;

    @Mock
    private CardMcAbuLogMapper cardMcAbuLogMapper;

    @Mock
    private SequenceIdGen sequenceIdGen;

    private String testCardNumber = "1234567890123456";
    private LocalDate testProcessDate = LocalDate.of(2024, 1, 15);
    private String testExpireDate = "1226";
    private String newExpireDate = "1227";
    private String testCurrency = "USD";
    private String testId = "TEST_ID_001";

    @BeforeEach
    void setUp() {
        // 只在需要时使用lenient模式
        lenient().when(sequenceIdGen.generateId(anyString())).thenReturn(testId);
    }

    /**
     * 测试方法：shouldInsertNewCardLogSuccessfully_whenNewCardStatus
     */
    @Test
    void shouldInsertNewCardLogSuccessfully_whenNewCardStatus() {
        try (MockedStatic<TenantUtils> tenantUtilsMock = mockStatic(TenantUtils.class);
             MockedStatic<LoginUserUtils> loginUtilsMock = mockStatic(LoginUserUtils.class)) {
            // Arrange
            tenantUtilsMock.when(TenantUtils::getTenantId).thenReturn("TENANT001");
            loginUtilsMock.when(LoginUserUtils::getLoginUserName).thenReturn("TEST_USER");
            when(cardMcAbuLogMapper.insertSelective(any(CardMcAbuLog.class))).thenReturn(1);

            // Act
            int result = cardMcAbuLogService.insertLog(
                CardMcAbuStatusEnum.NEW_CARD, testProcessDate, testCardNumber, 
                null, newExpireDate, testCurrency);

            // Assert
            assertEquals(1, result);
            verify(cardMcAbuLogMapper).insertSelective(argThat(log -> 
                "N".equals(log.getAbuStatus()) &&
                testCardNumber.equals(log.getNewCardNumber()) &&
                newExpireDate.equals(log.getNewExpireDate()) &&
                log.getOldCardNumber() == null
            ));
        }
    }

    /**
     * 测试方法：shouldInsertExpireDateChangeLogSuccessfully_whenExpireDateChangeStatus
     */
    @Test
    void shouldInsertExpireDateChangeLogSuccessfully_whenExpireDateChangeStatus() {
        try (MockedStatic<TenantUtils> tenantUtilsMock = mockStatic(TenantUtils.class);
             MockedStatic<LoginUserUtils> loginUtilsMock = mockStatic(LoginUserUtils.class)) {
            // Arrange
            tenantUtilsMock.when(TenantUtils::getTenantId).thenReturn("TENANT001");
            loginUtilsMock.when(LoginUserUtils::getLoginUserName).thenReturn("TEST_USER");
            when(cardMcAbuLogMapper.insertSelective(any(CardMcAbuLog.class))).thenReturn(1);

            // Act
            int result = cardMcAbuLogService.insertLog(
                CardMcAbuStatusEnum.EXPIRE_DATE_CHANGE, testProcessDate, testCardNumber, 
                testExpireDate, newExpireDate, testCurrency);

            // Assert
            assertEquals(1, result);
            verify(cardMcAbuLogMapper).insertSelective(argThat(log -> 
                "E".equals(log.getAbuStatus()) &&
                testCardNumber.equals(log.getOldCardNumber()) &&
                testCardNumber.equals(log.getNewCardNumber()) &&
                testExpireDate.equals(log.getOldExpireDate()) &&
                newExpireDate.equals(log.getNewExpireDate())
            ));
        }
    }

    /**
     * 测试方法：shouldInsertCloseCardLogAndUpdateExistingRecord_whenCloseCardStatusWithExistingNewCard
     */
    @Test
    void shouldInsertCloseCardLogAndUpdateExistingRecord_whenCloseCardStatusWithExistingNewCard() {
        try (MockedStatic<TenantUtils> tenantUtilsMock = mockStatic(TenantUtils.class);
             MockedStatic<LoginUserUtils> loginUtilsMock = mockStatic(LoginUserUtils.class)) {
            // Arrange
            tenantUtilsMock.when(TenantUtils::getTenantId).thenReturn("TENANT001");
            loginUtilsMock.when(LoginUserUtils::getLoginUserName).thenReturn("TEST_USER");
            
            CardMcAbuLog existingNewCardLog = createTestCardMcAbuLog();
            existingNewCardLog.setExtractInd(0);
            when(cardMcAbuLogMapper.selectByStatusDateAndNewCardNo(testProcessDate, testCardNumber))
                .thenReturn(Arrays.asList(existingNewCardLog));
            when(cardMcAbuLogMapper.updateByPrimaryKeySelective(any(CardMcAbuLog.class))).thenReturn(1);
            when(cardMcAbuLogMapper.insertSelective(any(CardMcAbuLog.class))).thenReturn(1);

            // Act
            int result = cardMcAbuLogService.insertLog(
                CardMcAbuStatusEnum.CLOSE_CARD, testProcessDate, testCardNumber, 
                testExpireDate, null, testCurrency);

            // Assert
            assertEquals(1, result);
            verify(cardMcAbuLogMapper).updateByPrimaryKeySelective(argThat(log -> 
                log.getExtractInd() == 1 && "CLOSE on the same day".equals(log.getUpdateBy())
            ));
            verify(cardMcAbuLogMapper).insertSelective(argThat(log -> 
                "C".equals(log.getAbuStatus()) &&
                testCardNumber.equals(log.getOldCardNumber()) &&
                testExpireDate.equals(log.getOldExpireDate()) &&
                log.getExtractInd() == 1
            ));
        }
    }

    /**
     * 测试方法：shouldInsertCloseCardLogSuccessfully_whenCloseCardStatusWithoutExistingNewCard
     */
    @Test
    void shouldInsertCloseCardLogSuccessfully_whenCloseCardStatusWithoutExistingNewCard() {
        try (MockedStatic<TenantUtils> tenantUtilsMock = mockStatic(TenantUtils.class);
             MockedStatic<LoginUserUtils> loginUtilsMock = mockStatic(LoginUserUtils.class)) {
            // Arrange
            tenantUtilsMock.when(TenantUtils::getTenantId).thenReturn("TENANT001");
            loginUtilsMock.when(LoginUserUtils::getLoginUserName).thenReturn("TEST_USER");
            when(cardMcAbuLogMapper.selectByStatusDateAndNewCardNo(testProcessDate, testCardNumber))
                .thenReturn(Collections.emptyList());
            when(cardMcAbuLogMapper.insertSelective(any(CardMcAbuLog.class))).thenReturn(1);

            // Act
            int result = cardMcAbuLogService.insertLog(
                CardMcAbuStatusEnum.CLOSE_CARD, testProcessDate, testCardNumber, 
                testExpireDate, null, testCurrency);

            // Assert
            assertEquals(1, result);
            verify(cardMcAbuLogMapper).insertSelective(argThat(log -> 
                "C".equals(log.getAbuStatus()) &&
                testCardNumber.equals(log.getOldCardNumber()) &&
                testExpireDate.equals(log.getOldExpireDate()) &&
                log.getExtractInd() == 0
            ));
            verify(cardMcAbuLogMapper, never()).updateByPrimaryKeySelective(any());
        }
    }

    /**
     * 测试方法：shouldThrowException_whenUpdateFailsInCloseCardStatus
     */
    @Test
    void shouldThrowException_whenUpdateFailsInCloseCardStatus() {
        try (MockedStatic<TenantUtils> tenantUtilsMock = mockStatic(TenantUtils.class);
             MockedStatic<LoginUserUtils> loginUtilsMock = mockStatic(LoginUserUtils.class)) {
            // Arrange
            tenantUtilsMock.when(TenantUtils::getTenantId).thenReturn("TENANT001");
            loginUtilsMock.when(LoginUserUtils::getLoginUserName).thenReturn("TEST_USER");
            
            CardMcAbuLog existingNewCardLog = createTestCardMcAbuLog();
            existingNewCardLog.setExtractInd(0);
            when(cardMcAbuLogMapper.selectByStatusDateAndNewCardNo(testProcessDate, testCardNumber))
                .thenReturn(Arrays.asList(existingNewCardLog));
            when(cardMcAbuLogMapper.updateByPrimaryKeySelective(any(CardMcAbuLog.class))).thenReturn(0);

            // Act & Assert
            AnyTxnCommonException exception = assertThrows(AnyTxnCommonException.class, () -> {
                cardMcAbuLogService.insertLog(
                    CardMcAbuStatusEnum.CLOSE_CARD, testProcessDate, testCardNumber, 
                    testExpireDate, null, testCurrency);
            });

            assertEquals("**********", exception.getErrCode());
            verify(cardMcAbuLogMapper).updateByPrimaryKeySelective(any());
            verify(cardMcAbuLogMapper, never()).insertSelective(any());
        }
    }

    /**
     * 测试方法：shouldProcessAccountClosureSuccessfully_whenNoExistingCloseRecord
     */
    @Test
    void shouldProcessAccountClosureSuccessfully_whenNoExistingCloseRecord() {
        try (MockedStatic<TenantUtils> tenantUtilsMock = mockStatic(TenantUtils.class);
             MockedStatic<LoginUserUtils> loginUtilsMock = mockStatic(LoginUserUtils.class)) {
            // Arrange
            tenantUtilsMock.when(TenantUtils::getTenantId).thenReturn("TENANT001");
            loginUtilsMock.when(LoginUserUtils::getLoginUserName).thenReturn("TEST_USER");
            when(cardMcAbuLogMapper.selectByStatusDateAndOldCardNo(testProcessDate, testCardNumber))
                .thenReturn(Collections.emptyList());
            when(cardMcAbuLogMapper.insertSelective(any(CardMcAbuLog.class))).thenReturn(1);

            // Act
            assertDoesNotThrow(() -> {
                cardMcAbuLogService.processForAccountClosure(testCardNumber, testProcessDate, testExpireDate);
            });

            // Assert
            verify(cardMcAbuLogMapper).insertSelective(argThat(log -> 
                "C".equals(log.getAbuStatus()) &&
                testCardNumber.equals(log.getOldCardNumber()) &&
                testExpireDate.equals(log.getOldExpireDate())
            ));
        }
    }

    /**
     * 测试方法：shouldUpdateExistingRecords_whenAccountClosureWithExistingRecords
     */
    @Test
    void shouldUpdateExistingRecords_whenAccountClosureWithExistingRecords() {
        // Arrange
        CardMcAbuLog existingCloseLog = createTestCardMcAbuLog();
        existingCloseLog.setExtractInd(1);
        CardMcAbuLog existingNewLog = createTestCardMcAbuLog();
        existingNewLog.setExtractInd(0);
        
        when(cardMcAbuLogMapper.selectByStatusDateAndOldCardNo(testProcessDate, testCardNumber))
            .thenReturn(Arrays.asList(existingCloseLog));
        when(cardMcAbuLogMapper.selectByStatusDateAndNewCardNo(testProcessDate, testCardNumber))
            .thenReturn(Arrays.asList(existingNewLog));
        when(cardMcAbuLogMapper.updateByPrimaryKeySelective(any(CardMcAbuLog.class))).thenReturn(1);

        // Act
        assertDoesNotThrow(() -> {
            cardMcAbuLogService.processForAccountClosure(testCardNumber, testProcessDate, testExpireDate);
        });

        // Assert
        verify(cardMcAbuLogMapper).updateByPrimaryKeySelective(argThat(log -> 
            log.getExtractInd() == 1 && "Account closure".equals(log.getUpdateBy())
        ));
        verify(cardMcAbuLogMapper, never()).insertSelective(any());
    }

    /**
     * 测试方法：shouldRestoreCloseRecord_whenAccountClosureWithoutNewRecord
     */
    @Test
    void shouldRestoreCloseRecord_whenAccountClosureWithoutNewRecord() {
        // Arrange
        CardMcAbuLog existingCloseLog = createTestCardMcAbuLog();
        existingCloseLog.setExtractInd(1);
        
        when(cardMcAbuLogMapper.selectByStatusDateAndOldCardNo(testProcessDate, testCardNumber))
            .thenReturn(Arrays.asList(existingCloseLog));
        when(cardMcAbuLogMapper.selectByStatusDateAndNewCardNo(testProcessDate, testCardNumber))
            .thenReturn(Collections.emptyList());
        when(cardMcAbuLogMapper.updateByPrimaryKeySelective(any(CardMcAbuLog.class))).thenReturn(1);

        // Act
        assertDoesNotThrow(() -> {
            cardMcAbuLogService.processForAccountClosure(testCardNumber, testProcessDate, testExpireDate);
        });

        // Assert
        verify(cardMcAbuLogMapper).updateByPrimaryKeySelective(argThat(log -> 
            log.getExtractInd() == 0 && "Account closure".equals(log.getUpdateBy())
        ));
        verify(cardMcAbuLogMapper, never()).insertSelective(any());
    }

    /**
     * 测试方法：shouldProcessAccountClosureCancellationSuccessfully_whenExistingCloseRecord
     */
    @Test
    void shouldProcessAccountClosureCancellationSuccessfully_whenExistingCloseRecord() {
        try (MockedStatic<LoginUserUtils> loginUtilsMock = mockStatic(LoginUserUtils.class)) {
            // Arrange
            loginUtilsMock.when(LoginUserUtils::getLoginUserName).thenReturn("TEST_USER");
            
            CardMcAbuLog existingCloseLog = createTestCardMcAbuLog();
            existingCloseLog.setExtractInd(0);
            CardMcAbuLog existingNewLog = createTestCardMcAbuLog();
            existingNewLog.setExtractInd(1);
            
            when(cardMcAbuLogMapper.selectByStatusDateAndOldCardNo(testProcessDate, testCardNumber))
                .thenReturn(Arrays.asList(existingCloseLog));
            when(cardMcAbuLogMapper.selectByStatusDateAndNewCardNo(testProcessDate, testCardNumber))
                .thenReturn(Arrays.asList(existingNewLog));
            when(cardMcAbuLogMapper.updateByPrimaryKeySelective(any(CardMcAbuLog.class))).thenReturn(1);

            // Act
            assertDoesNotThrow(() -> {
                cardMcAbuLogService.processForAccountClosureCancellation(testCardNumber, testProcessDate, testExpireDate);
            });

            // Assert
            verify(cardMcAbuLogMapper, times(2)).updateByPrimaryKeySelective(any(CardMcAbuLog.class));
            verify(cardMcAbuLogMapper, never()).insertSelective(any());
        }
    }

    /**
     * 测试方法：shouldCreateNewRecord_whenAccountClosureCancellationWithoutExistingNewRecord
     */
    @Test
    void shouldCreateNewRecord_whenAccountClosureCancellationWithoutExistingNewRecord() {
        try (MockedStatic<TenantUtils> tenantUtilsMock = mockStatic(TenantUtils.class);
             MockedStatic<LoginUserUtils> loginUtilsMock = mockStatic(LoginUserUtils.class)) {
            // Arrange
            tenantUtilsMock.when(TenantUtils::getTenantId).thenReturn("TENANT001");
            loginUtilsMock.when(LoginUserUtils::getLoginUserName).thenReturn("TEST_USER");
            
            CardMcAbuLog existingCloseLog = createTestCardMcAbuLog();
            existingCloseLog.setExtractInd(0);
            
            when(cardMcAbuLogMapper.selectByStatusDateAndOldCardNo(testProcessDate, testCardNumber))
                .thenReturn(Arrays.asList(existingCloseLog));
            when(cardMcAbuLogMapper.selectByStatusDateAndNewCardNo(testProcessDate, testCardNumber))
                .thenReturn(Collections.emptyList());
            when(cardMcAbuLogMapper.updateByPrimaryKeySelective(any(CardMcAbuLog.class))).thenReturn(1);
            when(cardMcAbuLogMapper.insertSelective(any(CardMcAbuLog.class))).thenReturn(1);

            // Act
            assertDoesNotThrow(() -> {
                cardMcAbuLogService.processForAccountClosureCancellation(testCardNumber, testProcessDate, testExpireDate);
            });

            // Assert
            verify(cardMcAbuLogMapper).updateByPrimaryKeySelective(any(CardMcAbuLog.class));
            verify(cardMcAbuLogMapper).insertSelective(argThat(log -> 
                "N".equals(log.getAbuStatus()) &&
                testCardNumber.equals(log.getNewCardNumber()) &&
                testExpireDate.equals(log.getNewExpireDate()) &&
                log.getExtractInd() == 0
            ));
        }
    }

    private CardMcAbuLog createTestCardMcAbuLog() {
        CardMcAbuLog log = new CardMcAbuLog();
        log.setId(testId);
        log.setAbuStatus("N");
        log.setAbuStatusDate(testProcessDate);
        log.setNewCardNumber(testCardNumber);
        log.setNewExpireDate(newExpireDate);
        log.setCurrency(testCurrency);
        log.setExtractInd(0);
        log.setCreateTime(LocalDateTime.now());
        log.setUpdateTime(LocalDateTime.now());
        log.setUpdateBy("TEST_USER");
        log.setVersionNumber(1L);
        return log;
    }
} 