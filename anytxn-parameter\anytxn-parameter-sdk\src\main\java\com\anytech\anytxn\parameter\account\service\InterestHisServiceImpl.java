package com.anytech.anytxn.parameter.account.service;

import com.alibaba.fastjson.JSONObject;
import com.github.pagehelper.Page;
import com.github.pagehelper.PageHelper;
import com.anytech.anytxn.common.core.base.PageResultDTO;
import com.anytech.anytxn.common.core.utils.BeanMapping;
import com.anytech.anytxn.parameter.base.account.domain.dto.InterestHisResDTO;
import com.anytech.anytxn.parameter.base.account.domain.dto.InterestResDTO;
import com.anytech.anytxn.parameter.base.account.service.IInterestHisService;
import com.anytech.anytxn.parameter.account.mapper.PmInterestHisMapper;
import com.anytech.anytxn.parameter.account.mapper.PmInterestHisSelfMapper;
import com.anytech.anytxn.parameter.base.account.domain.model.PmInterestHis;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;

/**
 * 利率历史接口
 *
 * <AUTHOR>
 * @date 2021-04-08
 **/
@Slf4j
@Service
public class InterestHisServiceImpl implements IInterestHisService{

    @Autowired
    private PmInterestHisMapper pmInterestHisMapper;

    @Autowired
    private PmInterestHisSelfMapper pmInterestHisSelfMapper;

    @Override
    public List<InterestHisResDTO> findByOrgAndInterestTableId(String organizationNumber, String interestTableId) {
        List<PmInterestHis> pmInterestHisList = pmInterestHisSelfMapper.selectByOrgAndInterestTableId(organizationNumber, interestTableId);
        if(CollectionUtils.isEmpty(pmInterestHisList)){
            return Collections.emptyList();
        }
        List<InterestHisResDTO> interestResDTOS = new ArrayList<>();
        for (PmInterestHis pmInterestHis : pmInterestHisList) {
            InterestHisResDTO interestHisResDTO = BeanMapping.copy(pmInterestHis,InterestHisResDTO.class);
            setInterestInfo(interestHisResDTO,pmInterestHis);
            interestResDTOS.add(interestHisResDTO);
        }
        return interestResDTOS;
    }



    @Override
    public PageResultDTO<InterestHisResDTO> findPageByOrgAndInterestTableId(Integer pageNum, Integer pageSize, String organizationNumber, String interestTableId) {
        Page<InterestHisResDTO> page = PageHelper.startPage(pageNum, pageSize);
        List<PmInterestHis> pmInterestHisList = pmInterestHisSelfMapper.selectByCondition(organizationNumber, interestTableId);
        List<InterestHisResDTO> interestResDTOS = new ArrayList<>();
        for (PmInterestHis pmInterestHis : pmInterestHisList) {
            InterestHisResDTO interestHisResDTO = BeanMapping.copy(pmInterestHis,InterestHisResDTO.class);
            setInterestInfo(interestHisResDTO,pmInterestHis);
            interestResDTOS.add(interestHisResDTO);
        }
        return new PageResultDTO<>(pageNum,pageSize,page.getTotal(), page.getPages(),interestResDTOS);
    }

    @Override
    public InterestHisResDTO findById(Long id) {
        PmInterestHis pmInterestHis = pmInterestHisMapper.selectByPrimaryKey(id);
        if(null == pmInterestHis){
            return null;
        }
        InterestHisResDTO interestHisResDTO = BeanMapping.copy(pmInterestHis,InterestHisResDTO.class);
        setInterestInfo(interestHisResDTO,pmInterestHis);
        return interestHisResDTO;
    }


    /**
     * set利率相关信息
     * @param pmInterestHis PmInterestHis
     */
    private void setInterestInfo(InterestHisResDTO interestHisResDTO,PmInterestHis pmInterestHis) {
        InterestResDTO interestResDTO = JSONObject.parseObject(pmInterestHis.getJsonValue(), InterestResDTO.class);
        interestHisResDTO.setDescription(interestResDTO.getDescription());
        interestHisResDTO.setInterestType(interestResDTO.getInterestType());
        interestHisResDTO.setAdjustRate(interestResDTO.getAdjustRate());
        interestHisResDTO.setAdjustRatePercent(interestResDTO.getAdjustRatePercent());
        interestHisResDTO.setBaseRate(interestResDTO.getBaseRate());
        interestHisResDTO.setBaseRatePercent(interestResDTO.getBaseRatePercent());
        interestHisResDTO.setGraceOption(interestResDTO.getGraceOption());
        interestHisResDTO.setInterestOnInterestOption(interestResDTO.getInterestOnInterestOption());
        interestHisResDTO.setMonthBase(interestResDTO.getMonthBase());
        interestHisResDTO.setYearBase(interestResDTO.getYearBase());
        interestHisResDTO.setPaymentBackdateOption(interestResDTO.getPaymentBackdateOption());
        interestHisResDTO.setStartDateOption(interestResDTO.getStartDateOption());
        interestHisResDTO.setWavieOption(interestResDTO.getWavieOption());
        interestHisResDTO.setInterestBillingTxnCode(interestResDTO.getInterestBillingTxnCode());
        interestHisResDTO.setInterestBackDateDays(interestResDTO.getInterestBackDateDays());
        interestHisResDTO.setLmtUnitCodeFollowIndicator(interestResDTO.getLmtUnitCodeFollowIndicator());
        interestHisResDTO.setCreditIntPostTxnCde(interestResDTO.getCreditIntPostTxnCde());
        interestHisResDTO.setCreditIntCalType(interestResDTO.getCreditIntCalType());
        interestHisResDTO.setIntTaxPostTxnCde(interestResDTO.getIntTaxPostTxnCde());
        interestHisResDTO.setIntTaxRate(interestResDTO.getIntTaxRate());
        interestHisResDTO.setPaymentRestoreBackdateOption(interestResDTO.getPaymentRestoreBackdateOption());
        interestHisResDTO.setPaymentRestoreBackdateDays(interestResDTO.getPaymentRestoreBackdateDays());
    }
}
