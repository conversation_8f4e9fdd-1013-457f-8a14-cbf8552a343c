package com.anytech.anytxn.parameter.accounting.service;

import com.anytech.anytxn.parameter.common.mapper.deprecated.TPmsGlamsMapper;
import com.anytech.anytxn.parameter.common.mapper.deprecated.TPmsGlamsSelfMapper;
import com.github.pagehelper.Page;
import com.github.pagehelper.PageHelper;
import com.anytech.anytxn.common.core.base.PageResultDTO;
import com.anytech.anytxn.common.core.utils.BeanMapping;
import com.anytech.anytxn.common.core.utils.OrgNumberUtils;
import com.anytech.anytxn.common.sequence.utils.Number16IdGen;
import com.anytech.anytxn.common.core.utils.TenantUtils;
import com.anytech.anytxn.parameter.base.accounting.domain.dto.TPmsGlamsDTO;
import com.anytech.anytxn.parameter.base.accounting.service.ITPmsGlamsService;
import com.anytech.anytxn.parameter.base.common.enums.AnyTxnParameterRespCodeEnum;
import com.anytech.anytxn.parameter.base.common.exception.AnyTxnParameterException;
import com.anytech.anytxn.parameter.base.accounting.domain.model.TPmsGlams;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.cglib.beans.BeanCopier;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.util.ArrayList;
import java.util.List;

/**
 * @description: 流水拆分分录参数
 * @author: ZXL
 * @create: 2019-10-09 14:26
 */

@Service
public class TPmsGlamsServiceImpl implements ITPmsGlamsService {

    private static final Logger logger = LoggerFactory.getLogger(TPmsGlamsServiceImpl.class);

    @Autowired
    private TPmsGlamsSelfMapper tPmsGlamsSelfMapper;
    @Autowired
    private TPmsGlamsMapper tPmsGlamsMapper;

    @Autowired
    private Number16IdGen numberIdGenerator;

    /**
     * 根据ORG+交易码+核算状态+表内表外标志+LOGO +价税分离标志查询 会计流水分录参数
     *
     * @return List<TPmsGlamsDTO>
     * @throws AnyTxnParameterException 异常
     */
    @Override
    public List<TPmsGlamsDTO> selectByParams(String organizationNumber, String brancdid, String txnCode,
                                             String financeStatus, String intInt,
                                             String productNumber, String priceTaxInd) throws AnyTxnParameterException {

        List<TPmsGlams> pmsGlamsList = tPmsGlamsSelfMapper.selectByParams(organizationNumber, brancdid, txnCode,
                financeStatus, intInt, productNumber, priceTaxInd);
        if (pmsGlamsList.isEmpty()) {
            return null;
        }
        return BeanMapping.copyList(pmsGlamsList, TPmsGlamsDTO.class);
    }

    @Override
    public PageResultDTO<TPmsGlamsDTO> page(int page, int rows) {
        logger.info("分页查询流水拆分分录参数列表，当前页：{}，每页展示条数：{}", page, rows);
        Page pageInfo =  PageHelper.startPage(page, rows);
        List<TPmsGlamsDTO> tPmsGlamsDTOList = null;
        try {
            List<TPmsGlams> tPmsGlamsList = tPmsGlamsSelfMapper.selectAll(false, OrgNumberUtils.getOrg());
            if (!CollectionUtils.isEmpty(tPmsGlamsList)) {
                tPmsGlamsDTOList = new ArrayList<>();
                BeanCopier beanCopier = BeanCopier.create(TPmsGlams.class, TPmsGlamsDTO.class, false);
                for (TPmsGlams tPmsGlams : tPmsGlamsList) {
                    TPmsGlamsDTO tPmsGlamsDTO = new TPmsGlamsDTO();
                    beanCopier.copy(tPmsGlams, tPmsGlamsDTO, null);
                    tPmsGlamsDTOList.add(tPmsGlamsDTO);
                }
            }
            return new PageResultDTO<>(page, rows, pageInfo.getTotal(), pageInfo.getPages(),tPmsGlamsDTOList);
        } catch (Exception e) {
            logger.error("分页查询流水拆分分录参数失败，当前页：{}，每页展示条数：{},错误信息：{}", page, rows, e.getMessage());
            /*throw new AnyTXNBusRuntimeException(TransactionEnum.DATABASE_ERROR.getCode(),
                    TransactionEnum.DATABASE_ERROR.getReason());*/
            throw new AnyTxnParameterException(AnyTxnParameterRespCodeEnum.D_PAGE_TPMS_GLAMS_FAULT);
        }
    }

    @Override
    public TPmsGlamsDTO detail(Long id) {
        TPmsGlams tPmsGlams = tPmsGlamsMapper.selectByPrimaryKey(id);
        if(tPmsGlams != null){
            TPmsGlamsDTO tPmsGlamsDTO = new TPmsGlamsDTO();
            BeanCopier beanCopier = BeanCopier.create(TPmsGlams.class, TPmsGlamsDTO.class, false);
            beanCopier.copy(tPmsGlams, tPmsGlamsDTO, null);
            return tPmsGlamsDTO;
        }
        return null;
    }

    @Override
    public boolean remove(Long id) {
        return tPmsGlamsMapper.deleteByPrimaryKey(id) > 0;
    }

    @Override
    public boolean add(TPmsGlamsDTO data) {
        if(data == null){
            return false;
        }
        TPmsGlams tPmsGlams = new TPmsGlams();
        BeanCopier beanCopier = BeanCopier.create(TPmsGlamsDTO.class, TPmsGlams.class, false);
        beanCopier.copy(data,tPmsGlams,null);

        numberIdGenerator.generateId(TenantUtils.getTenantId());
        return tPmsGlamsSelfMapper.insert(tPmsGlams) > 0;
    }

    @Override
    public boolean update(TPmsGlamsDTO data) {
        if(data == null || data.getId() == null){
            return false;
        }
        TPmsGlams tPmsGlams = new TPmsGlams();
        BeanCopier beanCopier = BeanCopier.create(TPmsGlamsDTO.class, TPmsGlams.class, false);
        beanCopier.copy(data,tPmsGlams,null);
        return tPmsGlamsMapper.updateByPrimaryKeySelective(tPmsGlams) > 0;
    }


}