package com.anytech.anytxn.parameter.card.service;

import com.alibaba.fastjson.JSON;
import com.github.pagehelper.Page;
import com.github.pagehelper.PageHelper;
import com.anytech.anytxn.common.core.annotation.db.DeleteParameterAnnotation;
import com.anytech.anytxn.common.core.annotation.db.InsertParameterAnnotation;
import com.anytech.anytxn.common.core.annotation.db.UpdateParameterAnnotation;
import com.anytech.anytxn.business.base.audit.AbstractParameterService;
import com.anytech.anytxn.common.core.base.ParameterCompare;
import com.anytech.anytxn.business.base.audit.ParmModificationRecord;
import com.anytech.anytxn.common.core.base.PageResultDTO;
import com.anytech.anytxn.common.core.utils.BeanMapping;
import com.anytech.anytxn.common.core.utils.OrgNumberUtils;
import com.anytech.anytxn.common.sequence.utils.Number16IdGen;
import com.anytech.anytxn.common.core.utils.TenantUtils;
import com.anytech.anytxn.parameter.base.card.domain.dto.CardIssueReqDTO;
import com.anytech.anytxn.parameter.base.card.domain.dto.CardIssueResDTO;
import com.anytech.anytxn.parameter.base.card.service.ICardIssueService;
import com.anytech.anytxn.parameter.base.common.enums.AnyTxnParameterRespCodeEnum;
import com.anytech.anytxn.parameter.base.common.exception.AnyTxnParameterException;
import com.anytech.anytxn.parameter.card.mapper.broadcast.ParmCardIssueMapper;
import com.anytech.anytxn.parameter.card.mapper.broadcast.ParmCardIssueSelfMapper;
import com.anytech.anytxn.parameter.base.card.domain.model.ParmCardIssue;
//import org.apache.shardingsphere.transaction.annotation.ShardingSphereTransactionType;
//import org.apache.shardingsphere.transaction.core.TransactionType;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.StringUtils;

import java.util.List;

/**
 * 发卡参数
 *
 * <AUTHOR>
 * @date 2018-12-27
 **/
@Service(value = "parm_card_issue_serviceImpl")
public class CardIssueServiceImpl extends AbstractParameterService implements ICardIssueService {

    private Logger logger= LoggerFactory.getLogger(CardIssueServiceImpl.class);

    @Autowired
    private ParmCardIssueSelfMapper parmCardIssueSelfMapper;
    @Autowired
    private ParmCardIssueMapper parmCardIssueMapper;

    @Autowired
    private Number16IdGen numberIdGenerator;

    /**
     * 根据机构号、参数表id查询发卡参数
     * @param organizationNumber 机构号
     * @param tableId 参数表id
     * @return ParmCardIssue
     */
    @Override
    //@PreGetProcess(args = {"#0","#1"})
    public CardIssueResDTO findByOrgAndTableId(String organizationNumber, String tableId) {
        ParmCardIssue parmCardIssue = parmCardIssueSelfMapper.selectByOrgAndTableId(organizationNumber, tableId);
        if (parmCardIssue==null) {
            logger.error("获取发卡参数, 通过organizationNumber={}, tableId={} 未查到数据", organizationNumber, tableId);
            throw new AnyTxnParameterException(AnyTxnParameterRespCodeEnum.S_GET_PARM_CARD_ISSUE_FAULT);
        }
        return BeanMapping.copy(parmCardIssue, CardIssueResDTO.class);
    }

    /**
     * 新增
     *
     * @param cardIssueReqDTO 传入数据
     * @return CardIssueResDTO
     */
    @Override
    @InsertParameterAnnotation(tableName = "parm_card_issue", tableDesc = "Issuing Parameter")
    public ParameterCompare add(CardIssueReqDTO cardIssueReqDTO) {

        if (cardIssueReqDTO == null) {
            logger.error("传入参数不能为空，发卡参数新增接口");
            throw new AnyTxnParameterException(AnyTxnParameterRespCodeEnum.P_PARAMETER_EMPTY_FAULT);
        }
        if (cardIssueReqDTO.getOrganizationNumber() == null) {
            cardIssueReqDTO.setOrganizationNumber(OrgNumberUtils.getOrg());
        }

        if (parmCardIssueSelfMapper.isExists(cardIssueReqDTO.getOrganizationNumber(), cardIssueReqDTO.getTableId()) > 0) {
            logger.error("发卡参数已存在,机构号{}，参数表id{}",cardIssueReqDTO.getOrganizationNumber(), cardIssueReqDTO.getTableId());
            throw new AnyTxnParameterException(AnyTxnParameterRespCodeEnum.S_EXIST_PARM_CARD_ISSUE_FAULT);
        }

        ParmCardIssue parmCardIssue = BeanMapping.copy(cardIssueReqDTO, ParmCardIssue.class);

        parmCardIssue.setId(String.valueOf(numberIdGenerator.generateId(TenantUtils.getTenantId())));

        return ParameterCompare.getBuilder().withAfter(parmCardIssue).build(ParmCardIssue.class);
    }

    /**
     * 删除
     *
     * @param id id
     * @return CardIssueResDTO
     */
    @Override
    @DeleteParameterAnnotation(tableName = "parm_card_issue", tableDesc = "Issuing Parameter")
    public ParameterCompare removeById(String id) {

        if (id == null) {
            logger.error("传入参数不能为空，发卡参数删除接口");
            throw new AnyTxnParameterException(AnyTxnParameterRespCodeEnum.P_PARAMETER_EMPTY_FAULT);
        }

        ParmCardIssue parmCardIssue = parmCardIssueMapper.selectByPrimaryKey(id);

        if (parmCardIssue == null) {
            logger.error("待删除发卡参数不存在，id:{}",id);
            throw new AnyTxnParameterException(AnyTxnParameterRespCodeEnum.S_GET_PARM_CARD_ISSUE_BY_ID_FAULT);
        }

        return ParameterCompare.getBuilder().withBefore(parmCardIssue).build(ParmCardIssue.class);
    }

    @Override
    @DeleteParameterAnnotation(tableName = "parm_card_issue", tableDesc = "Issuing Parameter")
    public ParameterCompare removeByOrgAndTableId(String organizationNumber, String tableId) {
        if (StringUtils.isEmpty(organizationNumber) || StringUtils.isEmpty(tableId)) {
            logger.error("传入参数不能为空，发卡参数删除接口");
            throw new AnyTxnParameterException(AnyTxnParameterRespCodeEnum.P_PARAMETER_EMPTY_FAULT);
        }

        ParmCardIssue parmCardIssue = parmCardIssueSelfMapper.selectByOrgAndTableId(organizationNumber,tableId);

        if (parmCardIssue == null) {
            logger.error("待删除发卡参数不存在，org:{},tableId:{}",organizationNumber,tableId);
            throw new AnyTxnParameterException(AnyTxnParameterRespCodeEnum.S_GET_PARM_CARD_ISSUE_BY_ID_FAULT);
        }

        return ParameterCompare.getBuilder().withBefore(parmCardIssue).build(ParmCardIssue.class);
    }

    /**
     * 修改
     *
     * @param cardIssueReqDTO 传入数据
     * @return CardIssueResDTO
     */
    @Override
    @UpdateParameterAnnotation(tableName = "parm_card_issue", tableDesc = "Issuing Parameter")
    public ParameterCompare modify(CardIssueReqDTO cardIssueReqDTO) {

        if (cardIssueReqDTO == null) {
            logger.error("传入参数不能为空，发卡参数修改接口");
            throw new AnyTxnParameterException(AnyTxnParameterRespCodeEnum.P_PARAMETER_EMPTY_FAULT);
        }

        ParmCardIssue oldParmCardIssue = parmCardIssueMapper.selectByPrimaryKey(cardIssueReqDTO.getId());

        if (!oldParmCardIssue.getOrganizationNumber().equals(cardIssueReqDTO.getOrganizationNumber()) ||
                !oldParmCardIssue.getTableId().equals(cardIssueReqDTO.getTableId())) {
            Boolean ex = parmCardIssueSelfMapper.isExists(cardIssueReqDTO.getOrganizationNumber(), cardIssueReqDTO.getTableId()) > 0;
            if (ex) {
                logger.error("发卡参数已存在,机构号{}，参数表id{}", cardIssueReqDTO.getOrganizationNumber(), cardIssueReqDTO.getTableId());
                throw new AnyTxnParameterException(AnyTxnParameterRespCodeEnum.S_EXIST_PARM_CARD_ISSUE_FAULT);
            }
        }

        ParmCardIssue parmCardIssue = BeanMapping.copy(cardIssueReqDTO, ParmCardIssue.class);

        return ParameterCompare.getBuilder().withAfter(parmCardIssue).withBefore(oldParmCardIssue).build(ParmCardIssue.class);
    }

    /**
     * 根据id查询
     *
     * @param id id
     * @return CardIssueResDTO
     */
    @Override
    //@PreGetProcess(byPrimaryKey = true)
    public CardIssueResDTO findById(String id) {

        if (id == null) {
            logger.error("传入参数不能为空，发卡参数根据id查询接口");
            throw new AnyTxnParameterException(AnyTxnParameterRespCodeEnum.P_PARAMETER_EMPTY_FAULT);
        }

        ParmCardIssue parmCardIssue = parmCardIssueMapper.selectByPrimaryKey(id);

        if (parmCardIssue == null) {
            logger.error("发卡参数不存在，id:{}",id);
            throw new AnyTxnParameterException(AnyTxnParameterRespCodeEnum.S_GET_PARM_CARD_ISSUE_BY_ID_FAULT);
        }

        return BeanMapping.copy(parmCardIssue, CardIssueResDTO.class);
    }

    /**
     * 分页
     *
     * @param pageNum  页码
     * @param pageSize 页面大小
     * @return PageResultDTO<CardIssueResDTO>
     */
    @Override
    public PageResultDTO<CardIssueResDTO> findList(Integer pageNum, Integer pageSize, String tableId,String description, String organizationNumber) {

        Page<ParmCardIssue> page = PageHelper.startPage(pageNum, pageSize);
        organizationNumber = StringUtils.isEmpty(organizationNumber) ? OrgNumberUtils.getOrg() : organizationNumber;
        List<ParmCardIssue> parmCardIssueList = parmCardIssueSelfMapper.selectByCondition(tableId, description, organizationNumber);
        List<CardIssueResDTO> cardIssueResDTOList = BeanMapping.copyList(parmCardIssueList, CardIssueResDTO.class);

        return new PageResultDTO<>(pageNum,pageSize,page.getTotal(),page.getPages(),cardIssueResDTOList);
    }

    @Override
    @Transactional(rollbackFor = Exception.class,propagation = Propagation.REQUIRED)
    //@ShardingSphereTransactionType(TransactionType.XA)
    protected boolean updateDb(ParmModificationRecord parmModificationRecord) {
        ParmCardIssue parmCardIssue = JSON.parseObject(parmModificationRecord.getParmBody(), ParmCardIssue.class);
        parmCardIssue.initUpdateDateTime();
        parmCardIssue.setUpdateBy(parmModificationRecord.getApplicationBy());
        parmCardIssueMapper.updateByPrimaryKeySelective(parmCardIssue);
        return true;
    }

    @Override
    @Transactional(rollbackFor = Exception.class,propagation = Propagation.REQUIRED)
    //@ShardingSphereTransactionType(TransactionType.XA)
    protected boolean insertDb(ParmModificationRecord parmModificationRecord) {
        ParmCardIssue parmCardIssue = JSON.parseObject(parmModificationRecord.getParmBody(), ParmCardIssue.class);
        parmCardIssue.initCreateDateTime();
        parmCardIssue.initUpdateDateTime();
        parmCardIssue.setVersionNumber(1L);
        parmCardIssue.setUpdateBy(parmModificationRecord.getApplicationBy());
        int i = parmCardIssueMapper.insertSelective(parmCardIssue);
        return i > 0;
    }

    @Override
    @Transactional(rollbackFor = Exception.class,propagation = Propagation.REQUIRED)
    //@ShardingSphereTransactionType(TransactionType.XA)
    protected boolean deleteDb(ParmModificationRecord parmModificationRecord) {
        ParmCardIssue parmCardIssue = JSON.parseObject(parmModificationRecord.getParmBody(), ParmCardIssue.class);
        int i = parmCardIssueMapper.deleteByPrimaryKey(parmCardIssue.getId());
        return i > 0;
    }
}
