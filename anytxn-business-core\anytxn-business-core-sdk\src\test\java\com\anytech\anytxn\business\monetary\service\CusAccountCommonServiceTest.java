package com.anytech.anytxn.business.monetary.service;

import com.anytech.anytxn.business.dao.card.model.CardAuthorizationInfo;
import com.anytech.anytxn.common.core.utils.OrgNumberUtils;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.MockedStatic;
import org.mockito.junit.jupiter.MockitoExtension;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.List;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.Mockito.*;

/**
 * CusAccountCommonServiceImpl 测试类
 * 
 * <AUTHOR>
 * @date 2024-01-01
 */
@ExtendWith(MockitoExtension.class)
class CusAccountCommonServiceTest {

    @InjectMocks
    private CusAccountCommonServiceImpl cusAccountCommonService;

    /**
     * 测试方法：shouldGetTmpCardNumber_whenValidCardListProvided
     * 用来测试 CusAccountCommonServiceImpl 方法 getTmpCardNumber
     * 验证当提供有效卡片列表时，能够正确获取TMP卡号
     */
    @Test
    void shouldGetTmpCardNumber_whenValidCardListProvided() {
        try (MockedStatic<OrgNumberUtils> orgUtilsMock = mockStatic(OrgNumberUtils.class)) {
            // Mock OrgNumberUtils for BaseEntity constructor
            orgUtilsMock.when(OrgNumberUtils::getOrg).thenReturn("TEST_ORG");
            
            // Arrange - 准备测试数据
            List<CardAuthorizationInfo> cards = new ArrayList<>();
            
            // 创建普通卡片
            CardAuthorizationInfo normalCard = new CardAuthorizationInfo();
            normalCard.setProductNumber("NM001");
            normalCard.setCardNumber("****************");
            cards.add(normalCard);
            
            // 创建TMP公司卡片，有tmpPrincipleCardNumber
            CardAuthorizationInfo tmpCard = new CardAuthorizationInfo();
            tmpCard.setProductNumber("TP001");
            tmpCard.setCardNumber("****************");
            tmpCard.setTmpPrincipleCardNumber("****************");
            cards.add(tmpCard);

            // Act - 执行被测方法
            String result = cusAccountCommonService.getTmpCardNumber(cards);

            // Assert - 验证结果
            assertEquals("****************", result);
        }
    }

    /**
     * 测试方法：shouldReturnCardNumber_whenTmpCardHasNoTmpPrincipleCardNumber
     * 用来测试 CusAccountCommonServiceImpl 方法 getTmpCardNumber
     * 验证当TMP卡片没有tmpPrincipleCardNumber时，返回卡号
     */
    @Test
    void shouldReturnCardNumber_whenTmpCardHasNoTmpPrincipleCardNumber() {
        try (MockedStatic<OrgNumberUtils> orgUtilsMock = mockStatic(OrgNumberUtils.class)) {
            // Mock OrgNumberUtils for BaseEntity constructor
            orgUtilsMock.when(OrgNumberUtils::getOrg).thenReturn("TEST_ORG");
            
            // Arrange - 准备测试数据
            List<CardAuthorizationInfo> cards = new ArrayList<>();
            
            // 创建TMP公司卡片，但没有tmpPrincipleCardNumber
            CardAuthorizationInfo tmpCard = new CardAuthorizationInfo();
            tmpCard.setProductNumber("TP002");
            tmpCard.setCardNumber("****************");
            tmpCard.setTmpPrincipleCardNumber(null); // 没有tmpPrincipleCardNumber
            cards.add(tmpCard);

            // Act - 执行被测方法
            String result = cusAccountCommonService.getTmpCardNumber(cards);

            // Assert - 验证结果
            assertEquals("****************", result);
        }
    }

    /**
     * 测试方法：shouldReturnCardNumber_whenTmpCardHasEmptyTmpPrincipleCardNumber
     * 用来测试 CusAccountCommonServiceImpl 方法 getTmpCardNumber
     * 验证当TMP卡片有空的tmpPrincipleCardNumber时，返回卡号
     */
    @Test
    void shouldReturnCardNumber_whenTmpCardHasEmptyTmpPrincipleCardNumber() {
        try (MockedStatic<OrgNumberUtils> orgUtilsMock = mockStatic(OrgNumberUtils.class)) {
            // Mock OrgNumberUtils for BaseEntity constructor
            orgUtilsMock.when(OrgNumberUtils::getOrg).thenReturn("TEST_ORG");
            
            // Arrange - 准备测试数据
            List<CardAuthorizationInfo> cards = new ArrayList<>();
            
            // 创建TMP公司卡片，但tmpPrincipleCardNumber为空字符串
            CardAuthorizationInfo tmpCard = new CardAuthorizationInfo();
            tmpCard.setProductNumber("TP003");
            tmpCard.setCardNumber("****************");
            tmpCard.setTmpPrincipleCardNumber(""); // 空字符串
            cards.add(tmpCard);

            // Act - 执行被测方法
            String result = cusAccountCommonService.getTmpCardNumber(cards);

            // Assert - 验证结果
            assertEquals("****************", result);
        }
    }

    /**
     * 测试方法：shouldReturnFirstCardNumber_whenNoTmpCardFound
     * 用来测试 CusAccountCommonServiceImpl 方法 getTmpCardNumber
     * 验证当没有找到TMP卡片时，返回第一个卡片的卡号
     */
    @Test
    void shouldReturnFirstCardNumber_whenNoTmpCardFound() {
        try (MockedStatic<OrgNumberUtils> orgUtilsMock = mockStatic(OrgNumberUtils.class)) {
            // Mock OrgNumberUtils for BaseEntity constructor
            orgUtilsMock.when(OrgNumberUtils::getOrg).thenReturn("TEST_ORG");
            
            // Arrange - 准备测试数据
            List<CardAuthorizationInfo> cards = new ArrayList<>();
            
            // 创建普通卡片
            CardAuthorizationInfo normalCard1 = new CardAuthorizationInfo();
            normalCard1.setProductNumber("NM001");
            normalCard1.setCardNumber("****************");
            cards.add(normalCard1);
            
            CardAuthorizationInfo normalCard2 = new CardAuthorizationInfo();
            normalCard2.setProductNumber("NM002");
            normalCard2.setCardNumber("****************");
            cards.add(normalCard2);

            // Act - 执行被测方法
            String result = cusAccountCommonService.getTmpCardNumber(cards);

            // Assert - 验证结果
            assertEquals("****************", result);
        }
    }

    /**
     * 测试方法：shouldReturnEmptyString_whenCardsListIsEmpty
     * 用来测试 CusAccountCommonServiceImpl 方法 getTmpCardNumber
     * 验证当卡片列表为空时，返回空字符串
     */
    @Test
    void shouldReturnEmptyString_whenCardsListIsEmpty() {
        // Arrange - 准备测试数据
        List<CardAuthorizationInfo> cards = new ArrayList<>();

        // Act - 执行被测方法
        String result = cusAccountCommonService.getTmpCardNumber(cards);

        // Assert - 验证结果
        assertEquals("", result);
    }

    /**
     * 测试方法：shouldThrowNullPointerException_whenCardsListIsNull
     * 用来测试 CusAccountCommonServiceImpl 方法 getTmpCardNumber
     * 验证当卡片列表为null时，抛出NullPointerException
     */
    @Test
    void shouldThrowNullPointerException_whenCardsListIsNull() {
        // Act & Assert - 验证抛出异常
        assertThrows(NullPointerException.class, () -> {
            cusAccountCommonService.getTmpCardNumber(null);
        });
    }

    /**
     * 测试方法：shouldReturnFirstCardNumber_whenProductNumberIsNull
     * 用来测试 CusAccountCommonServiceImpl 方法 getTmpCardNumber
     * 验证当产品编号为null时，跳过该卡片并返回第一个卡片的卡号
     */
    @Test
    void shouldReturnFirstCardNumber_whenProductNumberIsNull() {
        try (MockedStatic<OrgNumberUtils> orgUtilsMock = mockStatic(OrgNumberUtils.class)) {
            // Mock OrgNumberUtils for BaseEntity constructor
            orgUtilsMock.when(OrgNumberUtils::getOrg).thenReturn("TEST_ORG");
            
            // Arrange - 准备测试数据
            List<CardAuthorizationInfo> cards = new ArrayList<>();
            
            // 创建产品编号为null的卡片
            CardAuthorizationInfo cardWithNullProduct = new CardAuthorizationInfo();
            cardWithNullProduct.setProductNumber(null);
            cardWithNullProduct.setCardNumber("****************");
            cards.add(cardWithNullProduct);
            
            // 创建普通卡片
            CardAuthorizationInfo normalCard = new CardAuthorizationInfo();
            normalCard.setProductNumber("NM003");
            normalCard.setCardNumber("****************");
            cards.add(normalCard);

            // Act - 执行被测方法
            String result = cusAccountCommonService.getTmpCardNumber(cards);

            // Assert - 验证结果
            assertEquals("****************", result); // 返回第一个卡片的卡号
        }
    }

    /**
     * 测试方法：shouldReturnFirstCardNumber_whenProductNumberIsEmpty
     * 用来测试 CusAccountCommonServiceImpl 方法 getTmpCardNumber
     * 验证当产品编号为空字符串时，跳过该卡片并返回第一个卡片的卡号
     */
    @Test
    void shouldReturnFirstCardNumber_whenProductNumberIsEmpty() {
        try (MockedStatic<OrgNumberUtils> orgUtilsMock = mockStatic(OrgNumberUtils.class)) {
            // Mock OrgNumberUtils for BaseEntity constructor
            orgUtilsMock.when(OrgNumberUtils::getOrg).thenReturn("TEST_ORG");
            
            // Arrange - 准备测试数据
            List<CardAuthorizationInfo> cards = new ArrayList<>();
            
            // 创建产品编号为空字符串的卡片
            CardAuthorizationInfo cardWithEmptyProduct = new CardAuthorizationInfo();
            cardWithEmptyProduct.setProductNumber("");
            cardWithEmptyProduct.setCardNumber("****************");
            cards.add(cardWithEmptyProduct);
            
            // 创建普通卡片
            CardAuthorizationInfo normalCard = new CardAuthorizationInfo();
            normalCard.setProductNumber("NM004");
            normalCard.setCardNumber("****************");
            cards.add(normalCard);

            // Act - 执行被测方法
            String result = cusAccountCommonService.getTmpCardNumber(cards);

            // Assert - 验证结果
            assertEquals("****************", result); // 返回第一个卡片的卡号
        }
    }

    /**
     * 测试方法：shouldPrioritizeTmpCard_whenMultipleTmpCardsExist
     * 用来测试 CusAccountCommonServiceImpl 方法 getTmpCardNumber
     * 验证当存在多个TMP卡片时，返回第一个找到的TMP卡片信息
     */
    @Test
    void shouldPrioritizeTmpCard_whenMultipleTmpCardsExist() {
        try (MockedStatic<OrgNumberUtils> orgUtilsMock = mockStatic(OrgNumberUtils.class)) {
            // Mock OrgNumberUtils for BaseEntity constructor
            orgUtilsMock.when(OrgNumberUtils::getOrg).thenReturn("TEST_ORG");
            
            // Arrange - 准备测试数据
            List<CardAuthorizationInfo> cards = new ArrayList<>();
            
            // 创建第一个TMP卡片
            CardAuthorizationInfo tmpCard1 = new CardAuthorizationInfo();
            tmpCard1.setProductNumber("TP001");
            tmpCard1.setCardNumber("****************");
            tmpCard1.setTmpPrincipleCardNumber("****************");
            cards.add(tmpCard1);
            
            // 创建第二个TMP卡片
            CardAuthorizationInfo tmpCard2 = new CardAuthorizationInfo();
            tmpCard2.setProductNumber("TP002");
            tmpCard2.setCardNumber("****************");
            tmpCard2.setTmpPrincipleCardNumber("****************");
            cards.add(tmpCard2);

            // Act - 执行被测方法
            String result = cusAccountCommonService.getTmpCardNumber(cards);

            // Assert - 验证结果
            assertEquals("****************", result); // 返回第一个TMP卡片的tmpPrincipleCardNumber
        }
    }
} 