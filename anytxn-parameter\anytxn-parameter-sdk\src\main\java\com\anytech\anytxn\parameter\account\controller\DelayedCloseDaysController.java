package com.anytech.anytxn.parameter.account.controller;

import io.swagger.v3.oas.annotations.tags.Tag;
import io.swagger.v3.oas.annotations.Operation;
import com.anytech.anytxn.common.core.base.PageResultDTO;
import com.anytech.anytxn.common.core.base.AnyTxnHttpResponse;
import com.anytech.anytxn.common.core.base.BizBaseController;
import com.anytech.anytxn.parameter.base.account.domain.dto.DelayedCloseDaysDTO;
import com.anytech.anytxn.parameter.base.account.service.IDelayedCloseDaysService;
import com.anytech.anytxn.parameter.base.common.enums.ParameterRepDetailEnum;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import jakarta.validation.Valid;

/**
 * <AUTHOR>
 * @date 2019-11-19
 */
@Tag(name = "延迟销户天数参数")
@RestController
public class DelayedCloseDaysController extends BizBaseController {

    @Autowired
    private IDelayedCloseDaysService delayedCloseDaysService;

    /**
     * 根据机构号分页查询延迟销户天数信息
     * @param pageNum 页码
     * @param pageSize 每页大小
     * @return AnyTxnHttpResponse<PageResultDTO<BinCardNumberUsedDTO>>
     */
    @GetMapping(value = "/param/delayedCloseDays/pageNum/{pageNum}/pageSize/{pageSize}")
    @Operation(summary="分页查询延迟销户天数信息", description = "需传入页码以及页面大小")
    public AnyTxnHttpResponse<PageResultDTO<DelayedCloseDaysDTO>> getPageByStatus (@PathVariable(value = "pageNum") Integer pageNum,
                                                                                   @PathVariable(value = "pageSize") Integer pageSize,
                                                                                   @RequestParam(value = "tableId",required = false) String tableId,
                                                                                   @RequestParam(value = "description",required = false) String description,
                                                                                   @RequestParam(value = "delayedCloseDays",required = false) String delayedCloseDays,
                                                                                   @RequestParam(required = false) String organizationNumber) {
        PageResultDTO<DelayedCloseDaysDTO> pageResultDto = delayedCloseDaysService.findAll(pageNum, pageSize, tableId,description,delayedCloseDays, organizationNumber);
        return AnyTxnHttpResponse.success(pageResultDto);
    }

    /**
     * 根据id查询延迟销户天数信息
     * @param id 延迟销户天数id
     * @return AnyTxnHttpResponse<BinCardNumberUsedDTO>
     */
    @Operation(summary="根据id查询延迟销户天数信息", description = "")
    @GetMapping(value = "/param/delayedCloseDays/id/{id}")
    public AnyTxnHttpResponse<DelayedCloseDaysDTO> getByIndex(@PathVariable String id){
        DelayedCloseDaysDTO delayedCloseDaysDTO = delayedCloseDaysService.findById(id);
        return AnyTxnHttpResponse.success(delayedCloseDaysDTO);
    }

    /**
     * 新增延迟销户天数信息
     * @param record 延迟销户天数信息请求数据
     * @return HttpApiResponse
     */
    @PostMapping("/param/delayedCloseDays")
    @Operation(summary = "新增延迟销户天数信息")
    public AnyTxnHttpResponse<Object> create(@Valid @RequestBody DelayedCloseDaysDTO record) {
        return AnyTxnHttpResponse.success(delayedCloseDaysService.add(record),ParameterRepDetailEnum.CREATE.message());
    }

    /**
     * 更新延迟销户天数信息
     * @param record 延迟销户天数信息请求数据
     * @return HttpApiResponse
     */
    @PutMapping(value = "/param/delayedCloseDays")
    @Operation(summary="根据id更新延迟销户天数信息")
    public AnyTxnHttpResponse<Object> modify(@Valid @RequestBody DelayedCloseDaysDTO record) {
        return AnyTxnHttpResponse.success(delayedCloseDaysService.update(record),ParameterRepDetailEnum.UPDATE.message());
    }

    /**
     * 通过id删除延迟销户天数信息
     * @param id 技术id
     * @return HttpApiResponse
     */
    @DeleteMapping(value = "/param/delayedCloseDays/id/{id}")
    @Operation(summary="根据id删除延迟销户天数信息", description = "需传入id")
    public AnyTxnHttpResponse<Object> remove(@PathVariable String id) {
        return AnyTxnHttpResponse.success(delayedCloseDaysService.delete(id),ParameterRepDetailEnum.DEL.message());
    }
}
