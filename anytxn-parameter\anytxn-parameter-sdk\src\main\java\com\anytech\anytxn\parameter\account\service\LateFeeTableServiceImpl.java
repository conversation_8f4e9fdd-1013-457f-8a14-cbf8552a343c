package com.anytech.anytxn.parameter.account.service;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.TypeReference;
import com.github.pagehelper.Page;
import com.github.pagehelper.PageHelper;
import com.anytech.anytxn.common.core.annotation.db.DeleteParameterAnnotation;
import com.anytech.anytxn.common.core.annotation.db.InsertParameterAnnotation;
import com.anytech.anytxn.common.core.annotation.db.UpdateParameterAnnotation;
import com.anytech.anytxn.business.base.audit.AbstractParameterService;
import com.anytech.anytxn.common.core.base.ParameterCompare;
import com.anytech.anytxn.business.base.audit.ParmModificationRecord;
import com.anytech.anytxn.common.core.base.PageResultDTO;
import com.anytech.anytxn.common.core.utils.BeanMapping;
import com.anytech.anytxn.common.core.utils.OrgNumberUtils;
import com.anytech.anytxn.common.sequence.utils.Number16IdGen;
import com.anytech.anytxn.common.core.utils.TenantUtils;
import com.anytech.anytxn.parameter.base.account.domain.dto.LateFeeTableReqDTO;
import com.anytech.anytxn.parameter.base.account.domain.dto.LateFeeTableResDTO;
import com.anytech.anytxn.parameter.base.account.domain.dto.LateFeeTableSearchDTO;
import com.anytech.anytxn.parameter.base.account.service.ILateFeeTableService;
import com.anytech.anytxn.parameter.base.common.enums.AnyTxnParameterRespCodeEnum;
import com.anytech.anytxn.parameter.base.common.enums.ParameterRepDetailEnum;
import com.anytech.anytxn.parameter.base.common.exception.AnyTxnParameterException;
import com.anytech.anytxn.parameter.account.mapper.ParmLateFeeTableMapper;
import com.anytech.anytxn.parameter.account.mapper.ParmLateFeeTableSelfMapper;
import com.anytech.anytxn.parameter.base.account.domain.model.ParmLateFeeTable;
//import org.apache.shardingsphere.transaction.annotation.ShardingSphereTransactionType;
//import org.apache.shardingsphere.transaction.core.TransactionType;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.StringUtils;

import java.math.BigDecimal;
import java.util.HashMap;
import java.util.List;
import java.util.Objects;

/**
 * 违约金参数 业务接口实现
 * <AUTHOR>
 * @date 2020/3/26
 */
@Service(value = "parm_late_fee_table_serviceImpl")
public class LateFeeTableServiceImpl extends AbstractParameterService implements ILateFeeTableService {

    private final Logger log = LoggerFactory.getLogger(LateFeeTableServiceImpl.class);

    @Autowired
    private ParmLateFeeTableMapper parmLateFeeTableMapper;
    @Autowired
    private ParmLateFeeTableSelfMapper parmLateFeeTableSelfMapper;

    @Autowired
    private Number16IdGen numberIdGenerator;

    /**
     * 查询违约金参数
     * @return 违约金参数响应参数
     * @param pageNum
     * @param pageSize
     */
    @Override
    public PageResultDTO<LateFeeTableResDTO> findAll(Integer pageNum, Integer pageSize, LateFeeTableSearchDTO lateFeeTableSearchDTO){
        if(null == lateFeeTableSearchDTO){
            lateFeeTableSearchDTO = new LateFeeTableSearchDTO();
        }

        if(!StringUtils.isEmpty(lateFeeTableSearchDTO.getChargePercent())){
            BigDecimal chargePercent = null;
            try {
                chargePercent = new BigDecimal(lateFeeTableSearchDTO.getChargePercent());
            }catch (NumberFormatException e){
                throw new AnyTxnParameterException(AnyTxnParameterRespCodeEnum.PARAMETER_EXCEPTION, ParameterRepDetailEnum.NUMBER_ERROR);
            }
            lateFeeTableSearchDTO.setChargePercentDeci(chargePercent);
        }

        if(!StringUtils.isEmpty(lateFeeTableSearchDTO.getFixedAmnt())){
            BigDecimal fixedAmnt = null;
            try {
                fixedAmnt = new BigDecimal(lateFeeTableSearchDTO.getFixedAmnt());
            }catch (NumberFormatException e){
                throw new AnyTxnParameterException(AnyTxnParameterRespCodeEnum.PARAMETER_EXCEPTION, ParameterRepDetailEnum.NUMBER_ERROR);
            }
            lateFeeTableSearchDTO.setFixedAmntDeci(fixedAmnt);
        }

        Page<ParmLateFeeTable> page = PageHelper.startPage(pageNum, pageSize);
        lateFeeTableSearchDTO.setOrganizationNumber(StringUtils.isEmpty(lateFeeTableSearchDTO.getOrganizationNumber()) ? OrgNumberUtils.getOrg() : lateFeeTableSearchDTO.getOrganizationNumber());
        List<ParmLateFeeTable> lateFeeTableList = parmLateFeeTableSelfMapper.selectByCondition(lateFeeTableSearchDTO);
        List<LateFeeTableResDTO> res = BeanMapping.copyList(lateFeeTableList, LateFeeTableResDTO.class);
        return new PageResultDTO<>(pageNum,pageSize,page.getTotal(), page.getPages(),res);
    }



    /**
     * 添加违约金参数
     * @param  lateFeeTableReq 违约金参数入参对象
     * @return 违约金参数响应参数
     */
    @Override
    @InsertParameterAnnotation(tableName = "parm_late_fee_table", tableDesc = "Late Fee")
    public ParameterCompare addParmFee(LateFeeTableReqDTO lateFeeTableReq){
        // 根据机构号和table_id查询该记录是否已经存在
        ParmLateFeeTable parmLateFeeTable = parmLateFeeTableSelfMapper.selectByOrgAndTid(OrgNumberUtils.getOrg(lateFeeTableReq.getOrganizationNumber()), lateFeeTableReq.getTableId());
        if (parmLateFeeTable != null) {
            log.error("违约金参数已存在,id:{}",lateFeeTableReq.getId());
            throw new AnyTxnParameterException(AnyTxnParameterRespCodeEnum.P_PARAMETER_TABLE_ID_EXIST);
        }
        checkParam(lateFeeTableReq);
        ParmLateFeeTable parmFeeTableReq = BeanMapping.copy(lateFeeTableReq, ParmLateFeeTable.class);
        // 设置默认值
        parmFeeTableReq.setId(String.valueOf(numberIdGenerator.generateId(TenantUtils.getTenantId())));

        return ParameterCompare.getBuilder().withAfter(parmFeeTableReq).build(ParmLateFeeTable.class);
    }

    /**
     * 更新违约金参数
     * @param  lateFeeTableReq 违约金数入参对象
     * @return 违约金参数响应参数
     */
    @Override
    @UpdateParameterAnnotation(tableName = "parm_late_fee_table", tableDesc = "Late Fee")
    public ParameterCompare modifyParmFee(LateFeeTableReqDTO lateFeeTableReq) {
        ParmLateFeeTable parmLateFeeTable = parmLateFeeTableMapper.selectByPrimaryKey(lateFeeTableReq.getId());
        if (null == parmLateFeeTable) {
            log.error("违约金参数数据不存在");
            throw new AnyTxnParameterException(AnyTxnParameterRespCodeEnum.D_GET_IS_NULL_FAULT, ParameterRepDetailEnum.LATE_FREE);
        }
        // 判断修改后的数据在表里是否是以机构号,表id唯一确定一条数据
        if (!lateFeeTableReq.getOrganizationNumber().equals(parmLateFeeTable.getOrganizationNumber())
                || !lateFeeTableReq.getTableId().equals(parmLateFeeTable.getTableId())) {
            ParmLateFeeTable isExsit = parmLateFeeTableSelfMapper.selectByOrgAndTid(lateFeeTableReq.getOrganizationNumber(), lateFeeTableReq.getTableId());
            if (isExsit != null) {
                log.error("违约金参数已存在,id:{}",lateFeeTableReq.getId());
                throw new AnyTxnParameterException(AnyTxnParameterRespCodeEnum.P_PARAMETER_TABLE_ID_EXIST);
            }
        }
        checkParam(lateFeeTableReq);
        ParmLateFeeTable updateLateFee = BeanMapping.copy(lateFeeTableReq, ParmLateFeeTable.class);

        return ParameterCompare.getBuilder().withAfter(updateLateFee).withBefore(parmLateFeeTable).build(ParmLateFeeTable.class);

    }


    private void checkParam(LateFeeTableReqDTO lateFeeTableReq){
        //如果按次收取， 则次数相关字段校验
        if (!Objects.equals("3",lateFeeTableReq.getChargeOption())){
            return;
        }

        if (lateFeeTableReq.getLateFeeLimitAmount() == null
                || JSON.parseObject(lateFeeTableReq.getLateFeeMaxNumAmount(),new TypeReference<HashMap<String,String>>() {}).size()
                != lateFeeTableReq.getLateFeeMaxNum()){
            throw new AnyTxnParameterException(AnyTxnParameterRespCodeEnum.P_PARAMETER_EMPTY_FAULT);
        }

        if (lateFeeTableReq.getLateFeeMaxNum() <= 0 || lateFeeTableReq.getLateFeeMaxNum() > 36){
            throw new AnyTxnParameterException(AnyTxnParameterRespCodeEnum.P_PARAMETER_EMPTY_FAULT);
        }
    }





    /**
     * 通过Id主键删除违约金参数
     * @param  id 主键
     * @return Boolean
     */
    @Transactional(rollbackFor = Exception.class,propagation = Propagation.REQUIRED)
    @Override
    @DeleteParameterAnnotation(tableName = "parm_late_fee_table", tableDesc = "Late Fee")
    public ParameterCompare removeParmFee(String id){
        ParmLateFeeTable parmLateFeeTable = new ParmLateFeeTable();
        // 如果id不存在，则查询该记录
        if (null != id) {
            parmLateFeeTable = parmLateFeeTableMapper.selectByPrimaryKey(id);
        }
        // 如果该记录不存在，抛出异常，否则就删除该记录
        if (null == parmLateFeeTable) {
            log.error("违约金参数数据不存在");
            throw new AnyTxnParameterException(AnyTxnParameterRespCodeEnum.D_GET_IS_NULL_FAULT, ParameterRepDetailEnum.LATE_FREE);
        }
        return ParameterCompare.getBuilder().withBefore(parmLateFeeTable).build(ParmLateFeeTable.class);

    }

    /**
     * 通过Id查询违约金参数信息
     * @param id 主键id
     * @return AnnualFeeTableResDTO
     */
    @Override
    public LateFeeTableResDTO findById(String id){
        if (id == null) {
            log.error("id不能为空");
            throw new AnyTxnParameterException(AnyTxnParameterRespCodeEnum.P_PARAMETER_ID_EMPTY_FAULT, ParameterRepDetailEnum.ID_NULL);
        }
        ParmLateFeeTable parmLateFeeTable = parmLateFeeTableMapper.selectByPrimaryKey(id);
        return BeanMapping.copy(parmLateFeeTable, LateFeeTableResDTO.class);
    }

    /**
     * 根据表Id查询违约金参数
     *
     * @param tableId 表Id
     * @return 违约金参数响应参数
     */
    @Override
    public LateFeeTableResDTO findByOrgAndTableId(String orgNum, String tableId){
        ParmLateFeeTable parmLateFeeTable = parmLateFeeTableSelfMapper.selectByOrgAndTid(orgNum, tableId);
        if (parmLateFeeTable == null) {
            log.error("违约金参数数据不存在");
            throw new AnyTxnParameterException(AnyTxnParameterRespCodeEnum.D_GET_IS_NULL_FAULT, ParameterRepDetailEnum.LATE_FREE);
        }
        return BeanMapping.copy(parmLateFeeTable, LateFeeTableResDTO.class);
    }


    @Override
    @Transactional(rollbackFor = Exception.class,propagation = Propagation.REQUIRED)
    //@ShardingSphereTransactionType(TransactionType.XA)
    protected boolean updateDb(ParmModificationRecord parmModificationRecord) {
        ParmLateFeeTable parmLateFeeTable = JSON.parseObject(parmModificationRecord.getParmBody(), ParmLateFeeTable.class);
        parmLateFeeTable.initUpdateDateTime();
        int i = parmLateFeeTableMapper.updateByPrimaryKeySelective(parmLateFeeTable);
        return i > 0;
    }

    @Override
    @Transactional(rollbackFor = Exception.class,propagation = Propagation.REQUIRED)
    //@ShardingSphereTransactionType(TransactionType.XA)
    protected boolean insertDb(ParmModificationRecord parmModificationRecord) {
        ParmLateFeeTable parmLateFeeTable = JSON.parseObject(parmModificationRecord.getParmBody(), ParmLateFeeTable.class);
        parmLateFeeTable.initCreateDateTime();
        parmLateFeeTable.initUpdateDateTime();
        int i = parmLateFeeTableMapper.insertSelective(parmLateFeeTable);
        return i > 0;
    }

    @Override
    @Transactional(rollbackFor = Exception.class,propagation = Propagation.REQUIRED)
    //@ShardingSphereTransactionType(TransactionType.XA)
    protected boolean deleteDb(ParmModificationRecord parmModificationRecord) {
        ParmLateFeeTable parmLateFeeTable = JSON.parseObject(parmModificationRecord.getParmBody(), ParmLateFeeTable.class);
        int i = parmLateFeeTableMapper.deleteByPrimaryKey(parmLateFeeTable.getId());
        return i > 0;
    }
}
