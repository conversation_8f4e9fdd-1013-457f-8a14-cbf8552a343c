package com.anytech.anytxn.parameter.common.controller.system.components;

import io.swagger.v3.oas.annotations.tags.Tag;
import io.swagger.v3.oas.annotations.Operation;
import com.anytech.anytxn.common.core.base.PageResultDTO;
import com.anytech.anytxn.common.core.base.AnyTxnHttpResponse;
import com.anytech.anytxn.common.core.base.BizBaseController;
import com.anytech.anytxn.parameter.base.common.enums.ParameterRepDetailEnum;
import com.anytech.anytxn.parameter.base.common.domain.dto.system.ParmSysDictDTO;
import com.anytech.anytxn.parameter.base.common.domain.dto.system.ParmSysDictSearchKeyDTO;
import com.anytech.anytxn.parameter.base.common.domain.dto.system.ParmSysDictSimpleResDTO;
import com.anytech.anytxn.parameter.base.common.service.system.IParmSysOrgDictService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.HashMap;
import java.util.List;

/**
 * 系统机构字典 api
 * <AUTHOR>
 * @date 2021-01-21
 **/
@Slf4j
@Tag(name = "系统字典")
@RestController
public class ParmSysOrgDictController extends BizBaseController {

    @Autowired
    private IParmSysOrgDictService parmSysOrgDictService;

    /**
     * 通过字典类型ID集获取各字典列表，字典类型ID分组
     * @param typeIds
     * @return
     */
    @Operation(summary = "通过字典类型ID集获取各字典列表，字典类型ID分组")
    @GetMapping(value = "/param/sysOrgDict/getMap")
    public AnyTxnHttpResponse<HashMap<String, List<ParmSysDictSimpleResDTO>>> getByTypeIds(@RequestParam List<String> typeIds,
                                                                                           @RequestParam String organizationNumber) {
        HashMap<String, List<ParmSysDictSimpleResDTO>> sysCodeMap = parmSysOrgDictService.findMapByTypeIds(typeIds, organizationNumber);
        return AnyTxnHttpResponse.success(sysCodeMap);
    }

    /**
     * 查询所有字典数据
     * @return
     */
    @Operation(summary = "获取所有字典数据")
    @GetMapping(value = "/param/sysOrgDict/all")
    public AnyTxnHttpResponse<List<ParmSysDictDTO>> selectAll(@RequestParam String organizationNumber) {
        List<ParmSysDictDTO> list = parmSysOrgDictService.selectAll(organizationNumber);
        return AnyTxnHttpResponse.success(list);
    }

    /**
     * 分页查询系统字典类型集合
     * @param searchKey
     * @return
     */
    @Operation(summary = "分页查询系统字典类型信息",description = "分页查询系统字典类型信息")
    @GetMapping(value = "/param/sysOrgDictType/findAll/pageNum/{pageNum}/pageSize/{pageSize}")
    public AnyTxnHttpResponse<PageResultDTO<ParmSysDictDTO>> getPage(@PathVariable(value = "pageNum") Integer pageNum,
                                                                        @PathVariable(value = "pageSize") Integer pageSize, @RequestBody(required = false) ParmSysDictSearchKeyDTO searchKey)  {
        if (null == searchKey) {
            searchKey = new ParmSysDictSearchKeyDTO();
        }
        searchKey.setPage(pageNum);
        searchKey.setRows(pageSize);
        PageResultDTO<ParmSysDictDTO> txnPage = parmSysOrgDictService.findPage(searchKey);
        return AnyTxnHttpResponse.success(txnPage, ParameterRepDetailEnum.QUERY.message());
    }

    /**
     * 根据父id获取字典表数据，只查当前父id下的那一级
     * @param pid
     * @return
     */
    @Operation(summary = "根据父id获取字典表数据,只查一级")
    @GetMapping(value = "/param/sysOrgDict/pid/{pid}")
    public AnyTxnHttpResponse<List<ParmSysDictDTO>> selectListByPid(@PathVariable(value = "pid") String pid) {
        List<ParmSysDictDTO> resultList = parmSysOrgDictService.selectListByPid(pid);
        return AnyTxnHttpResponse.success(resultList,ParameterRepDetailEnum.QUERY.message());
    }

    /**
     * 根据字典类型获取字典表数据
     * @param typeId
     * @return
     */
    @Operation(summary = "根据字典类型获取字典数据,包括该类型下的所有层级字典数据")
    @GetMapping(value = "/param/sysOrgDict/typeId/{typeId}")
    public AnyTxnHttpResponse<List<ParmSysDictDTO>> selectListByTypeId(@PathVariable(value = "typeId") String typeId, @RequestParam String organizationNumber) {
        List<ParmSysDictDTO> resultList = parmSysOrgDictService.selectListByTypeId(typeId, organizationNumber);
        return AnyTxnHttpResponse.success(resultList,ParameterRepDetailEnum.QUERY.message());
    }

    /**
     * 根据字典数据表id获取字典表数据
     * @param id
     * @return
     */
    @Operation(summary = "根据字典数据表id获取字典表数据")
    @GetMapping(value = "/param/sysOrgDict/id/{id}")
    public AnyTxnHttpResponse<ParmSysDictDTO> getByPrimaryId(@PathVariable(value = "id") String id) {
        ParmSysDictDTO sysCode = parmSysOrgDictService.findSysOrgDictById(id);
        return AnyTxnHttpResponse.success(sysCode,ParameterRepDetailEnum.QUERY.message());
    }

    /**
     * 新增字典表数据
     * @param parmSysDictDTO
     * @return
     */
    @Operation(summary = "新增字典表数据")
    @PostMapping(value = "/param/sysOrgDict")
    public AnyTxnHttpResponse<Object> addSysOrgDict(@RequestBody ParmSysDictDTO parmSysDictDTO) {
        return AnyTxnHttpResponse.success(parmSysOrgDictService.addParmSysOrgDict(parmSysDictDTO),ParameterRepDetailEnum.CREATE.message());
    }

    /**
     * 修改字典表数据
     * @param parmSysDictDTO
     * @return
     */
    @Operation(summary = "修改字典表数据")
    @PutMapping(value = "/param/sysOrgDict")
    public AnyTxnHttpResponse<Object> modifySysOrgDict(@RequestBody ParmSysDictDTO parmSysDictDTO) {
        return AnyTxnHttpResponse.success(parmSysOrgDictService.modifyParmSysOrgDict(parmSysDictDTO),ParameterRepDetailEnum.UPDATE.message());
    }

    /**
     * 根据字典数据表id删除字典表数据
     * @param id
     * @return
     */
    @Operation(summary = "根据字典数据表id删除字典表数据")
    @DeleteMapping(value = "/param/sysOrgDict/id/{id}")
    public AnyTxnHttpResponse<Object> deleteSysCode(@PathVariable(value = "id") String id) {
        return AnyTxnHttpResponse.success(parmSysOrgDictService.deleteParmSysOrgDict(id),ParameterRepDetailEnum.DEL.message());
    }

}
