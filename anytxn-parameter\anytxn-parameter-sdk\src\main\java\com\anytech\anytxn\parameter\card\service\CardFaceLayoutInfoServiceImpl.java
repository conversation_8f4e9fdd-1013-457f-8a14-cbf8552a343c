package com.anytech.anytxn.parameter.card.service;


import com.anytech.anytxn.common.core.utils.BeanMapping;
import com.anytech.anytxn.common.sequence.utils.Number16IdGen;
import com.anytech.anytxn.common.core.utils.TenantUtils;
import com.anytech.anytxn.parameter.base.card.domain.dto.CardFaceLayoutInfoDTO;
import com.anytech.anytxn.parameter.base.card.domain.dto.CardFaceReqDTO;
import com.anytech.anytxn.parameter.base.card.service.ICardFaceLayoutInfoService;
import com.anytech.anytxn.parameter.base.common.constants.Constants;
import com.anytech.anytxn.parameter.base.common.enums.AnyTxnParameterRespCodeEnum;
import com.anytech.anytxn.parameter.base.common.exception.AnyTxnParameterException;
import com.anytech.anytxn.parameter.base.card.domain.model.ParmCardFaceLayoutInfo;
import com.anytech.anytxn.parameter.card.mapper.broadcast.ParmCardFaceLayoutInfoMapper;
import com.anytech.anytxn.parameter.card.mapper.broadcast.ParmCardFaceLayoutInfoSelfMapper;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @Date 2023/9/14  14:17
 * @Version 1.0
 */

@Service
@Slf4j
public  class CardFaceLayoutInfoServiceImpl   implements ICardFaceLayoutInfoService {

    private Logger logger= LoggerFactory.getLogger(CardFaceServiceImpl.class);

    @Autowired
    private ParmCardFaceLayoutInfoSelfMapper parmCardFaceLayoutInfoSelfMapper;

    @Autowired
    private ParmCardFaceLayoutInfoMapper parmCardFaceLayoutInfoMapper;
    @Autowired
    private Number16IdGen numberIdGenerator;
    /**
     * 新增卡版版面信息
     *
     * @param cardFaceReqDTO 卡版参数列表
     */
    @Override
    public void addCardFaceLayoutCodes(CardFaceReqDTO cardFaceReqDTO ) {
        List<CardFaceLayoutInfoDTO> cardFaceLayoutInfoDTOList = cardFaceReqDTO.getCardFaceLayoutInfoDTOList();
        logger.info("新增卡版版面信息,开始");
        if(CollectionUtils.isEmpty(cardFaceLayoutInfoDTOList)){
            logger.error("新增卡版版面信息列表不能为空");
            //TODO 暂时没设新的错误码
            throw new AnyTxnParameterException(AnyTxnParameterRespCodeEnum.D_QUERY_PARM_CARD_FACE_FAULT);
        }
        List<ParmCardFaceLayoutInfo> parmCardFaceLayoutInfoList = new ArrayList<>();
        for (CardFaceLayoutInfoDTO cardFaceLayoutInfoDTO : cardFaceLayoutInfoDTOList) {
            //校验此卡版版面信息是否已存在
            List<CardFaceLayoutInfoDTO> parmCardFaceLayoutInfos = parmCardFaceLayoutInfoMapper.selectLayoutCodeByFaceIdAndLayoutCode(
                    cardFaceReqDTO.getOrganizationNumber(), cardFaceReqDTO.getTableId());
            List<CardFaceLayoutInfoDTO> exisitList = new ArrayList<>();
            if(CollectionUtils.isNotEmpty( parmCardFaceLayoutInfos)){
                exisitList = parmCardFaceLayoutInfos.stream().filter(
                        e -> cardFaceLayoutInfoDTO.getCardLayoutCode().equals(e.getCardLayoutCode())).collect(Collectors.toList());
            }
            if(CollectionUtils.isNotEmpty(exisitList)){
                continue;
            }
            ParmCardFaceLayoutInfo parmCardFaceLayoutInfo = new ParmCardFaceLayoutInfo();
            BeanMapping.copy(cardFaceLayoutInfoDTO,parmCardFaceLayoutInfo);
            parmCardFaceLayoutInfo.setId(numberIdGenerator.generateId(TenantUtils.getTenantId()));                                //技术主键
            parmCardFaceLayoutInfo.setOrganizationNumber(cardFaceReqDTO.getOrganizationNumber());        //机构号
            parmCardFaceLayoutInfo.setCardFaceTableId(cardFaceReqDTO.getTableId());                      //卡版参数表id
            parmCardFaceLayoutInfo.setStatus("1");                                                       //状态
            parmCardFaceLayoutInfo.setCreateTime(LocalDateTime.now());
            parmCardFaceLayoutInfo.setUpdateTime(LocalDateTime.now());
            parmCardFaceLayoutInfo.setUpdateBy(Constants.DEFAULT_UPDATE_BY);
            parmCardFaceLayoutInfo.setVersionNumber(1L);
            int i = parmCardFaceLayoutInfoMapper.insert(parmCardFaceLayoutInfo);
            if(i<1){
                logger.error("批量新增卡版版面信息失败，{}", parmCardFaceLayoutInfo);
                //TODO 暂时没设新的错误码
                throw new AnyTxnParameterException(AnyTxnParameterRespCodeEnum.INSERT_CARD_FACE_LAYOUT_EXCEPTION);
            }
        }
        logger.info("新增卡版版面信息,结束,卡版参数表id:{},版面代码:{}", cardFaceReqDTO.getTableId(), cardFaceReqDTO.getCardFaceLayoutInfoDTOList());

    }


    /**
     * 删除卡版版面信息
     * @param organizationNumber
     * @param cardFaceTableId
     */
    @Override
    public void deleteByOrgAndFaceId(String organizationNumber, String cardFaceTableId) {
        parmCardFaceLayoutInfoSelfMapper.deleteByOrgAndFaceId(organizationNumber,cardFaceTableId);
    }

    /**
     * 查询卡版版面信息
     * @param organizationNumber 机构号
     * @param cardFaceTableId 卡板参数表tableId
     */
    @Override
    public List<CardFaceLayoutInfoDTO> queryByOrgAndFaceId(String organizationNumber, String cardFaceTableId) {
        return   parmCardFaceLayoutInfoMapper.selectLayoutCodeByFaceIdAndLayoutCode(organizationNumber, cardFaceTableId);
    }
}
