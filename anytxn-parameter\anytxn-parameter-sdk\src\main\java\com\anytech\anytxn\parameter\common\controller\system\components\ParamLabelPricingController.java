package com.anytech.anytxn.parameter.common.controller.system.components;

import com.anytech.anytxn.common.core.base.PageResultDTO;
import com.anytech.anytxn.common.core.base.AnyTxnHttpResponse;
import com.anytech.anytxn.parameter.base.common.enums.ParameterRepDetailEnum;
import com.anytech.anytxn.parameter.base.common.domain.dto.system.ParamLabelPricingDTO;
import com.anytech.anytxn.parameter.base.common.service.system.IParamLabelPricingService;
import io.swagger.v3.oas.annotations.tags.Tag;
import io.swagger.v3.oas.annotations.Operation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

/**
 * 标签定价相关接口
 *
 * <AUTHOR>
 */
@RestController
@Tag(name = "标签定价相关接口")
@Slf4j
@RequestMapping("/anytxn/v2/api")
public class ParamLabelPricingController {

    @Autowired
    private IParamLabelPricingService paramLabelPricingService;

    /**
     * 添加 或者 复制
     * @return Object
     */
    @Operation(summary = "添加或者复制")
    @PostMapping("/param/labelPricing/add")
    public AnyTxnHttpResponse<Object> add(@RequestBody ParamLabelPricingDTO paramLabelPricingDTO){
        return AnyTxnHttpResponse.success(paramLabelPricingService.add(paramLabelPricingDTO), ParameterRepDetailEnum.CREATE.message());
    }

    /**
     * 修改
     * @return Object
     */
    @Operation(summary = "编辑接口")
    @PostMapping("/param/labelPricing/modify")
    public AnyTxnHttpResponse<Object> modify(@RequestBody ParamLabelPricingDTO paramLabelPricingDTO){
        return AnyTxnHttpResponse.success(paramLabelPricingService.modify(paramLabelPricingDTO), ParameterRepDetailEnum.UPDATE.message());
    }

    /**
     * 分页
     * @return ParamLabelPricingDTO
     */
    @PostMapping("/param/labelPricing/findPage")
    public AnyTxnHttpResponse<PageResultDTO<ParamLabelPricingDTO>> findByPage(@RequestBody ParamLabelPricingDTO paramLabelDto){
        return AnyTxnHttpResponse.success(paramLabelPricingService.findPage(paramLabelDto),ParameterRepDetailEnum.QUERY.message());
    }

    /**
     * 删除
     * @return Object
     */
    @Operation(summary = "删除接口")
    @PostMapping("/param/labelPricing/remove")
    public AnyTxnHttpResponse<Object> remove(@RequestParam("id") String id){
        return AnyTxnHttpResponse.success(paramLabelPricingService.remove(id), ParameterRepDetailEnum.DEL.message());
    }

    /**
     * 查看
     * @return ParamLabelPricingDTO
     */
    @Operation(summary = "查看接口")
    @PostMapping("/param/labelPricing/findById")
    public AnyTxnHttpResponse<ParamLabelPricingDTO> findById(@RequestParam("id") String id){
        return AnyTxnHttpResponse.success(paramLabelPricingService.findById(id),ParameterRepDetailEnum.QUERY.message());
    }

    /**
     * 添加/修改/复制
     */
    @Operation(summary = "添加/修改/复制")
    @PostMapping("/param/labelPricing/change")
    public AnyTxnHttpResponse<Object> change(@RequestBody ParamLabelPricingDTO paramLabelPricingDTO){
        boolean result = paramLabelPricingService.change(paramLabelPricingDTO);
        return AnyTxnHttpResponse.success(result, ParameterRepDetailEnum.CREATE.message());
    }


}
