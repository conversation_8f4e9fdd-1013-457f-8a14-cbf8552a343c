package com.anytech.anytxn.parameter.common.controller.system.components;

import io.swagger.v3.oas.annotations.tags.Tag;
import io.swagger.v3.oas.annotations.Operation;
import com.anytech.anytxn.common.core.utils.OrgNumberUtils;
import com.anytech.anytxn.parameter.base.common.constants.Constants;
import com.anytech.anytxn.common.core.base.PageResultDTO;
import com.anytech.anytxn.common.core.base.AnyTxnHttpResponse;
import com.anytech.anytxn.common.core.base.BizBaseController;
import com.anytech.anytxn.parameter.base.common.enums.ParameterRepDetailEnum;
import com.anytech.anytxn.parameter.base.common.domain.dto.TransactionTypeReqDTO;
import com.anytech.anytxn.parameter.base.common.domain.dto.TransactionTypeResDTO;
import com.anytech.anytxn.parameter.base.common.service.ITransactionTypeService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import jakarta.validation.Valid;
import java.util.List;


/**
 * 交易类型
 * <AUTHOR>
 * @date 2018-08-14
 **/
@Tag(name = "交易类型")
@RestController
public class TransactionTypeController extends BizBaseController {

    @Autowired
    private ITransactionTypeService transactionTypeService;


    /**
     * 创建交易类型
     * @param transactionTypeReq 交易类型请求数据
     * @return
     */
    @Operation(summary="创建交易类型", description = "")
    @PostMapping("/param/transactionType"
    )
    public AnyTxnHttpResponse<Object> create(@Valid @RequestBody TransactionTypeReqDTO transactionTypeReq) {
        return AnyTxnHttpResponse.success(transactionTypeService.add(transactionTypeReq), ParameterRepDetailEnum.CREATE.message());

    }

    /**
     * 更新交易类型
     * @param transactionTypeReq 交易类型请求数据
     * @return
     */
    @Operation(summary="更新交易类型", description = "")
    @PutMapping("/param/transactionType")
    public AnyTxnHttpResponse<Object> modify(@Valid @RequestBody TransactionTypeReqDTO transactionTypeReq) {
        return AnyTxnHttpResponse.success(transactionTypeService.modify(transactionTypeReq),ParameterRepDetailEnum.UPDATE.message());

    }

    /**
     * 删除交易类型，通过id
     * @param id 交易类型id
     * @return
     */
    @Operation(summary="删除交易类型", description = "")
    @DeleteMapping(value = "/param/transactionType/id/{id}")
    public AnyTxnHttpResponse<Object> remove(@PathVariable String id) {
        return AnyTxnHttpResponse.success(transactionTypeService.remove(id),ParameterRepDetailEnum.DEL.message());

    }

    @Operation(summary="获取交易类型详情通过id", description = "")
    @GetMapping(value = "/param/transactionType/id/{id}")
    public AnyTxnHttpResponse<TransactionTypeResDTO> getById(@PathVariable String id) {
        return AnyTxnHttpResponse.success(transactionTypeService.find(id));
    }

    /**
     * 分页查询，当前机构下交易类型
     * @param pageNum 页码
     * @param pageSize 每页大小
     * @return
     */
    @Operation(summary="分页查询，当前机构下交易类型", description = "")
    @GetMapping(value = "/param/transactionType/pageNum/{pageNum}/pageSize/{pageSize}")
    public AnyTxnHttpResponse<PageResultDTO<TransactionTypeResDTO>> getPageList(@PathVariable(value = "pageNum") Integer pageNum,
                                                                                       @PathVariable(value = "pageSize") Integer pageSize,
                                                                                       TransactionTypeReqDTO transactionTypeReqDTO) {
        transactionTypeReqDTO = transactionTypeReqDTO == null ? new TransactionTypeReqDTO() : transactionTypeReqDTO;
        return AnyTxnHttpResponse.success(transactionTypeService.findPageByOrgNumber(pageNum, pageSize, transactionTypeReqDTO.getTransactionTypeCode(),transactionTypeReqDTO.getDescription(),transactionTypeReqDTO.getFinanceFlag(), transactionTypeReqDTO.getOrganizationNumber()));
    }

    /**
     * business需要的接口
     * 获得交易类型参数表
     * @param transactionTypeCode 交易类型
     * @param organizationNumber  机构号
     * @return AnyTxnHttpResponse<TransactionTypeRes>
     */
    @Operation(summary="获取交易类型参数", description = "通过机构号和交易类型")
    @GetMapping(value = "/param/transactionType/organizationNumber/{organizationNumber}/transactionTypeCode/{transactionTypeCode}")
    public AnyTxnHttpResponse<TransactionTypeResDTO> getTransactionType(@PathVariable String organizationNumber,
                                                                     @PathVariable String transactionTypeCode) {
        TransactionTypeResDTO transactionTypeRes = transactionTypeService.findTransactionType(organizationNumber,transactionTypeCode);
        return AnyTxnHttpResponse.success(transactionTypeRes);

    }


    /**
     * business需要的接口
     * 获得交易类型参数表
     * @param financeFlag  交易类型标志
     * @param organizationNumber  机构号
     * @return AnyTxnHttpResponse<TransactionTypeRes>
     */
    @Operation(summary="通过机构号和交易类型标志查询", description = "通过机构号和交易类型标志查询")
    @GetMapping(value = "/param/transactionType/organizationNumber/{organizationNumber}/financeFlag/{financeFlag}")
    public AnyTxnHttpResponse<TransactionTypeResDTO> getByOrgAndFinanceFlag(@PathVariable String organizationNumber,
                                                                         @PathVariable String financeFlag) {
        TransactionTypeResDTO transactionTypeRes = transactionTypeService.findByOrgAndFinanceFlag(organizationNumber,financeFlag);
        return AnyTxnHttpResponse.success(transactionTypeRes);

    }


    /**
     * 获取所有启用状态的交易类型
     * @return
     */
    @Operation(summary="获取所有启用状态的交易类型", description = "")
    @GetMapping(value = "/param/transactionType/list")
    public AnyTxnHttpResponse<List<TransactionTypeResDTO>> getList(@RequestParam(required = false) String organizationNumber) {
        organizationNumber = OrgNumberUtils.getOrg(organizationNumber);
        List<TransactionTypeResDTO> typeResList = transactionTypeService.findListByOrgNumber(organizationNumber, Constants.ENABLED);
        return AnyTxnHttpResponse.success(typeResList);

    }


    /**
     * 获取贷方余额交易类型
     * @return
     */
    @Operation(summary="获取贷方的交易类型", description = "")
    @GetMapping(value = "/param/transactionType/credit/list")
    public AnyTxnHttpResponse<List<TransactionTypeResDTO>> getListByCredit(@RequestParam(required = false) String organizationNumber) {
        organizationNumber = OrgNumberUtils.getOrg(organizationNumber);
        List<TransactionTypeResDTO> typeResList = transactionTypeService.findListByOrgNumberAndBalanceLoanDirection(organizationNumber);
        return AnyTxnHttpResponse.success(typeResList);

    }
}
