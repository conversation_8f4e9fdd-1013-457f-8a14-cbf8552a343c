package com.anytech.anytxn.parameter.common.service.system;

import com.alibaba.fastjson.JSON;
import com.github.pagehelper.Page;
import com.github.pagehelper.PageHelper;
import com.anytech.anytxn.common.core.annotation.db.DeleteParameterAnnotation;
import com.anytech.anytxn.common.core.annotation.db.InsertParameterAnnotation;
import com.anytech.anytxn.common.core.annotation.db.UpdateParameterAnnotation;
import com.anytech.anytxn.business.base.audit.AbstractParameterService;
import com.anytech.anytxn.common.core.base.ParameterCompare;
import com.anytech.anytxn.business.base.audit.ParmModificationRecord;
import com.anytech.anytxn.common.core.utils.OrgNumberUtils;
import com.anytech.anytxn.common.core.base.PageResultDTO;
import com.anytech.anytxn.common.core.utils.BeanMapping;
import com.anytech.anytxn.common.sequence.utils.Number16IdGen;
import com.anytech.anytxn.common.core.utils.TenantUtils;
import com.anytech.anytxn.parameter.base.common.domain.dto.BlockCodeCustomerReqDTO;
import com.anytech.anytxn.parameter.base.common.domain.dto.BlockCodeCustomerResDTO;
import com.anytech.anytxn.parameter.base.common.service.IBlockCodeCustomerService;
import com.anytech.anytxn.parameter.base.common.enums.AnyTxnParameterRespCodeEnum;
import com.anytech.anytxn.parameter.base.common.exception.AnyTxnParameterException;
import com.anytech.anytxn.parameter.common.mapper.broadcast.system.ParmBlockCodeCustomerMapper;
import com.anytech.anytxn.parameter.common.mapper.broadcast.system.ParmBlockCodeCustomerSelfMapper;
import com.anytech.anytxn.parameter.base.account.domain.model.ParmBlockCodeCustomer;
import org.apache.commons.lang3.StringUtils;
//import org.apache.shardingsphere.transaction.annotation.ShardingSphereTransactionType;
//import org.apache.shardingsphere.transaction.core.TransactionType;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.ObjectUtils;

import java.util.List;

/**
 * 客户层封锁码 业务接口实现
 * <AUTHOR>
 * @date 2018-08-17
 **/
@Service("parm_block_code_cust_serviceImpl")
public class BlockCodeCustomerServiceImpl extends AbstractParameterService implements IBlockCodeCustomerService {

    private final Logger logger = LoggerFactory.getLogger(BlockCodeCustomerServiceImpl.class);

    @Autowired
    private ParmBlockCodeCustomerMapper parmBlockCodeCustomerMapper;
    @Autowired
    private ParmBlockCodeCustomerSelfMapper parmBlockCodeCustomerSelfMapper;
    @Autowired
    private Number16IdGen numberIdGenerator;

    /**
     * 添加客户层封锁码
     *
     * @param req 客户层封锁码入参对象
     * @return 客户层封锁码详情
     * @throws AnyTxnParameterException
     */
    @Override
    @InsertParameterAnnotation(tableName = "parm_block_code_cust",tableDesc = "customer block code")
    public ParameterCompare add(BlockCodeCustomerReqDTO req) {
        ParmBlockCodeCustomer parmBlockCodeCustomer = parmBlockCodeCustomerSelfMapper.selectCustBlockCodeByOrgAngBlockCode(req.getOrganizationNumber(), req.getBlockCode());
        if(null != parmBlockCodeCustomer) {
            logger.warn("参数表ID和封锁码已存在, orgNumber={}, blockCode={}",
                    OrgNumberUtils.getOrg(), req.getBlockCode());
            throw new AnyTxnParameterException(AnyTxnParameterRespCodeEnum.P_EXIST_BLOCK_CODE_AND_ID_FAULT);
        }
        // 构建客户层封锁码
        ParmBlockCodeCustomer blockCodeCustomer = BeanMapping.copy(req, ParmBlockCodeCustomer.class);
        blockCodeCustomer.setId(numberIdGenerator.generateId(TenantUtils.getTenantId())+"");
        return ParameterCompare
                .getBuilder()
                .withAfter(blockCodeCustomer)
                .build(ParmBlockCodeCustomer.class);
    }

    /**
     * 修改客户层封锁码
     *
     * @param req 客户层封锁码入参对象
     * @return 客户层封锁码详情
     * @throws AnyTxnParameterException
     */
    @Override
    @UpdateParameterAnnotation(tableName = "parm_block_code_cust",tableDesc = "customer block code")
    public ParameterCompare modify(BlockCodeCustomerReqDTO req)  {
        if (req.getId() == null) {

            throw new AnyTxnParameterException(AnyTxnParameterRespCodeEnum.P_BLOCK_CODE_CUSTOMER_REQ_ID_IS_NULL_FAULT);
        }

        ParmBlockCodeCustomer blockCodeCustomer = parmBlockCodeCustomerMapper.selectByPrimaryKey(req.getId());
        if (blockCodeCustomer == null) {
            logger.error("修改客户层封锁码, 通过主键id({})未找到数据", req.getId());

            throw new AnyTxnParameterException(AnyTxnParameterRespCodeEnum.P_BLOCK_CODE_CUSTOMER_BY_ID_FAULT);
        }

        if(blockCodeCustomer.getBlockCode() == null || !blockCodeCustomer.getBlockCode().equals(req.getBlockCode())){
            List<ParmBlockCodeCustomer> dataList = parmBlockCodeCustomerSelfMapper.selectListByOrgNumber(blockCodeCustomer.getOrganizationNumber(), true);
            dataList.forEach(element->{

                boolean b1 = element.getBlockCode()!=null && element.getBlockCode().equals(req.getBlockCode());
//                boolean b2 = element.getBlockCode()==null && (element.getBlockCode() == req.getBlockCode() || "".equals(req.getBlockCode()));

                if(b1){
                    logger.error("修改客户层封锁码, 封锁码已存在({})", req.getBlockCode());

                    throw new AnyTxnParameterException(AnyTxnParameterRespCodeEnum.D_EXIST_PARM_BLOCK_CODE_CUSTOMER_FAULT);
                }
            });
        }
        ParmBlockCodeCustomer modify = BeanMapping.copy(req, ParmBlockCodeCustomer.class);
        return ParameterCompare
                .getBuilder()
                .withAfter(modify)
                .withBefore(blockCodeCustomer)
                .build(ParmBlockCodeCustomer.class);
    }

    /**
     * 删除客户层封锁码，通过id
     *
     * @param id 客户层封锁码ID
     * @return true:成功|false:失败
     * @throws AnyTxnParameterException
     */
    @Override
    @DeleteParameterAnnotation(tableName = "parm_block_code_cust",tableDesc = "customer block code")
    public ParameterCompare remove(String id)  {
        if (id == null) {

            throw new AnyTxnParameterException(AnyTxnParameterRespCodeEnum.P_PARAMETER_EMPTY_FAULT);
        }

        ParmBlockCodeCustomer blockCodeCustomer = parmBlockCodeCustomerMapper.selectByPrimaryKey(id);
        if (blockCodeCustomer == null) {
            logger.error("删除客户层封锁码, 通过主键id({})未找到数据", id);
            throw new AnyTxnParameterException(AnyTxnParameterRespCodeEnum.P_BLOCK_CODE_CUSTOMER_BY_ID_FAULT);

        }
        return ParameterCompare
                .getBuilder()
                .withBefore(blockCodeCustomer)
                .build(ParmBlockCodeCustomer.class);
    }

    /**
     * 获取客户层封锁码详情，通过id
     *
     * @param id 客户层封锁码ID
     * @return 客户层封锁码详情
     * @throws AnyTxnParameterException
     */
    @Override
    public BlockCodeCustomerResDTO find(String id) {
        if (id == null) {
            throw new AnyTxnParameterException(AnyTxnParameterRespCodeEnum.P_PARAMETER_EMPTY_FAULT);

        }

        ParmBlockCodeCustomer blockCodeCustomer = parmBlockCodeCustomerMapper.selectByPrimaryKey(id);
        if (blockCodeCustomer == null) {
            logger.error("获取客户层封锁码详情, 通过主键id({})未找到数据", id);
            throw new AnyTxnParameterException(AnyTxnParameterRespCodeEnum.P_BLOCK_CODE_CUSTOMER_BY_ID_FAULT);

        }

        return BeanMapping.copy(blockCodeCustomer, BlockCodeCustomerResDTO.class);
    }

    /**
     * 查询客户层封锁集合，通过机构编号及启用状态
     *
     * @param orgNumber 机构编号
     * @param status    客户层封锁码启用状态，不传查询所有状态
     * @return 分页的客户层封锁码
     * @throws AnyTxnParameterException
     */
    @Override
    public List<BlockCodeCustomerResDTO> findListByOrgNumber(String orgNumber, String status) {
        List<ParmBlockCodeCustomer> blockCodeList = parmBlockCodeCustomerSelfMapper.selectListByOrgNumber(orgNumber, true);
        return BeanMapping.copyList(blockCodeList, BlockCodeCustomerResDTO.class);
    }

    /**
     * 分页查询客户层封锁码，通过机构编号及启用状态
     *
     * @param pageNum   页号
     * @param pageSize  每页大小
     * @return 分页的客户层封锁码
     * @throws AnyTxnParameterException
     */
    @Override
    public PageResultDTO<BlockCodeCustomerResDTO> findPageByOrgNumber(Integer pageNum, Integer pageSize, String blockCode,String description,String authIndicator,String priorityStart,String priorityEnd, String organizationNumber) {
        //优先级范围判断
        if((ObjectUtils.isEmpty(priorityStart) && !ObjectUtils.isEmpty(priorityEnd)) || (!ObjectUtils.isEmpty(priorityStart) && ObjectUtils.isEmpty(priorityEnd))){
            throw new AnyTxnParameterException(AnyTxnParameterRespCodeEnum.P_PARAMETER_RULEPRIORITY_LIMITS_IS_ERROR);
        }
        Integer priorityStartInt = null;
        Integer priorityEndInt = null;
        if(!ObjectUtils.isEmpty(priorityStart) && !ObjectUtils.isEmpty(priorityEnd)){
            try {
                priorityStartInt = Integer.parseInt(priorityStart);
                priorityEndInt = Integer.parseInt(priorityEnd);
            }catch (Exception e){
                throw new AnyTxnParameterException(AnyTxnParameterRespCodeEnum.P_PARAMETER_RULEPRIORITY_LIMITS_IS_ERROR);
            }
            if(priorityStartInt > priorityEndInt){
                throw new AnyTxnParameterException(AnyTxnParameterRespCodeEnum.P_PARAMETER_RULEPRIORITY_LIMITS_IS_ERROR);
            }
        }

        Page page = PageHelper.startPage(pageNum, pageSize);
        organizationNumber = StringUtils.isEmpty(organizationNumber) ? OrgNumberUtils.getOrg() : organizationNumber;
        List<ParmBlockCodeCustomer> dataList = parmBlockCodeCustomerSelfMapper.selectByCondition(blockCode,description,authIndicator,priorityStartInt,priorityEndInt, organizationNumber);
        List<BlockCodeCustomerResDTO> listData = BeanMapping.copyList(dataList, BlockCodeCustomerResDTO.class);
        return new PageResultDTO(pageNum, pageSize, page.getTotal(), page.getPages(), listData);
    }

    /**
     * 通过机构号和封锁码，查询客户封锁码
     * @param orgNumber 机构号
     * @param blockCode 封锁码
     * @return BlockCodeCustomerResDTO
     */
    @Override
    public BlockCodeCustomerResDTO findCustBlockCodeByOrgAndBlockCode(String orgNumber, String blockCode) {
        ParmBlockCodeCustomer parmBlockCodeCustomer = parmBlockCodeCustomerSelfMapper.selectCustBlockCodeByOrgAngBlockCode(orgNumber, blockCode);
        return BeanMapping.copy(parmBlockCodeCustomer, BlockCodeCustomerResDTO.class);
    }

    @Override
    @Transactional(rollbackFor = Exception.class,propagation = Propagation.REQUIRED)
    //@ShardingSphereTransactionType(TransactionType.XA)
    protected boolean updateDb(ParmModificationRecord parmModificationRecord) {
        ParmBlockCodeCustomer parmBlockCodeCustomer = JSON.parseObject(parmModificationRecord.getParmBody(), ParmBlockCodeCustomer.class);
        parmBlockCodeCustomer.initUpdateDateTime();
        if("".equals(parmBlockCodeCustomer.getBlockCode())){
            parmBlockCodeCustomer.setBlockCode(null);
        }
        int res = parmBlockCodeCustomerMapper.updateByPrimaryKey(parmBlockCodeCustomer);
        return res > 0;
    }

    @Override
    @Transactional(rollbackFor = Exception.class,propagation = Propagation.REQUIRED)
    //@ShardingSphereTransactionType(TransactionType.XA)
    protected boolean insertDb(ParmModificationRecord parmModificationRecord) {
        ParmBlockCodeCustomer parmBlockCodeCustomer = JSON.parseObject(parmModificationRecord.getParmBody(), ParmBlockCodeCustomer.class);
        parmBlockCodeCustomer.initUpdateDateTime();
        parmBlockCodeCustomer.initCreateDateTime();
        if("".equals(parmBlockCodeCustomer.getBlockCode())){
            parmBlockCodeCustomer.setBlockCode(null);
        }
        int res = parmBlockCodeCustomerMapper.insert(parmBlockCodeCustomer);
        return res > 0;
    }

    @Override
    @Transactional(rollbackFor = Exception.class,propagation = Propagation.REQUIRED)
    //@ShardingSphereTransactionType(TransactionType.XA)
    protected boolean deleteDb(ParmModificationRecord parmModificationRecord) {
        ParmBlockCodeCustomer parmBlockCodeCustomer = JSON.parseObject(parmModificationRecord.getParmBody(), ParmBlockCodeCustomer.class);
        int deleteRow = parmBlockCodeCustomerMapper.deleteByPrimaryKey(parmBlockCodeCustomer.getId());
        return deleteRow > 0;
    }
}
