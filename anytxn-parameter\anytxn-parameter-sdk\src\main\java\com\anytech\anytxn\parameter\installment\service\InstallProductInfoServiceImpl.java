package com.anytech.anytxn.parameter.installment.service;

import com.alibaba.fastjson.JSON;
import com.anytech.anytxn.common.core.enums.AnyTxnCommonRespCodeEnum;
import com.anytech.anytxn.common.core.enums.ParamFileTypeEnum;
import com.anytech.anytxn.business.base.common.domain.dto.ParamExportDTO;
import com.anytech.anytxn.business.base.common.domain.dto.ParamExportPageDTO;
import com.anytech.anytxn.business.base.common.domain.dto.ParamImportDTO;
import com.anytech.anytxn.business.base.common.domain.dto.ParamImportResultDTO;
import com.anytech.anytxn.common.core.utils.JacksonUtils;
import com.anytech.anytxn.common.core.base.AnyTxnHttpResponse;
import com.github.pagehelper.Page;
import com.github.pagehelper.PageHelper;
import com.anytech.anytxn.common.core.annotation.db.DeleteParameterAnnotation;
import com.anytech.anytxn.common.core.annotation.db.InsertParameterAnnotation;
import com.anytech.anytxn.common.core.annotation.db.UpdateParameterAnnotation;
import com.anytech.anytxn.business.base.audit.AbstractParameterService;
import com.anytech.anytxn.common.core.base.ParameterCompare;
import com.anytech.anytxn.business.base.audit.ParmModificationRecord;
import com.anytech.anytxn.common.core.utils.OrgNumberUtils;
import com.anytech.anytxn.common.core.base.PageResultDTO;
import com.anytech.anytxn.common.core.utils.BeanMapping;
import com.anytech.anytxn.common.sequence.utils.Number16IdGen;
import com.anytech.anytxn.common.core.utils.TenantUtils;
import com.anytech.anytxn.parameter.base.common.enums.AnyTxnParameterRespCodeEnum;
import com.anytech.anytxn.parameter.base.common.enums.ParameterRepDetailEnum;
import com.anytech.anytxn.parameter.base.common.exception.AnyTxnParameterException;
import com.anytech.anytxn.parameter.base.installment.domain.dto.InstallProductInfoReqDTO;
import com.anytech.anytxn.parameter.base.installment.domain.dto.InstallProductInfoResDTO;
import com.anytech.anytxn.parameter.base.installment.domain.dto.InstallProductInfoSearchDTO;
import com.anytech.anytxn.parameter.base.installment.service.IInstallProductInfoService;
import com.anytech.anytxn.parameter.installment.mapper.InstallProductInfoMapper;
import com.anytech.anytxn.parameter.installment.mapper.InstallProductInfoSelfMapper;
import com.anytech.anytxn.parameter.base.installment.domain.model.InstallProductInfo;
import org.apache.commons.io.IOUtils;
import org.apache.commons.lang3.ObjectUtils;
//import org.apache.shardingsphere.transaction.annotation.ShardingSphereTransactionType;
//import org.apache.shardingsphere.transaction.core.TransactionType;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.aop.framework.AopContext;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import java.io.ByteArrayInputStream;
import java.io.IOException;
import java.util.*;
import java.util.stream.Collectors;

/**
 *分期产品参数表 业务层
 * <AUTHOR>
 * @date 2019-05-15 11:42
 **/
@Service("parm_install_product_info_serviceImpl")
public class InstallProductInfoServiceImpl extends AbstractParameterService implements IInstallProductInfoService {

    private final Logger logger = LoggerFactory.getLogger(InstallProductInfoServiceImpl.class);

    @Autowired
    private InstallProductInfoMapper installProductInfoMapper;
    @Autowired
    private InstallProductInfoSelfMapper installProductInfoSelfMapper;

    @Autowired
    private Number16IdGen numberIdGenerator;

    /**
     * 查询分期产品参数表
     * @param  id
     * @return HttpApiResponse<InstallProductInfoResDTO>
     */
    @Override
    public InstallProductInfoResDTO getById(String id) {
        if (id == null) {

            throw new AnyTxnParameterException(AnyTxnParameterRespCodeEnum.P_PRIMARY_KEY_NULL_FAULT);
        }
        InstallProductInfo productInfo = installProductInfoMapper.selectByPrimaryKey(id);

        if (productInfo == null) {
            logger.error("查询分期产品参数信息，通过id:{}未查到数据", id);

            throw new AnyTxnParameterException(AnyTxnParameterRespCodeEnum.D_QUERY_INSTALL_PRODUCT_INFO_BY_ID_FAULT);
        }
        return BeanMapping.copy(productInfo, InstallProductInfoResDTO.class);
    }
    /**
     * 新增分期产品参数表
     * @param  productInfoReqDTO
     * @return HttpApiResponse<InstallProductInfoResDTO>
     */
    @Override
    @InsertParameterAnnotation(tableName = "parm_install_product_info", tableDesc = "Installment Product Management")
    public ParameterCompare add(InstallProductInfoReqDTO productInfoReqDTO) {
        if(ObjectUtils.isEmpty(productInfoReqDTO)){
            logger.warn("分期产品参数表参数为空");
            throw new AnyTxnParameterException(AnyTxnParameterRespCodeEnum.P_PARAMETER_EMPTY_FAULT);
        }
        //检查必输字段是否为空
        checkRequiredInputs(productInfoReqDTO);
        int isExists = installProductInfoSelfMapper.isExists(productInfoReqDTO.getOrganizationNumber(), productInfoReqDTO.getProductCode());
        if (isExists > 0) {
            logger.warn("分期产品参数已存在，productCode={},organizationNumber={}",
                    productInfoReqDTO.getOrganizationNumber(), productInfoReqDTO.getProductCode());
            throw new AnyTxnParameterException(AnyTxnParameterRespCodeEnum.D_EXIST_INSTALL_PRODUCT_INFO_FAULT);
        }
        InstallProductInfo productInfo = BeanMapping.copy(productInfoReqDTO, InstallProductInfo.class);
        productInfo.setId(numberIdGenerator.generateId(TenantUtils.getTenantId())+"");

        return ParameterCompare.getBuilder()
                .withAfter(productInfo)
                .build(InstallProductInfo.class);
    }
    /**
     * 删除分期产品参数表
     * @param  id
     * @return HttpApiResponse<InstallProductInfoResDTO>
     */
    @Override
    @DeleteParameterAnnotation(tableName = "parm_install_product_info", tableDesc = "Installment Product Management")
    public ParameterCompare remove(String id) {
        if(null == id){
            throw new AnyTxnParameterException(AnyTxnParameterRespCodeEnum.P_PRIMARY_KEY_NULL_FAULT);
        }
        InstallProductInfo productInfo = installProductInfoMapper.selectByPrimaryKey(id);
        productInfo.setOrganizationNumber(OrgNumberUtils.getOrg());

        if (productInfo == null) {
            logger.error("删除分期产品参数信息，通过id:{}未查到数据", id);
            throw new AnyTxnParameterException(AnyTxnParameterRespCodeEnum.D_QUERY_INSTALL_PRODUCT_INFO_BY_ID_FAULT);
        }
        return ParameterCompare.getBuilder()
                .withBefore(productInfo)
                .build(InstallProductInfo.class);
    }
    /**
     * 修改分期产品参数表
     * @param  productInfoReqDTO
     * @return HttpApiResponse<InstallProductInfoResDTO>
     */
    @Override
    @UpdateParameterAnnotation(tableName = "parm_install_product_info", tableDesc = "Installment Product Management")
    public ParameterCompare modify(InstallProductInfoReqDTO productInfoReqDTO) {
        if (null == productInfoReqDTO.getId() ) {
            throw new AnyTxnParameterException(AnyTxnParameterRespCodeEnum.P_PRIMARY_KEY_NULL_FAULT);
        }
        InstallProductInfo productInfo1 = installProductInfoMapper.selectByPrimaryKey(String.valueOf(productInfoReqDTO.getId()));

        if (productInfo1 == null) {
            logger.error("修改分期产品参数信息，通过id:{}未查到数据", productInfoReqDTO.getId());
            throw new AnyTxnParameterException(AnyTxnParameterRespCodeEnum.D_QUERY_INSTALL_PRODUCT_INFO_BY_ID_FAULT);
        }
        InstallProductInfo productInfo = BeanMapping.copy(productInfoReqDTO, InstallProductInfo.class);
        productInfo.setId(String.valueOf(productInfoReqDTO.getId()));
        productInfo.setOrganizationNumber(productInfoReqDTO.getOrganizationNumber());

        return ParameterCompare.getBuilder()
                .withAfter(productInfo)
                .withBefore(productInfo1)
                .build(InstallProductInfo.class);
    }
    /**
     * 分页 分期产品参数表
     * @param  pageNum pageSize
     * @return HttpApiResponse<InstallProductInfoResDTO>
     */
    @Override
    public PageResultDTO<InstallProductInfoResDTO> findPage(Integer pageNum, Integer pageSize, InstallProductInfoSearchDTO installProductInfoSearchDTO) {
        if(null == installProductInfoSearchDTO){
            installProductInfoSearchDTO = new InstallProductInfoSearchDTO();
        }
        installProductInfoSearchDTO.setOrganizationNumber(StringUtils.isEmpty(installProductInfoSearchDTO.getOrganizationNumber()) ? OrgNumberUtils.getOrg() : installProductInfoSearchDTO.getOrganizationNumber());

        Integer termInt = null;
        if(!StringUtils.isEmpty(installProductInfoSearchDTO.getTerm())){
            try {
                termInt = Integer.parseInt(installProductInfoSearchDTO.getTerm());
            }catch (NumberFormatException e){
                throw new AnyTxnParameterException(AnyTxnParameterRespCodeEnum.PARAMETER_EXCEPTION, ParameterRepDetailEnum.NUMBER_ERROR);
            }
            installProductInfoSearchDTO.setTermInt(termInt);
        }

        logger.debug("分页查询分期产品参数信息");
        Page<InstallProductInfo> page = PageHelper.startPage(pageNum, pageSize);
        List<InstallProductInfo> installProductInfos = installProductInfoSelfMapper.selectByCondition(installProductInfoSearchDTO);
        List<InstallProductInfoResDTO> currencyRateRes = BeanMapping.copyList(installProductInfos, InstallProductInfoResDTO.class);
        return new PageResultDTO<>(pageNum, pageSize, page.getTotal(), page.getPages(), currencyRateRes);
    }

    @Override
    public List<InstallProductInfoResDTO> findAll(String organizationNumber,String installmentType){
        InstallProductInfoSearchDTO condition = new InstallProductInfoSearchDTO();
        condition.setOrganizationNumber(organizationNumber);
        condition.setProdType(installmentType);
        List<InstallProductInfo> installProductInfos = installProductInfoSelfMapper.selectByCondition(condition);
        return BeanMapping.copyList(installProductInfos, InstallProductInfoResDTO.class);
    }


    @Override
    public Map<String,Object> getInstallProInfoIsExistByCode(String productCode, String organizationNumber) {
        if (null == productCode || "".equals(productCode)) {

            throw new AnyTxnParameterException(AnyTxnParameterRespCodeEnum.D_INSTALL_PRODUCT_INFO_PRODUCT_CODE_FAULT);
        }
        Integer productInfo = installProductInfoSelfMapper.getInstallProInfoIsExistByCode(productCode, organizationNumber);
        if (0 == productInfo) {
            logger.error("查询分期产品参数信息，通过productCode:{}未查到数据", productCode);

            throw new AnyTxnParameterException(AnyTxnParameterRespCodeEnum.D_QUERY_INSTALL_PRODUCT_INFO_BY_PRODUCT_CODE_FAULT);
        }
        Map<String,Object> hashMap = new HashMap<>(4);
        hashMap.put("productCode",productInfo);
        return hashMap;
    }

    //根据机构号 分期类型查询
    @Override
    public InstallProductInfoResDTO findByIndex(String organizationNumber, String productCode) {
        if (null == organizationNumber || "".equals(organizationNumber)) {

            throw new AnyTxnParameterException(AnyTxnParameterRespCodeEnum.D_INSTALL_ACCOUNTING_TRANS_PARM_ORG_FAULT);
        }
        if (null == productCode || "".equals(productCode))
        {

            throw new AnyTxnParameterException(AnyTxnParameterRespCodeEnum.D_INSTALL_PRODUCT_INFO_PRODUCT_CODE_FAULT);
        }
        InstallProductInfo productInfo = installProductInfoSelfMapper.selectByIndex(organizationNumber,productCode);
        if (productInfo == null) {
            logger.error("查询分期产品参数信息，organizationNumber:{}productCode;{}未查到数据",organizationNumber, productCode);

            throw new AnyTxnParameterException(AnyTxnParameterRespCodeEnum.D_QUERY_INSTALL_PRODUCT_INFO_FAULT);
        }
        return BeanMapping.copy(productInfo, InstallProductInfoResDTO.class);
    }
    //根据机构号,分期类型,分期期数,还款方式,手续费收取方式、尾款抛账方式查询
    @Override
    public InstallProductInfoResDTO findByIndexOrgNumTypeTermPayWayFee(String organizationNumber, String prodType, Integer term, String paymentWay, String feeReceiveFlag,String balanceMethod) {
        if (null == organizationNumber || "".equals(organizationNumber)) {

            throw new AnyTxnParameterException(AnyTxnParameterRespCodeEnum.D_INSTALL_ACCOUNTING_TRANS_PARM_ORG_FAULT);
        }
        if (null == prodType || "".equals(prodType))
        {

            throw new AnyTxnParameterException(AnyTxnParameterRespCodeEnum.D_INSTALL_PRODUCT_INFO_PRODUCT_TYPE_FAULT);
        }
        if (null == term )
        {

            throw new AnyTxnParameterException(AnyTxnParameterRespCodeEnum.D_INSTALL_PRODUCT_INFO_TERM_FAULT);
        }
        if (null == paymentWay || "".equals(paymentWay))
        {

            throw new AnyTxnParameterException(AnyTxnParameterRespCodeEnum.D_INSTALL_PRODUCT_INFO_PAYMENT_WAY_FAULT);
        }
        if (null == feeReceiveFlag || "".equals(feeReceiveFlag))
        {

            throw new AnyTxnParameterException(AnyTxnParameterRespCodeEnum.D_INSTALL_PRODUCT_INFO_RECEIVE_FLAG_FAULT);
        }
        InstallProductInfo productInfo = installProductInfoSelfMapper.selectByIndexTwo( organizationNumber, prodType, term, paymentWay, feeReceiveFlag,balanceMethod);
        if (productInfo == null) {
            logger.error("查询分期产品参数信息，通过organizationNumber:{},prodType:{},term:{},paymentWay:{},feeReceiveFlag:{}未查到数据",organizationNumber,prodType,term,paymentWay,feeReceiveFlag);

            throw new AnyTxnParameterException(AnyTxnParameterRespCodeEnum.D_QUERY_INSTALL_PRODUCT_INFO_BY_ALL_FAULT);
        }
        return BeanMapping.copy(productInfo, InstallProductInfoResDTO.class);
    }

    @Override
    public InstallProductInfoResDTO findProInfoByTermAndType(String organizationNumber, String prodType, Integer term) {
        if (null == organizationNumber || "".equals(organizationNumber)) {

            throw new AnyTxnParameterException(AnyTxnParameterRespCodeEnum.D_INSTALL_ACCOUNTING_TRANS_PARM_ORG_FAULT);
        }
        if (null == prodType || "".equals(prodType))
        {

            throw new AnyTxnParameterException(AnyTxnParameterRespCodeEnum.D_INSTALL_PRODUCT_INFO_PRODUCT_TYPE_FAULT);
        }
        if (null == term)
        {

            throw new AnyTxnParameterException(AnyTxnParameterRespCodeEnum.D_INSTALL_PRODUCT_INFO_TERM_FAULT);
        }
        InstallProductInfo productInfos = installProductInfoSelfMapper.findProInfoByTermAndType( organizationNumber, prodType, term);
        if (null==productInfos ) {
            logger.error("查询分期产品参数信息，organizationNumber:{}prodType:{},term:{}未查到数据",organizationNumber, prodType,term);

            throw new AnyTxnParameterException(AnyTxnParameterRespCodeEnum.D_QUERY_INSTALL_PRODUCT_INFO_BY_THREE_FAULT);
        }
        return BeanMapping.copy(productInfos, InstallProductInfoResDTO.class);

    }

    /**
     * 根据机构号,分期类型,还款方式,手续费收取方式查询
     * @return InstallProductInfoResDTO
     */
    @Override
    public List<InstallProductInfoResDTO> findByIndexOrgNumTypePayWayFee(String organizationNumber, String prodType, String paymentWay, String feeReceiveFlag) {
        if (null == organizationNumber || "".equals(organizationNumber)) {

            throw new AnyTxnParameterException(AnyTxnParameterRespCodeEnum.D_INSTALL_ACCOUNTING_TRANS_PARM_ORG_FAULT);
        }
        if (null == prodType || "".equals(prodType))
        {

            throw new AnyTxnParameterException(AnyTxnParameterRespCodeEnum.D_INSTALL_PRODUCT_INFO_PRODUCT_TYPE_FAULT);
        }
        if (null == paymentWay || "".equals(paymentWay))
        {

            throw new AnyTxnParameterException(AnyTxnParameterRespCodeEnum.D_INSTALL_PRODUCT_INFO_PAYMENT_WAY_FAULT);
        }
        if (null == feeReceiveFlag || "".equals(feeReceiveFlag))
        {

            throw new AnyTxnParameterException(AnyTxnParameterRespCodeEnum.D_INSTALL_PRODUCT_INFO_RECEIVE_FLAG_FAULT);
        }
        List<InstallProductInfo> installProductInfoList = installProductInfoSelfMapper.selectByOrgAndTypeAndPayAndFee(organizationNumber, prodType, paymentWay, feeReceiveFlag);
        if (installProductInfoList == null || installProductInfoList.isEmpty()) {
            logger.error("查询分期产品参数信息，通过organizationNumber:{},prodType:{},paymentWay:{},feeReceiveFlag:{}未查到数据",organizationNumber,prodType,paymentWay,feeReceiveFlag);

            throw new AnyTxnParameterException(AnyTxnParameterRespCodeEnum.D_QUERY_INSTALL_PRODUCT_INFO_BY_FOUR_FAULT);
        }
        return BeanMapping.copyList(installProductInfoList, InstallProductInfoResDTO.class);
    }

    /**
     * 根据机构号、分期类型、还款方式查询
     * @param organizationNumber 机构号
     * @param prodType           分期类型
     * @param paymentWay         还款方式
     * @return List<InstallProductInfoResDTO>
     */
    @Override
    public List<InstallProductInfoResDTO> findByOrgNumTypePayWay(String organizationNumber, String prodType, String paymentWay) {
        if (null == organizationNumber || "".equals(organizationNumber)) {

            throw new AnyTxnParameterException(AnyTxnParameterRespCodeEnum.D_INSTALL_ACCOUNTING_TRANS_PARM_ORG_FAULT);
        }
        if (null == prodType || "".equals(prodType))
        {

            throw new AnyTxnParameterException(AnyTxnParameterRespCodeEnum.D_INSTALL_PRODUCT_INFO_PRODUCT_TYPE_FAULT);
        }
        if (null == paymentWay || "".equals(paymentWay))
        {

            throw new AnyTxnParameterException(AnyTxnParameterRespCodeEnum.D_INSTALL_PRODUCT_INFO_PAYMENT_WAY_FAULT);
        }
        List<InstallProductInfo> installProductInfoList = installProductInfoSelfMapper.selectByOrgAndTypeAndPay(organizationNumber, prodType, paymentWay);
        if (installProductInfoList == null || installProductInfoList.isEmpty()) {
            logger.error("查询分期产品参数信息，通过organizationNumber:{},prodType:{},paymentWay:{}未查到数据",organizationNumber,prodType,paymentWay);

            throw new AnyTxnParameterException(AnyTxnParameterRespCodeEnum.D_QUERY_INSTALL_PRODUCT_INFO_BY_FIVE_FAULT);
        }
        return BeanMapping.copyList(installProductInfoList, InstallProductInfoResDTO.class);
    }

    /**
     * 机构号,分期类型,分期期数,还款方式查询
     * @param organizationNumber 机构号
     * @param prodType           分期类型
     * @param term               分期期数
     * @param paymentWay         还款方式
     * @return InstallProductInfoResDTO
     */
    @Override
    public InstallProductInfoResDTO findByOrgNumTypeTermPayWay(String organizationNumber, String prodType, Integer term, String paymentWay) {
        if (null == organizationNumber || "".equals(organizationNumber)) {

            throw new AnyTxnParameterException(AnyTxnParameterRespCodeEnum.D_INSTALL_ACCOUNTING_TRANS_PARM_ORG_FAULT);
        }
        if (null == prodType || "".equals(prodType))
        {

            throw new AnyTxnParameterException(AnyTxnParameterRespCodeEnum.D_INSTALL_PRODUCT_INFO_PRODUCT_TYPE_FAULT);
        }
        if (null == term )
        {

            throw new AnyTxnParameterException(AnyTxnParameterRespCodeEnum.D_INSTALL_PRODUCT_INFO_TERM_FAULT);
        }
        if (null == paymentWay || "".equals(paymentWay))
        {

            throw new AnyTxnParameterException(AnyTxnParameterRespCodeEnum.D_INSTALL_PRODUCT_INFO_PAYMENT_WAY_FAULT);
        }
        InstallProductInfo productInfo = installProductInfoSelfMapper.selectByOrgTypeTermPaymentWay( organizationNumber, prodType, term, paymentWay);
        if (productInfo == null) {
            logger.error("查询分期产品参数信息，通过organizationNumber:{},prodType:{},term:{},paymentWay:{}未查到数据",organizationNumber,prodType,term,paymentWay);

            throw new AnyTxnParameterException(AnyTxnParameterRespCodeEnum.D_QUERY_INSTALL_PRODUCT_INFO_BY_SIX_FAULT);
        }
        return BeanMapping.copy(productInfo, InstallProductInfoResDTO.class);
    }

    /**
     * 根据机构号、分期类型查询
     * @param organizationNumber 机构号
     * @param prodType           分期类型
     * @return List<InstallProductInfoResDTO>
     */
    @Override
    public List<InstallProductInfoResDTO> findByOrgNumType(String organizationNumber, String prodType) {
        if (null == organizationNumber || "".equals(organizationNumber)) {

            throw new AnyTxnParameterException(AnyTxnParameterRespCodeEnum.D_INSTALL_ACCOUNTING_TRANS_PARM_ORG_FAULT);
        }
        if (null == prodType || "".equals(prodType))
        {

            throw new AnyTxnParameterException(AnyTxnParameterRespCodeEnum.D_INSTALL_PRODUCT_INFO_PRODUCT_TYPE_FAULT);
        }
        List<InstallProductInfo> installProductInfoList = installProductInfoSelfMapper.selectByOrgAndType(organizationNumber, prodType);
        if (installProductInfoList == null || installProductInfoList.isEmpty()) {
            logger.error("查询分期产品参数信息，通过organizationNumber:{},prodType:{}未查到数据",organizationNumber,prodType);

            throw new AnyTxnParameterException(AnyTxnParameterRespCodeEnum.D_QUERY_INSTALL_PRODUCT_INFO_BY_SEVEN_FAULT);
        }
        return BeanMapping.copyList(installProductInfoList, InstallProductInfoResDTO.class);
    }

    /**
     * 根据机构号、多个分期类型查询
     * @param organizationNumber 机构号
     * @param prodTypes           分期类型
     * @return List<InstallProductInfoResDTO>
     */
    @Override
    public List<InstallProductInfoResDTO> findByOrgAndTypes(String organizationNumber, List<String> prodTypes) {
        if (null == organizationNumber || "".equals(organizationNumber)) {

            throw new AnyTxnParameterException(AnyTxnParameterRespCodeEnum.D_INSTALL_ACCOUNTING_TRANS_PARM_ORG_FAULT);
        }
        if (null == prodTypes || prodTypes.isEmpty())
        {
            throw new AnyTxnParameterException(AnyTxnParameterRespCodeEnum.D_INSTALL_PRODUCT_INFO_PRODUCT_TYPE_FAULT);
        }
        List<InstallProductInfo> installProductInfoList = installProductInfoSelfMapper.selectByOrgAndTypes(organizationNumber, prodTypes);

        if("0000".equals(organizationNumber)){
            installProductInfoList = installProductInfoList.stream().collect(
                    Collectors.collectingAndThen(
                            Collectors.toCollection(() -> new TreeSet<>(Comparator.comparing(InstallProductInfo::getProductCode))), ArrayList::new));
        }
        return BeanMapping.copyList(installProductInfoList, InstallProductInfoResDTO.class);
    }

    //检查必输字段是否为空
    public void checkRequiredInputs(InstallProductInfoReqDTO productInfoReq){
        if (null == productInfoReq.getOrganizationNumber() || "".equals(productInfoReq.getOrganizationNumber()) )
        {

            throw new AnyTxnParameterException(AnyTxnParameterRespCodeEnum.D_INSTALL_ACCOUNTING_TRANS_PARM_ORG_FAULT);
        }
        if (org.apache.commons.lang3.StringUtils.isBlank(productInfoReq.getProductCode()))
        {

            throw new AnyTxnParameterException(AnyTxnParameterRespCodeEnum.D_INSTALL_PRODUCT_INFO_PRODUCT_CODE_FAULT);
        }
        if (null == productInfoReq.getStatus() || "".equals(productInfoReq.getStatus()))
        {

            throw new AnyTxnParameterException(AnyTxnParameterRespCodeEnum.D_INSTALL_ACCOUNTING_TRANS_PARM_STATUS_FAULT);
        }
        if (null == productInfoReq.getProdType() || "".equals(productInfoReq.getProdType()))
        {

            throw new AnyTxnParameterException(AnyTxnParameterRespCodeEnum.D_INSTALL_PRODUCT_INFO_PRODUCT_TYPE_FAULT);
        }
        if (null == productInfoReq.getTerm())
        {

            throw new AnyTxnParameterException(AnyTxnParameterRespCodeEnum.D_INSTALL_PRODUCT_INFO_TERM_FAULT);
        }
        if (null == productInfoReq.getCycle() || "".equals(productInfoReq.getCycle()))
        {

            throw new AnyTxnParameterException(AnyTxnParameterRespCodeEnum.D_INSTALL_PRODUCT_INFO_CYCLE_FAULT);
        }
        if (org.apache.commons.lang3.StringUtils.isBlank(productInfoReq.getPaymentWay())){

            throw new AnyTxnParameterException(AnyTxnParameterRespCodeEnum.D_INSTALL_PRODUCT_INFO_PAYMENT_WAY_FAULT);
        }
        if (null == productInfoReq.getFeeCodeId() || "".equals(productInfoReq.getFeeCodeId()))
        {

            throw new AnyTxnParameterException(AnyTxnParameterRespCodeEnum.D_INSTALL_PRODUCT_INFO_FEE_CODE_FAULT);
        }
        if (null == productInfoReq.getPostingTransactionParmId() || "".equals(productInfoReq.getPostingTransactionParmId()))
        {

            throw new AnyTxnParameterException(AnyTxnParameterRespCodeEnum.D_INSTALL_PRODUCT_INFO_POSTING_TRANSACTION_FAULT);
        }
        if (null == productInfoReq.getAdvancedEndParmId() || "".equals(productInfoReq.getAdvancedEndParmId()))
        {

            throw new AnyTxnParameterException(AnyTxnParameterRespCodeEnum.D_INSTALL_PRODUCT_INFO_ADVANCED_END_FAULT);
        }
        if (null== productInfoReq.getAutoInstallmentFlag() || "".equals(productInfoReq.getAutoInstallmentFlag()))
        {

            throw new AnyTxnParameterException(AnyTxnParameterRespCodeEnum.D_INSTALL_PRODUCT_INFO_AUTO_INSTALLMENT_FAULT);
        }
    }


    /**
     * 获取可分期期数
     * @param orgNum 机构号
     * @param installType 分期类型
     * @return List<Integer> 可分期期数
     */
    @Override
    public List<Integer> getInstallTermByOrgAndType(String orgNum ,String installType){
        List<InstallProductInfo> installProductInfos = installProductInfoSelfMapper.selectByOrgAndInstallType(orgNum, installType);
        if(CollectionUtils.isEmpty(installProductInfos)){
            return Collections.emptyList();
        }
        return installProductInfos.stream()
                .filter(x -> "I".equals(x.getFeeReceiveFlag()))
                .map(InstallProductInfo::getTerm)
                .collect(Collectors.toList());
    }

    @Override
    @Transactional(rollbackFor = Exception.class,propagation = Propagation.REQUIRED)
    //@ShardingSphereTransactionType(TransactionType.XA)
    protected boolean updateDb(ParmModificationRecord parmModificationRecord) {
        InstallProductInfo productInfo = JSON.parseObject(parmModificationRecord.getParmBody(), InstallProductInfo.class);
        productInfo.initUpdateDateTime();
        productInfo.setUpdateBy(parmModificationRecord.getApplicationBy());

        try {
            int i = installProductInfoMapper.updateByPrimaryKeySelective(productInfo);
            return i > 0;
        } catch (Exception exce) {
            logger.error("修改分期产品参数 修改数据库失败",exce);
            throw new AnyTxnParameterException(AnyTxnParameterRespCodeEnum.D_UPDATE_INSTALL_PRODUCT_INFO_FAULT);
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class,propagation = Propagation.REQUIRED)
    //@ShardingSphereTransactionType(TransactionType.XA)
    protected boolean insertDb(ParmModificationRecord parmModificationRecord) {
        InstallProductInfo productInfo = JSON.parseObject(parmModificationRecord.getParmBody(), InstallProductInfo.class);
        productInfo.initCreateDateTime();
        productInfo.initUpdateDateTime();

        try {
            int i = installProductInfoMapper.insertSelective(productInfo);
            return i > 0;
        } catch (Exception exce) {
            logger.warn("新建分期产品参数 插入数据库失败");
            throw new AnyTxnParameterException(AnyTxnParameterRespCodeEnum.D_INSERT_INSTALL_PRODUCT_INFO_FAULT);
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class,propagation = Propagation.REQUIRED)
    //@ShardingSphereTransactionType(TransactionType.XA)
    protected boolean deleteDb(ParmModificationRecord parmModificationRecord) {
        InstallProductInfo installProductInfo = JSON.parseObject(parmModificationRecord.getParmBody(), InstallProductInfo.class);

        logger.warn("删除分期产品参数信息");
        try {
            int deleteRow = installProductInfoMapper.deleteByPrimaryKey(installProductInfo.getId());
            return deleteRow > 0;
        } catch (Exception e) {
            logger.warn("删除分期产品参数 删除数据库失败");
            throw new AnyTxnParameterException(AnyTxnParameterRespCodeEnum.D_DELETE_INSTALL_PRODUCT_INFO_FAULT);
        }
    }

    @Override
    public PageResultDTO<ParamExportPageDTO> exPortPages(ParamExportDTO paramExportDTO) {
        InstallProductInfoSearchDTO reqDTO = new InstallProductInfoSearchDTO();
        reqDTO.setOrganizationNumber(paramExportDTO.getOrganizationNumber());

        Page<InstallProductInfo> page = PageHelper.startPage(paramExportDTO.getPageNum(), paramExportDTO.getPageSize());
        List<InstallProductInfo> data = installProductInfoSelfMapper.selectByCondition(reqDTO);

        List<ParamExportPageDTO> pageDTOList = data.stream().map(t -> {
            ParamExportPageDTO paramExportPageDTO = new ParamExportPageDTO();
            paramExportPageDTO.setDesc(t.getProductDesc());
            paramExportPageDTO.setId(t.getId());
            paramExportPageDTO.setTableId(t.getProductCode());
            paramExportPageDTO.setOrganizationNumber(t.getOrganizationNumber());
            paramExportPageDTO.setStatus(t.getStatus());
            return paramExportPageDTO;
        }).collect(Collectors.toList());

        return new PageResultDTO<>(paramExportDTO.getPageNum(), paramExportDTO.getPageSize(),
                page.getTotal(), page.getPages(), pageDTOList);
    }



    @Override
    public ParamImportResultDTO importData(ParamImportResultDTO paramImportResultDTO) throws IOException {
        if (Objects.equals(paramImportResultDTO.getFileType(), ParamFileTypeEnum.TXT.getType())){
            return parseTxtData(paramImportResultDTO);
        }


        paramImportResultDTO.buildErrorInfo(AnyTxnCommonRespCodeEnum.RESPONSE_UNKNOWN,"Unsupported file type");
        return paramImportResultDTO;
    }

    private ParamImportResultDTO parseTxtData(ParamImportResultDTO paramImportResultDTO) throws IOException {

        ByteArrayInputStream byteArrayInputStream = new ByteArrayInputStream(paramImportResultDTO.getBytes());

        List<String> readLines = IOUtils.readLines(byteArrayInputStream, "utf-8");

        int successNum = 0;
        int lineNum = 0;
        for (String readLine : readLines) {
            try {
                ++lineNum;
                if (org.apache.commons.lang3.StringUtils.isBlank(readLine)){continue;}

                InstallProductInfoReqDTO installProductInfoReqDTO = JSON.parseObject(readLine, InstallProductInfoReqDTO.class);
                ((InstallProductInfoServiceImpl)(AopContext.currentProxy())).add(installProductInfoReqDTO);
                paramImportResultDTO.addResult(AnyTxnHttpResponse.success());
                successNum ++;
            }catch (Exception e){
                paramImportResultDTO.addResult(buildExceptionInfo(e, lineNum));
            }
        }
        paramImportResultDTO.setTotalNum(String.valueOf(readLines.size()));
        paramImportResultDTO.setSuccessNum(String.valueOf(successNum));
        return paramImportResultDTO.buildSuccessInfo();

    }


    @Override
    public ParamImportDTO exportData(ParamExportDTO paramPoiExportDTO) {

        if (Objects.equals(paramPoiExportDTO.getFileType(), ParamFileTypeEnum.TXT.getType())){
            return buildTxtData(paramPoiExportDTO);
        }
        return buildExcelData(paramPoiExportDTO);
    }

    private ParamImportDTO buildExcelData(ParamExportDTO paramPoiExportDTO) {
        return null;
    }


    private ParamImportDTO buildTxtData(ParamExportDTO paramPoiExportDTO) {
        List<String> strings = paramPoiExportDTO.getParamCodeIds().get(paramPoiExportDTO.getParamCodes());
        StringBuilder stringBuilder = new StringBuilder();
        String size = String.valueOf(strings.size());


        if (org.apache.commons.collections4.CollectionUtils.isEmpty(strings)){
            InstallProductInfoSearchDTO reqDTO = new InstallProductInfoSearchDTO();
            reqDTO.setOrganizationNumber(paramPoiExportDTO.getOrganizationNumber());

            List<InstallProductInfo> data = installProductInfoSelfMapper.selectByCondition(reqDTO);
            size = String.valueOf(org.apache.commons.collections4.CollectionUtils.size(data));
            data.forEach(t -> stringBuilder.append(JacksonUtils.toJsonStr(t)).append("\r\n"));

        }else {
            strings.forEach(t -> {
                InstallProductInfoResDTO productInfoResDTO = getById(t);
                stringBuilder.append(JacksonUtils.toJsonStr(productInfoResDTO)).append("\r\n");
            });
        }


        return ParamImportDTO.ParamImportDTOBuilder
                .aParamImportDTO()
                .withBytes(stringBuilder.toString().getBytes())
                .withTotalNum(size)
                .build();
    }
}

