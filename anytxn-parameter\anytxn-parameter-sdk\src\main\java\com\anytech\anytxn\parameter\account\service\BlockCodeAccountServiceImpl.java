package com.anytech.anytxn.parameter.account.service;

import com.github.pagehelper.Page;
import com.github.pagehelper.PageHelper;
import com.anytech.anytxn.common.core.utils.OrgNumberUtils;
import com.anytech.anytxn.parameter.base.common.constants.Constants;
import com.anytech.anytxn.common.core.base.PageResultDTO;
import com.anytech.anytxn.common.core.utils.BeanMapping;
import com.anytech.anytxn.common.sequence.utils.Number16IdGen;
import com.anytech.anytxn.common.core.utils.TenantUtils;
import com.anytech.anytxn.parameter.base.account.domain.dto.BlockCodeAccountReqDTO;
import com.anytech.anytxn.parameter.base.account.domain.dto.BlockCodeAccountResDTO;
import com.anytech.anytxn.parameter.base.account.service.IBlockCodeAccountService;
import com.anytech.anytxn.parameter.base.common.constants.ParamConstant;
import com.anytech.anytxn.parameter.base.common.enums.AnyTxnParameterRespCodeEnum;
import com.anytech.anytxn.parameter.base.common.exception.AnyTxnParameterException;
import com.anytech.anytxn.parameter.account.mapper.ParmBlockCodeAccountMapper;
import com.anytech.anytxn.parameter.account.mapper.ParmBlockCodeAccountSelfMapper;
import com.anytech.anytxn.parameter.base.account.domain.model.ParmBlockCodeAccount;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.util.List;

/**
 * 账户层封锁码 业务接口实现
 * <AUTHOR>
 * @date 2018-08-16
 **/
@Service
public class BlockCodeAccountServiceImpl implements IBlockCodeAccountService {
    private Logger logger = LoggerFactory.getLogger(BlockCodeAccountServiceImpl.class);

    @Autowired
    private ParmBlockCodeAccountMapper parmBlockCodeAccountMapper;
    @Autowired
    private ParmBlockCodeAccountSelfMapper parmBlockCodeAccountSelfMapper;

    @Autowired
    private Number16IdGen numberIdGenerator;


    /**
     * 添加账户层封锁码
     *
     * @param req 账户层封锁码入参对象
     * @return 账户层封锁码详情
     * @throws AnyTxnParameterException
     */
    @Override
    public BlockCodeAccountResDTO add(BlockCodeAccountReqDTO req)  {
        boolean isExists = parmBlockCodeAccountSelfMapper.isExists(req.getOrganizationNumber(), req.getTableId(), req.getBlockCode())>0;
        if(isExists) {
            logger.warn("参数表ID和封锁码已存在, orgNumber={}, tableId={}, blockCode={}",
                    OrgNumberUtils.getOrg(), req.getTableId(), req.getBlockCode());
            throw new AnyTxnParameterException(AnyTxnParameterRespCodeEnum.P_EXIST_BLOCK_CODE_AND_ID_FAULT);
        }

        // 构建交易码
        ParmBlockCodeAccount blockCodePlastic = BeanMapping.copy(req, ParmBlockCodeAccount.class);
        blockCodePlastic.setCreateTime(LocalDateTime.now());
        blockCodePlastic.setUpdateTime(LocalDateTime.now());
        blockCodePlastic.setUpdateBy(Constants.DEFAULT_USER);
        blockCodePlastic.setVersionNumber(1L);
        blockCodePlastic.setId(numberIdGenerator.generateId(TenantUtils.getTenantId()));
        parmBlockCodeAccountMapper.insertSelective(blockCodePlastic);

        return BeanMapping.copy(blockCodePlastic, BlockCodeAccountResDTO.class);
    }

    /**
     * 修改账户层封锁码
     *
     * @param req 账户层封锁码入参对象
     * @return 账户层封锁码详情
     * @throws AnyTxnParameterException
     */
    @Override
    public BlockCodeAccountResDTO modify(BlockCodeAccountReqDTO req)  {
        if (req.getId() == null) {
            throw new AnyTxnParameterException(AnyTxnParameterRespCodeEnum.P_BLOCK_CODE_ACCOUNT_REQ_ID_FAULT);
        }

        ParmBlockCodeAccount blockCodePlastic = parmBlockCodeAccountMapper.selectByPrimaryKey(req.getId());
        if (blockCodePlastic == null) {
            logger.error("修改账户层封锁码, 通过主键id({})未找到数据", req.getId());
            throw new AnyTxnParameterException(AnyTxnParameterRespCodeEnum.D_QUERY_PARM_BLOCK_CODE_ACCOUNT_FAULT);
        }

        // 如果交易码编码被修改，则验证新的交易码编码是否存在
        String oldTableId = blockCodePlastic.getTableId();
        String newTableId = req.getTableId();
        if(!oldTableId.equals(newTableId)) {
            boolean isExists = false;
            isExists = parmBlockCodeAccountSelfMapper.isExists(blockCodePlastic.getOrganizationNumber(),
                    req.getTableId(), req.getBlockCode())>0;
            if(isExists) {
                logger.warn("参数表ID和封锁码已存在, orgNumber={}, tableId={}, blockCode={}",
                        blockCodePlastic.getOrganizationNumber(), req.getTableId(), req.getBlockCode());
                throw new AnyTxnParameterException(AnyTxnParameterRespCodeEnum.P_EXIST_BLOCK_CODE_AND_ID_FAULT);
            }
        }

        // 拷贝修改的数据并更新
        BeanMapping.copy(req, blockCodePlastic);
        blockCodePlastic.setUpdateTime(LocalDateTime.now());
        blockCodePlastic.setUpdateBy(Constants.DEFAULT_USER);
        parmBlockCodeAccountMapper.updateByPrimaryKeySelective(blockCodePlastic);

        //历史表中添加记录

        return BeanMapping.copy(blockCodePlastic, BlockCodeAccountResDTO.class);
    }

    /**
     * 删除账户层封锁码，通过id
     *
     * @param id 账户层封锁码ID
     * @return true:成功|false:失败
     * @throws AnyTxnParameterException
     */
    @Override
    public Boolean remove(Long id)  {
        if (id == null) {
            throw new AnyTxnParameterException(AnyTxnParameterRespCodeEnum.P_PARAMETER_EMPTY_FAULT);
        }

        ParmBlockCodeAccount blockCodePlastic = parmBlockCodeAccountMapper.selectByPrimaryKey(id);
        if (blockCodePlastic == null) {
            logger.error("删除账户层封锁码, 通过主键id({})未找到数据", id);
            throw new AnyTxnParameterException(AnyTxnParameterRespCodeEnum.P_EXIST_BLOCK_CODE_BY_ID_FAULT);
        }

        logger.warn("通过id删除账户层封锁码, {}", blockCodePlastic);
        int deleteRow = parmBlockCodeAccountMapper.deleteByPrimaryKey(id);

        //历史表中添加记录

        return deleteRow > 0;
    }

    /**
     * 获取账户层封锁码详情，通过id
     *
     * @param id 账户层封锁码ID
     * @return 账户层封锁码详情
     * @throws AnyTxnParameterException
     */
    @Override
    public BlockCodeAccountResDTO find(Long id)  {
        if (id == null) {
            throw new AnyTxnParameterException(AnyTxnParameterRespCodeEnum.P_EXIST_BLOCK_CODE_BY_ID_FAULT);
        }

        ParmBlockCodeAccount blockCodePlastic = parmBlockCodeAccountMapper.selectByPrimaryKey(id);
        if (blockCodePlastic == null) {
            logger.error("获取账户层封锁码详情, 通过主键id({})未找到数据", id);
            throw new AnyTxnParameterException(AnyTxnParameterRespCodeEnum.D_GET_PARM_BLOCK_CODE_ACCOUNT_FAULT);
        }

        return BeanMapping.copy(blockCodePlastic, BlockCodeAccountResDTO.class);
    }

    /**
     * 查询账户层封锁码集合，通过机构编号及启用状态
     *
     * @param orgNumber 机构编号
     * @param status    账户层封锁码启用状态，不传查询所有状态
     * @return 分页的账户层封锁码
     * @throws AnyTxnParameterException
     */
    @Override
    public List<BlockCodeAccountResDTO> findListByOrgNumber(String orgNumber, String status)  {
        List<ParmBlockCodeAccount> blockCodeList = parmBlockCodeAccountSelfMapper.selectListByOrgNumber(orgNumber, status, true);
        return BeanMapping.copyList(blockCodeList, BlockCodeAccountResDTO.class);
    }

    /**
     * 分页查询账户层封锁码，通过机构编号及启用状态
     *
     * @param pageNum   页号
     * @param pageSize  每页大小
     * @param orgNumber 机构编号
     * @param status    账户层封锁码启用状态，不传查询所有状态
     * @return 分页的账户层封锁码
     * @throws AnyTxnParameterException
     */
    @Override
    public PageResultDTO<BlockCodeAccountResDTO> findPageByOrgNumber(Integer pageNum, Integer pageSize,
                                                                     String orgNumber, String status)  {
        logger.info("分页查询账户层封锁码, pageNum={}, pageSize={}, orgNumber={}, status={}",
                pageNum, pageSize, orgNumber, status);
        Page<ParmBlockCodeAccount> page = PageHelper.startPage(pageNum, pageSize);
        List<ParmBlockCodeAccount> dataList = parmBlockCodeAccountSelfMapper.selectListByOrgNumber(orgNumber, status, false);
        List<BlockCodeAccountResDTO> listData = BeanMapping.copyList(dataList, BlockCodeAccountResDTO.class);

        return new PageResultDTO<>(pageNum, pageSize, page.getTotal(),page.getPages(), listData);
    }

    /**
     * 获取账户层封锁码，通过机构编号和参数表ID和封锁码
     * @param organizationNumber 机构号
     * @param tableId            参数表ID
     * @param blockCode          封锁码
     * @return
     */
    @Override
    public BlockCodeAccountResDTO findBlockCodeAccount(String organizationNumber, String tableId, String blockCode)  {
        ParmBlockCodeAccount blockCodeAccount = parmBlockCodeAccountSelfMapper.selectParmBlockCodeAccount(organizationNumber, tableId, blockCode);
        if (blockCodeAccount == null) {
            logger.error("获取账户层封锁码参数, 通过organizationNumber={},tableId={},blockCode={}未找到数据", organizationNumber,
                    tableId, blockCode);
            throw new AnyTxnParameterException(AnyTxnParameterRespCodeEnum.D_GET_IS_NULL_FAULT);
        }
        return BeanMapping.copy(blockCodeAccount, BlockCodeAccountResDTO.class);
    }

    /**
     * 账户层封锁码获取
     *
     * @return
     * @throws AnyTxnParameterException
     */
    @Override
    public List<BlockCodeAccountResDTO> findBlockCode(String organizationNumber)  {
        List<ParmBlockCodeAccount> blockCodeList = parmBlockCodeAccountSelfMapper.selectListByOrgNumberAndType(organizationNumber, Constants.ENABLED, ParamConstant.TYPE_ZERO);
        return BeanMapping.copyList(blockCodeList, BlockCodeAccountResDTO.class);
    }
}
