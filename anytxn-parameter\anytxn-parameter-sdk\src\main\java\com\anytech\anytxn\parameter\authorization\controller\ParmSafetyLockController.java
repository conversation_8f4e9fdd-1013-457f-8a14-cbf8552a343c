package com.anytech.anytxn.parameter.authorization.controller;

import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import io.swagger.v3.oas.annotations.Operation;
import com.anytech.anytxn.common.core.base.PageResultDTO;
import com.anytech.anytxn.common.core.base.AnyTxnHttpResponse;
import com.anytech.anytxn.common.core.base.BizBaseController;
import com.anytech.anytxn.parameter.base.authorization.domain.dto.SafeLockDTO;
import com.anytech.anytxn.parameter.base.authorization.service.ISafeLockService;
import com.anytech.anytxn.parameter.base.common.enums.ParameterRepDetailEnum;
import com.anytech.anytxn.parameter.base.common.utils.AnyTxnHttpResponseHelper;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import jakarta.validation.Valid;

/**
 * @description 安全开关规则
 * <AUTHOR>
 * @date 2020-07-09
 */
@RestController
@Tag(name = "安全开关规则服务")
public class ParmSafetyLockController extends BizBaseController {

   @Autowired
   private ISafeLockService safeLockService;

    /**
     * @description 分页查询安全开关规则列表
     * <AUTHOR>
     * @date 2020/07/09
     * @param page, rows
     * @return jrx.anytxn.biz.common.web.AnyTxnHttpResponse<jrx.anytxn.biz.common.dto.PageResultDTO<jrx.anytxn.parameter.authorization.dto.SafeLockDTO>>
     */
    @Operation(summary = "分页查询安全开关规则列表")
    @GetMapping("/param/safelocks/{page}/{rows}")
    public AnyTxnHttpResponse<PageResultDTO<SafeLockDTO>> getVelocitySetDefinitionList(@Parameter(name = "page", description = "当前页", example = "1")@PathVariable("page") int page,
                                                                                       @Parameter(name = "rows", description = "每页大小", example = "8")@PathVariable("rows") int rows,
                                                                                       @RequestParam(value = "cardProductCode",required = false) String cardProductCode,
                                                                                       @RequestParam(value = "organizationNumber",required = false) String organizationNumber) {
        PageResultDTO<SafeLockDTO> result = safeLockService.findListSafeLock(page, rows, cardProductCode,organizationNumber);
        return AnyTxnHttpResponse.success(result);
    }

    /**
     * @description 新增安全开关规则信息
     * <AUTHOR>
     * @date 2020/07/09
     * @param safeLockDTO
     * @return jrx.anytxn.biz.common.web.AnyTxnHttpResponse
     */

    @Operation(summary = "新增安全开关规则信息")
    @PostMapping("/param/safelock/create")
    public AnyTxnHttpResponse create(@Valid @RequestBody SafeLockDTO safeLockDTO) {
        safeLockService.addSafeLock(safeLockDTO);
        return AnyTxnHttpResponseHelper.succ(ParameterRepDetailEnum.CREATE.message());
    }

    /**
     * @description 安全开关规则信息修改
     * <AUTHOR>
     * @date 2020/07/09
     * @param safeLockDTO
     * @return jrx.anytxn.biz.common.web.AnyTxnHttpResponse
     */

    @Operation(summary = "安全开关规则信息修改")
    @PutMapping("/param/safelock")
    public AnyTxnHttpResponse modify(@Valid @RequestBody SafeLockDTO safeLockDTO) {
        safeLockService.modifySafeLock(safeLockDTO);
        return AnyTxnHttpResponseHelper.succ(ParameterRepDetailEnum.UPDATE.message());
    }

    /**
     * @description 删除安全开关规则信息
     * <AUTHOR>
     * @date 2020/07/09
     * @param cardProductCode
     * @return jrx.anytxn.biz.common.web.AnyTxnHttpResponse
     */

    @Operation(summary = "删除安全开关规则信息")
    @DeleteMapping("/param/safelock/{cardProductCode}")
    public AnyTxnHttpResponse cancelVelocitySetDefinition(@PathVariable String cardProductCode, @RequestParam String organizationNumber) {
        safeLockService.removeSafeLock(cardProductCode, organizationNumber);
        return AnyTxnHttpResponseHelper.succ(ParameterRepDetailEnum.DEL.message());
    }

    /**
     * @description 根据主键ID删除安全开关规则信息
     * <AUTHOR>
     * @date 2020/07/24
     * @param id
     * @return jrx.anytxn.biz.common.web.AnyTxnHttpResponse
     */

    @Operation(summary = "根据主键ID删除安全开关规则信息")
    @DeleteMapping("/param/safeLockById/{id}")
    public AnyTxnHttpResponse cancelVelocitySetDefinitionById(@PathVariable String id) {
        safeLockService.removeSafeLockById(id);
        return AnyTxnHttpResponseHelper.succ(ParameterRepDetailEnum.DEL.message());
    }

    /**
     * @description 根据cardProductCode查询安全开关规则信息
     * @param cardProductCode 卡片编号
     * <AUTHOR>
     * @date 2020/07/10
     * @return HttpApiResponse<VelocityControlDTO>
     */
    @Operation(summary="根据id查询安全开关规则信息")
    @GetMapping(value = "/param/safelock/cardProductCode/{cardProductCode}")
    public AnyTxnHttpResponse<SafeLockDTO> getSafeLockByCardProductCode(@PathVariable String cardProductCode, @RequestParam String organizationNumber){
        SafeLockDTO safeLockDTO = safeLockService.findSafeLockByCardProNum(cardProductCode, organizationNumber);
        return AnyTxnHttpResponse.success(safeLockDTO);
    }

}
