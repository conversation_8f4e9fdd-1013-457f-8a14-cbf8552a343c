package com.anytech.anytxn.parameter.common.service.product;


import com.anytech.anytxn.parameter.base.card.domain.dto.ParmManageFeeTableDTO;
import com.anytech.anytxn.parameter.base.card.domain.model.ParmCardFeeGroupInfo;
import com.anytech.anytxn.parameter.base.common.enums.AnyTxnParameterRespCodeEnum;
import com.anytech.anytxn.parameter.base.common.enums.ParameterRepDetailEnum;
import com.anytech.anytxn.parameter.base.common.exception.AnyTxnParameterException;
import com.anytech.anytxn.parameter.card.mapper.broadcast.*;
import com.anytech.anytxn.parameter.base.common.domain.model.system.ParmManageFeeTable;
import com.anytech.anytxn.parameter.base.common.service.IParmManageFeeService;
import com.github.pagehelper.Page;
import com.github.pagehelper.PageHelper;
import com.anytech.anytxn.common.core.base.PageResultDTO;
import com.anytech.anytxn.common.core.utils.BeanMapping;
import com.anytech.anytxn.common.core.utils.TenantUtils;
import com.anytech.anytxn.common.sequence.utils.Number16IdGen;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.util.List;
import java.util.Objects;

/**
 * 卡管理费用配置信息
 *
 * <AUTHOR>
 * @date 2018-12-04
 **/
@Service("parmManageFeeServiceImpl")
public class ParmManageFeeServiceImpl implements IParmManageFeeService {

    private final Logger logger = LoggerFactory.getLogger(ParmManageFeeServiceImpl.class);

    @Autowired
    private ParmManageFeeTableSelfMapper parmManageFeeTableSelfMapper;

    @Autowired
    private Number16IdGen numberIdGenerator;

    @Override
    public ParmManageFeeTableDTO create(ParmManageFeeTableDTO request) {
        // 根据机构号和table_id查询该记录是否已经存在
        ParmManageFeeTable condition = new ParmManageFeeTable();
        condition.setTableId(request.getTableId());
        condition.setTxnCode(request.getTxnCode());
        condition.setTenantId(request.getTenantId());
        condition.setStatus("1");
        ParmManageFeeTable parmManageFeeTable = parmManageFeeTableSelfMapper.selectOneByCondition(condition);
        if (parmManageFeeTable != null) {
            logger.error("卡片管理费用参数数据不存在");
            throw new AnyTxnParameterException(AnyTxnParameterRespCodeEnum.P_PARAMETER_DATA_EXIST, ParameterRepDetailEnum.CARD_MANAGE_FEE_EXIST);
        }
        request.setTableId(String.valueOf(numberIdGenerator.generateId(TenantUtils.getTenantId())));
        if (parmManageFeeTableSelfMapper.insert(BeanMapping.copy(request, ParmManageFeeTable.class)) > 0) {
            return request;
        }
        return null;

    }

    @Override
    public boolean delete(String id) {
        ParmManageFeeTable parmManageFeeTable = parmManageFeeTableSelfMapper.selectByPrimaryKey(id);
        if (parmManageFeeTable == null) {
            logger.error("卡片管理费用参数数据不存在");
            throw new AnyTxnParameterException(AnyTxnParameterRespCodeEnum.P_PARAMETER_DATA_EXIST, ParameterRepDetailEnum.CARD_MANAGE_FEE_NOT_EXIST);
        }
        parmManageFeeTable.setStatus("0");
        return parmManageFeeTableSelfMapper.updateByPrimaryKeySelective(parmManageFeeTable) > 0;
    }

    @Override
    public ParmManageFeeTableDTO modify(ParmManageFeeTableDTO request) {
        ParmManageFeeTable parmManageFeeTable = parmManageFeeTableSelfMapper.selectByPrimaryKey(request.getId());
        if (parmManageFeeTable == null) {
            logger.error("卡片管理费用参数数据不存在");
            throw new AnyTxnParameterException(AnyTxnParameterRespCodeEnum.P_PARAMETER_DATA_EXIST, ParameterRepDetailEnum.CARD_MANAGE_FEE_NOT_EXIST);
        }
        if (parmManageFeeTableSelfMapper.updateByPrimaryKeySelective(BeanMapping.copy(request, ParmManageFeeTable.class))>0) {
            return request;
        }
        return null;
    }

    @Override
    public ParmManageFeeTableDTO getByTableId(String tableId) {
        // 根据机构号和table_id查询该记录是否已经存在
        ParmManageFeeTable condition = new ParmManageFeeTable();
        condition.setTableId(tableId);
        condition.setStatus("1");
        ParmManageFeeTable parmManageFeeTable = parmManageFeeTableSelfMapper.selectOneByCondition(condition);
        if (parmManageFeeTable == null) {
            logger.error("卡片管理费用参数数据不存在");
            throw new AnyTxnParameterException(AnyTxnParameterRespCodeEnum.P_PARAMETER_DATA_EXIST, ParameterRepDetailEnum.CARD_MANAGE_FEE_EXIST);
        }
        return BeanMapping.copy(parmManageFeeTable, ParmManageFeeTableDTO.class);
    }

    @Override
    public PageResultDTO<ParmManageFeeTableDTO> selectByPage(ParmManageFeeTableDTO request, Integer pageNum, Integer pageSize) {
        ParmManageFeeTable condition = BeanMapping.copy(request, ParmManageFeeTable.class);
        Page<ParmCardFeeGroupInfo> page = PageHelper.startPage(pageNum, pageSize);

        List<ParmManageFeeTable> parmManageFeeTables = parmManageFeeTableSelfMapper.selectListByCondition(condition);
        if (!CollectionUtils.isEmpty(parmManageFeeTables)) {
            logger.error("卡片管理费用参数数据不存在");
            List<ParmManageFeeTableDTO> res = BeanMapping.copyList(parmManageFeeTables, ParmManageFeeTableDTO.class);
//            log.error("卡片费用组参数数据不存在");
//            throw new AnyTxnParameterException(AnyTxnParameterRespCodeEnum.D_GET_IS_NULL_FAULT,"卡片费用组参数数据不存在");
            return new PageResultDTO<>(pageNum, pageSize, page.getTotal(), page.getPages(), res);
        }
        return null;
    }

    @Override
    public ParmManageFeeTableDTO selectOneByCondition(ParmManageFeeTableDTO request) {
        ParmManageFeeTable condition = BeanMapping.copy(request, ParmManageFeeTable.class);

        ParmManageFeeTable parmManageFeeTable = parmManageFeeTableSelfMapper.selectOneByCondition(condition);
        if (Objects.isNull(parmManageFeeTable)) {
            logger.error("卡片管理费用参数数据不存在");
            return null;
        }
        return BeanMapping.copy(parmManageFeeTable, ParmManageFeeTableDTO.class);
    }

}
