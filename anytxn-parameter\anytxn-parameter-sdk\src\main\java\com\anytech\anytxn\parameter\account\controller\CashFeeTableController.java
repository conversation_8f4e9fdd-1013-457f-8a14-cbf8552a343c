package com.anytech.anytxn.parameter.account.controller;

import io.swagger.v3.oas.annotations.tags.Tag;
import io.swagger.v3.oas.annotations.Operation;
import com.anytech.anytxn.common.core.base.ParameterCompare;
import com.anytech.anytxn.common.core.base.PageResultDTO;
import com.anytech.anytxn.common.core.base.AnyTxnHttpResponse;
import com.anytech.anytxn.common.core.base.BizBaseController;
import com.anytech.anytxn.parameter.base.account.domain.dto.CashFeeTableReqDTO;
import com.anytech.anytxn.parameter.base.account.domain.dto.CashFeeTableResDTO;
import com.anytech.anytxn.parameter.base.account.domain.dto.CashFeeTableSearchDTO;
import com.anytech.anytxn.parameter.base.account.service.ICashFeeTableService;
import com.anytech.anytxn.parameter.base.common.enums.ParameterRepDetailEnum;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

/**
 * 取现手续field参数参数
 * <AUTHOR>
 * @date 2020/3/26
 */
@Tag(name = "取现手续费参数")
@RestController
public class CashFeeTableController extends BizBaseController {

    @Autowired
    private ICashFeeTableService cashFeeTableService;

    /**
     * 新增取现手续费参数
     * @param feeTableReq
     * @return FeeTableRes
     *
     */
    @Operation(summary = "新增取现手续费参数",description = "新增取现手续费参数")
    @PostMapping(value = "/param/cashFee")
    public AnyTxnHttpResponse<Object> create(@RequestBody CashFeeTableReqDTO feeTableReq) {
        ParameterCompare res = cashFeeTableService.addParmFee(feeTableReq);
        return AnyTxnHttpResponse.success(res, ParameterRepDetailEnum.CREATE.message());
    }

    /**
     * 查询取现手续费参数信息
     * @return FeeTableRes
     *
     */
    @Operation(summary = "取现手续费参数分页查询",description = "分页查询")
    @GetMapping(value = "/param/cashFee/pageNum/{pageNum}/pageSize/{pageSize}")
    public AnyTxnHttpResponse<PageResultDTO<CashFeeTableResDTO>> getAllPage(@PathVariable(value = "pageNum") Integer pageNum,
                                                                     @PathVariable(value = "pageSize") Integer pageSize,
                                                                            CashFeeTableSearchDTO cashFeeTableSearchDTO) {
        PageResultDTO<CashFeeTableResDTO> cashFeePage = cashFeeTableService.findAll(pageNum,pageSize,cashFeeTableSearchDTO);
        return AnyTxnHttpResponse.success(cashFeePage);

    }


    /**
     * 修改取现手续费参数信息
     * @param feeTableReq
     * @return FeeTableRes
     *
     */
    @Operation(summary = "取现手续费参数信息修改", description = "只更新需要更新的,其他不变")
    @PutMapping(value = "/param/cashFee")
    public AnyTxnHttpResponse<Object> modify(@RequestBody CashFeeTableReqDTO feeTableReq) {
        ParameterCompare res = cashFeeTableService.modifyParmFee(feeTableReq);
        return AnyTxnHttpResponse.success(res,ParameterRepDetailEnum.UPDATE.message());
    }

    /**
     * 通过id删除取现手续费参数信息
     * @param id
     * @return Boolean
     *
     */
    @Operation(summary = "删除取现手续费参数信息", description = "通过id删除取现手续费参数信息")
    @DeleteMapping(value = "/param/cashFee/id/{id}")
    public AnyTxnHttpResponse<Object> remove(@PathVariable String id) {
        ParameterCompare flag = cashFeeTableService.removeParmFee(id);
            return AnyTxnHttpResponse.success(flag,ParameterRepDetailEnum.DEL.message());
    }

    /**
     * 通过id查询取现手续费参数信息
     * @param id
     * @return HttpApiResponse<FeeTableRes>
     *
     */
    @Operation(summary = "通过id查询取现手续费参数信息", description = "通过id查询取现手续费参数信息")
    @GetMapping(value = "/param/cashFee/id/{id}")
    public AnyTxnHttpResponse<CashFeeTableResDTO> getById(@PathVariable String id) {
        CashFeeTableResDTO res = cashFeeTableService.findById(id);
        return AnyTxnHttpResponse.success(res);

    }

    /**
     * 通过机构号,tableId查询取现手续费参数信息
     * @param organizationNumber 机构号
     * @param tableId 表Id
     * @return
     *
     */
    @Operation(summary = "通过机构号,tableId查询取现手续费参数信息", description = "通过tableId查询取现手续费参数信息")
    @GetMapping(value = "/param/cashFee/organizationNumber/{organizationNumber}/tableId/{tableId}")
    public AnyTxnHttpResponse<CashFeeTableResDTO> getFeeTable(@PathVariable String organizationNumber, @PathVariable String tableId) {
        CashFeeTableResDTO res = cashFeeTableService.findByOrgAndTableId(organizationNumber,tableId);
        return AnyTxnHttpResponse.success(res);

    }

}
