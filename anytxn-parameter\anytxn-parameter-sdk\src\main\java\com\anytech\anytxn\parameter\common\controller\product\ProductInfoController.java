package com.anytech.anytxn.parameter.common.controller.product;

import io.swagger.v3.oas.annotations.tags.Tag;
import io.swagger.v3.oas.annotations.Operation;
import com.anytech.anytxn.common.core.base.PageResultDTO;
import com.anytech.anytxn.common.core.base.AnyTxnHttpResponse;
import com.anytech.anytxn.common.core.base.BizBaseController;
import com.anytech.anytxn.parameter.base.common.enums.ParameterRepDetailEnum;
import com.anytech.anytxn.parameter.base.common.domain.dto.product.ProductInfoReqDTO;
import com.anytech.anytxn.parameter.base.common.domain.dto.product.ProductInfoResDTO;
import com.anytech.anytxn.parameter.base.common.service.product.IProductInfoService;
import com.anytech.anytxn.parameter.base.common.utils.AnyTxnHttpResponseHelper;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import jakarta.validation.Valid;
import java.util.ArrayList;
import java.util.List;


/**
 * 产品参数
 *
 * <AUTHOR>
 * @date 2018-08-15 9:49
 **/
@RestController
@Tag(name = "产品信息")
public class ProductInfoController extends BizBaseController {

    @Autowired
    private IProductInfoService productInfoService;

    /**
     * business需要的接口
     * 获得产品参数表
     * @param organizationNumber 机构号
     * @param productNumber 产品代码
     * @param currency 币种
     * @return AnyTxnHttpResponse<ProductInfoRes>
     */
    @GetMapping(value = "/param/productInfo/organizationNumber/{organizationNumber}/prodNumber/{prodNumber}/currency/{currency}")
    @Operation(summary = "查询产品信息根据机构号,产品代码,币种")
    public AnyTxnHttpResponse<ProductInfoReqDTO> getProductInfo(@PathVariable(value = "organizationNumber") String organizationNumber,
                                                             @PathVariable(value = "prodNumber") String productNumber,
                                                             @PathVariable(value = "currency") String currency) {
        ArrayList<ProductInfoReqDTO> productInfoRes = (ArrayList)productInfoService.findProductInfo(organizationNumber,productNumber,currency);
        return AnyTxnHttpResponse.success(productInfoRes.get(0));

    }

    /**
     * business需要的接口
     * 根据机构号和产品代码查询产品参数表
     * @param organizationNumber 机构号
     * @param productNumber 产品代码
     * @return AnyTxnHttpResponse<ArrayList<ProductInfoRes>>
     */
    @GetMapping(value = "/param/productInfo/organizationNumber/{organizationNumber}/prodNumber/{prodNumber}")
    @Operation(summary = "查询产品信息根据机构号,产品代码")
    public AnyTxnHttpResponse<ArrayList<ProductInfoReqDTO>> getByOrgAndProductNum(@PathVariable(value = "organizationNumber") String organizationNumber,
                                                                               @PathVariable(value = "prodNumber") String productNumber) {
        ArrayList<ProductInfoReqDTO> productInfoRes = (ArrayList) productInfoService.findProductInfo(organizationNumber,productNumber,null);
        return AnyTxnHttpResponse.success(productInfoRes);

    }


    /**
     * 创建产品信息条目
     * @param productInfoReq 产品信息请求数据
     * @return AnyTxnHttpResponse<ProductInfoRes>
     */
    @PostMapping("/param/productInfo")
    @Operation(summary = "产品信息")
    public AnyTxnHttpResponse create(@Valid @RequestBody ProductInfoReqDTO productInfoReq) {
        productInfoService.add(productInfoReq);
        return AnyTxnHttpResponseHelper.succ(ParameterRepDetailEnum.CREATE.message());

    }

    /**
     * 删除产品信息条目，通过id
     * @param id 技术id
     * @return AnyTxnHttpResponse<Boolean>
     */
    @DeleteMapping(value = "/param/productInfo/id/{id}")
    @Operation(summary="根据id删除", description = "需传入id")
    public AnyTxnHttpResponse remove(@PathVariable String id) {
        productInfoService.remove(id);
        return AnyTxnHttpResponseHelper.succ(ParameterRepDetailEnum.DEL.message());

    }

    /**
     * 更新产品信息
     * @param productInfoReq 产品信息请求数据
     * @return AnyTxnHttpResponse<ProductInfoRes>
     */
    @PutMapping(value = "/param/productInfo")
    @Operation(summary="根据id更新信息", description = "")
    public AnyTxnHttpResponse modify(@Valid @RequestBody ProductInfoReqDTO productInfoReq) {
        productInfoService.modify(productInfoReq);
        return AnyTxnHttpResponseHelper.succ(ParameterRepDetailEnum.UPDATE.message());

    }

    /**
     * 根据根据id获取详情，进行编辑
     * @param id 产品信息id
     * @return AnyTxnHttpResponse<ProductInfoRes>
     */
    @Operation(summary="获取产品详情，通过id", description = "")
    @GetMapping(value = "/param/productInfo/id/{id}")
    public AnyTxnHttpResponse<ProductInfoResDTO> getByIndex(@PathVariable String id){
        ProductInfoResDTO productInfoRes = productInfoService.find(id);
        return AnyTxnHttpResponse.success(productInfoRes);
    }

    /**
     * 分页查询,获取产品信息 根据机构号
     * @param pageNum 页码
     * @param pageSize 每页大小
     * @return AnyTxnHttpResponse<PageResultDTO<ProductInfoRes>>
     */
    @GetMapping(value = "/param/productInfo/pageNum/{pageNum}/pageSize/{pageSize}")
    @Operation(summary="获取列表,分页", description = "需传入页码以及页面大小")
    public AnyTxnHttpResponse<PageResultDTO<ProductInfoResDTO>> getPageByStatus (@PathVariable(value = "pageNum") Integer pageNum,
                                                                                 @PathVariable(value = "pageSize") Integer pageSize,
                                                                                 @RequestParam(required = false) String organizationNumber) {
        PageResultDTO<ProductInfoResDTO> pageResultDto = productInfoService.findPageByOrgNumber(pageNum, pageSize, organizationNumber);
        return AnyTxnHttpResponse.success(pageResultDto);

    }

    /**
     * 获取产品信息 根据机构号
     * @return AnyTxnHttpResponse<PageResultDTO<ProductInfoRes>>
     */
    @GetMapping(value = "/param/productInfo/organizationNumber/{organizationNumber}")
    @Operation(summary="获取列表", description = "需传入机构号")
    public AnyTxnHttpResponse<List<ProductInfoResDTO>> getPageByStatus (@PathVariable String organizationNumber) {
        List<ProductInfoResDTO> pageResultDto = productInfoService.findProductInfo(organizationNumber);
        return AnyTxnHttpResponse.success(pageResultDto);

    }
}
