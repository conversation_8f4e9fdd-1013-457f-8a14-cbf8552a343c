package com.anytech.anytxn.parameter.account.controller;

import io.swagger.v3.oas.annotations.tags.Tag;
import io.swagger.v3.oas.annotations.Operation;
import com.anytech.anytxn.common.core.base.PageResultDTO;
import com.anytech.anytxn.common.core.base.AnyTxnHttpResponse;
import com.anytech.anytxn.common.core.base.BizBaseController;
import com.anytech.anytxn.parameter.base.account.domain.dto.MinimumPaymentPercentReqDTO;
import com.anytech.anytxn.parameter.base.account.domain.dto.MinimumPaymentPercentResDTO;
import com.anytech.anytxn.parameter.base.account.service.IMinimumPaymentPercentService;
import com.anytech.anytxn.parameter.base.common.enums.ParameterRepDetailEnum;
import com.anytech.anytxn.parameter.base.common.utils.AnyTxnHttpResponseHelper;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.ArrayList;

/**
 * 最低还款比例
 * <AUTHOR> tingting
 * @date 2018/9/20
 */
@Tag(name = "最低还款比例参数")
@RestController
public class MinimumPaymentPercentController extends BizBaseController {

    @Autowired
    private IMinimumPaymentPercentService minimumPaymentPercentService;

    /**
     * 通过机构号,tableId查询最低还款比例参数信息
     * @return MinimumPaymentPercentRes
     *
     */
    @Operation(summary = "通过机构号,tableId查询")
    @GetMapping(value = "/param/minimumPaymentPercent/organizationNumber/{organizationNumber}/tableId/{tableId}")
    public AnyTxnHttpResponse<MinimumPaymentPercentResDTO> getMinimumPaymentPercent(@PathVariable String organizationNumber,
                                                                                 @PathVariable String tableId) {
        MinimumPaymentPercentResDTO res;
        res = minimumPaymentPercentService.findByOrgAndTableId(organizationNumber,tableId);
        return AnyTxnHttpResponse.success(res);
    }

    /**
     * 查询所有最低还款比例参数信息
     * @return MinimumPaymentPercentRes
     *
     */
    @Operation(summary = "最低还款比例参数参数分页查询",description = "分页查询")
    @GetMapping(value = "/param/minimumPaymentPercent/pageNum/{pageNum}/pageSize/{pageSize}")
    public AnyTxnHttpResponse<PageResultDTO<MinimumPaymentPercentResDTO>> getAllPage(@PathVariable(value = "pageNum") Integer pageNum,
                                                                                  @PathVariable(value = "pageSize") Integer pageSize,
                                                                                  @RequestParam(value = "tableId",required = false) String tableId,
                                                                                  @RequestParam(value = "description",required = false) String description,
                                                                                  @RequestParam(value = "billEvenDollars",required = false) String billEvenDollars,
                                                                                  @RequestParam(value = "curMinPaymentPercentage",required = false) String curMinPaymentPercentage,
                                                                                  @RequestParam(value = "preMinPaymentPercentage",required = false) String preMinPaymentPercentage,
                                                                                  @RequestParam(required = false) String organizationNumber) {
        PageResultDTO<MinimumPaymentPercentResDTO> response;
        response = minimumPaymentPercentService.findAll(pageNum,pageSize,tableId,description,billEvenDollars,curMinPaymentPercentage,preMinPaymentPercentage,organizationNumber);
        return AnyTxnHttpResponse.success(response);

    }

    /**
     * 通过id查询最低还款比例参数信息
     * @param id
     * @return
     *
     */
    @Operation(summary = "通过id查询最低还款比例参数信息", description = "通过id查询最低还款比例参数信息")
    @GetMapping(value = "/param/minimumPaymentPercent/id/{id}")
    public AnyTxnHttpResponse<MinimumPaymentPercentResDTO> getById(@PathVariable String id) {
        MinimumPaymentPercentResDTO res;
        res = minimumPaymentPercentService.findById(id);
        return AnyTxnHttpResponse.success(res);

    }

    /**
     * 添加最低还款比例参数
     * @param minimumPaymentPercentReq 最低还款比例参数入参对象
     * @return 最低还款比例参数响应参数
     */
    @Operation(summary = "新增低还款比例参数",description = "新增低还款比例参数")
    @PostMapping(value = "/param/minimumPaymentPercent")
    public AnyTxnHttpResponse create(@RequestBody MinimumPaymentPercentReqDTO minimumPaymentPercentReq) {
        minimumPaymentPercentService.addParmMinimumPaymentPercent(minimumPaymentPercentReq);
        return AnyTxnHttpResponseHelper.succ(ParameterRepDetailEnum.CREATE.message());

    }


    /**
     * 更新最低还款比例参数
     * @param  minimumPaymentPercentReq 最低还款比例参数入参对象
     * @return 最低还款比例参数响应参数
     */
    @Operation(summary = "最低还款比例参数信息修改", description = "只更新需要更新的,其他不变")
    @PutMapping(value = "/param/minimumPaymentPercent")
    public AnyTxnHttpResponse modify(@RequestBody MinimumPaymentPercentReqDTO minimumPaymentPercentReq) {
        minimumPaymentPercentService.modifyParmMinimumPaymentPercent(minimumPaymentPercentReq);
        return AnyTxnHttpResponseHelper.succ(ParameterRepDetailEnum.UPDATE.message());

    }

    /**
     * 通过Id主键删除最低还款比例参数
     * @param  id 主键
     * @return Boolean
     */
    @Operation(summary = "删除最低还款比例参数信息", description = "通过id删除最低还款比例参数信息")
    @DeleteMapping(value = "/param/minimumPaymentPercent/id/{id}")
    public AnyTxnHttpResponse remove(@PathVariable String id) {
        minimumPaymentPercentService.removeParmMinimumPaymentPercent(id);
        return AnyTxnHttpResponseHelper.succ(ParameterRepDetailEnum.DEL.message());

    }

    /**
     * 通过机构号查询最低还款比例参数信息
     *
     * @param organizationNumber 机构号
     * @return
     */
    @Operation(summary = "通过机构号查询最低还款比例参数信息", description = "通过机构号查询最低还款比例参数信息")
    @GetMapping(value = "/param/minimumPaymentPercent/organizationNumber/{organizationNumber}")
    public AnyTxnHttpResponse<ArrayList<MinimumPaymentPercentResDTO>> getByStatus(@PathVariable String organizationNumber) {
        ArrayList<MinimumPaymentPercentResDTO> resList;
        resList = (ArrayList) minimumPaymentPercentService.findAllByOrgNumber(organizationNumber);
        return AnyTxnHttpResponse.success(resList);

    }
}
