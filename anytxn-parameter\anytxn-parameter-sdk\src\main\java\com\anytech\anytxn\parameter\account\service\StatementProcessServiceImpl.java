package com.anytech.anytxn.parameter.account.service;

import com.alibaba.fastjson.JSON;
import com.github.pagehelper.Page;
import com.github.pagehelper.PageHelper;
import com.anytech.anytxn.common.core.annotation.db.DeleteParameterAnnotation;
import com.anytech.anytxn.common.core.annotation.db.InsertParameterAnnotation;
import com.anytech.anytxn.common.core.annotation.db.UpdateParameterAnnotation;
import com.anytech.anytxn.business.base.audit.AbstractParameterService;
import com.anytech.anytxn.common.core.base.ParameterCompare;
import com.anytech.anytxn.business.base.audit.ParmModificationRecord;
import com.anytech.anytxn.common.core.utils.OrgNumberUtils;
import com.anytech.anytxn.parameter.base.account.domain.dto.StatementProcessSearchDTO;
import com.anytech.anytxn.common.core.base.PageResultDTO;
import com.anytech.anytxn.common.core.utils.BeanMapping;
import com.anytech.anytxn.common.sequence.utils.Number16IdGen;
import com.anytech.anytxn.common.core.utils.TenantUtils;
import com.anytech.anytxn.parameter.base.account.domain.dto.StatementProcessReqDTO;
import com.anytech.anytxn.parameter.base.account.domain.dto.StatementProcessResDTO;
import com.anytech.anytxn.parameter.base.account.service.IStatementProcessService;
import com.anytech.anytxn.parameter.base.common.enums.AnyTxnParameterRespCodeEnum;
import com.anytech.anytxn.parameter.base.common.enums.ParameterRepDetailEnum;
import com.anytech.anytxn.parameter.base.common.exception.AnyTxnParameterException;
import com.anytech.anytxn.parameter.account.mapper.ParmStatementProcessMapper;
import com.anytech.anytxn.parameter.account.mapper.ParmStatementProcessSelfMapper;
import com.anytech.anytxn.parameter.base.account.domain.model.ParmStatementProcess;
//import org.apache.shardingsphere.transaction.annotation.ShardingSphereTransactionType;
//import org.apache.shardingsphere.transaction.core.TransactionType;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.StringUtils;

import java.math.BigDecimal;
import java.util.List;

/**
 * 账单处理参数 业务接口实现
 *
 * <AUTHOR> tingting
 * @date 2018/8/21
 */
@Service(value = "parm_statement_process_serviceImpl")
public class StatementProcessServiceImpl extends AbstractParameterService implements IStatementProcessService {

    @Autowired
    private ParmStatementProcessMapper parmStatementProcessMapper;
    @Autowired
    private ParmStatementProcessSelfMapper parmStatementProcessSelfMapper;

    @Autowired
    private Number16IdGen numberIdGenerator;

    /**
     * 查询所有账单处理参数
     *
     * @param pageNum
     * @param pageSize
     * @return 账单处理参数响应参数
     * @throws AnyTxnParameterException
     */
    @Override
    public PageResultDTO<StatementProcessResDTO> findAll(Integer pageNum, Integer pageSize, StatementProcessSearchDTO statementProcessSearchDTO) {
        if (null == statementProcessSearchDTO) {
            statementProcessSearchDTO = new StatementProcessSearchDTO();
        }

        if (!StringUtils.isEmpty(statementProcessSearchDTO.getDueDays())) {
            Integer dueDays = null;
            try {
                dueDays = Integer.parseInt(statementProcessSearchDTO.getDueDays());
            } catch (NumberFormatException e) {
                throw new AnyTxnParameterException(AnyTxnParameterRespCodeEnum.PARAMETER_EXCEPTION, ParameterRepDetailEnum.NUMBER_ERROR);
            }
            statementProcessSearchDTO.setDueDaysInt(dueDays);
        }

        if (!StringUtils.isEmpty(statementProcessSearchDTO.getGraceDays())) {
            Integer graceDays = null;
            try {
                graceDays = Integer.parseInt(statementProcessSearchDTO.getGraceDays());
            } catch (NumberFormatException e) {
                throw new AnyTxnParameterException(AnyTxnParameterRespCodeEnum.PARAMETER_EXCEPTION, ParameterRepDetailEnum.NUMBER_ERROR);
            }
            statementProcessSearchDTO.setGraceDaysInt(graceDays);
        }

        if (!StringUtils.isEmpty(statementProcessSearchDTO.getLateDays())) {
            Integer lateDays = null;
            try {
                lateDays = Integer.parseInt(statementProcessSearchDTO.getLateDays());
            } catch (NumberFormatException e) {
                throw new AnyTxnParameterException(AnyTxnParameterRespCodeEnum.PARAMETER_EXCEPTION, ParameterRepDetailEnum.NUMBER_ERROR);
            }
            statementProcessSearchDTO.setLateDaysInt(lateDays);
        }

        if (!StringUtils.isEmpty(statementProcessSearchDTO.getPaymentVariance())) {
            BigDecimal paymentVariance = null;
            try {
                paymentVariance = new BigDecimal(statementProcessSearchDTO.getPaymentVariance());
            } catch (NumberFormatException e) {
                throw new AnyTxnParameterException(AnyTxnParameterRespCodeEnum.PARAMETER_EXCEPTION, ParameterRepDetailEnum.NUMBER_ERROR);
            }
            statementProcessSearchDTO.setPaymentVarianceDeci(paymentVariance);
        }

        if (!StringUtils.isEmpty(statementProcessSearchDTO.getPayoffVariance())) {
            BigDecimal payoffVariance = null;
            try {
                payoffVariance = new BigDecimal(statementProcessSearchDTO.getPayoffVariance());
            } catch (NumberFormatException e) {
                throw new AnyTxnParameterException(AnyTxnParameterRespCodeEnum.PARAMETER_EXCEPTION, ParameterRepDetailEnum.NUMBER_ERROR);
            }
            statementProcessSearchDTO.setPayoffVarianceDeci(payoffVariance);
        }

        Page<ParmStatementProcess> page = PageHelper.startPage(pageNum, pageSize);
        statementProcessSearchDTO.setOrganizationNumber(StringUtils.isEmpty(statementProcessSearchDTO.getOrganizationNumber()) ? OrgNumberUtils.getOrg() : statementProcessSearchDTO.getOrganizationNumber());
        List<ParmStatementProcess> parmStatementProcesses = parmStatementProcessSelfMapper.selectByCondition(statementProcessSearchDTO);
        List<StatementProcessResDTO> res = BeanMapping.copyList(parmStatementProcesses, StatementProcessResDTO.class);
        return new PageResultDTO<>(pageNum, pageSize, page.getTotal(), page.getPages(), res);
    }

    /**
     * 通过Id主键查询账单处理参数
     *
     * @param id 主键
     * @return 账单处理参数响应参数
     */
    @Override
    //@PreGetProcess(byPrimaryKey = true)
    public StatementProcessResDTO findById(String id) {
        if (id == null) {

            throw new AnyTxnParameterException(AnyTxnParameterRespCodeEnum.P_PARAMETER_EMPTY_FAULT);
        }
        ParmStatementProcess parmStatementProcess = parmStatementProcessMapper.selectByPrimaryKey(id);
        return BeanMapping.copy(parmStatementProcess, StatementProcessResDTO.class);
    }

    /**
     * 添加账单处理参数
     *
     * @param statementProcessReq 账单处理参数入参对象
     * @return 账单处理参数响应参数
     */
    @Override
    @InsertParameterAnnotation(tableName = "parm_statement_process", tableDesc = "Statement Processing")
    public ParameterCompare addStatementProcess(StatementProcessReqDTO statementProcessReq) {
        // 根据机构号和table_id查询该记录是否已经存在
        ParmStatementProcess statementProcess = parmStatementProcessSelfMapper.isExists(OrgNumberUtils.getOrg(statementProcessReq.getOrganizationNumber()), statementProcessReq.getTableId());
        if (statementProcess != null) {

            throw new AnyTxnParameterException(AnyTxnParameterRespCodeEnum.D_EXIST_PARM_PRODUCT_STATEMENT_PROCESS_FAULT);
        }
        ParmStatementProcess parmStatementProcessReq = BeanMapping.copy(statementProcessReq, ParmStatementProcess.class);

        // 设置默认值
        parmStatementProcessReq.setId(String.valueOf(numberIdGenerator.generateId(TenantUtils.getTenantId())));

        return ParameterCompare.getBuilder().withAfter(parmStatementProcessReq).build(ParmStatementProcess.class);
    }

    /**
     * 更新账单处理参数
     *
     * @param statementProcessReq 账单处理参数入参对象
     * @return 账单处理参数响应参数
     */
    @Override
    @UpdateParameterAnnotation(tableName = "parm_statement_process", tableDesc = "Statement Processing")
    public ParameterCompare modifyStatementProcess(StatementProcessReqDTO statementProcessReq) {
        // 查询此记录是否存在
        ParmStatementProcess statementProcess = parmStatementProcessMapper.selectByPrimaryKey(statementProcessReq.getId());
        if (null == statementProcess) {

            throw new AnyTxnParameterException(AnyTxnParameterRespCodeEnum.D_QUERY_PARM_PRODUCT_STATEMENT_PROCESS_BY_ID_FAULT);
        }
        // 判断修改后的数据在表里是否是以机构号,表id唯一确定一条数据
        if (!statementProcessReq.getOrganizationNumber().equals(statementProcessReq.getOrganizationNumber())
                || !statementProcessReq.getTableId().equals(statementProcessReq.getTableId())) {
            ParmStatementProcess isExsit = parmStatementProcessSelfMapper.isExists(statementProcessReq.getOrganizationNumber(), statementProcessReq.getTableId());
            if (isExsit != null) {

                throw new AnyTxnParameterException(AnyTxnParameterRespCodeEnum.D_EXIST_PARM_PRODUCT_STATEMENT_PROCESS_FAULT);
            }
        }
        ParmStatementProcess parmStatementProcess = BeanMapping.copy(statementProcessReq, ParmStatementProcess.class);

        return ParameterCompare.getBuilder().withAfter(parmStatementProcess).withBefore(statementProcess).build(ParmStatementProcess.class);
    }

    /**
     * 通过Id主键删除账单处理参数
     *
     * @param id 主键
     * @return Boolean
     */
    @Override
    @DeleteParameterAnnotation(tableName = "parm_statement_process", tableDesc = "Statement Processing")
    public ParameterCompare removeStatementProcess(String id) {
        ParmStatementProcess statementProcess = new ParmStatementProcess();
        // 如果id不存在，则查询该记录
        if (null != id) {
            statementProcess = parmStatementProcessMapper.selectByPrimaryKey(id);
        }
        // 如果该记录不存在，抛出异常，否则就删除该记录
        if (null == statementProcess) {

            throw new AnyTxnParameterException(AnyTxnParameterRespCodeEnum.D_QUERY_PARM_PRODUCT_STATEMENT_PROCESS_BY_ID_FAULT);
        }
        return ParameterCompare.getBuilder().withBefore(statementProcess).build(ParmStatementProcess.class);
    }

    /**
     * 通过机构号,tableId查询账单处理参数
     *
     * @param orgNumber 机构号
     * @param tableId   表Id
     * @return 账单处理返回对象
     */
    @Override
    //@PreGetProcess(args = {"#0","#1"})
    public StatementProcessResDTO findByOrgAndTableId(String orgNumber, String tableId) {
        ParmStatementProcess statementProcess = parmStatementProcessSelfMapper.isExists(orgNumber, tableId);
        // 如果该记录不存在，抛出异常，否则就删除该记录
        if (null == statementProcess) {

            throw new AnyTxnParameterException(AnyTxnParameterRespCodeEnum.D_QUERY_PARM_PRODUCT_STATEMENT_PROCESS_FAULT);
        }
        return BeanMapping.copy(statementProcess, StatementProcessResDTO.class);
    }

    @Override
    @Transactional(rollbackFor = Exception.class, propagation = Propagation.REQUIRED)
    //@ShardingSphereTransactionType(TransactionType.XA)
    protected boolean updateDb(ParmModificationRecord parmModificationRecord) {
        ParmStatementProcess statementProcess = JSON.parseObject(parmModificationRecord.getParmBody(), ParmStatementProcess.class);
        statementProcess.initUpdateDateTime();
        int i = parmStatementProcessMapper.updateByPrimaryKey(statementProcess);
        return i > 0;
    }

    @Override
    @Transactional(rollbackFor = Exception.class, propagation = Propagation.REQUIRED)
    //@ShardingSphereTransactionType(TransactionType.XA)
    protected boolean insertDb(ParmModificationRecord parmModificationRecord) {
        ParmStatementProcess statementProcess = JSON.parseObject(parmModificationRecord.getParmBody(), ParmStatementProcess.class);
        statementProcess.initCreateDateTime();
        statementProcess.initUpdateDateTime();
        int insert = parmStatementProcessMapper.insert(statementProcess);
        return insert > 0;
    }

    @Override
    @Transactional(rollbackFor = Exception.class, propagation = Propagation.REQUIRED)
    //@ShardingSphereTransactionType(TransactionType.XA)
    protected boolean deleteDb(ParmModificationRecord parmModificationRecord) {
        ParmStatementProcess statementProcess = JSON.parseObject(parmModificationRecord.getParmBody(), ParmStatementProcess.class);
        int i = parmStatementProcessMapper.deleteByPrimaryKey(statementProcess.getId());
        return i > 0;
    }
}
