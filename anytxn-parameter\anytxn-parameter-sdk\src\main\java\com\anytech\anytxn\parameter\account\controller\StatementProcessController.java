package com.anytech.anytxn.parameter.account.controller;

import io.swagger.v3.oas.annotations.tags.Tag;
import io.swagger.v3.oas.annotations.Operation;
import com.anytech.anytxn.common.core.base.PageResultDTO;
import com.anytech.anytxn.common.core.base.AnyTxnHttpResponse;
import com.anytech.anytxn.common.core.base.BizBaseController;
import com.anytech.anytxn.parameter.base.account.domain.dto.StatementProcessReqDTO;
import com.anytech.anytxn.parameter.base.account.domain.dto.StatementProcessResDTO;
import com.anytech.anytxn.parameter.base.account.domain.dto.StatementProcessSearchDTO;
import com.anytech.anytxn.parameter.base.account.service.IStatementProcessService;
import com.anytech.anytxn.parameter.base.common.enums.ParameterRepDetailEnum;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

/**
 * 账单处理参数
 * <AUTHOR> tingt<PERSON>
 * @date 2018/8/21
 */
@Tag(name = "账单处理参数")
@RestController
public class StatementProcessController extends BizBaseController {

    @Autowired
    private IStatementProcessService statementProcessService;

    /**
     * 新增账单处理参数
     * @param statementProcessReq
     * @return StatementProcessRes
     *
     */
    @Operation(summary = "新增账单处理参数", description = "新增账单处理参数")
    @PostMapping(value = "/param/statementProcess")
    public AnyTxnHttpResponse<Object> create(@RequestBody StatementProcessReqDTO statementProcessReq) {
        return AnyTxnHttpResponse.success(statementProcessService.addStatementProcess(statementProcessReq),ParameterRepDetailEnum.CREATE.message());

    }

    /**
     * 查询所有账单处理参数信息
     * @return StatementProcessRes
     *
     */
    @Operation(summary = "账单处理参数分页查询", description = "分页查询")
    @GetMapping(value = "/param/statementProcess/pageNum/{pageNum}/pageSize/{pageSize}")
    public AnyTxnHttpResponse<PageResultDTO<StatementProcessResDTO>> getAllPage(@PathVariable(value = "pageNum") Integer pageNum,
                                                                             @PathVariable(value = "pageSize") Integer pageSize,
                                                                                StatementProcessSearchDTO statementProcessSearchDTO) {
        PageResultDTO<StatementProcessResDTO> response;
        response = statementProcessService.findAll(pageNum,pageSize,statementProcessSearchDTO);
        return AnyTxnHttpResponse.success(response);

    }


    /**
     * 修改账单处理参数信息
     * @param statementProcessReq
     * @return StatementProcessRes
     *
     */
    @Operation(summary = "账单处理参数信息修改", description = "只更新需要更新的,其他不变")
    @PutMapping(value = "/param/statementProcess")
    public AnyTxnHttpResponse<Object> modify(@RequestBody StatementProcessReqDTO statementProcessReq) {
        return AnyTxnHttpResponse.success(statementProcessService.modifyStatementProcess(statementProcessReq),ParameterRepDetailEnum.UPDATE.message());

    }

    /**
     * 通过id删除账单处理参数信息
     * @param id
     * @return Boolean
     *
     */
    @Operation(summary = "删除账单处理参数信息", description = "通过id删除账单处理参数信息")
    @DeleteMapping(value = "/param/statementProcess/id/{id}")
    public AnyTxnHttpResponse<Object> remove(@PathVariable String id) {
        return AnyTxnHttpResponse.success(statementProcessService.removeStatementProcess(id),ParameterRepDetailEnum.DEL.message());

    }

    /**
     * 通过id查询账单处理参数信息
     * @param id
     * @return AnyTxnHttpResponse<StatementProcessRes>
     *
     */
    @Operation(summary = "通过id查询账单处理参数信息", description = "通过id查询账单处理参数信息")
    @GetMapping(value = "/param/statementProcess/id/{id}")
    public AnyTxnHttpResponse<StatementProcessResDTO> getById(@PathVariable String id) {
        StatementProcessResDTO res;
        res = statementProcessService.findById(id);
        return AnyTxnHttpResponse.success(res);

    }

    /**
     * 通过机构号,tableId查询账单处理参数信息
     * @param organizationNumber 机构号
     * @param tableId 表id
     * @return 账单处理返回对象
     *
     */
    @Operation(summary = "通过机构号,tableId查询账单处理参数信息", description = "通过机构号,tableId查询账单处理参数信息")
    @GetMapping(value = "/param/statementProcess/organizationNumber/{organizationNumber}/tableId/{tableId}")
    public AnyTxnHttpResponse<StatementProcessResDTO> getByOrgAndTableId(@PathVariable String organizationNumber, @PathVariable String tableId) {
        StatementProcessResDTO res;
        res = statementProcessService.findByOrgAndTableId(organizationNumber,tableId);
        return AnyTxnHttpResponse.success(res);

    }

}
