package com.anytech.anytxn.parameter.authorization.service;

import com.anytech.anytxn.common.core.base.PageResultDTO;
import com.anytech.anytxn.common.core.utils.BeanMapping;
import com.anytech.anytxn.common.core.utils.OrgNumberUtils;
import com.anytech.anytxn.parameter.base.authorization.domain.dto.CountryMccDTO;
import com.anytech.anytxn.parameter.base.authorization.service.ICountryMccService;
import com.anytech.anytxn.parameter.base.common.enums.AnyTxnParameterRespCodeEnum;
import com.anytech.anytxn.parameter.base.common.exception.AnyTxnParameterException;
import com.anytech.anytxn.parameter.authorization.mapper.ParmCountryMccMapper;
import com.anytech.anytxn.parameter.authorization.mapper.ParmCountryMccSelfMapper;
import com.anytech.anytxn.parameter.common.mapper.broadcast.ParmCountryCodeSelfMapper;
import com.anytech.anytxn.parameter.base.common.domain.model.unicast.ParmCountryCode;
import com.anytech.anytxn.parameter.base.authorization.domain.model.ParmCountryMcc;
import com.github.pagehelper.Page;
import com.github.pagehelper.PageHelper;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.cglib.beans.BeanCopier;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 国家MCC具体实现类
 *
 * <AUTHOR>
 * @Date 2021/11/6 3:44 PM
 * Version 1.0
 **/
@Service
public class CountryMccServiceImpl implements ICountryMccService {

    private Logger logger = LoggerFactory.getLogger(CountryMccServiceImpl.class);

    @Autowired
    private ParmCountryMccMapper parmCountryMccMapper;

    @Autowired
    private ParmCountryMccSelfMapper parmCountryMccSelfMapper;

    @Autowired
    private ParmCountryCodeSelfMapper parmCountryCodeSelfMapper;


    @Override
    public PageResultDTO<CountryMccDTO> findListCountryMcc(Integer page, Integer rows, String organizationNumber) {

        logger.info("分页查询国家MCC，当前页：{}，每页展示条数：{}", page, rows);
        Page pageInfo =  PageHelper.startPage(page, rows);
        List<CountryMccDTO> countryMccDTOList = null;
        try {
            organizationNumber = StringUtils.isEmpty(organizationNumber) ? OrgNumberUtils.getOrg() : organizationNumber;
            List<ParmCountryMcc> parmCountryMccList = parmCountryMccSelfMapper.selectAllByOrganizationNumber(organizationNumber);
            if (!CollectionUtils.isEmpty(parmCountryMccList)) {
                countryMccDTOList = new ArrayList<>();
                BeanCopier beanCopier = BeanCopier.create(ParmCountryMcc.class, CountryMccDTO.class, false);
                for (ParmCountryMcc parmCountryMcc : parmCountryMccList) {
                    CountryMccDTO countryMccDTO = new CountryMccDTO();
                    beanCopier.copy(parmCountryMcc, countryMccDTO, null);
                    countryMccDTOList.add(countryMccDTO);
                }
            }
            return new PageResultDTO<>(page, rows, pageInfo.getTotal(), pageInfo.getPages(),countryMccDTOList);
        } catch (Exception e) {
            logger.error("分页查询国家MCC信息失败，当前页：{}，每页展示条数：{},错误信息：{}", page, rows, e.getMessage());
            /*throw new AnyTXNBusRuntimeException(TransactionEnum.DATABASE_ERROR.getCode(),
                    TransactionEnum.DATABASE_ERROR.getReason());*/
            throw new AnyTxnParameterException(AnyTxnParameterRespCodeEnum.D_PAGE_MCC_CODE_FAULT);
        }
    }

    

    @Override
    public CountryMccDTO findCountryMcc(Long id) {

        logger.info("根据主键:{},获取国家MCC信息",id);
        CountryMccDTO countryMccDTO = null;
        try{
            ParmCountryMcc parmcountryMcc = parmCountryMccMapper.selectByPrimaryKey(id);
            if (parmcountryMcc != null) {
                countryMccDTO = new CountryMccDTO();
                BeanCopier beanCopier = BeanCopier.create(ParmCountryMcc.class, CountryMccDTO.class, false);
                beanCopier.copy(parmcountryMcc, countryMccDTO, null);
            }
        }
        catch(Exception e){
            logger.error("根据主键:{},获取国家MCC信息失败,错误信息:{}",id,e.getMessage());
            /*throw new AnyTXNBusRuntimeException(TransactionEnum.DATABASE_ERROR.getCode(),
                    TransactionEnum.DATABASE_ERROR.getReason());*/
            throw new AnyTxnParameterException(AnyTxnParameterRespCodeEnum.D_QUERY_MCC_CODE_BY_ID_FAULT);

        }
        if (countryMccDTO== null) {
            logger.error("根据主键:{},获取国家MCC信息失败", id);

            throw new AnyTxnParameterException(AnyTxnParameterRespCodeEnum.D_QUERY_MCC_CODE_NULL_BY_ID_FAULT);
        }
        return countryMccDTO;

    }

    @Override
    public Boolean modifyCountryMcc(CountryMccDTO countryMccDTO) {

        logger.info("修改国家MCC信息,国家码:{} MCC:{}", countryMccDTO.getIsoGeoCodeNumeric(), countryMccDTO.getMcc());
        try {
            ParmCountryMcc parmCountryMcc = new ParmCountryMcc();
            BeanCopier beanCopier = BeanCopier.create(CountryMccDTO.class, ParmCountryMcc.class, false);
            beanCopier.copy(countryMccDTO, parmCountryMcc, null);
            parmCountryMcc.setUpdateTime(LocalDateTime.now());
            return parmCountryMccMapper.updateByPrimaryKeySelective(parmCountryMcc) > 0;
        } catch (Exception e) {
            logger.error("调用[{}]更新国家MCC表[{}]失败,错误信息[{}]",
                    "updateByPrimaryKeySelective", "MCC_COD", e);

            throw new AnyTxnParameterException(AnyTxnParameterRespCodeEnum.D_UPDATE_MCC_CODE_FAULT);
        }

    }

    @Override
    public Boolean removeCountryMcc(Long id) {

        ParmCountryMcc parmCountryMcc = parmCountryMccMapper.selectByPrimaryKey(id);
        logger.info("查询国家MCC信息 id:{}", id);
        if (parmCountryMcc == null) {
            logger.error("待删除国家MCC信息不存在。 id:{}", id);
            throw new AnyTxnParameterException(AnyTxnParameterRespCodeEnum.D_QUERY_MCC_CODE_NULL_BY_ID_FAULT);
        }
        try {
            return parmCountryMccMapper.deleteByPrimaryKey(id) > 0;
        } catch (Exception e) {
            throw new AnyTxnParameterException(AnyTxnParameterRespCodeEnum.D_DELETE_MCC_CODE_FAULT);
        }

    }


    @Override
    public Boolean addCountryMcc(CountryMccDTO countryMccDTO) {

        //转换
        ParmCountryMcc parmCountryMcc ;
        if (countryMccDTO == null) {
            throw new AnyTxnParameterException(AnyTxnParameterRespCodeEnum.P_PARAMETER_EMPTY_FAULT);
        }
        String countryName = countryMccDTO.getCountryName();
        String allMcc = countryMccDTO.getAllMcc();
        BigDecimal triggerAmount = countryMccDTO.getTriggerAmount();
        String mcc = countryMccDTO.getMcc();
        ParmCountryCode parmCountryCode = parmCountryCodeSelfMapper.selectByDescription(countryName);
        String countryCode = parmCountryCode.getNumericCountryCode();
        String  countryCodeString = countryCode;

        parmCountryMcc = BeanMapping.copy(countryMccDTO, ParmCountryMcc.class);
        int isExists = parmCountryMccSelfMapper.existsCountryMcc(countryCodeString,countryMccDTO.getMcc());
        if (isExists > 0) {
            logger.error("已存在!");
            throw new AnyTxnParameterException(AnyTxnParameterRespCodeEnum.D_EXIST_MCC_CODE_FAULT);
        }
        parmCountryMcc.setAllMcc(allMcc);
        parmCountryMcc.setCountryName(countryName);
        parmCountryMcc.setTriggerAmount(triggerAmount);
        parmCountryMcc.setMcc(mcc);
        parmCountryMcc.setIsoGeoCodeNumeric(countryCode);
        parmCountryMcc.setCreateTime(LocalDateTime.now());
        parmCountryMcc.setUpdateTime(LocalDateTime.now());
        parmCountryMcc.setUpdateBy("admin");
        try {
            return parmCountryMccMapper.insert(parmCountryMcc) > 0;
        } catch (Exception e) {
            logger.error("exception:{}",e);
            throw new AnyTxnParameterException(AnyTxnParameterRespCodeEnum.D_INSERT_MCC_CODE_FAULT);
        }

    }

    @Override
    public List<Map<String, String>> getMccByCountry(String countryCode) {

        logger.info("获取所有国家MCC");
        List<ParmCountryMcc> parmCountryMccList = parmCountryMccSelfMapper.selectAll(countryCode);
        List<Map<String,String>> list = new ArrayList<>();
        parmCountryMccList.forEach(parmCountryMcc -> {
            Map<String,String> map = new HashMap<>(4);
            map.put("value",parmCountryMcc.getMcc());
            map.put("label",parmCountryMcc.getDescription());
            list.add(map);
        });
        return list;
    }


    @Override
    public Boolean existsCountryMcc(String countryCode, String mcc) {

        int exist = parmCountryMccSelfMapper.existsCountryMcc(countryCode, mcc);

        if (exist == 0) {
            return false;
        }
        return true;
    }

    @Override
    public List<CountryMccDTO> findCountryMccByCountryName(String countryName) {

        logger.info("获取所有国家MCC");
        List<ParmCountryMcc> parmCountryMccs = parmCountryMccSelfMapper.selectAllByCountryName(countryName);
        List<CountryMccDTO> collect = parmCountryMccs.stream().map(x -> BeanMapping.copy(x, CountryMccDTO.class)).collect(Collectors.toList());
        return collect;
    }

    @Override
    public List<CountryMccDTO> findCountryMccBymcc(String mcc) {
        logger.info("获取所有国家MCC");
        List<ParmCountryMcc> parmCountryMccs = parmCountryMccSelfMapper.selectAllByMcc(mcc);
        List<CountryMccDTO> collect = parmCountryMccs.stream().map(x -> BeanMapping.copy(x, CountryMccDTO.class)).collect(Collectors.toList());
        return collect;
    }

    @Override
    public Boolean addCountryMccList(CountryMccDTO countryMccDTO) {

        logger.info("开始添加。。。");
        String countryName = countryMccDTO.getCountryName();
        String allMcc = countryMccDTO.getAllMcc();
        BigDecimal triggerAmount = countryMccDTO.getTriggerAmount();
        List<String> mccList = countryMccDTO.getMccList();
        ParmCountryCode parmCountryCode = parmCountryCodeSelfMapper.selectByDescription(countryName);
        String countryCode = parmCountryCode.getNumericCountryCode();
        String  countryCodeString = countryCode;
        int insertFlag = 0;
        for (String mcc : mccList) {
            logger.info("进入循环。。。");
            int exist = parmCountryMccSelfMapper.existsCountryMcc(countryCodeString, mcc);

            if (exist > 0) {
                logger.info("结束本次循环。。。");
                continue;
            }else {
                ParmCountryMcc parmCountryMcc = new ParmCountryMcc();
                parmCountryMcc.setAllMcc(allMcc);
                parmCountryMcc.setMcc(mcc);
                parmCountryMcc.setCountryName(countryName);
                parmCountryMcc.setIsoGeoCodeNumeric(countryCode);
                parmCountryMcc.setTriggerAmount(triggerAmount);
                parmCountryMcc.setCreateTime(LocalDateTime.now());
                parmCountryMcc.setUpdateTime(LocalDateTime.now());

                int insert = parmCountryMccMapper.insert(parmCountryMcc);
                logger.info("值改变了。。。");
                insertFlag = insertFlag + insert;
            }
        }
        return insertFlag > 0;
    }


}
