package com.anytech.anytxn.business.card.service;

import com.anytech.anytxn.business.base.card.domain.dto.CardManageFeeRecordDTO;
import com.anytech.anytxn.business.dao.card.mapper.CardManageFeeRecordSelfMapper;
import com.anytech.anytxn.business.dao.card.model.CardManageFeeRecord;
import com.anytech.anytxn.common.core.utils.BeanMapping;
import com.anytech.anytxn.common.core.utils.OrgNumberUtils;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockedStatic;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.jdbc.core.namedparam.NamedParameterJdbcTemplate;
import org.springframework.jdbc.core.namedparam.SqlParameterSource;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.Mockito.*;

/**
 * CardManageFeeRecordServiceImpl 测试类
 * 
 * <AUTHOR>
 * @date 2024-01-01
 */
@ExtendWith(MockitoExtension.class)
class CardManageFeeRecordServiceTest {

    @InjectMocks
    private CardManageFeeRecordServiceImpl cardManageFeeRecordService;

    @Mock
    private CardManageFeeRecordSelfMapper cardManageFeeRecordSelfMapper;

    @Mock
    private NamedParameterJdbcTemplate bizJdbcTemplate;

    private List<CardManageFeeRecordDTO> testDTOList;
    private List<CardManageFeeRecord> testRecordList;

    @BeforeEach
    void setUp() {
        try (MockedStatic<OrgNumberUtils> orgUtilsMock = mockStatic(OrgNumberUtils.class)) {
            // Mock OrgNumberUtils for BaseEntity constructor if needed
            orgUtilsMock.when(OrgNumberUtils::getOrg).thenReturn("TEST_ORG");
            
            // 准备测试DTO数据
            testDTOList = createTestDTOList();
            
            // 准备测试Record数据
            testRecordList = createTestRecordList();
        }
    }

    /**
     * 测试方法：shouldInsertBatch_whenValidDTOListProvided
     * 用来测试 CardManageFeeRecordServiceImpl 方法 insertBatch
     * 验证当提供有效DTO列表时，能够正确调用批量插入
     */
    @Test
    void shouldInsertBatch_whenValidDTOListProvided() {
        try (MockedStatic<BeanMapping> beanMappingMock = mockStatic(BeanMapping.class)) {
            // Arrange - 准备测试数据
            List<CardManageFeeRecordDTO> expectedMappedList = new ArrayList<>();
            beanMappingMock.when(() -> BeanMapping.copyList(testDTOList, CardManageFeeRecordDTO.class))
                    .thenReturn(expectedMappedList);

            // Act - 执行被测方法
            cardManageFeeRecordService.insertBatch(testDTOList);

            // Assert - 验证结果
            verify(cardManageFeeRecordSelfMapper).insertBatch(expectedMappedList);
            beanMappingMock.verify(() -> BeanMapping.copyList(testDTOList, CardManageFeeRecordDTO.class));
        }
    }

    /**
     * 测试方法：shouldReturnResultArray_whenBatchInsetCalled
     * 用来测试 CardManageFeeRecordServiceImpl 方法 batchInset
     * 验证批量插入方法能够返回正确的结果数组
     */
    @Test
    void shouldReturnResultArray_whenBatchInsetCalled() {
        // Arrange - 准备测试数据
        int[] expectedResult = {1, 1, 1};
        when(bizJdbcTemplate.batchUpdate(anyString(), any(SqlParameterSource[].class)))
                .thenReturn(expectedResult);

        // Act - 执行被测方法
        int[] result = cardManageFeeRecordService.batchInset(testRecordList);

        // Assert - 验证结果
        assertArrayEquals(expectedResult, result);
        verify(bizJdbcTemplate).batchUpdate(anyString(), any(SqlParameterSource[].class));
    }

    /**
     * 测试方法：shouldCallJdbcTemplate_whenBatchInsertCalled
     * 用来测试 CardManageFeeRecordServiceImpl 方法 batchInsert
     * 验证通用批量插入方法能够正确调用JDBC模板
     */
    @Test
    void shouldCallJdbcTemplate_whenBatchInsertCalled() {
        // Arrange - 准备测试数据
        String testSql = "INSERT INTO test_table VALUES (?, ?)";
        int[] expectedResult = {1, 1, 1};
        when(bizJdbcTemplate.batchUpdate(eq(testSql), any(SqlParameterSource[].class)))
                .thenReturn(expectedResult);

        // Act - 执行被测方法
        int[] result = cardManageFeeRecordService.batchInsert(testSql, testRecordList);

        // Assert - 验证结果
        assertArrayEquals(expectedResult, result);
        verify(bizJdbcTemplate).batchUpdate(eq(testSql), any(SqlParameterSource[].class));
    }

    /**
     * 测试方法：shouldReturnResultArray_whenBatchUpdateByRecordIdCalled
     * 用来测试 CardManageFeeRecordServiceImpl 方法 batchUpdateByRecordId
     * 验证根据记录ID批量更新方法能够返回正确的结果数组
     */
    @Test
    void shouldReturnResultArray_whenBatchUpdateByRecordIdCalled() {
        // Arrange - 准备测试数据
        int[] expectedResult = {1, 1, 1};
        when(bizJdbcTemplate.batchUpdate(anyString(), any(SqlParameterSource[].class)))
                .thenReturn(expectedResult);

        // Act - 执行被测方法
        int[] result = cardManageFeeRecordService.batchUpdateByRecordId(testRecordList);

        // Assert - 验证结果
        assertArrayEquals(expectedResult, result);
        verify(bizJdbcTemplate).batchUpdate(anyString(), any(SqlParameterSource[].class));
    }

    /**
     * 测试方法：shouldCallJdbcTemplate_whenBatchUpdateCalled
     * 用来测试 CardManageFeeRecordServiceImpl 方法 batchUpdate
     * 验证通用批量更新方法能够正确调用JDBC模板
     */
    @Test
    void shouldCallJdbcTemplate_whenBatchUpdateCalled() {
        // Arrange - 准备测试数据
        String testSql = "UPDATE test_table SET column1 = ? WHERE id = ?";
        int[] expectedResult = {1, 1, 1};
        when(bizJdbcTemplate.batchUpdate(eq(testSql), any(SqlParameterSource[].class)))
                .thenReturn(expectedResult);

        // Act - 执行被测方法
        int[] result = cardManageFeeRecordService.batchUpdate(testSql, testRecordList);

        // Assert - 验证结果
        assertArrayEquals(expectedResult, result);
        verify(bizJdbcTemplate).batchUpdate(eq(testSql), any(SqlParameterSource[].class));
    }

    /**
     * 测试方法：shouldHandleEmptyList_whenEmptyDTOListProvided
     * 用来测试 CardManageFeeRecordServiceImpl 方法 insertBatch
     * 验证当提供空DTO列表时，能够正确处理
     */
    @Test
    void shouldHandleEmptyList_whenEmptyDTOListProvided() {
        try (MockedStatic<BeanMapping> beanMappingMock = mockStatic(BeanMapping.class)) {
            // Arrange - 准备测试数据
            List<CardManageFeeRecordDTO> emptyList = new ArrayList<>();
            List<CardManageFeeRecordDTO> expectedMappedList = new ArrayList<>();
            beanMappingMock.when(() -> BeanMapping.copyList(emptyList, CardManageFeeRecordDTO.class))
                    .thenReturn(expectedMappedList);

            // Act - 执行被测方法
            cardManageFeeRecordService.insertBatch(emptyList);

            // Assert - 验证结果
            verify(cardManageFeeRecordSelfMapper).insertBatch(expectedMappedList);
            beanMappingMock.verify(() -> BeanMapping.copyList(emptyList, CardManageFeeRecordDTO.class));
        }
    }

    /**
     * 测试方法：shouldHandleEmptyRecordList_whenBatchInsetCalledWithEmptyList
     * 用来测试 CardManageFeeRecordServiceImpl 方法 batchInset
     * 验证当提供空记录列表时，能够正确处理
     */
    @Test
    void shouldHandleEmptyRecordList_whenBatchInsetCalledWithEmptyList() {
        // Arrange - 准备测试数据
        List<CardManageFeeRecord> emptyList = new ArrayList<>();
        int[] expectedResult = new int[0];
        when(bizJdbcTemplate.batchUpdate(anyString(), any(SqlParameterSource[].class)))
                .thenReturn(expectedResult);

        // Act - 执行被测方法
        int[] result = cardManageFeeRecordService.batchInset(emptyList);

        // Assert - 验证结果
        assertArrayEquals(expectedResult, result);
        verify(bizJdbcTemplate).batchUpdate(anyString(), any(SqlParameterSource[].class));
    }

    // 创建测试DTO数据的辅助方法
    private List<CardManageFeeRecordDTO> createTestDTOList() {
        List<CardManageFeeRecordDTO> dtoList = new ArrayList<>();
        
        CardManageFeeRecordDTO dto1 = new CardManageFeeRecordDTO();
        dto1.setId("RECORD001");
        dto1.setGlobalFlowNumber("FLOW001");
        dto1.setCardType("P");
        dto1.setTxnBillingAmount(new BigDecimal("100.50"));
        dto1.setTxnBillingCurrency("CNY");
        dto1.setTxnCardNumber("1111222233334444");
        dto1.setTxnCountryCode("CN");
        dto1.setTxnForcePostIndicator("Y");
        dto1.setTxnPostMethod("0");
        dto1.setTxnTransactionAmount(new BigDecimal("100.50"));
        dto1.setTxnTransactionCode("001");
        dto1.setTxnTransactionCurrency("CNY");
        dto1.setTxnTransactionDate("20240101");
        dto1.setTxnOrganizationNumber("ORG001");
        dto1.setTxnPostDate(LocalDateTime.now());
        dto1.setTxnPostStatus("Y");
        dto1.setServiceType("FEE");
        dto1.setCreateTime(LocalDateTime.now());
        dto1.setUpdateTime(LocalDateTime.now());
        dto1.setUpdateBy("SYSTEM");
        dtoList.add(dto1);

        CardManageFeeRecordDTO dto2 = new CardManageFeeRecordDTO();
        dto2.setId("RECORD002");
        dto2.setGlobalFlowNumber("FLOW002");
        dto2.setCardType("A");
        dto2.setTxnBillingAmount(new BigDecimal("200.75"));
        dto2.setTxnBillingCurrency("USD");
        dto2.setTxnCardNumber("2222333344445555");
        dtoList.add(dto2);

        CardManageFeeRecordDTO dto3 = new CardManageFeeRecordDTO();
        dto3.setId("RECORD003");
        dto3.setGlobalFlowNumber("FLOW003");
        dto3.setCardType("P");
        dto3.setTxnBillingAmount(new BigDecimal("50.25"));
        dtoList.add(dto3);

        return dtoList;
    }

    // 创建测试Record数据的辅助方法
    private List<CardManageFeeRecord> createTestRecordList() {
        List<CardManageFeeRecord> recordList = new ArrayList<>();
        
        CardManageFeeRecord record1 = new CardManageFeeRecord();
        record1.setId("RECORD001");
        record1.setGlobalFlowNumber("FLOW001");
        record1.setCardType("P");
        record1.setTxnBillingAmount(new BigDecimal("100.50"));
        record1.setTxnBillingCurrency("CNY");
        record1.setTxnCardNumber("1111222233334444");
        recordList.add(record1);

        CardManageFeeRecord record2 = new CardManageFeeRecord();
        record2.setId("RECORD002");
        record2.setGlobalFlowNumber("FLOW002");
        record2.setCardType("A");
        record2.setTxnBillingAmount(new BigDecimal("200.75"));
        recordList.add(record2);

        CardManageFeeRecord record3 = new CardManageFeeRecord();
        record3.setId("RECORD003");
        record3.setGlobalFlowNumber("FLOW003");
        record3.setCardType("P");
        record3.setTxnBillingAmount(new BigDecimal("50.25"));
        recordList.add(record3);

        return recordList;
    }
} 