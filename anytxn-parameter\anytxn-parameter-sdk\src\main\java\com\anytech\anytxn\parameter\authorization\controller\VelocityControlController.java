package com.anytech.anytxn.parameter.authorization.controller;

import io.swagger.v3.oas.annotations.tags.Tag;
import io.swagger.v3.oas.annotations.Operation;
import com.anytech.anytxn.common.core.base.PageResultDTO;
import com.anytech.anytxn.common.core.base.AnyTxnHttpResponse;
import com.anytech.anytxn.common.core.base.BizBaseController;
import com.anytech.anytxn.parameter.base.authorization.domain.dto.VelocityControlDTO;
import com.anytech.anytxn.parameter.base.authorization.service.IVelocityControlService;
import com.anytech.anytxn.parameter.base.common.enums.ParameterRepDetailEnum;
import com.anytech.anytxn.parameter.base.common.utils.AnyTxnHttpResponseHelper;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import jakarta.validation.Valid;

/**
 * <AUTHOR>
 * @date 2020-06-23
 */

@RestController
@Tag(name = "流量规则参数")
public class VelocityControlController extends BizBaseController {

    @Autowired
    private IVelocityControlService velocityControlService;

    /**
     * 分页查询流量规则信息
     * @param pageNum 页码
     * @param pageSize 每页大小
     * @return AnyTxnHttpResponse<PageResultDTO<VelocityControlDTO>>
     */
    @GetMapping(value = "/param/velocityControl/pageNum/{pageNum}/pageSize/{pageSize}")
    @Operation(summary="分页查询流量规则信息", description = "需传入页码以及页面大小")
    public AnyTxnHttpResponse<PageResultDTO<VelocityControlDTO>> getPage (@PathVariable(value = "pageNum") Integer pageNum,
                                                                          @PathVariable(value = "pageSize") Integer pageSize,
                                                                          @RequestParam(value = "cardProductCode",required = false) String cardProductCode,
                                                                          @RequestParam(required = false) String organizationNumber) {
        PageResultDTO<VelocityControlDTO> controlServiceByPage = velocityControlService.findByPage(pageNum, pageSize,cardProductCode, organizationNumber);
        return AnyTxnHttpResponse.success(controlServiceByPage);
    }

    /**
     * 根据cardProductCode查询流量规则信息
     * @param cardProductCode 卡片编号
     * @return HttpApiResponse<VelocityControlDTO>
     */
    @Operation(summary="根据卡片编号查询流量规则信息", description = "需传入cardProductCode")
    @GetMapping(value = "/param/velocityControl/cardProductCode/{cardProductCode}")
    public AnyTxnHttpResponse<VelocityControlDTO> getByCardProductCode(@PathVariable String cardProductCode,
                                                                       @RequestParam(required = false) String organizationNumber){
        VelocityControlDTO velocityControlDTO = velocityControlService.findByCardProNum(cardProductCode, organizationNumber);
        return AnyTxnHttpResponse.success(velocityControlDTO);
    }

    /**
     * 新建流量规则信息
     * @param record 流量规则信息请求数据
     * @return HttpApiResponse
     */
    @PostMapping("/param/velocityControl")
    @Operation(summary = "新增流量规则信息")
    public AnyTxnHttpResponse create(@Valid @RequestBody VelocityControlDTO record) {
        velocityControlService.addVelocityControl(record);
        return AnyTxnHttpResponseHelper.succ(ParameterRepDetailEnum.CREATE.message());
    }

    /**
     * 更新流量规则信息
     * @param record 流量规则信息请求数据
     * @return HttpApiResponse
     */
    @PutMapping(value = "/param/velocityControl")
    @Operation(summary="根据id更新流量规则信息")
    public AnyTxnHttpResponse modify(@Valid @RequestBody VelocityControlDTO record) {
        velocityControlService.updateVelocityControl(record);
        return AnyTxnHttpResponseHelper.succ(ParameterRepDetailEnum.UPDATE.message());
    }

    /**
     * 通过id删除流量规则信息
     * @param id 技术id
     * @return HttpApiResponse
     */
    @DeleteMapping(value = "/param/velocityControl/cardProductCode/{cardProductCode}")
    @Operation(summary="根据cardProductCode删除流量规则信息", description = "需传入cardProductCode")
    public AnyTxnHttpResponse remove(@PathVariable String cardProductCode, @RequestParam String organizationNumber) {
        velocityControlService.deleteVelocityControl(cardProductCode, organizationNumber);
        return AnyTxnHttpResponseHelper.succ(ParameterRepDetailEnum.DEL.message());
    }
}
