package com.anytech.anytxn.parameter.common.service.system;

import com.alibaba.fastjson.JSON;
import com.anytech.anytxn.common.core.annotation.db.DeleteParameterAnnotation;
import com.anytech.anytxn.common.core.annotation.db.InsertParameterAnnotation;
import com.anytech.anytxn.common.core.annotation.db.UpdateParameterAnnotation;
import com.anytech.anytxn.business.base.audit.AbstractParameterService;
import com.anytech.anytxn.common.core.base.ParameterCompare;
import com.anytech.anytxn.business.base.audit.ParmModificationRecord;
import com.anytech.anytxn.common.core.base.PageResultDTO;
import com.anytech.anytxn.common.core.utils.BeanMapping;
import com.anytech.anytxn.common.core.utils.OrgNumberUtils;
import com.anytech.anytxn.common.sequence.utils.Number16IdGen;
import com.anytech.anytxn.common.core.utils.TenantUtils;
import com.anytech.anytxn.parameter.base.common.domain.dto.ParmDccFeeInfoDTO;
import com.anytech.anytxn.parameter.base.common.enums.AnyTxnParameterRespCodeEnum;
import com.anytech.anytxn.parameter.base.common.exception.AnyTxnParameterException;
import com.anytech.anytxn.parameter.common.mapper.broadcast.ParmDccFeeInfoMapper;
import com.anytech.anytxn.parameter.common.mapper.broadcast.ParmDccFeeInfoSelfMapper;
import com.anytech.anytxn.parameter.base.common.domain.model.ParmDccFeeInfo;
import com.anytech.anytxn.parameter.base.common.domain.model.system.ParmTransactionCode;
import com.anytech.anytxn.parameter.base.common.service.IDccFeeService;
import com.github.pagehelper.Page;
import com.github.pagehelper.PageHelper;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
//import org.apache.shardingsphere.transaction.annotation.ShardingSphereTransactionType;
//import org.apache.shardingsphere.transaction.core.TransactionType;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import jakarta.annotation.Resource;
import java.time.LocalDateTime;
import java.util.List;

@Service("parm_dcc_fee_serviceImpl")
@Slf4j
public class DccFeeInfoServiceImpl extends AbstractParameterService implements IDccFeeService {

    @Resource
    private Number16IdGen numberIdGenerator;

    @Resource
    private ParmDccFeeInfoMapper parmDccFeeInfoMapper;

    @Resource
    private ParmDccFeeInfoSelfMapper parmDccFeeInfoSelfMapper;

    @Override
    @InsertParameterAnnotation(tableName = "parm_dcc_fee",tableDesc = "Dcc Fee")
    public ParameterCompare add(ParmDccFeeInfoDTO req) {
        boolean isExists = parmDccFeeInfoSelfMapper.isExists(req.getTableId()) > 0;
        if (isExists) {
            log.warn("Dcc费参数已存在, orgNumber={}",
                    OrgNumberUtils.getOrg());
            throw new AnyTxnParameterException(AnyTxnParameterRespCodeEnum.D_DATABASE_EXCEPTION);
        }
        req.setId(numberIdGenerator.generateId(TenantUtils.getTenantId())+"");
        req.setCreateTime(LocalDateTime.now());
        req.setUpdateTime(LocalDateTime.now());
        req.setUpdatedBy("admin");
        req.setVersionNumber("1");
        ParmDccFeeInfo add = BeanMapping.copy(req, ParmDccFeeInfo.class);
        return ParameterCompare
                .getBuilder()
                .withAfter(add)
                .build(ParmDccFeeInfo.class);
    }

    @Override
    @UpdateParameterAnnotation(tableName = "parm_dcc_fee",tableDesc = "Dcc Fee")
    public ParameterCompare modify(ParmDccFeeInfoDTO req) throws AnyTxnParameterException {
        log.info("修改Dcc参数开始");
        if (req.getId() == null) {
            throw new AnyTxnParameterException(AnyTxnParameterRespCodeEnum.P_PARAMETER_ID_EMPTY_FAULT);
        }
        ParmDccFeeInfo parmDccFeeInfo = parmDccFeeInfoMapper.selectByPrimaryKey(req.getId());
        if (parmDccFeeInfo == null) {
            log.error("修改交易码, 通过主键id({})未找到数据", req.getId());
            throw new AnyTxnParameterException(AnyTxnParameterRespCodeEnum.D_QUERY_DCC_FEE_BY_ID_FAULT);
        }

        String oldTableId = parmDccFeeInfo.getTableId();
        String newTableId = req.getTableId();
        if (!oldTableId.equals(newTableId)) {
            boolean isExists = false;
            isExists = parmDccFeeInfoSelfMapper.isExists(req.getTableId()) > 0;
            if (isExists) {
                log.warn("Dcc费参数已存在, orgNumber={}",
                        OrgNumberUtils.getOrg());
                throw new AnyTxnParameterException(AnyTxnParameterRespCodeEnum.D_DATABASE_EXCEPTION);
            }
        }
        // 拷贝修改的数据并更新
        ParmDccFeeInfo modify = BeanMapping.copy(req, ParmDccFeeInfo.class);
        return ParameterCompare
                .getBuilder()
                .withAfter(modify)
                .withBefore(parmDccFeeInfo)
                .build(ParmTransactionCode.class);
    }

    @Override
    @DeleteParameterAnnotation(tableName = "parm_dcc_fee",tableDesc = "Dcc Fee")
    public ParameterCompare remove(String id) throws AnyTxnParameterException {
        if (id == null) {
            throw new AnyTxnParameterException(AnyTxnParameterRespCodeEnum.P_PARAMETER_EMPTY_FAULT);
        }

        ParmDccFeeInfo parmDccFeeInfo = parmDccFeeInfoMapper.selectByPrimaryKey(id);
        if (parmDccFeeInfo == null) {
            log.error("删除DCC参数, 通过主键id({})未找到数据", id);

            throw new AnyTxnParameterException(AnyTxnParameterRespCodeEnum.D_QUERY_DCC_FEE_BY_ID_FAULT);
        }
        return ParameterCompare
                .getBuilder()
                .withBefore(parmDccFeeInfo)
                .build(ParmDccFeeInfo.class);
    }

    @Override
    public ParmDccFeeInfoDTO find(String id) throws AnyTxnParameterException {
        if (id == null) {
            throw new AnyTxnParameterException(AnyTxnParameterRespCodeEnum.P_PARAMETER_EMPTY_FAULT);
        }

        ParmDccFeeInfo parmDccFeeInfo = parmDccFeeInfoMapper.selectByPrimaryKey(id);
        if (parmDccFeeInfo == null) {
            log.error("获取DCC费参数详情, 通过主键id({})未找到数据", id);

            throw new AnyTxnParameterException(AnyTxnParameterRespCodeEnum.D_QUERY_DCC_FEE_BY_ID_FAULT);
        }

        return BeanMapping.copy(parmDccFeeInfo, ParmDccFeeInfoDTO.class);
    }

    @Override
    public List<ParmDccFeeInfoDTO> findListByOrgNumber(String orgNumber, String status) throws AnyTxnParameterException {
        /*List<ParmTransactionCode> typeList = parmDccFeeInfoSelfMapper.selectListByOrgNumber(orgNumber, status, true);
        return BeanMapping.copyList(typeList, ParmDccFeeInfoDTO.class);*/
        return null;
    }

    @Override
    public ParmDccFeeInfoDTO findListByOrgAndTableId(String orgNumber, String tableId) throws AnyTxnParameterException {
        if (StringUtils.isAnyEmpty(orgNumber,tableId)){
            throw new AnyTxnParameterException(AnyTxnParameterRespCodeEnum.P_PARAMETER_EMPTY_FAULT);
        }
        ParmDccFeeInfoDTO parmDccFeeInfoDTO = parmDccFeeInfoMapper.selectByOrgAndTableId(orgNumber, tableId);
        if (ObjectUtils.isNotEmpty(parmDccFeeInfoDTO)){
            return parmDccFeeInfoDTO;
        }
        return new ParmDccFeeInfoDTO();
    }

    @Override
    public PageResultDTO<ParmDccFeeInfoDTO> findPageByOrgNumber(Integer pageNum, Integer pageSize, ParmDccFeeInfoDTO parmDccFeeInfoDTO) throws AnyTxnParameterException {
        log.info("分页查询交易码, pageNum={}, pageSize={}, orgNumber={}, status={}", pageNum, pageSize);

        Page<ParmDccFeeInfo> page = PageHelper.startPage(pageNum, pageSize);
        parmDccFeeInfoDTO.setOrganizationNumber(StringUtils.isEmpty(parmDccFeeInfoDTO.getOrganizationNumber()) ? OrgNumberUtils.getOrg() : parmDccFeeInfoDTO.getOrganizationNumber());
        List<ParmDccFeeInfo> parmDccFeeInfos = parmDccFeeInfoSelfMapper.selectByCondition(parmDccFeeInfoDTO);
        List<ParmDccFeeInfoDTO> listData = BeanMapping.copyList(parmDccFeeInfos, ParmDccFeeInfoDTO.class);

        return new PageResultDTO<>(pageNum, pageSize, page.getTotal(), page.getPages(), listData);
    }

    @Override
    public ParmDccFeeInfoDTO findTransactionCode(String orgNumber, String transactionCode) throws AnyTxnParameterException {
        return null;
    }

    @Override
    public List<ParmDccFeeInfoDTO> findAll(String organizationNumber) {
        List<ParmDccFeeInfo> typeList = parmDccFeeInfoSelfMapper.selectAll(OrgNumberUtils.getOrg(organizationNumber));
        return BeanMapping.copyList(typeList, ParmDccFeeInfoDTO.class);
    }

    @Override
    @Transactional(rollbackFor = Exception.class,propagation = Propagation.REQUIRED)
    //@ShardingSphereTransactionType(TransactionType.XA)
    protected boolean updateDb(ParmModificationRecord parmModificationRecord) throws Exception {
        ParmDccFeeInfo parmDccFeeInfo = JSON.parseObject(parmModificationRecord.getParmBody(), ParmDccFeeInfo.class);
        parmDccFeeInfo.initUpdateDateTime();
        int res = parmDccFeeInfoMapper.updateByPrimaryKeySelective(parmDccFeeInfo);
        return res > 0;
    }

    @Override
    @Transactional(rollbackFor = Exception.class,propagation = Propagation.REQUIRED)
    //@ShardingSphereTransactionType(TransactionType.XA)
    protected boolean insertDb(ParmModificationRecord parmModificationRecord) throws Exception {
        ParmDccFeeInfo parmDccFeeInfo = JSON.parseObject(parmModificationRecord.getParmBody(), ParmDccFeeInfo.class);
        parmDccFeeInfo.initUpdateDateTime();
        parmDccFeeInfo.initCreateDateTime();
        int res = parmDccFeeInfoMapper.insert(parmDccFeeInfo);
        return res > 0;
    }

    @Override
    @Transactional(rollbackFor = Exception.class,propagation = Propagation.REQUIRED)
    //@ShardingSphereTransactionType(TransactionType.XA)
    protected boolean deleteDb(ParmModificationRecord parmModificationRecord) throws Exception {
        ParmDccFeeInfo parmDccFeeInfo = JSON.parseObject(parmModificationRecord.getParmBody(), ParmDccFeeInfo.class);
        int deleteRow = parmDccFeeInfoMapper.deleteByPrimaryKey(parmDccFeeInfo.getId());
        return deleteRow > 0;
    }
}
