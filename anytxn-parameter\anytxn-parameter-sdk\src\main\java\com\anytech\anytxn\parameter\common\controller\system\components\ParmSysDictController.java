package com.anytech.anytxn.parameter.common.controller.system.components;

import com.anytech.anytxn.parameter.base.common.domain.dto.system.ParmSysDictDTO;
import com.anytech.anytxn.parameter.base.common.domain.dto.system.ParmSysDictSimpleResDTO;
import io.swagger.v3.oas.annotations.tags.Tag;
import io.swagger.v3.oas.annotations.Operation;
import com.anytech.anytxn.common.core.base.PageResultDTO;
import com.anytech.anytxn.common.core.base.AnyTxnHttpResponse;
import com.anytech.anytxn.common.core.base.BizBaseController;
import com.anytech.anytxn.parameter.base.common.enums.ParameterRepDetailEnum;
import com.anytech.anytxn.parameter.base.common.service.system.IParmSysDictService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.HashMap;
import java.util.List;

/**
 * 系统字典 api
 * <AUTHOR>
 * @date 2021-01-06
 **/
@Slf4j
@Tag(name = "系统字典")
@RestController
public class ParmSysDictController extends BizBaseController {

    @Autowired
    private IParmSysDictService parmSysDictService;

    /**
     * 通过字典类型ID集获取各字典列表，字典类型ID分组
     * @param typeIds
     * @return
     */
    @Operation(summary = "通过字典类型ID集获取各字典列表，字典类型ID分组")
    @GetMapping(value = "/param/sysDict/getMap")
    public AnyTxnHttpResponse<HashMap<String, List<ParmSysDictSimpleResDTO>>> getByTypeIds(@RequestParam List<String> typeIds) {
        HashMap<String, List<ParmSysDictSimpleResDTO>> sysCodeMap = parmSysDictService.findMapByTypeIds(typeIds);
        return AnyTxnHttpResponse.success(sysCodeMap);
    }

    /**
     * 查询所有字典数据
     * @return
     */
    @Operation(summary = "获取所有字典数据")
    @GetMapping(value = "/param/sysDict/all")
    public AnyTxnHttpResponse<List<ParmSysDictDTO>> selectAll() {
        List<ParmSysDictDTO> list = parmSysDictService.selectAll();
        return AnyTxnHttpResponse.success(list);
    }

    /**
     * 分页查询系统字典类型集合
     * @param searchKey
     * @return
     */
//    @Operation(summary = "分页查询系统字典类型信息",description = "分页查询系统字典类型信息")
//    @PostMapping(value = "/param/sysDictType/findAll")
//    public AnyTxnHttpResponse<PageResultDTO<ParmSysDictDTO>> getPage(@RequestBody(required = false) ParmSysDictSearchKeyDTO searchKey)  {
//        PageResultDTO<ParmSysDictDTO> txnPage = parmSysDictService.findPage(searchKey);
//        return AnyTxnHttpResponse.success(txnPage,"查询成功");
//    }



    @Operation(summary = "分页查询系统字典类型信息",description = "分页查询系统字典类型信息")
    @GetMapping(value = "/param/sysDictType/findAll/pageNum/{pageNum}/pageSize/{pageSize}")
    public AnyTxnHttpResponse<PageResultDTO<ParmSysDictDTO>> getPage(@PathVariable(value = "pageNum") Integer pageNum,
                                                                                       @PathVariable(value = "pageSize") Integer pageSize,
                                                                        @RequestParam(value = "typeId",required = false) String typeId,
                                                                        @RequestParam(value = "typeName",required = false) String typeName) {
        PageResultDTO<ParmSysDictDTO> page = null;
        page = parmSysDictService.findPage(pageNum, pageSize, typeId, typeName);
        return AnyTxnHttpResponse.success(page);
    }

    /**
     * 根据父id获取字典表数据，只查当前父id下的那一级
     * @param pid
     * @return
     */
    @Operation(summary = "根据父id获取字典表数据,只查一级")
    @GetMapping(value = "/param/sysDict/pid/{pid}")
    public AnyTxnHttpResponse<List<ParmSysDictDTO>> selectListByPid(@PathVariable(value = "pid") String pid) {
        List<ParmSysDictDTO> resultList = parmSysDictService.selectListByPid(pid);
        return AnyTxnHttpResponse.success(resultList, ParameterRepDetailEnum.QUERY.message());
    }

    /**
     * 根据字典类型获取字典表数据
     * @param typeId
     * @return
     */
    @Operation(summary = "根据字典类型获取字典数据,包括该类型下的所有层级字典数据")
    @GetMapping(value = "/param/sysDict/typeId/{typeId}")
    public AnyTxnHttpResponse<List<ParmSysDictDTO>> selectListByTypeId(@PathVariable(value = "typeId") String typeId) {
        List<ParmSysDictDTO> resultList = parmSysDictService.selectListByTypeId(typeId);
        return AnyTxnHttpResponse.success(resultList,ParameterRepDetailEnum.QUERY.message());
    }

    /**
     * 根据字典数据表id获取字典表数据
     * @param id
     * @return
     */
    @Operation(summary = "根据字典数据表id获取字典表数据")
    @GetMapping(value = "/param/sysDict/id/{id}")
    public AnyTxnHttpResponse<ParmSysDictDTO> getByPrimaryId(@PathVariable(value = "id") String id) {
        ParmSysDictDTO sysCode = parmSysDictService.findSysDictById(id);
        return AnyTxnHttpResponse.success(sysCode,ParameterRepDetailEnum.QUERY.message());
    }

    /**
     * 新增字典表数据
     * @param parmSysDictDTO
     * @return
     */
    @Operation(summary = "新增字典表数据")
    @PostMapping(value = "/param/sysDict")
    public AnyTxnHttpResponse<Object> addSysDict(@RequestBody ParmSysDictDTO parmSysDictDTO) {
        return AnyTxnHttpResponse.success(parmSysDictService.addParmSysDict(parmSysDictDTO),ParameterRepDetailEnum.CREATE.message());
    }

    /**
     * 修改字典表数据
     * @param parmSysDictDTO
     * @return
     */
    @Operation(summary = "修改字典表数据")
    @PutMapping(value = "/param/sysDict")
    public AnyTxnHttpResponse<Object> modifySysCode(@RequestBody ParmSysDictDTO parmSysDictDTO) {
        return AnyTxnHttpResponse.success(parmSysDictService.modifyParmSysDict(parmSysDictDTO),ParameterRepDetailEnum.UPDATE.message());
    }

    /**
     * 根据字典数据表id删除字典表数据
     * @param id
     * @return
     */
    @Operation(summary = "根据字典数据表id删除字典表数据")
    @DeleteMapping(value = "/param/sysDict/id/{id}")
    public AnyTxnHttpResponse<Object> deleteSysCode(@PathVariable(value = "id") String id) {
        return AnyTxnHttpResponse.success(parmSysDictService.deleteParmSysDict(id),ParameterRepDetailEnum.DEL.message());
    }

}
