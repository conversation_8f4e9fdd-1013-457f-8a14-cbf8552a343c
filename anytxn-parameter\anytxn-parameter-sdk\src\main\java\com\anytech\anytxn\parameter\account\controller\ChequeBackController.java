package com.anytech.anytxn.parameter.account.controller;

import com.anytech.anytxn.common.core.base.ParameterCompare;
import com.anytech.anytxn.common.core.base.PageResultDTO;
import com.anytech.anytxn.common.core.base.AnyTxnHttpResponse;
import com.anytech.anytxn.common.core.base.BizBaseController;
import com.anytech.anytxn.parameter.base.account.domain.dto.ChequeBackDTO;
import com.anytech.anytxn.parameter.base.account.domain.dto.ChequeBackResDTO;
import com.anytech.anytxn.parameter.base.account.service.IParmChequeBackService;
import com.anytech.anytxn.parameter.base.common.enums.ParameterRepDetailEnum;
import io.swagger.v3.oas.annotations.tags.Tag;
import io.swagger.v3.oas.annotations.Operation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import jakarta.validation.Valid;
import java.math.BigDecimal;
import java.util.ArrayList;

/**
 * Description    退票费用参数管理
 * Copyright:	Copyright (c) 2021
 * Company:		江融信
 * Author:		liurui
 * Version:		1.0
 * Create at:	2021/10/23 2:38 PM
 *
 * 修改历史:
 * 日期    作者    版本  修改描述
 * ------------------------------------------------------------------
 *
 **/
@Tag(name = "退票费用参数定义")
@RestController
public class ChequeBackController extends BizBaseController {
    @Autowired
    private IParmChequeBackService parmChequeBackService;

    /**
     * 根据机构号（organizationNumber），
     * 费用请求参数表id（tableId），获得费用请求参数表信息
     *
     * @param organizationNumber 机构号
     * @param tableId 表Id
     * @return
     */
    @Operation(summary = "通过机构号和表Id查询退票费用参数信息", description = "通过机构号和表Id查询")
    @GetMapping(value = "/param/chequeBack/organizationNumber/{organizationNumber}/tableId/{tableId}")
    public AnyTxnHttpResponse<ChequeBackResDTO> getChequeBackInfo(@PathVariable String organizationNumber, @PathVariable String tableId) {
        ChequeBackResDTO res = parmChequeBackService.findByTableIdAndOrgNo(tableId,organizationNumber);
        return AnyTxnHttpResponse.success(res);
    }

    /**
     * 创建退票费用信息
     * @param chequeBackDTO 退票费用请求数据
     * @return HttpApiResponse<ChequeBackDTO>
     */
    @PostMapping(value = "/param/chequeBack")
    @Operation(summary = "创建退票费用信息")
    public AnyTxnHttpResponse<Object> create(@Valid @RequestBody ChequeBackDTO chequeBackDTO) {
        ParameterCompare chequeBackResDTO = parmChequeBackService.add(chequeBackDTO);
        return AnyTxnHttpResponse.success(chequeBackResDTO, ParameterRepDetailEnum.CREATE.message());
    }

    /**
     * 更新退票费用信息
     * @param chequeBackDTO 退票费用请求数据
     * @return HttpApiResponse<ChequeBackDTO>
     */
    @PutMapping(value = "/param/chequeBack")
    @Operation(summary="根据id更新退票费用信息", description = "")
    public AnyTxnHttpResponse<Object> modify(@Valid @RequestBody ChequeBackDTO chequeBackDTO) {
        ParameterCompare chequeBackResDTO = parmChequeBackService.modify(chequeBackDTO);
        return AnyTxnHttpResponse.success(chequeBackResDTO,ParameterRepDetailEnum.UPDATE.message());
    }

    /**
     * 删除退票费用信息，通过id
     * @param id 技术id
     * @return HttpApiResponse<Boolean>
     */
    @DeleteMapping(value = "/param/chequeBack/id/{id}")
    @Operation(summary="根据id删除", description = "需传入id")
    public AnyTxnHttpResponse<Object> remove(@PathVariable String id) {
        ParameterCompare bool = parmChequeBackService.remove(id);
        return AnyTxnHttpResponse.success(bool,ParameterRepDetailEnum.DEL.message());
    }
//加一个根据tableID和机构号删除的方法
    /**
     * 删除退票费用信息，通过参数表id和机构号
     * @param tableId 参数表
     * @param orgNum 机构号
     * @return HttpApiResponse<Boolean>
     */
    @DeleteMapping(value = "/param/chequeBack/tableId/{tableId}/organizationNumber/{orgNum}")
    @Operation(summary="根据参数表id和机构号删除", description = "需传入tableId和机构号")
    public AnyTxnHttpResponse<Object> remove(@PathVariable String tableId, @PathVariable String orgNum) {
        ParameterCompare bool = parmChequeBackService.remove(tableId,orgNum);
        return AnyTxnHttpResponse.success(bool,ParameterRepDetailEnum.DEL.message());
    }

    /**
     * 根据根据id获取详情，进行编辑
     * @param id id
     * @return HttpApiResponse<ChequeBackResDTO>
     */
    @Operation(summary = "获取退票费用详情，通过id", description = "")
    @GetMapping(value = "/param/chequeBack/id/{id}")
    public AnyTxnHttpResponse<ChequeBackResDTO> getByIndex(@PathVariable String id){
        ChequeBackResDTO chequeBackResDTO = parmChequeBackService.find(id);
        return AnyTxnHttpResponse.success(chequeBackResDTO);
    }
// 加一个根据tableID和机构号联合查询的方法

    /**
     * 根据根据tableId和机构号获取详情，进行编辑
     * @param tableId 参数表id
     * @param orgNum 机构号
     * @return HttpApiResponse<ProductInfoRes>
     */
    @Operation(summary = "获取退票费用详情，通过tableId和orgNum", description = "")
    @GetMapping(value = "/param/chequeBack/tableId/{tableId}/organizationNumber/{orgNum}")
    public AnyTxnHttpResponse<ChequeBackResDTO> getByIndex(@PathVariable String tableId,@PathVariable String orgNum){
        ChequeBackResDTO chequeBackResDTO = parmChequeBackService.find(tableId,orgNum);
        return AnyTxnHttpResponse.success(chequeBackResDTO);
    }

    /**
     * 查询所有退票分配定义参数信息
     *
     * @return TxnTypeControlRes
     */
    @Operation(summary = "支票退票参数分页查询", description = "分页查询")
    @GetMapping(value = "/param/chequeBack/pageNum/{pageNum}/pageSize/{pageSize}")
    public AnyTxnHttpResponse<PageResultDTO<ChequeBackResDTO>> getAllPage(@PathVariable(value = "pageNum") Integer pageNum,
                                                                                @PathVariable(value = "pageSize") Integer pageSize,
                                                                                @RequestParam(value = "tableId",required = false) String tableId,
                                                                                @RequestParam(value = "description",required = false) String description,
                                                                                @RequestParam(value = "feeIndicator",required = false) String feeIndicator,
                                                                                @RequestParam(value = "status",required = false) String status,
                                                                                @RequestParam(value = "transactionCode",required = false) String transactionCode,
                                                                                @RequestParam(value = "interestIndicator",required = false) String interestIndicator,
                                                                                @RequestParam(value = "fixedFee",required = false) BigDecimal fixedFee,
                                                                                @RequestParam(required = false) String organizationNumber) {
        PageResultDTO<ChequeBackResDTO> response;
        response = parmChequeBackService.findAll(pageNum, pageSize, tableId, description, feeIndicator, status, transactionCode, interestIndicator, fixedFee, organizationNumber);
        return AnyTxnHttpResponse.success(response);
    }

    /**
     * 通过表状态查询退票费用参数
     *
     * @param status 参数表状态
     * @return
     */
    @Operation(summary = "通过表状态查询退票费用参数", description = "通过表状态查询退票费用参数")
    @GetMapping(value = "/param/chequeBack/status/{status}")
    public AnyTxnHttpResponse<ArrayList<ChequeBackResDTO>> getByStatus(@PathVariable String status) {
        ArrayList<ChequeBackResDTO> resList = (ArrayList)parmChequeBackService.findByStatus(status);
        return AnyTxnHttpResponse.success(resList);
    }
}
