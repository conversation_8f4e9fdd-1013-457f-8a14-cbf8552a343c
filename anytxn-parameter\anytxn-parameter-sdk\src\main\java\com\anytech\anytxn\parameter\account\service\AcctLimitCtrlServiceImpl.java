package com.anytech.anytxn.parameter.account.service;

import com.alibaba.fastjson.JSON;
import com.anytech.anytxn.business.base.audit.AbstractParameterService;
import com.anytech.anytxn.business.base.audit.ParmModificationRecord;
import com.anytech.anytxn.common.core.annotation.db.DeleteParameterAnnotation;
import com.anytech.anytxn.common.core.annotation.db.InsertParameterAnnotation;
import com.anytech.anytxn.common.core.annotation.db.UpdateParameterAnnotation;
import com.anytech.anytxn.common.core.base.PageResultDTO;
import com.anytech.anytxn.common.core.base.ParameterCompare;
import com.anytech.anytxn.common.core.utils.BeanMapping;
import com.anytech.anytxn.common.core.utils.OrgNumberUtils;
import com.anytech.anytxn.common.core.utils.TenantUtils;
import com.anytech.anytxn.common.sequence.utils.Number16IdGen;
import com.anytech.anytxn.parameter.account.mapper.ParmAcctLimitCtrlMapper;
import com.anytech.anytxn.parameter.account.mapper.ParmAcctLimitCtrlSelfMapper;
import com.anytech.anytxn.parameter.base.account.domain.dto.AcctLimitCtrlDefDTO;
import com.anytech.anytxn.parameter.base.account.domain.dto.AcctLimitCtrlReqDTO;
import com.anytech.anytxn.parameter.base.account.domain.dto.AcctLimitCtrlResDTO;
import com.anytech.anytxn.parameter.base.account.domain.model.ParmAcctLimitCtrl;
import com.anytech.anytxn.parameter.base.account.service.IAcctLimitCtrlService;
import com.anytech.anytxn.parameter.base.common.constants.Constants;
import com.anytech.anytxn.parameter.base.common.enums.AnyTxnParameterRespCodeEnum;
import com.anytech.anytxn.parameter.base.common.enums.ParameterRepDetailEnum;
import com.anytech.anytxn.parameter.base.common.exception.AnyTxnParameterException;
import com.github.pagehelper.Page;
import com.github.pagehelper.PageHelper;
import com.google.common.collect.Iterators;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;
import org.springframework.util.ObjectUtils;
import org.springframework.util.StringUtils;

import java.time.LocalDateTime;
import java.util.*;

/**
 * 账户额度控制 业务接口
 *
 * <AUTHOR>
 * @date 2020-11-30
 */
@Service(value = "parm_acct_limit_ctrl_serviceImpl")
public class AcctLimitCtrlServiceImpl extends AbstractParameterService implements IAcctLimitCtrlService {

    private final Logger log = LoggerFactory.getLogger(AcctLimitCtrlServiceImpl.class);
    @Autowired
    private ParmAcctLimitCtrlMapper parmAcctLimitCtrlMapper;
    @Autowired
    private ParmAcctLimitCtrlSelfMapper parmAcctLimitCtrlSelfMapper;
    @Autowired
    private Number16IdGen numberIdGenerator;

    @Override
    public PageResultDTO<AcctLimitCtrlResDTO> findAll(Integer pageNum, Integer pageSize, AcctLimitCtrlReqDTO acctLimitCtrlReq) {
        if (null == acctLimitCtrlReq) {
            acctLimitCtrlReq = new AcctLimitCtrlReqDTO();
        }
        acctLimitCtrlReq.setOrganizationNumber(StringUtils.isEmpty(acctLimitCtrlReq) ? OrgNumberUtils.getOrg() : acctLimitCtrlReq.getOrganizationNumber());
        Page<ParmAcctLimitCtrl> page = PageHelper.startPage(pageNum, pageSize);
        List<ParmAcctLimitCtrl> acctLimitCtrlList = parmAcctLimitCtrlSelfMapper.selectByCondition(acctLimitCtrlReq);
        List<AcctLimitCtrlResDTO> res = BeanMapping.copyList(acctLimitCtrlList, AcctLimitCtrlResDTO.class);
        return new PageResultDTO<>(pageNum, pageSize, page.getTotal(), page.getPages(), res);
    }

    @Override
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = Exception.class)
    @InsertParameterAnnotation(tableName = "parm_acct_limit_ctrl", tableDesc = "Accounting Limit Ctrl", isJoinTable = true)
    public ParameterCompare addAcctLimitCtrl(AcctLimitCtrlReqDTO acctLimitCtrlReqDTO) {
        if (Objects.isNull(acctLimitCtrlReqDTO.getAcctLimitCtrlId())) {
            throw new AnyTxnParameterException(AnyTxnParameterRespCodeEnum.P_PARAMETER_NOT_EMPTY_FAULT);
        }
        //如果有账户额度控制信息，判断是否有重复
        if (!CollectionUtils.isEmpty(acctLimitCtrlReqDTO.getAcctLimitCtrlDefDTOList())) {
            Set<String> productInfoDtoSet = new HashSet<>();
            for (AcctLimitCtrlDefDTO acctLimitCtrlDefDTO : acctLimitCtrlReqDTO.getAcctLimitCtrlDefDTOList()) {
                productInfoDtoSet.add(acctLimitCtrlDefDTO.getLimitTypeCode());
            }
            int listSize = acctLimitCtrlReqDTO.getAcctLimitCtrlDefDTOList().size();
            int setSize = productInfoDtoSet.size();
            if (listSize != setSize) {
                throw new AnyTxnParameterException(AnyTxnParameterRespCodeEnum.D_PARAM_REPEAT_FAULT, ParameterRepDetailEnum.LIMIT_TYPE_EXIST);
            }
        }
        List<ParmAcctLimitCtrl> acctLimitCtrlList = parmAcctLimitCtrlSelfMapper.selectByOrgAndAcctId(acctLimitCtrlReqDTO.getOrganizationNumber(),
                acctLimitCtrlReqDTO.getAcctLimitCtrlId());
        if (!CollectionUtils.isEmpty(acctLimitCtrlList)) {
            throw new AnyTxnParameterException(AnyTxnParameterRespCodeEnum.D_DATA_ALREADY_EXISTS_FAULT, ParameterRepDetailEnum.ACCOUNT_LIMIT_EXIST);
        }
        acctLimitCtrlReqDTO.setId(String.valueOf(numberIdGenerator.generateId(TenantUtils.getTenantId())));
        return ParameterCompare.getBuilder()
                .withMainParmId(acctLimitCtrlReqDTO.getAcctLimitCtrlId())
                .withAfter(acctLimitCtrlReqDTO)
                .build(AcctLimitCtrlReqDTO.class);
    }

    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = Exception.class)
    void insertBatch(AcctLimitCtrlReqDTO acctLimitCtrlReqDTO) {
        if (!CollectionUtils.isEmpty(acctLimitCtrlReqDTO.getAcctLimitCtrlDefDTOList())) {
            List<ParmAcctLimitCtrl> list = new ArrayList<>();
            for (AcctLimitCtrlDefDTO acctLimitCtrlDefDTO : acctLimitCtrlReqDTO.getAcctLimitCtrlDefDTOList()) {
                ParmAcctLimitCtrl parmAcctLimitCtrl = new ParmAcctLimitCtrl();
                parmAcctLimitCtrl.setId(String.valueOf(numberIdGenerator.generateId(TenantUtils.getTenantId())));
                parmAcctLimitCtrl.setOrganizationNumber(acctLimitCtrlReqDTO.getOrganizationNumber());
                parmAcctLimitCtrl.setAcctLimitCtrlId(acctLimitCtrlReqDTO.getAcctLimitCtrlId());
                parmAcctLimitCtrl.setCreateTime(LocalDateTime.now());
                parmAcctLimitCtrl.setLimitTypeCode(acctLimitCtrlDefDTO.getLimitTypeCode());
                parmAcctLimitCtrl.setMaxLimitAmount(acctLimitCtrlDefDTO.getMaxLimitAmount());
                if (!StringUtils.isEmpty(acctLimitCtrlDefDTO.getCreditFormula())) {
                    parmAcctLimitCtrl.setCreditFormula(acctLimitCtrlDefDTO.getCreditFormula());
                } else if (StringUtils.isEmpty(acctLimitCtrlDefDTO.getFixedFormula())) {
                    parmAcctLimitCtrl.setCreditFormula(acctLimitCtrlDefDTO.getTempFormula());
                } else {
                    parmAcctLimitCtrl.setCreditFormula(acctLimitCtrlDefDTO.getFixedFormula());
                }
                parmAcctLimitCtrl.setOverlimitPercent(acctLimitCtrlDefDTO.getOverlimitPercent());
                parmAcctLimitCtrl.setOverlimitCountPerCycle(acctLimitCtrlDefDTO.getOverlimitCountPerCycle());
                parmAcctLimitCtrl.setDescription(acctLimitCtrlReqDTO.getDescription());
                parmAcctLimitCtrl.setUpdateBy(Constants.DEFAULT_UPDATE_BY);
                parmAcctLimitCtrl.setCreateTime(LocalDateTime.now());
                parmAcctLimitCtrl.setUpdateTime(LocalDateTime.now());
                parmAcctLimitCtrl.setVersionNumber(1L);
                list.add(parmAcctLimitCtrl);
            }
            try {
                parmAcctLimitCtrlSelfMapper.insertBatch(list);
            } catch (Exception e) {
                log.error("批量插入账户额度控制表失败", e);
                throw new AnyTxnParameterException(AnyTxnParameterRespCodeEnum.D_DATABASE_UPDATE_ERROR, ParameterRepDetailEnum.BATCH_ACCOUNT_LIMIT);
            }
        }
    }

    @Override
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = Exception.class)
    @UpdateParameterAnnotation(tableName = "parm_acct_limit_ctrl", tableDesc = "Accounting Limit Ctrl", isJoinTable = true)
    public ParameterCompare modifyAcctLimitCtrl(AcctLimitCtrlReqDTO acctLimitCtrlReqDTO) {
        List<AcctLimitCtrlDefDTO> acctLimitCtrlDefDTOList = acctLimitCtrlReqDTO.getAcctLimitCtrlDefDTOList();
        if (CollectionUtils.isEmpty(acctLimitCtrlDefDTOList)) {
            log.error("参数不能为空");
            throw new AnyTxnParameterException(AnyTxnParameterRespCodeEnum.P_PARAMETER_EMPTY_FAULT);
        }
        //如果有账户额度控制信息，判断是否有重复
        Set<String> productInfoDtoSet = new HashSet<>();
        for (AcctLimitCtrlDefDTO acctLimitCtrlDefDTO : acctLimitCtrlDefDTOList) {
            productInfoDtoSet.add(acctLimitCtrlDefDTO.getLimitTypeCode());
        }
        int listSize = acctLimitCtrlDefDTOList.size();
        int setSize = productInfoDtoSet.size();
        if (listSize != setSize) {
            throw new AnyTxnParameterException(AnyTxnParameterRespCodeEnum.D_PARAM_REPEAT_FAULT, ParameterRepDetailEnum.LIMIT_TYPE_EXIST);
        }
        AcctLimitCtrlResDTO byId = findById(acctLimitCtrlReqDTO.getId());
        if (null == byId) {
            log.error("账户额度控制参数数据不存在");
            throw new AnyTxnParameterException(AnyTxnParameterRespCodeEnum.D_GET_IS_NULL_FAULT, ParameterRepDetailEnum.ACCOUNT_LIMIT_NOT_EXIST);
        }
        return ParameterCompare.getBuilder()
                .withMainParmId(acctLimitCtrlReqDTO.getAcctLimitCtrlId())
                .withAfter(acctLimitCtrlReqDTO)
                .withBefore(byId)
                .build(AcctLimitCtrlReqDTO.class);
    }

    @Override
    public Boolean removeAcctLimitCtrl(String id) {
        ParmAcctLimitCtrl acctLimitCtrl = new ParmAcctLimitCtrl();
        if (null != id) {
            acctLimitCtrl = parmAcctLimitCtrlMapper.selectByPrimaryKey(id);
        }
        if (null == acctLimitCtrl) {
            log.error("账户额度控制参数数据不存在");
            throw new AnyTxnParameterException(AnyTxnParameterRespCodeEnum.D_GET_IS_NULL_FAULT, ParameterRepDetailEnum.ACCOUNT_LIMIT_NOT_EXIST);
        }
        int i = parmAcctLimitCtrlMapper.deleteByPrimaryKey(id);
        return i > 0;
    }

    @Override
    public AcctLimitCtrlResDTO findById(String id) {
        if (id == null) {
            log.error("id不能为空");
            throw new AnyTxnParameterException(AnyTxnParameterRespCodeEnum.P_PARAMETER_ID_EMPTY_FAULT, ParameterRepDetailEnum.ID_NULL);
        }
        ParmAcctLimitCtrl acctLimitCtrl = parmAcctLimitCtrlMapper.selectByPrimaryKey(id);
        return BeanMapping.copy(acctLimitCtrl, AcctLimitCtrlResDTO.class);
    }

    @Override
    public AcctLimitCtrlResDTO findByOrgNumAndAcctLimitCtrlId(String orgNum, String acctLimitCtrlId) {
        AcctLimitCtrlResDTO acctLimitCtrlResDTO = null;
        if (ObjectUtils.isEmpty(orgNum)) {
            throw new AnyTxnParameterException(AnyTxnParameterRespCodeEnum.P_PARAMETER_EMPTY_FAULT, ParameterRepDetailEnum.OTG_NULL);
        }
        if (ObjectUtils.isEmpty(acctLimitCtrlId)) {
            throw new AnyTxnParameterException(AnyTxnParameterRespCodeEnum.P_PARAMETER_EMPTY_FAULT, ParameterRepDetailEnum.ID_NULL);
        }
        List<ParmAcctLimitCtrl> acctLimitCtrlList = parmAcctLimitCtrlSelfMapper.selectByOrgAndAcctId(orgNum, acctLimitCtrlId);
        if (!CollectionUtils.isEmpty(acctLimitCtrlList)) {
            acctLimitCtrlResDTO = BeanMapping.copy(acctLimitCtrlList.get(0), AcctLimitCtrlResDTO.class);
            List<AcctLimitCtrlDefDTO> limitCtrlDefDTOS = new ArrayList<>();
            for (ParmAcctLimitCtrl acctLimitCtrl : acctLimitCtrlList) {
                AcctLimitCtrlDefDTO acctLimitCtrlDefDTO = new AcctLimitCtrlDefDTO();
                acctLimitCtrlDefDTO.setLimitTypeCode(acctLimitCtrl.getLimitTypeCode());
                acctLimitCtrlDefDTO.setMaxLimitAmount(acctLimitCtrl.getMaxLimitAmount());
                acctLimitCtrlDefDTO.setCreditFormula(acctLimitCtrl.getCreditFormula());
                acctLimitCtrlDefDTO.setOverlimitPercent(acctLimitCtrl.getOverlimitPercent());
                acctLimitCtrlDefDTO.setOverlimitCountPerCycle(acctLimitCtrl.getOverlimitCountPerCycle());
                if (!StringUtils.isEmpty(acctLimitCtrl.getCreditFormula())) {
                    acctLimitCtrlDefDTO.setCreditFormulaFlag("1");
                }
                limitCtrlDefDTOS.add(acctLimitCtrlDefDTO);
            }
            acctLimitCtrlResDTO.setAcctLimitCtrlDefDTOList(limitCtrlDefDTOS);
        }
        return acctLimitCtrlResDTO;
    }

    @Override
    @DeleteParameterAnnotation(tableName = "parm_acct_limit_ctrl", tableDesc = "Accounting Limit Ctrl", isJoinTable = true)
    public ParameterCompare removeAcctLimitCtrlByOrgAndAcctId(String orgNum, String acctLimitCtrlId) {
        if (ObjectUtils.isEmpty(orgNum)) {
            throw new AnyTxnParameterException(AnyTxnParameterRespCodeEnum.P_PARAMETER_EMPTY_FAULT, ParameterRepDetailEnum.OTG_NULL);
        }
        if (ObjectUtils.isEmpty(acctLimitCtrlId)) {
            throw new AnyTxnParameterException(AnyTxnParameterRespCodeEnum.P_PARAMETER_EMPTY_FAULT, ParameterRepDetailEnum.ID_NULL);
        }
        List<ParmAcctLimitCtrl> acctLimitCtrlList = parmAcctLimitCtrlSelfMapper.selectByOrgAndAcctId(orgNum, acctLimitCtrlId);
        if (CollectionUtils.isEmpty(acctLimitCtrlList)) {
            log.error("账户额度控制参数数据不存在");
            throw new AnyTxnParameterException(AnyTxnParameterRespCodeEnum.D_GET_IS_NULL_FAULT, ParameterRepDetailEnum.ACCOUNT_LIMIT_NOT_EXIST);
        }
        return ParameterCompare.getBuilder()
                .withMainParmId(acctLimitCtrlId)
                .withBefore(acctLimitCtrlList.get(0))
                .build(ParmAcctLimitCtrl.class);
    }

    @Override
    @Transactional(rollbackFor = Exception.class, propagation = Propagation.REQUIRED)
    //@ShardingSphereTransactionType(TransactionType.XA)
    protected boolean updateDb(ParmModificationRecord parmModificationRecord) {
        AcctLimitCtrlReqDTO acctLimitCtrlReqDTO = JSON.parseObject(parmModificationRecord.getParmBody(), AcctLimitCtrlReqDTO.class);
        List<ParmAcctLimitCtrl> acctLimitCtrlList = parmAcctLimitCtrlSelfMapper.selectByOrgAndAcctId(acctLimitCtrlReqDTO.getOrganizationNumber(),
                acctLimitCtrlReqDTO.getAcctLimitCtrlId());
        if (!CollectionUtils.isEmpty(acctLimitCtrlList)) {
            boolean b = Iterators.elementsEqual(acctLimitCtrlReqDTO.getAcctLimitCtrlDefDTOList().iterator(), acctLimitCtrlList.iterator());
            if (b) {
                log.error("账户额度控制参数数据已存在，id:{}", acctLimitCtrlList.get(0).getId());
                throw new AnyTxnParameterException(AnyTxnParameterRespCodeEnum.P_PARAMETER_DATA_EXIST, ParameterRepDetailEnum.ACCOUNT_LIMIT_EXIST);
            } else {
                for (ParmAcctLimitCtrl parmAcctLimitCtrl : acctLimitCtrlList) {
                    parmAcctLimitCtrlSelfMapper.deleteByOrgAndAcctLimitCtrlId(parmAcctLimitCtrl.getOrganizationNumber(), parmAcctLimitCtrl.getAcctLimitCtrlId(), parmAcctLimitCtrl.getLimitTypeCode());
                }
            }
        }
        insertBatch(acctLimitCtrlReqDTO);
        return true;
    }

    @Override
    @Transactional(rollbackFor = Exception.class, propagation = Propagation.REQUIRED)
    //@ShardingSphereTransactionType(TransactionType.XA)
    protected boolean insertDb(ParmModificationRecord parmModificationRecord) {
        AcctLimitCtrlReqDTO acctLimitCtrlReqDTO = JSON.parseObject(parmModificationRecord.getParmBody(), AcctLimitCtrlReqDTO.class);
        insertBatch(acctLimitCtrlReqDTO);
        return true;
    }

    @Override
    @Transactional(rollbackFor = Exception.class, propagation = Propagation.REQUIRED)
    //@ShardingSphereTransactionType(TransactionType.XA)
    protected boolean deleteDb(ParmModificationRecord parmModificationRecord) {
        ParmAcctLimitCtrl parmAcctLimitCtrl = JSON.parseObject(parmModificationRecord.getParmBody(), ParmAcctLimitCtrl.class);
        int i = parmAcctLimitCtrlSelfMapper.deleteByOrgAndAcctId(parmAcctLimitCtrl.getOrganizationNumber(), parmAcctLimitCtrl.getAcctLimitCtrlId());
        return i > 0;
    }
}
