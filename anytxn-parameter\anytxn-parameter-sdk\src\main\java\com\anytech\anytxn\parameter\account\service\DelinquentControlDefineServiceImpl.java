package com.anytech.anytxn.parameter.account.service;

import com.alibaba.fastjson.JSON;
import com.github.pagehelper.Page;
import com.github.pagehelper.PageHelper;
import com.anytech.anytxn.common.core.annotation.db.DeleteParameterAnnotation;
import com.anytech.anytxn.common.core.annotation.db.InsertParameterAnnotation;
import com.anytech.anytxn.common.core.annotation.db.UpdateParameterAnnotation;
import com.anytech.anytxn.business.base.audit.AbstractParameterService;
import com.anytech.anytxn.common.core.base.ParameterCompare;
import com.anytech.anytxn.business.base.audit.ParmModificationRecord;
import com.anytech.anytxn.common.core.utils.OrgNumberUtils;
import com.anytech.anytxn.parameter.base.common.constants.Constants;
import com.anytech.anytxn.common.core.base.PageResultDTO;
import com.anytech.anytxn.common.core.utils.BeanMapping;
import com.anytech.anytxn.common.sequence.utils.Number16IdGen;
import com.anytech.anytxn.common.core.utils.TenantUtils;
import com.anytech.anytxn.parameter.base.account.domain.dto.DelinquentControlDefineReqDTO;
import com.anytech.anytxn.parameter.base.account.domain.dto.DelinquentControlDefineResDTO;
import com.anytech.anytxn.parameter.base.account.domain.dto.DelinquentControlReqDTO;
import com.anytech.anytxn.parameter.base.account.domain.dto.DelinquentControlResDTO;
import com.anytech.anytxn.parameter.base.account.service.IDelinquentControlDefineService;
import com.anytech.anytxn.parameter.base.common.enums.AnyTxnParameterRespCodeEnum;
import com.anytech.anytxn.parameter.base.common.exception.AnyTxnParameterException;
import com.anytech.anytxn.parameter.account.mapper.ParmDelinquentControlDefineMapper;
import com.anytech.anytxn.parameter.account.mapper.ParmDelinquentControlDefineSelfMapper;
import com.anytech.anytxn.parameter.account.mapper.ParmDelinquentControlSelfMapper;
import com.anytech.anytxn.parameter.base.account.domain.model.ParmDelinquentControl;
import com.anytech.anytxn.parameter.base.account.domain.model.ParmDelinquentControlDefine;
//import org.apache.shardingsphere.transaction.annotation.ShardingSphereTransactionType;
//import org.apache.shardingsphere.transaction.core.TransactionType;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.StringUtils;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;

/**
 * Description   延滞控制参数定义 业务接口实现
 * Copyright:	Copyright (c) 2018
 * Company:		江融信
 * Author:		lichao
 * Version:		1.0
 * Create at:	2018/9/21 上午10:43
 * <p>
 * 修改历史:
 * 日期    作者    版本  修改描述
 * ------------------------------------------------------------------
 **/
@Service(value = "parm_delinquent_control_define_serviceImpl")
public class DelinquentControlDefineServiceImpl extends AbstractParameterService implements IDelinquentControlDefineService {

    private Logger log = LoggerFactory.getLogger(getClass());

    @Autowired
    private ParmDelinquentControlDefineMapper parmDelinquentControlDefineMapper;
    @Autowired
    private ParmDelinquentControlDefineSelfMapper parmDelinquentControlDefineSelfMapper;
    @Autowired
    private ParmDelinquentControlSelfMapper parmDelinquentControlSelfMapper;

    @Autowired
    private Number16IdGen numberIdGenerator;

    private static final Integer BADCYCLEDUE_5 = 5;

    private static final Integer BADCYCLEDUE_15 = 15;

    /**
     * 通过机构号,tableId查询延滞控制参数
     *
     * @param orgNum  机构号
     * @param tableId 表id
     * @return List<DelinquentControlDefineRes> 延滞控制参数响应参数list
     */
    @Override
    public DelinquentControlDefineResDTO findDelinquentControlDefine(String orgNum, String tableId) {
        ParmDelinquentControlDefine delinquentControlDefine =
                parmDelinquentControlDefineSelfMapper.selectByOrgAndTableId(orgNum, tableId);
        if (delinquentControlDefine == null) {
            log.error("findDelinquentControlDefine({},{}) selectByOrgAndTableId result is null ", orgNum, tableId);
            throw new AnyTxnParameterException(AnyTxnParameterRespCodeEnum.D_GET_IS_NULL_FAULT);
        }
        return BeanMapping.copy(delinquentControlDefine, DelinquentControlDefineResDTO.class);
    }

    /**
     * 查询所有延滞控制定义参数
     *
     * @param pageNum
     * @param pageSize
     * @return 延滞控制定义响应参数
     * @throws AnyTxnParameterException
     */
    @Override
    public PageResultDTO<DelinquentControlDefineResDTO> findAll(Integer pageNum, Integer pageSize, DelinquentControlDefineReqDTO delinquentControlDefineReqDTO) {
        if (null == delinquentControlDefineReqDTO) {
            delinquentControlDefineReqDTO = new DelinquentControlDefineReqDTO();
        }
        delinquentControlDefineReqDTO.setOrganizationNumber(StringUtils.isEmpty(delinquentControlDefineReqDTO.getOrganizationNumber()) ? OrgNumberUtils.getOrg() : delinquentControlDefineReqDTO.getOrganizationNumber());
        Page<ParmDelinquentControlDefine> page = PageHelper.startPage(pageNum, pageSize);
        List<ParmDelinquentControlDefine> allocatedList = parmDelinquentControlDefineSelfMapper.selectByCondition(delinquentControlDefineReqDTO);
        if (allocatedList.isEmpty()) {
            log.error("findAll({},{}) selectAll result is empty ", pageNum, pageSize);
            allocatedList = new ArrayList<>();
            //throw new AnyTxnParameterException(AnyTxnParameterRespCodeEnum.D_GET_IS_NULL_FAULT);
        }
        List<DelinquentControlDefineResDTO> res = BeanMapping.copyList(allocatedList,
                DelinquentControlDefineResDTO.class);
        return new PageResultDTO<>(pageNum, pageSize, page.getTotal(), page.getPages(), res);
    }

    /**
     * 延滞控制定义根据id查询
     *
     * @param id
     * @return 延滞控制定义响应参数
     * @throws AnyTxnParameterException
     */
    @Override
    public DelinquentControlDefineResDTO findById(String id) {
        if (id == null) {
            throw new AnyTxnParameterException(AnyTxnParameterRespCodeEnum.P_PARAMETER_EMPTY_FAULT);
        }
        // 根据id查询延滞控制定义表中的信息
        ParmDelinquentControlDefine parmDelinquentControlDefine =
                parmDelinquentControlDefineMapper.selectByPrimaryKey(id);

        List<ParmDelinquentControl> controlList = null;
        if (parmDelinquentControlDefine != null) {
            // 根据机构号和tableId查询延滞控制定义表中信息
            String orgNum = parmDelinquentControlDefine.getOrganizationNumber();
            String tableId = parmDelinquentControlDefine.getTableId();
            controlList = parmDelinquentControlSelfMapper.selectByOrgAndTableIdAndCycleDue(orgNum, tableId, null);

            // 组装数据
            DelinquentControlDefineResDTO res = new DelinquentControlDefineResDTO();
            BeanUtils.copyProperties(parmDelinquentControlDefine, res);
            res.setControlReqList(BeanMapping.copyList(controlList, DelinquentControlResDTO.class));

            return res;
        } else {
            log.error("findById({}) selectByPrimaryKey is null", id);
            throw new AnyTxnParameterException(AnyTxnParameterRespCodeEnum.D_QUERY_PARM_DELINQUENT_CONTROL_DEFINE_FAULT);

        }
    }

    /**
     * 新增延滞控制定义
     *
     * @param delinquentControlDefineReq
     * @return 延滞控制定义响应参数
     * @throws AnyTxnParameterException
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    @InsertParameterAnnotation(tableName = "parm_delinquent_control_define", tableDesc = "Delinquent Processing", isJoinTable = true)
    public ParameterCompare addDelinquentControlDefine(DelinquentControlDefineReqDTO delinquentControlDefineReq) {
        try {
            int badCycleDue = Integer.parseInt(delinquentControlDefineReq.getBadCycleDue());
            if (!(badCycleDue >= BADCYCLEDUE_5 && badCycleDue <= BADCYCLEDUE_15)) {
                throw new AnyTxnParameterException(AnyTxnParameterRespCodeEnum.D_QUERY_PARM_DELINQUENT_CONTROL_DEFINE_BY_BAD_CYCLE_DUE_FAULT);
            }
        } catch (Exception e) {
            throw new AnyTxnParameterException(AnyTxnParameterRespCodeEnum.D_QUERY_PARM_DELINQUENT_CONTROL_DEFINE_BY_BAD_CYCLE_DUE_FAULT);
        }
        // 判断延滞控制定义表是否唯一约束冲突
        ParmDelinquentControlDefine isExsit =
                parmDelinquentControlDefineSelfMapper.selectByOrgAndTableId(OrgNumberUtils.getOrg(delinquentControlDefineReq.getOrganizationNumber()),
                        delinquentControlDefineReq.getTableId());
        if (isExsit != null) {
            throw new AnyTxnParameterException(AnyTxnParameterRespCodeEnum.D_EXIST_PARM_DELINQUENT_CONTROL_DEFINE_FAULT);
        }
        delinquentControlDefineReq.setId(String.valueOf(numberIdGenerator.generateId(TenantUtils.getTenantId())));
        return ParameterCompare.getBuilder()
                .withMainParmId(delinquentControlDefineReq.getTableId())
                .withAfter(delinquentControlDefineReq).build(DelinquentControlDefineReqDTO.class);
    }

    /**
     * 编辑延滞控制定义
     *
     * @param delinquentControlDefineReq
     * @return 延滞控制定义响应参数
     * @throws AnyTxnParameterException
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    @UpdateParameterAnnotation(tableName = "parm_delinquent_control_define", tableDesc = "Delinquent Processing", isJoinTable = true)
    public ParameterCompare modifyDelinquentControlDefine(DelinquentControlDefineReqDTO delinquentControlDefineReq) {
        try {
            int badCycleDue = Integer.parseInt(delinquentControlDefineReq.getBadCycleDue());
            if (!(badCycleDue >= BADCYCLEDUE_5 && badCycleDue <= BADCYCLEDUE_15)) {
                throw new AnyTxnParameterException(AnyTxnParameterRespCodeEnum.D_QUERY_PARM_DELINQUENT_CONTROL_DEFINE_BY_BAD_CYCLE_DUE_FAULT);
            }
        } catch (Exception e) {
            throw new AnyTxnParameterException(AnyTxnParameterRespCodeEnum.D_QUERY_PARM_DELINQUENT_CONTROL_DEFINE_BY_BAD_CYCLE_DUE_FAULT);
        }
        DelinquentControlDefineResDTO delinquentControlDefineResDTO = findById(delinquentControlDefineReq.getId());

        return ParameterCompare.getBuilder()
                .withMainParmId(delinquentControlDefineReq.getTableId())
                .withAfter(delinquentControlDefineReq)
                .withBefore(BeanMapping.copy(delinquentControlDefineResDTO, DelinquentControlDefineReqDTO.class))
                .build(DelinquentControlDefineReqDTO.class);
    }

    /**
     * 删除延滞控制定义
     *
     * @param id
     * @return 延滞控制定义响应参数
     * @throws AnyTxnParameterException
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    @DeleteParameterAnnotation(tableName = "parm_delinquent_control_define", tableDesc = "Delinquent Processing", isJoinTable = true)
    public ParameterCompare removeDelinquentControlDefine(String id) {
        // 如果id不存在，则查询该记录
        if (null == id) {
            throw new AnyTxnParameterException(AnyTxnParameterRespCodeEnum.P_PARAMETER_EMPTY_FAULT);
        }
        ParmDelinquentControlDefine parmDelinquentControlDefine =
                parmDelinquentControlDefineMapper.selectByPrimaryKey(id);

        // 如果该记录不存在，抛出异常，否则就删除该记录
        if (null == parmDelinquentControlDefine) {
            throw new AnyTxnParameterException(AnyTxnParameterRespCodeEnum.D_QUERY_PARM_DELINQUENT_CONTROL_DEFINE_FAULT);
        }

        return ParameterCompare.getBuilder()
                .withMainParmId(parmDelinquentControlDefine.getTableId())
                .withBefore(parmDelinquentControlDefine).build(ParmDelinquentControlDefine.class);

    }


    @Override
    @Transactional(rollbackFor = Exception.class, propagation = Propagation.REQUIRED)
    //@ShardingSphereTransactionType(TransactionType.XA)
    protected boolean updateDb(ParmModificationRecord parmModificationRecord) {
        DelinquentControlDefineReqDTO delinquentControlDefineReq = JSON.parseObject(parmModificationRecord.getParmBody(), DelinquentControlDefineReqDTO.class);
        //更新延滞控制定义表
        ParmDelinquentControlDefine delinquentControlDefine = new ParmDelinquentControlDefine();
        BeanMapping.copy(delinquentControlDefineReq, delinquentControlDefine);
        delinquentControlDefine.initUpdateDateTime();
        delinquentControlDefine.setUpdateBy(parmModificationRecord.getApplicationBy());
        if (delinquentControlDefine.getVersionNumber() == null) {
            delinquentControlDefine.setVersionNumber(1L);
            parmDelinquentControlDefineMapper.addVersionNumber(delinquentControlDefine);
        }
        parmDelinquentControlDefineMapper.updateByPrimaryKeySelective(delinquentControlDefine);
        // 更新延滞控制表
        List<DelinquentControlReqDTO> list = delinquentControlDefineReq.getControlReqList();
        if (!list.isEmpty()) {
            parmDelinquentControlSelfMapper.deleteByOrgNumAndTableId(OrgNumberUtils.getOrg(),
                    delinquentControlDefineReq.getTableId());
        }
        List<ParmDelinquentControl> updateList = BeanMapping.copyList(list, ParmDelinquentControl.class);
        for (ParmDelinquentControl control : updateList) {
            control.setId(numberIdGenerator.generateId(TenantUtils.getTenantId()));
            control.setOrganizationNumber(delinquentControlDefineReq.getOrganizationNumber());
            control.setTableId(delinquentControlDefineReq.getTableId());
            control.setUpdateBy(Constants.DEFAULT_USER);
            control.setCreateTime(LocalDateTime.now());
            control.setUpdateTime(LocalDateTime.now());
            control.setVersionNumber(1L);

        }
        if (!list.isEmpty()) {
            parmDelinquentControlSelfMapper.insertList(updateList);
        }
        return true;
    }

    @Override
    @Transactional(rollbackFor = Exception.class, propagation = Propagation.REQUIRED)
    //@ShardingSphereTransactionType(TransactionType.XA)
    protected boolean insertDb(ParmModificationRecord parmModificationRecord) {
        DelinquentControlDefineReqDTO delinquentControlDefineReq = JSON.parseObject(parmModificationRecord.getParmBody(), DelinquentControlDefineReqDTO.class);
        // 延滞控制定义表插入数据
        ParmDelinquentControlDefine delinquentControlDefine = new ParmDelinquentControlDefine();
        BeanMapping.copy(delinquentControlDefineReq, delinquentControlDefine);
        delinquentControlDefine.initCreateDateTime();
        delinquentControlDefine.initUpdateDateTime();
        delinquentControlDefine.setVersionNumber(1L);
        delinquentControlDefine.setUpdateBy(parmModificationRecord.getApplicationBy());
        parmDelinquentControlDefineMapper.insert(delinquentControlDefine);
        // 延滞控制定义表插入数据
        List<DelinquentControlReqDTO> list = delinquentControlDefineReq.getControlReqList();
        if (!list.isEmpty()) {
            for (DelinquentControlReqDTO controlReq : list) {
                controlReq.setId(numberIdGenerator.generateId(TenantUtils.getTenantId()));
                controlReq.setOrganizationNumber(OrgNumberUtils.getOrg(delinquentControlDefineReq.getOrganizationNumber()));
                controlReq.setTableId(delinquentControlDefineReq.getTableId());
                controlReq.setCreateTime(LocalDateTime.now());
                controlReq.setUpdateTime(LocalDateTime.now());
                controlReq.setVersionNumber(1L);
                controlReq.setUpdateBy(Constants.DEFAULT_USER);
            }
            List<ParmDelinquentControl> insertList = BeanMapping.copyList(list, ParmDelinquentControl.class);
            int count = parmDelinquentControlSelfMapper.insertList(insertList);
            if (count != list.size()) {
                throw new AnyTxnParameterException(AnyTxnParameterRespCodeEnum.D_INSERT_PARM_DELINQUENT_CONTROL_FAULT);
            }
        }
        delinquentControlDefineReq.setControlReqList(list);
        return true;
    }

    @Override
    @Transactional(rollbackFor = Exception.class, propagation = Propagation.REQUIRED)
    //@ShardingSphereTransactionType(TransactionType.XA)
    protected boolean deleteDb(ParmModificationRecord parmModificationRecord) {
        ParmDelinquentControlDefine parmDelinquentControlDefine = JSON.parseObject(parmModificationRecord.getParmBody(), ParmDelinquentControlDefine.class);
        // 先删除延滞控制表中数据
        List<ParmDelinquentControl> controlList =
                parmDelinquentControlSelfMapper.selectByOrgAndTableIdAndCycleDue(parmDelinquentControlDefine.getOrganizationNumber(),
                        parmDelinquentControlDefine.getTableId(), null);
        if (!controlList.isEmpty()) {
            parmDelinquentControlSelfMapper.deleteByOrgNumAndTableId(parmDelinquentControlDefine.getOrganizationNumber(),
                    parmDelinquentControlDefine.getTableId());
        }
        parmDelinquentControlDefineMapper.deleteByPrimaryKey(parmDelinquentControlDefine.getId());
        return true;
    }
}
