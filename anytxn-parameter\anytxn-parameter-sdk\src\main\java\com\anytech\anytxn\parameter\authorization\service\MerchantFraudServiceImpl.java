package com.anytech.anytxn.parameter.authorization.service;

import com.github.pagehelper.Page;
import com.github.pagehelper.PageHelper;
import com.anytech.anytxn.common.core.base.PageResultDTO;
import com.anytech.anytxn.common.core.utils.BeanMapping;
import com.anytech.anytxn.common.core.utils.OrgNumberUtils;
import com.anytech.anytxn.common.sequence.utils.Number16IdGen;
import com.anytech.anytxn.common.core.utils.TenantUtils;
import com.anytech.anytxn.parameter.base.authorization.domain.dto.AuthorizationRuleDTO;
import com.anytech.anytxn.parameter.base.authorization.domain.dto.MerchantFraudDTO;
import com.anytech.anytxn.parameter.base.authorization.service.IMerchantFraudService;
import com.anytech.anytxn.parameter.base.common.enums.AnyTxnParameterRespCodeEnum;
import com.anytech.anytxn.parameter.base.common.exception.AnyTxnParameterException;
import com.anytech.anytxn.parameter.authorization.mapper.MerchantFraudMapper;
import com.anytech.anytxn.parameter.authorization.mapper.MerchantFraudSelfMapper;
import com.anytech.anytxn.parameter.base.authorization.domain.model.MerchantFraud;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Objects;

/**
 * <AUTHOR>
 * @version 1.0
 * @description
 * @date 2020-07-07 14:06
 **/
@Service
public class MerchantFraudServiceImpl implements IMerchantFraudService {

    private static final Logger logger = LoggerFactory.getLogger(MerchantFraudServiceImpl.class);
    @Autowired
    private MerchantFraudMapper merchantFraudMapper;
    @Autowired
    private MerchantFraudSelfMapper merchantFraudSelfMapper;

    @Autowired
    private Number16IdGen numberIdGenerator;

    @Override
    public int add(MerchantFraudDTO merchantFraudDTO) {

        if (Objects.isNull(merchantFraudDTO)) {
            logger.error("merchantFraudDTO is null!");
            throw new AnyTxnParameterException(AnyTxnParameterRespCodeEnum.P_PARAMETER_EMPTY_FAULT);
        }
        MerchantFraud merchantFraud = BeanMapping.copy(merchantFraudDTO, MerchantFraud.class);

        int isExists = merchantFraudSelfMapper.isExists(merchantFraud.getOrganizationNumber(), merchantFraud.getMerchantId());
        if (isExists > 0) {
            logger.error("exist! merchantFraudDTO!");
            throw new AnyTxnParameterException(AnyTxnParameterRespCodeEnum.D_EXIST_MERCHAANT_FRAUD);
        }
        merchantFraud.setId(numberIdGenerator.generateId(TenantUtils.getTenantId()));
        merchantFraud.setCreateTime(LocalDateTime.now());
        merchantFraud.setUpdateTime(LocalDateTime.now());
        merchantFraud.setUpdateBy("admin");
        try {
            return merchantFraudMapper.insertSelective(merchantFraud);
        } catch (Exception e) {
            logger.error("exception:{}", e);
            throw new AnyTxnParameterException(AnyTxnParameterRespCodeEnum.D_INSERT_AUTH_RULE_FAULT);
        }
    }

    @Override
    public int modify(MerchantFraudDTO merchantFraudDTO) {
        logger.info("修改欺诈商户表 {}", merchantFraudDTO);
        if (Objects.isNull(merchantFraudDTO)) {
            logger.error("merchantFraudDTO is null!");
            throw new AnyTxnParameterException(AnyTxnParameterRespCodeEnum.P_PARAMETER_EMPTY_FAULT);
        }
        MerchantFraud merchantFraud = BeanMapping.copy(merchantFraudDTO, MerchantFraud.class);
        merchantFraud.setUpdateTime(LocalDateTime.now());
        try {
            return merchantFraudMapper.updateByPrimaryKeySelective(merchantFraud);
        } catch (Exception e) {
            logger.error("修改欺诈商户表", e);
            throw new AnyTxnParameterException(AnyTxnParameterRespCodeEnum.D_DATABASE_UPDATE_ERROR);
        }
    }

    @Override
    public int delete(Long id) {
        MerchantFraud merchantFraud = merchantFraudMapper.selectByPrimaryKey(id);
        if (null == merchantFraud) {
            logger.error("待删除欺诈商户表 不存在。 id:{}", id);
            throw new AnyTxnParameterException(AnyTxnParameterRespCodeEnum.D_DATA_NOT_EXISTS_FAULT);
        }
        try {
            return merchantFraudMapper.deleteByPrimaryKey(id);
        } catch (Exception e) {
            throw new AnyTxnParameterException(AnyTxnParameterRespCodeEnum.D_DATABASE_UPDATE_ERROR);
        }
    }

    @Override
    public MerchantFraudDTO selectById(Long id) {
        logger.info("根据主键:{},获取欺诈商户表信息",id);
        AuthorizationRuleDTO authorizationRuleDTO = null;
        try{
            MerchantFraud merchantFraud = merchantFraudMapper.selectByPrimaryKey(id);
            if (merchantFraud != null) {
               return BeanMapping.copy(merchantFraud,MerchantFraudDTO.class);
            }
        }
        catch(Exception e){
            logger.error("获取欺诈商户表信息",e);
            throw new AnyTxnParameterException(AnyTxnParameterRespCodeEnum.D_DATABASE_UPDATE_ERROR);
        }
        return null;
    }

    @Override
    public PageResultDTO<MerchantFraudDTO> getPage(int pageNum, int pageSize) {
        logger.info("分页查询欺诈商户表列表，当前页：{}，每页展示条数：{}", pageNum, pageSize);
        Page pageInfo =  PageHelper.startPage(pageNum, pageSize);
        try {
            List<MerchantFraud> merchantFrauds = merchantFraudSelfMapper.selectAll(OrgNumberUtils.getOrg());

            List<MerchantFraudDTO> merchantFraudDtos = BeanMapping.copyList(merchantFrauds, MerchantFraudDTO.class);

            return new PageResultDTO<>(pageNum, pageSize, pageInfo.getTotal(), pageInfo.getPages(),merchantFraudDtos);
        } catch (Exception e) {
            logger.error("分页查询欺诈商户表信息失败，当前页：{}，每页展示条数：{},错误信息：{}", pageNum, pageSize, e.getMessage());
            throw new AnyTxnParameterException(AnyTxnParameterRespCodeEnum.D_PAGE_AUTH_RULE_FAULT);
        }
    }

}
