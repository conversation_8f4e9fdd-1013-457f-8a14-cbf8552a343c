package com.anytech.anytxn.parameter.accounting.service;

import com.alibaba.fastjson.JSON;
import com.anytech.anytxn.business.base.audit.AbstractParameterService;
import com.anytech.anytxn.business.base.audit.ParmModificationRecord;
import com.github.pagehelper.Page;
import com.github.pagehelper.PageHelper;
import com.anytech.anytxn.common.core.annotation.db.DeleteParameterAnnotation;
import com.anytech.anytxn.common.core.annotation.db.InsertParameterAnnotation;
import com.anytech.anytxn.common.core.annotation.db.UpdateParameterAnnotation;
import com.anytech.anytxn.common.core.base.ParameterCompare;
import com.anytech.anytxn.common.core.base.PageResultDTO;
import com.anytech.anytxn.common.core.utils.BeanMapping;
import com.anytech.anytxn.common.core.utils.OrgNumberUtils;
import com.anytech.anytxn.common.sequence.utils.Number16IdGen;
import com.anytech.anytxn.common.core.utils.TenantUtils;
import com.anytech.anytxn.parameter.base.accounting.domain.dto.TPmsGlacgnDTO;
import com.anytech.anytxn.parameter.base.accounting.service.ITPmsGlacgnService;
import com.anytech.anytxn.parameter.base.common.enums.AnyTxnParameterRespCodeEnum;
import com.anytech.anytxn.parameter.base.common.exception.AnyTxnParameterException;
import com.anytech.anytxn.parameter.accounting.mapper.TPmsGlacgnMapper;
import com.anytech.anytxn.parameter.accounting.mapper.TPmsGlacgnSelfMapper;
import com.anytech.anytxn.parameter.base.accounting.domain.model.TPmsGlacgn;
import org.apache.commons.lang3.ObjectUtils;
//import org.apache.shardingsphere.transaction.annotation.ShardingSphereTransactionType;
//import org.apache.shardingsphere.transaction.core.TransactionType;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.cglib.beans.BeanCopier;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import java.util.ArrayList;
import java.util.List;

/**
 * 授权检查控制
 *
 * <AUTHOR>
 * @date 2018-12-07
 **/
@Service(value = "parm_pms_glacgn_serviceImpl")
public class TPmsGlacgnServiceImpl extends AbstractParameterService implements ITPmsGlacgnService {

    private static final Logger logger = LoggerFactory.getLogger(TPmsGlacgnServiceImpl.class);

    @Autowired
    private TPmsGlacgnMapper tPmsGlacgnMapper;
    @Autowired
    private TPmsGlacgnSelfMapper tPmsGlacgnSelfMapper;

    @Autowired
    private Number16IdGen numberIdGenerator;

    /**
     * 根据结转标志查询 会计科目参数表
     *
     * @param glAnnualFlag 结转标志
     * @return List<TPmsGlacgnDTO>
     * @throws AnyTxnParameterException 异常
     */
    @Override
    public List<TPmsGlacgnDTO> findByAnualFlag(String glAnnualFlag) throws AnyTxnParameterException {

        List<TPmsGlacgn> pmsGlacgnList = tPmsGlacgnSelfMapper.findByAnualFlag(glAnnualFlag, OrgNumberUtils.getOrg());
        if (pmsGlacgnList.isEmpty()) {
            return null;
        }
        return BeanMapping.copyList(pmsGlacgnList, TPmsGlacgnDTO.class);
    }
    /**
     * 根据总分核对标志查询 会计科目参数表
     *
     * @param checkFlag 结转标志
     * @return List<TPmsGlacgnDTO>
     * @throws AnyTxnParameterException 异常
     */
    @Override
    public List<TPmsGlacgnDTO> findByCheckFlag(String checkFlag) throws AnyTxnParameterException {

        List<TPmsGlacgn> pmsGlacgnList = tPmsGlacgnSelfMapper.findByCheckFlag(checkFlag,OrgNumberUtils.getOrg());
        if (pmsGlacgnList.isEmpty()) {
            return null;
        }
        return BeanMapping.copyList(pmsGlacgnList, TPmsGlacgnDTO.class);
    }

    /**
     * 根据organization_number+BRANCHID+GL_ACCT+CURR_CODE 查询 会计科目参数表(唯一索引)
     *
     * @return TPmsGlacgnDTO
     * @throws AnyTxnParameterException 异常
     */
    @Override
    public TPmsGlacgnDTO findByGlAcctAndCurrCode(String organizationNumber, String branchid, String glAcct,
                                                 String currCode) throws AnyTxnParameterException {

        TPmsGlacgn pmsGlacgn = tPmsGlacgnSelfMapper.findByGlAcctAndCurrCode(organizationNumber, branchid, glAcct,
                currCode);
        if (pmsGlacgn == null) {
            return null;
        }
        return BeanMapping.copy(pmsGlacgn, TPmsGlacgnDTO.class);
    }

    /**
     * 查询全部会计科目参数表
     *
     * @return List<TPmsGlacgnDTO>
     * @throws AnyTxnParameterException 异常
     */
    @Override
    public List<TPmsGlacgnDTO> findAll(String organizationNumber) throws AnyTxnParameterException {

        List<TPmsGlacgn> pmsGlacgnList = tPmsGlacgnSelfMapper.findAll(true, organizationNumber);
        if (pmsGlacgnList.isEmpty()) {
            return null;
        }
        return BeanMapping.copyList(pmsGlacgnList, TPmsGlacgnDTO.class);
    }

    @Override
    public PageResultDTO<TPmsGlacgnDTO> page(int page, int rows,String organizationNumber,String glAcct,String glAcctName,String glClass,String currCode) {
        logger.info("分页查询会计科目参数表列表，当前页：{}，每页展示条数：{}", page, rows);
        Page pageInfo =  PageHelper.startPage(page, rows);
        List<TPmsGlacgnDTO> tPmsGlacgnDTOList = null;
        try {
            organizationNumber = StringUtils.isEmpty(organizationNumber) ? OrgNumberUtils.getOrg() : organizationNumber;
            List<TPmsGlacgn> tPmsGlacgnList = tPmsGlacgnSelfMapper.findByCondition(organizationNumber, glAcct, glAcctName, glClass, currCode);
            if (!CollectionUtils.isEmpty(tPmsGlacgnList)) {
                tPmsGlacgnDTOList = new ArrayList<>();
                BeanCopier beanCopier = BeanCopier.create(TPmsGlacgn.class, TPmsGlacgnDTO.class, false);
                for (TPmsGlacgn tPmsGlacgn : tPmsGlacgnList) {
                    TPmsGlacgnDTO tPmsGlacgnDTO = new TPmsGlacgnDTO();
                    beanCopier.copy(tPmsGlacgn, tPmsGlacgnDTO, null);
                    tPmsGlacgnDTOList.add(tPmsGlacgnDTO);
                }
            }
            return new PageResultDTO<>(page, rows, pageInfo.getTotal(), pageInfo.getPages(),tPmsGlacgnDTOList);
        } catch (Exception e) {
            logger.error("分页查询会计科目参数表失败，当前页：{}，每页展示条数：{},错误信息：{}", page, rows, e.getMessage());
            /*throw new AnyTXNBusRuntimeException(TransactionEnum.DATABASE_ERROR.getCode(),
                    TransactionEnum.DATABASE_ERROR.getReason());*/
            throw new AnyTxnParameterException(AnyTxnParameterRespCodeEnum.D_PAGE_TPMS_GLACGN_FAULT);
        }
    }

    @Override
    public TPmsGlacgnDTO detail(String id) {
        TPmsGlacgn tPmsGlacgn = tPmsGlacgnMapper.selectByPrimaryKey(id);
        if(tPmsGlacgn != null){
            TPmsGlacgnDTO tPmsGlacgnDTO = new TPmsGlacgnDTO();
            BeanCopier beanCopier = BeanCopier.create(TPmsGlacgn.class, TPmsGlacgnDTO.class, false);
            beanCopier.copy(tPmsGlacgn, tPmsGlacgnDTO, null);
            return tPmsGlacgnDTO;
        }
        return null;
    }

    @Override
    @DeleteParameterAnnotation(tableName = "parm_pms_glacgn", tableDesc = "Accounting Code Parameters")
    public ParameterCompare remove(String id) {
        if (null == id) {
            throw new AnyTxnParameterException(AnyTxnParameterRespCodeEnum.P_PARAMETER_EMPTY_FAULT);
        }
        TPmsGlacgn tPmsGlacgn = tPmsGlacgnMapper.selectByPrimaryKey(id);
        if(ObjectUtils.isEmpty(tPmsGlacgn)){
            throw new AnyTxnParameterException(AnyTxnParameterRespCodeEnum.D_DATA_NOT_EXISTS_TEMPLATE_FAULT);
        }

        return ParameterCompare
                .getBuilder()
                .withBefore(tPmsGlacgn)
                .build(TPmsGlacgn.class);
    }

    @Override
    @InsertParameterAnnotation(tableName = "parm_pms_glacgn", tableDesc = "Accounting Code Parameters")
    public ParameterCompare add(TPmsGlacgnDTO data) {
        if(ObjectUtils.isEmpty(data)){
            throw new AnyTxnParameterException(AnyTxnParameterRespCodeEnum.P_PARAMETER_DATA_NOT_EXIST);
        }
        data.setBranchid("DINERS");
        data.setId(numberIdGenerator.generateId(TenantUtils.getTenantId())+"");
        TPmsGlacgn byGlAcctAndCurrCode = tPmsGlacgnSelfMapper.findByGlAcctAndCurrCode(data.getOrganizationNumber(), data.getBranchid(), data.getGlAcct(), data.getCurrCode());
        if (!ObjectUtils.isEmpty(byGlAcctAndCurrCode)){
            throw new AnyTxnParameterException(AnyTxnParameterRespCodeEnum.P_PARAMETER_DATA_EXIST);
        }
        TPmsGlacgn tPmsGlacgn = new TPmsGlacgn();
        BeanCopier beanCopier = BeanCopier.create(TPmsGlacgnDTO.class, TPmsGlacgn.class, false);
        beanCopier.copy(data, tPmsGlacgn, null);

        return ParameterCompare.getBuilder()
                .withAfter(tPmsGlacgn).build(TPmsGlacgn.class);
    }


    @Override
    @UpdateParameterAnnotation(tableName = "parm_pms_glacgn", tableDesc = "Accounting Code Parameters")
    public ParameterCompare update(TPmsGlacgnDTO data) {
        if(ObjectUtils.isEmpty(data)){
            throw new AnyTxnParameterException(AnyTxnParameterRespCodeEnum.P_PARAMETER_EMPTY_FAULT);
        }
        TPmsGlacgn tPmsGlacgn1 = tPmsGlacgnMapper.selectByPrimaryKey(String.valueOf(data.getId()));

        if(ObjectUtils.isEmpty(tPmsGlacgn1)){
            throw new AnyTxnParameterException(AnyTxnParameterRespCodeEnum.D_DATA_NOT_EXISTS_TEMPLATE_FAULT);
        }

        TPmsGlacgn tPmsGlacgn = new TPmsGlacgn();
        BeanCopier beanCopier = BeanCopier.create(TPmsGlacgnDTO.class, TPmsGlacgn.class, false);
        beanCopier.copy(data, tPmsGlacgn, null);
        tPmsGlacgn.setBranchid("DINERS");
        tPmsGlacgn.setId(String.valueOf(data.getId()));

        return ParameterCompare.getBuilder()
                .withAfter(tPmsGlacgn)
                .withBefore(tPmsGlacgn1)
                .build(TPmsGlacgn.class);
    }

    @Override
    public Long selectCount() {
        return tPmsGlacgnSelfMapper.selectCount(OrgNumberUtils.getOrg());
    }

    @Override
    public List<TPmsGlacgnDTO> findByCheckOption(String glAcctBalchkOption) {
        List<TPmsGlacgn> pmsGlacgnList = tPmsGlacgnSelfMapper.findByCheckOption(glAcctBalchkOption,OrgNumberUtils.getOrg());
        if (pmsGlacgnList.isEmpty()) {
            return null;
        }
        return BeanMapping.copyList(pmsGlacgnList, TPmsGlacgnDTO.class);
    }

    @Override
    @Transactional(rollbackFor = Exception.class,propagation = Propagation.REQUIRED)
    //@ShardingSphereTransactionType(TransactionType.XA)
    protected boolean updateDb(ParmModificationRecord parmModificationRecord) {
        TPmsGlacgn tPmsGlacgn = JSON.parseObject(parmModificationRecord.getParmBody(), TPmsGlacgn.class);
        tPmsGlacgn.initUpdateDateTime();
        try {
            tPmsGlacgnMapper.updateByPrimaryKeySelective(tPmsGlacgn);
        } catch (Exception e) {
            logger.error("更新会计科目参数表失败", e);
            throw new AnyTxnParameterException(AnyTxnParameterRespCodeEnum.D_INSERT_TPMS_GLAMS_CONTROL_FAULT);
        }
        return true;
    }

    @Override
    @Transactional(rollbackFor = Exception.class,propagation = Propagation.REQUIRED)
    //@ShardingSphereTransactionType(TransactionType.XA)
    protected boolean insertDb(ParmModificationRecord parmModificationRecord) {
        TPmsGlacgn tPmsGlacgn = JSON.parseObject(parmModificationRecord.getParmBody(), TPmsGlacgn.class);
        tPmsGlacgn.initUpdateDateTime();
        tPmsGlacgn.initCreateDateTime();
        tPmsGlacgn.setVersionNumber(1L);
        TPmsGlacgn byGlAcctAndCurrCode = tPmsGlacgnSelfMapper.findByGlAcctAndCurrCode(tPmsGlacgn.getOrganizationNumber(), tPmsGlacgn.getBranchid(), tPmsGlacgn.getGlAcct(), tPmsGlacgn.getCurrCode());
        if (!ObjectUtils.isEmpty(byGlAcctAndCurrCode)){
            throw new AnyTxnParameterException(AnyTxnParameterRespCodeEnum.P_PARAMETER_DATA_EXIST);
        }
        try {
            tPmsGlacgnSelfMapper.insert(tPmsGlacgn);
        }catch (Exception e){
            logger.error("新增会计参数控制表失败的原因",e);
            throw new AnyTxnParameterException(AnyTxnParameterRespCodeEnum.D_INSERT_TPMS_GLAMS_CONTROL_FAULT);
        }
        return true;
    }

    @Override
    @Transactional(rollbackFor = Exception.class,propagation = Propagation.REQUIRED)
    //@ShardingSphereTransactionType(TransactionType.XA)
    protected boolean deleteDb(ParmModificationRecord parmModificationRecord) {
        TPmsGlacgn tPmsGlacgn = JSON.parseObject(parmModificationRecord.getParmBody(), TPmsGlacgn.class);

        try {
            tPmsGlacgnMapper.deleteByPrimaryKey(tPmsGlacgn.getId());
        } catch (Exception e) {
            logger.error("删除会计科目参数表失败", e);
            throw new AnyTxnParameterException(AnyTxnParameterRespCodeEnum.D_INSERT_TPMS_GLAMS_CONTROL_FAULT);
        }
        return true;
    }


}
