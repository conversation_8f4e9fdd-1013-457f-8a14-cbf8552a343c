package com.anytech.anytxn.parameter.common.service.system;

import com.alibaba.fastjson.JSON;
import com.anytech.anytxn.business.base.audit.ParmModificationRecord;
import com.anytech.anytxn.business.base.common.domain.dto.ParamExportDTO;
import com.anytech.anytxn.business.base.common.domain.dto.ParamExportPageDTO;
import com.anytech.anytxn.business.base.common.domain.dto.ParamImportDTO;
import com.anytech.anytxn.business.base.common.domain.dto.ParamImportResultDTO;
import com.anytech.anytxn.common.core.annotation.db.DeleteParameterAnnotation;
import com.anytech.anytxn.common.core.annotation.db.InsertParameterAnnotation;
import com.anytech.anytxn.common.core.annotation.db.UpdateParameterAnnotation;
import com.anytech.anytxn.common.core.enums.AnyTxnCommonRespCodeEnum;
import com.anytech.anytxn.common.core.base.PageResultDTO;
import com.anytech.anytxn.common.core.enums.ParamFileTypeEnum;
import com.anytech.anytxn.common.core.utils.BeanMapping;
import com.anytech.anytxn.common.core.utils.JacksonUtils;
import com.anytech.anytxn.common.core.utils.LoginUserUtils;
import com.anytech.anytxn.common.core.utils.OrgNumberUtils;
import com.anytech.anytxn.common.core.base.AnyTxnHttpResponse;
import com.anytech.anytxn.common.sequence.utils.Number16IdGen;
import com.anytech.anytxn.common.core.utils.TenantUtils;
import com.anytech.anytxn.business.base.audit.AbstractParameterService;
import com.anytech.anytxn.common.core.base.ParameterCompare;
import com.anytech.anytxn.parameter.base.common.enums.AnyTxnParameterRespCodeEnum;
import com.anytech.anytxn.parameter.base.common.enums.ParameterRepDetailEnum;
import com.anytech.anytxn.parameter.base.common.exception.AnyTxnParameterException;
import com.anytech.anytxn.parameter.common.mapper.broadcast.system.ParmCurrencyRateHistoryMapper;
import com.anytech.anytxn.parameter.common.mapper.broadcast.system.ParmCurrencyRateMapper;
import com.anytech.anytxn.parameter.common.mapper.broadcast.system.ParmCurrencyRateSelfMapper;
import com.anytech.anytxn.parameter.common.mapper.broadcast.system.ParmSysDictSelfMapper;
import com.anytech.anytxn.parameter.base.common.domain.model.system.ParmCurrencyRate;
import com.anytech.anytxn.parameter.base.common.domain.model.system.ParmCurrencyRateHistory;
import com.anytech.anytxn.parameter.base.common.domain.model.system.ParmSysDict;
import com.anytech.anytxn.parameter.base.common.domain.dto.CurrencyRateReqDTO;
import com.anytech.anytxn.parameter.base.common.domain.dto.CurrencyRateRes;
import com.anytech.anytxn.parameter.base.common.domain.dto.CurrencyRateResDTO;
import com.anytech.anytxn.parameter.base.common.service.ICurrencyRateService;
import com.anytech.anytxn.parameter.base.common.utils.FileFieldUtils;
import com.github.pagehelper.Page;
import com.github.pagehelper.PageHelper;
import org.apache.commons.io.IOUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.poi.hssf.usermodel.HSSFWorkbook;
import org.apache.poi.ss.usermodel.*;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;
//import org.apache.shardingsphere.transaction.annotation.ShardingSphereTransactionType;
//import org.apache.shardingsphere.transaction.core.TransactionType;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.aop.framework.AopContext;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;
import org.springframework.web.multipart.MultipartFile;

import java.io.ByteArrayInputStream;
import java.io.IOException;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 汇率参数表实现类
 * <AUTHOR> tingting
 * @date 2018/10/19
 */
@Service("parm_currency_rate_serviceImpl")
public class CurrencyRateServiceImpl extends AbstractParameterService implements ICurrencyRateService {

    private final Logger logger = LoggerFactory.getLogger(CurrencyRateServiceImpl.class);

    @Autowired
    private ParmCurrencyRateMapper parmCurrencyRateMapper;
    @Autowired
    private ParmCurrencyRateSelfMapper parmCurrencyRateSelfMapper;
    @Autowired
    private Number16IdGen numberIdGenerator;
    @Autowired
    private ParmCurrencyRateHistoryMapper parmCurrencyRateHistoryMapper;

    private static final Integer MAX_EXPONENT = 8;

    @Autowired
    private ParmSysDictSelfMapper parmSysDictSelfMapper;



    /**
     * 根据机构号,外币币种,人民币币种,汇率类型查询
     *
     * @param orgNumber           机构号
     * @param currencySource      来源币种
     * @param currencyDestination 目标币种
     * @param rateType            利率类型
     * @return CurrencyRateRes 汇率参数表返回参数
     */
    @Override
    public CurrencyRateResDTO findByOrgAndCurrencyAndRateType(String orgNumber, String currencySource, String currencyDestination, String rateType){
        ParmCurrencyRate parmCurrencyRate = parmCurrencyRateSelfMapper.selectByOrgAndCurrencyAndRateType(orgNumber, currencySource, currencyDestination, rateType);
        if (parmCurrencyRate == null) {
            throw new AnyTxnParameterException(AnyTxnParameterRespCodeEnum.D_GET_IS_NULL_FAULT);
        }

        return BeanMapping.copy(parmCurrencyRate, CurrencyRateResDTO.class);
    }

    /**
     * 根据id查询
     *
     * @param id id
     * @return CurrencyRateRes
     */
    @Override
    public CurrencyRateResDTO findOne(String id){
        if (id == null) {
            throw new AnyTxnParameterException(AnyTxnParameterRespCodeEnum.P_PARAMETER_EMPTY_FAULT);
        }

        ParmCurrencyRate parmCurrencyRate = parmCurrencyRateMapper.selectByPrimaryKey(id);

        if (parmCurrencyRate == null) {
            logger.error("查询汇率参数，通过id:{}未查到数据", id);
            throw new AnyTxnParameterException(AnyTxnParameterRespCodeEnum.D_QUERY_PARM_CURRENCY_RATE_FAULT);
        }

        return BeanMapping.copy(parmCurrencyRate, CurrencyRateResDTO.class);
    }

    /**
     * 查询列表 分页
     *
     * @param pageNum  页码
     * @param pageSize 页面容量
     * @return PageResultDTO<CurrencyRateRes>
     */
    @Override
    public PageResultDTO<CurrencyRateResDTO> findPage(Integer pageNum, Integer pageSize,String currencySource,String currencyDestination,String description,String rateValue,String organizationNumber){
        Long rateValueL = null;
        if(!StringUtils.isEmpty(rateValue)){
            try {
                rateValueL = Long.parseLong(rateValue);
            }catch (NumberFormatException e){
                throw new AnyTxnParameterException(AnyTxnParameterRespCodeEnum.PARAMETER_EXCEPTION, ParameterRepDetailEnum.NUMBER_ERROR);
            }
        }
        if(!"".equals(currencySource) && "".equals(currencyDestination)){
            currencyDestination = null;
        }
        if("".equals(currencySource) && !"".equals(currencyDestination)){
            currencySource = null;
        }
        Map<String,Object> map = new HashMap<>(8);
        map.put("currencySource",currencySource);
        map.put("currencyDestination",currencyDestination);
        map.put("description",description);
        map.put("rateValue",rateValue);
        map.put("rateValueL",rateValueL);
        map.put("organizationNumber", StringUtils.isEmpty(organizationNumber) ? OrgNumberUtils.getOrg() : organizationNumber);
        logger.debug("分页查询汇率参数");
        Page<ParmCurrencyRate> page = PageHelper.startPage(pageNum, pageSize);
        List<ParmCurrencyRate> currencyRates = parmCurrencyRateSelfMapper.selectByCondition(map);
        List<CurrencyRateResDTO> currencyRateRes = BeanMapping.copyList(currencyRates, CurrencyRateResDTO.class);
        return new PageResultDTO<>(pageNum, pageSize, page.getTotal(), page.getPages(), currencyRateRes);
    }

    /**
     * 修改
     *
     * @param currencyRateReq 汇率参数传入
     * @return int
     */
    @Override
    @UpdateParameterAnnotation(tableName = "parm_currency_rate",tableDesc = "Exchange Rate")
    public ParameterCompare modify(CurrencyRateReqDTO currencyRateReq){
        if (currencyRateReq.getId() == null) {
            throw new AnyTxnParameterException(AnyTxnParameterRespCodeEnum.P_PARAMETER_ID_EMPTY_FAULT);
        }

        ParmCurrencyRate parmCurrencyRate = parmCurrencyRateMapper.selectByPrimaryKey(currencyRateReq.getId());

        if (parmCurrencyRate == null) {
            logger.error("修改汇率参数，通过id:{}未查到数据", currencyRateReq.getId());
            throw new AnyTxnParameterException(AnyTxnParameterRespCodeEnum.D_QUERY_PARM_CURRENCY_RATE_FAULT);
        }

        ParmCurrencyRate currencyRate = parmCurrencyRateSelfMapper.selectByOrgAndCurrencyAndRateType(
                parmCurrencyRate.getOrganizationNumber(),parmCurrencyRate.getCurrencySource(),
                parmCurrencyRate.getCurrencyDestination(),parmCurrencyRate.getRateType());

        if (currencyRate != null && !currencyRate.getId().equals(parmCurrencyRate.getId())) {
            logger.warn("汇率参数已存在，orgNum={},currencySource={},currencyDestination={},rateType={}",
                    currencyRateReq.getOrganizationNumber(),currencyRateReq.getCurrencySource(),
                    currencyRateReq.getCurrencyDestination(),currencyRateReq.getRateType());
            throw new AnyTxnParameterException(AnyTxnParameterRespCodeEnum.D_EXIST_PARM_CURRENCY_RATE_FAULT);
        }

        ParmCurrencyRate modify = BeanMapping.copy(currencyRateReq, ParmCurrencyRate.class);
        return ParameterCompare
                .getBuilder()
                .withAfter(modify)
                .withBefore(parmCurrencyRate)
                .build(ParmCurrencyRate.class);
    }

    /**
     * 删除
     *
     * @param id id
     * @return Boolean
     */
    @Override
    @DeleteParameterAnnotation(tableName = "parm_currency_rate",tableDesc = "Exchange Rate")
    public ParameterCompare remove(String id){
        ParmCurrencyRate parmCurrencyRate = parmCurrencyRateMapper.selectByPrimaryKey(id);

        if (parmCurrencyRate == null) {
            logger.error("删除汇率参数，通过id:{}未查到数据", id);
            throw new AnyTxnParameterException(AnyTxnParameterRespCodeEnum.D_QUERY_PARM_CURRENCY_RATE_FAULT);
        }
        return ParameterCompare
                .getBuilder()
                .withBefore(parmCurrencyRate)
                .build(ParmCurrencyRate.class);
    }

    /**
     * 新增
     *
     * @param currencyRateReq 汇率参数传入
     * @return CurrencyRateRes
     */
    @Override
    @InsertParameterAnnotation(tableName = "parm_currency_rate",tableDesc = "Exchange Rate")
    public ParameterCompare add(CurrencyRateReqDTO currencyRateReq){

        boolean isExists = parmCurrencyRateSelfMapper.isExists(
                currencyRateReq.getOrganizationNumber(),currencyRateReq.getCurrencySource(),
                currencyRateReq.getCurrencyDestination(),currencyRateReq.getRateType())>0;
        if (isExists) {
            logger.warn("汇率参数已存在，orgNum={},currencySource={},currencyDestination={},rateType={}",
                    currencyRateReq.getOrganizationNumber(),currencyRateReq.getCurrencySource(),
                    currencyRateReq.getCurrencyDestination(),currencyRateReq.getRateType());
            throw new AnyTxnParameterException(AnyTxnParameterRespCodeEnum.D_EXIST_PARM_CURRENCY_RATE_FAULT);
        }
        ParmCurrencyRate parmCurrencyRate = BeanMapping.copy(currencyRateReq, ParmCurrencyRate.class);
        parmCurrencyRate.setId(numberIdGenerator.generateId(TenantUtils.getTenantId())+"");

        return ParameterCompare
                .getBuilder()
                .withAfter(parmCurrencyRate)
                .withMainParmId(parmCurrencyRate.getCurrencySource()+"-"+parmCurrencyRate.getCurrencyDestination())
                .build(ParmCurrencyRate.class);
    }

    /**
     * 从文件导入汇率
     * @param file
     */
    @Override
    @Transactional(rollbackFor = Exception.class, propagation = Propagation.REQUIRES_NEW)
    public void importCurrencyRateFromFile(MultipartFile file) {
        logger.info("开始导入汇率参数");
        //处理文件
        List<ParmCurrencyRate> currencyReteListFromFile = getCurrecyRateFromFile(file);
        //根据币种更新并备份汇率
        updateAndBackUpCurrencyRate(currencyReteListFromFile);
    }

    @Override
    public CurrencyRateRes findExchangeRateByCurrency(String sourceCurrency, String targetCurrency, String selectType) {
        logger.info("请求获取兑换率,来源币种:{},目标币种:{},查询类型:{}",sourceCurrency,targetCurrency,selectType);

        if(StringUtils.isBlank(sourceCurrency) ||
                (StringUtils.isNotBlank(sourceCurrency) && sourceCurrency.length() != 3)) {
            throw new AnyTxnParameterException(AnyTxnParameterRespCodeEnum.S_PARAM_CURRENCY_RATE_ERROR, ParameterRepDetailEnum.API_PARAM_SOURCE_CURRENCY_RATE_ERROR, sourceCurrency);
        }
        if(StringUtils.isBlank(targetCurrency) ||
                (StringUtils.isNotBlank(targetCurrency) && targetCurrency.length() != 3)) {
            throw new AnyTxnParameterException(AnyTxnParameterRespCodeEnum.S_PARAM_CURRENCY_RATE_ERROR, ParameterRepDetailEnum.API_PARAM_TARGET_CURRENCY_RATE_ERROR, targetCurrency);
        }
        if(!StringUtils.isBlank(selectType) && StringUtils.equals(selectType,"A")){
            List<ParmSysDict> sourceSysDict = parmSysDictSelfMapper.selectCodeIdByCodeNameAndTypeId(sourceCurrency,"CURRENCY_RATE");
            List<ParmSysDict> targetSysDict = parmSysDictSelfMapper.selectCodeIdByCodeNameAndTypeId(targetCurrency,"CURRENCY_RATE");

            if(CollectionUtils.isEmpty(sourceSysDict) || sourceSysDict.size() >1){
                throw new AnyTxnParameterException(AnyTxnParameterRespCodeEnum.S_QUERY_SYS_DICT_ERROR,ParameterRepDetailEnum.API_PARAM_SOURCE_CURRENCY_RATE_ERROR, sourceCurrency);
            }
            if(CollectionUtils.isEmpty(targetSysDict) || sourceSysDict.size() >1){
                throw new AnyTxnParameterException(AnyTxnParameterRespCodeEnum.S_QUERY_SYS_DICT_ERROR,ParameterRepDetailEnum.API_PARAM_SOURCE_CURRENCY_RATE_ERROR, targetCurrency);
            }

            sourceCurrency = sourceSysDict.get(0).getCodeId();
            targetCurrency = targetSysDict.get(0).getCodeId();
        }
        ParmCurrencyRate parmCurrencyRate = parmCurrencyRateSelfMapper.selectByOrgAndCurrencyAndRateType(OrgNumberUtils.getOrg(), sourceCurrency, targetCurrency, "0");
        if (parmCurrencyRate == null) {
            throw new AnyTxnParameterException(AnyTxnParameterRespCodeEnum.D_GET_IS_NULL_FAULT);
        }

        return BeanMapping.copy(parmCurrencyRate, CurrencyRateRes.class);
    }

    /**
     * 备份并更新汇率表
     * @param currencyReteListFromFile
     */
    private void updateAndBackUpCurrencyRate(List<ParmCurrencyRate> currencyReteListFromFile) {
        List<ParmCurrencyRate> parmCurrencyRateUpdateList = new ArrayList<>();
        List<ParmCurrencyRateHistory> parmCurrencyRateHistoryList = new ArrayList<>();
        List<ParmCurrencyRate> parmCurrencyRateInsertList = new ArrayList<>();
        String loginUserName = LoginUserUtils.getLoginUserName();
        for (ParmCurrencyRate parmCurrencyRate : currencyReteListFromFile) {
            //根据币种查询当前汇率表，如果查到则更新并备份，如果查不到则插入
            ParmCurrencyRate parmCurrencyRateExist = parmCurrencyRateSelfMapper.selectByOrgAndCurrencyAndRateType(parmCurrencyRate.getOrganizationNumber(), parmCurrencyRate.getCurrencySource(), parmCurrencyRate.getCurrencyDestination(), parmCurrencyRate.getRateType());
            if (!Objects.isNull(parmCurrencyRateExist)) {
                ParmCurrencyRateHistory parmCurrencyRateHistory = BeanMapping.copy(parmCurrencyRateExist, ParmCurrencyRateHistory.class);
                //更新汇率
                parmCurrencyRateExist.setRateValue(parmCurrencyRate.getRateValue());
                parmCurrencyRateExist.setExponent(parmCurrencyRate.getExponent());
                parmCurrencyRateExist.setUpdateTime(LocalDateTime.now());
                parmCurrencyRateExist.setUpdateBy(loginUserName);
                parmCurrencyRateExist.setVersionNumber(parmCurrencyRateExist.getVersionNumber() + 1);
                parmCurrencyRateUpdateList.add(parmCurrencyRateExist);
                //写入汇率历史
                parmCurrencyRateHistory.setId(String.valueOf(numberIdGenerator.generateId(TenantUtils.getTenantId())));
                parmCurrencyRateHistory.setBackupDate(LocalDate.now());
                parmCurrencyRateHistory.setUpdateTime(LocalDateTime.now());
                parmCurrencyRateHistoryList.add(parmCurrencyRateHistory);
            } else {
                parmCurrencyRate.setId(String.valueOf(numberIdGenerator.generateId(TenantUtils.getTenantId())));
                parmCurrencyRateInsertList.add(parmCurrencyRate);
            }
        }
        parmCurrencyRateSelfMapper.batchUpdateRateValueByPrimaryKey(parmCurrencyRateUpdateList);
        parmCurrencyRateHistoryMapper.batchInsert(parmCurrencyRateHistoryList);
        if (!CollectionUtils.isEmpty(parmCurrencyRateInsertList)) {
            parmCurrencyRateSelfMapper.batchInsert(parmCurrencyRateInsertList);
        }
    }

    /**
     * 读取文件构建汇率数据
     * @param file
     * @return 汇率数据的列表
     */
    private List<ParmCurrencyRate> getCurrecyRateFromFile(MultipartFile file) {
        String loginUserName = LoginUserUtils.getLoginUserName();
        List<ParmCurrencyRate> returnList = new ArrayList<>();
        //默认保留五位小数
        BigDecimal rateValueMulti = new BigDecimal("100000");
        BigDecimal rateValueDivide = new BigDecimal("1.000000");
        //i表示行数，
        int i = 0;
        try {
            Workbook workbook;
            if (file.getOriginalFilename().endsWith(".xlsx")) {
                workbook = new XSSFWorkbook(file.getInputStream());
            } else {
                workbook = new HSSFWorkbook(file.getInputStream());
            }
            Sheet sheet = workbook.getSheetAt(0);
            Row row;
            //定位数据
            while ((row = sheet.getRow(i)) != null) {
                i++;

                Cell cell = row.getCell(1);
                if (cell == null) {
                    continue;
                }
                String stringCellValue = getCellValue(cell);
                if ("CURRENCY".equalsIgnoreCase(stringCellValue)) {
                    break;
                }
            }
            //解析数据
            Row data;
            while ((data = sheet.getRow(i)) != null) {
                i++;
                Cell cell = data.getCell(0);
                if (cell == null || "".equals(getCellValue(cell))) {
                    break;
                }
                String countrySource = getCellValue(data.getCell(2));
                String sourceCurrencyCode1 = getCellValue(data.getCell(3));
                String sourceCurrencyCode2 = getCellValue(data.getCell(4));
                String sourceCurrencyCode3 = getCellValue(data.getCell(5));
                String sourceCurrencyCode = sourceCurrencyCode1 + sourceCurrencyCode2 + sourceCurrencyCode3;
                if ("702".equals(sourceCurrencyCode)) {
                    continue;
                }
                BigDecimal cellingExchangeRate = getCellNumValue(data.getCell(6));
                //数据处理
                BigDecimal divide = rateValueDivide.divide(cellingExchangeRate, RoundingMode.HALF_UP).setScale(6, RoundingMode.HALF_UP);
                int exponent = getExponent(cellingExchangeRate);
                processData(countrySource, "SGD", sourceCurrencyCode, "702", cellingExchangeRate.multiply(rateValueMulti.max(BigDecimal.valueOf(Math.pow(10, exponent))).setScale(0, RoundingMode.HALF_UP)), returnList, Math.max(exponent, 5), loginUserName);
                processData("SGD", countrySource, "702", sourceCurrencyCode, divide.multiply(rateValueMulti).setScale(0, RoundingMode.HALF_UP), returnList, loginUserName);
            }
            return returnList;
        } catch (Exception e) {
            logger.error("汇率参数导入文件读取失败！错误行数：{}", i);
            throw new AnyTxnParameterException(AnyTxnParameterRespCodeEnum.S_IMPORT_CURRENCY_RATE_FILE_FAULT, ParameterRepDetailEnum.IMPORT_CURRENCY_RATE_FILE_FAULT_LINE, i);
        }
    }

    private int getExponent(BigDecimal exchangeRate) {
        int value = 0;
        String[] split = exchangeRate.toString().split("\\.");
        if (split.length > 1) {
            String actualExchangeRate = split[1];
            value = split[1].length();
            if (value > MAX_EXPONENT) {
                //如果超过了最大精度值，按最大精度值进行四舍五入，再重新计算精度值
                exchangeRate = exchangeRate.setScale(MAX_EXPONENT, RoundingMode.HALF_EVEN);
                actualExchangeRate = exchangeRate.toString().split("\\.")[1];
            }
            //去掉末尾的0
            while (actualExchangeRate.endsWith("0")) {
                actualExchangeRate = StringUtils.removeEnd(actualExchangeRate, "0");
            }
            value = actualExchangeRate.length();
        }
        return value;
    }

    private void processData(String countrySource, String countryDestination, String currencySource, String currencyDestination, BigDecimal rateValue, List<ParmCurrencyRate> returnList, String loginUserName) {
        processData(countrySource, countryDestination, currencySource, currencyDestination, rateValue, returnList, 5, loginUserName);
    }

    /**
     * 将文件读取到的信息构建为汇率对象
     * @param countrySource 来源币种国家码
     * @param countryDestination 目标币种国家码
     * @param currencySource 来源币种
     * @param currencyDestination 目标币种
     * @param rateValue 汇率值
     * @param returnList
     */
    private void processData(String countrySource, String countryDestination, String currencySource, String currencyDestination, BigDecimal rateValue, List<ParmCurrencyRate> returnList, Integer exponent, String loginUserName) {
        LocalDateTime now = LocalDateTime.now();

        ParmCurrencyRate parmCurrencyRate = new ParmCurrencyRate();
        parmCurrencyRate.setCurrencySource(currencySource);
        parmCurrencyRate.setRateType("0");
        parmCurrencyRate.setCurrencyDestination(currencyDestination);
        parmCurrencyRate.setOrganizationNumber("101");
        parmCurrencyRate.setRateValue(rateValue.longValue());
        parmCurrencyRate.setExponent(exponent);
        parmCurrencyRate.setDescription(new StringBuilder(countrySource).append(" - ").append(countryDestination).toString());
        parmCurrencyRate.setStatus("1");
        parmCurrencyRate.setCreateTime(now);
        parmCurrencyRate.setUpdateBy(loginUserName);
        parmCurrencyRate.setUpdateTime(now);
        parmCurrencyRate.setVersionNumber(1L);

        returnList.add(parmCurrencyRate);
    }

    /**
     * 获取excel单元格的值为字符串
     * @param cell
     * @return
     */
    private String getCellValue(Cell cell) {
        cell.setCellType(CellType.STRING);
        return cell.getStringCellValue().trim();
    }

    /**
     * 获取excel单元格的值为字符串
     * @param cell
     * @return
     */
    private BigDecimal getCellNumValue(Cell cell) {
        return BigDecimal.valueOf(cell.getNumericCellValue());
    }

    @Override
    @Transactional(rollbackFor = Exception.class,propagation = Propagation.REQUIRED)
    //@ShardingSphereTransactionType(TransactionType.XA)
    protected boolean updateDb(ParmModificationRecord parmModificationRecord) {
        ParmCurrencyRate parmCurrencyRate = JSON.parseObject(parmModificationRecord.getParmBody(), ParmCurrencyRate.class);
        parmCurrencyRate.initUpdateDateTime();
        int res = parmCurrencyRateMapper.updateByPrimaryKeySelective(parmCurrencyRate);
        return res > 0;
    }

    @Override
    @Transactional(rollbackFor = Exception.class,propagation = Propagation.REQUIRED)
    //@ShardingSphereTransactionType(TransactionType.XA)
    protected boolean insertDb(ParmModificationRecord parmModificationRecord) {
        ParmCurrencyRate parmCurrencyRate = JSON.parseObject(parmModificationRecord.getParmBody(), ParmCurrencyRate.class);
        parmCurrencyRate.initUpdateDateTime();
        parmCurrencyRate.initCreateDateTime();
        int res = parmCurrencyRateMapper.insertSelective(parmCurrencyRate);
        return res > 0;
    }

    @Override
    @Transactional(rollbackFor = Exception.class,propagation = Propagation.REQUIRED)
    //@ShardingSphereTransactionType(TransactionType.XA)
    protected boolean deleteDb(ParmModificationRecord parmModificationRecord) {
        ParmCurrencyRate parmCurrencyRate = JSON.parseObject(parmModificationRecord.getParmBody(), ParmCurrencyRate.class);
        int deleteRow = parmCurrencyRateMapper.deleteByPrimaryKey(parmCurrencyRate.getId());
        return deleteRow > 0;
    }

    @Override
    public PageResultDTO<ParamExportPageDTO> exPortPages(ParamExportDTO paramExportDTO) {

        Map<String, Object> map = new HashMap<>(8);
        map.put("organizationNumber", paramExportDTO.getOrganizationNumber());
        Page<ParmCurrencyRate> page = PageHelper.startPage(paramExportDTO.getPageNum(), paramExportDTO.getPageSize());
        List<ParmCurrencyRate> data = parmCurrencyRateSelfMapper.selectByCondition(map);

        List<ParamExportPageDTO> pageDTOList = data.stream().map(t -> {
            ParamExportPageDTO paramExportPageDTO = new ParamExportPageDTO();
            paramExportPageDTO.setDesc(t.getDescription());
            paramExportPageDTO.setId(t.getId());
            paramExportPageDTO.setTableId(t.getCurrencySource()+" - "+t.getCurrencyDestination());
            paramExportPageDTO.setOrganizationNumber(t.getOrganizationNumber());
            paramExportPageDTO.setStatus(t.getStatus());
            return paramExportPageDTO;
        }).collect(Collectors.toList());

        return new PageResultDTO<>(paramExportDTO.getPageNum(), paramExportDTO.getPageSize(),
                page.getTotal(), page.getPages(), pageDTOList);
    }
    @Override
    public ParamImportDTO exportData(ParamExportDTO paramPoiExportDTO) {

        if (Objects.equals(paramPoiExportDTO.getFileType(), ParamFileTypeEnum.TXT.getType())) {
            return buildTxtData(paramPoiExportDTO);
        }
        if (Objects.equals(paramPoiExportDTO.getFileType(), ParamFileTypeEnum.CSV.getType())) {
            return buildCvsData(paramPoiExportDTO);
        }
        return null;
    }

    private ParamImportDTO buildTxtData(ParamExportDTO paramPoiExportDTO) {
        List<String> strings = paramPoiExportDTO.getParamCodeIds().get(paramPoiExportDTO.getParamCodes());
        StringBuilder stringBuilder = new StringBuilder();
        String size = String.valueOf(strings.size());

        Map<String, Object> map = new HashMap<>(8);
        map.put("organizationNumber", paramPoiExportDTO.getOrganizationNumber());
        List<ParmCurrencyRate> data = parmCurrencyRateSelfMapper.selectByCondition(map);
        if (CollectionUtils.isEmpty(strings)) {
            size = String.valueOf(org.apache.commons.collections4.CollectionUtils.size(data));
            data.forEach(t -> stringBuilder.append(JacksonUtils.toJsonStr(t)).append("\r\n"));
        } else {
            data.forEach(t -> {
                if (strings.contains(t.getId())) {
                    stringBuilder.append(JacksonUtils.toJsonStr(t)).append("\r\n");
                }
            });
        }

        return ParamImportDTO.ParamImportDTOBuilder
                .aParamImportDTO()
                .withBytes(stringBuilder.toString().getBytes())
                .withTotalNum(size)
                .build();
    }

    private ParamImportDTO buildCvsData(ParamExportDTO paramPoiExportDTO) {
        List<String> strings = paramPoiExportDTO.getParamCodeIds().get(paramPoiExportDTO.getParamCodes());
        StringBuilder stringBuilder = new StringBuilder();
        String size = String.valueOf(strings.size());

        String[] head = {"ID", "Original Currency", "Target Currency", "Description", "Exchange Rate", "Exchange Rate Decimal Places", "Organization", "Status", "RateType"};
        List<String> headfield = Arrays.asList("id", "currencySource", "currencyDestination", "description", "rateValue", "exponent", "organizationNumber", "status", "rateType");
        stringBuilder.append(StringUtils.join(head, ",")).append("\r\n");
        Map<String, Object> map = new HashMap<>(8);
        map.put("organizationNumber", paramPoiExportDTO.getOrganizationNumber());
        List<ParmCurrencyRate> data = parmCurrencyRateSelfMapper.selectByCondition(map);

        if (CollectionUtils.isEmpty(strings)) {
            size = String.valueOf(org.apache.commons.collections4.CollectionUtils.size(data));
            data.forEach(t -> stringBuilder.append(StringUtils.join(FileFieldUtils.showFieldValue(t, headfield), ",")).append("\r\n"));
        } else {
            data.forEach(t -> {
                if (strings.contains(t.getId())) {
                    stringBuilder.append(StringUtils.join(FileFieldUtils.showFieldValue(t, headfield), ",")).append("\r\n");
                }
            });
        }

        return ParamImportDTO.ParamImportDTOBuilder
                .aParamImportDTO()
                .withBytes(stringBuilder.toString().getBytes())
                .withTotalNum(size)
                .build();

    }

    @Override
    public ParamImportResultDTO importData(ParamImportResultDTO paramImportResultDTO) throws IOException {
        if (Objects.equals(paramImportResultDTO.getFileType(), ParamFileTypeEnum.TXT.getType())) {
            return parseTxtData(paramImportResultDTO);
        }
        if (Objects.equals(paramImportResultDTO.getFileType(), ParamFileTypeEnum.CSV.getType())) {
            return parseCsvData(paramImportResultDTO);
        }
        paramImportResultDTO.buildErrorInfo(AnyTxnCommonRespCodeEnum.RESPONSE_UNKNOWN, "Unsupported file type");
        return paramImportResultDTO;
    }

    private ParamImportResultDTO parseTxtData(ParamImportResultDTO paramImportResultDTO) throws IOException {

        Map<String, Object> map = new HashMap<>(8);
        map.put("organizationNumber", "0000");
        List<ParmCurrencyRate> data = parmCurrencyRateSelfMapper.selectByCondition(map);
        ByteArrayInputStream byteArrayInputStream = new ByteArrayInputStream(paramImportResultDTO.getBytes());
        List<String> readLines = IOUtils.readLines(byteArrayInputStream, "utf-8");
        int successNum = 0;
        int lineNum = 0;
        for (String readLine : readLines) {
            try {
                ++lineNum;
                if (org.apache.commons.lang3.StringUtils.isBlank(readLine)) {
                    continue;
                }
                CurrencyRateReqDTO currencyRateReq = JSON.parseObject(readLine, CurrencyRateReqDTO.class);
                if (!CollectionUtils.isEmpty(data)) {
                    List<String> keys = data.stream().map(p -> p.getOrganizationNumber() + "_" + p.getCurrencySource() + "_" + p.getCurrencyDestination()).collect(Collectors.toList());
                    if (!keys.contains(currencyRateReq.getOrganizationNumber() + "_" + currencyRateReq.getCurrencySource() + "_" + currencyRateReq.getCurrencyDestination())) {
                        ((CurrencyRateServiceImpl) (AopContext.currentProxy())).add(currencyRateReq);
                    } else {
                        ((CurrencyRateServiceImpl) (AopContext.currentProxy())).modify(currencyRateReq);
                    }
                } else {
                    ((CurrencyRateServiceImpl) (AopContext.currentProxy())).add(currencyRateReq);
                }
                paramImportResultDTO.addResult(AnyTxnHttpResponse.success());
                successNum++;
            } catch (Exception e) {
                paramImportResultDTO.addResult(buildExceptionInfo(e, lineNum));
            }
        }
        paramImportResultDTO.setTotalNum(String.valueOf(readLines.size()));
        paramImportResultDTO.setSuccessNum(String.valueOf(successNum));
        return paramImportResultDTO.buildSuccessInfo();
    }

    private ParamImportResultDTO parseCsvData(ParamImportResultDTO paramImportResultDTO) throws IOException {

        Map<String, Object> map = new HashMap<>(8);
        map.put("organizationNumber", "0000");
        List<ParmCurrencyRate> data = parmCurrencyRateSelfMapper.selectByCondition(map);
        ByteArrayInputStream byteArrayInputStream = new ByteArrayInputStream(paramImportResultDTO.getBytes());
        List<String> readLines = IOUtils.readLines(byteArrayInputStream, "utf-8");
        int successNum = 0;
        int lineNum = 0;
        for (String readLine : readLines) {
            try {
                ++lineNum;
                if (org.apache.commons.lang3.StringUtils.isBlank(readLine) || lineNum == 1) {
                    continue;
                }
                CurrencyRateReqDTO currencyRateReq = new CurrencyRateReqDTO();
                List<String> headfield = Arrays.asList("id", "currencySource", "currencyDestination", "description", "rateValue", "exponent", "organizationNumber", "status", "rateType");
                FileFieldUtils.configFieldValue(currencyRateReq, headfield, Arrays.asList(readLine.split(",")));
                if (!CollectionUtils.isEmpty(data)) {
                    List<String> keys = data.stream().map(p -> p.getOrganizationNumber() + "_" + p.getCurrencySource() + "_" + p.getCurrencyDestination()).collect(Collectors.toList());
                    if (!keys.contains(currencyRateReq.getOrganizationNumber() + "_" + currencyRateReq.getCurrencySource() + "_" + currencyRateReq.getCurrencyDestination())) {
                        ((CurrencyRateServiceImpl) (AopContext.currentProxy())).add(currencyRateReq);
                    } else {
                        ((CurrencyRateServiceImpl) (AopContext.currentProxy())).modify(currencyRateReq);
                    }
                } else {
                    ((CurrencyRateServiceImpl) (AopContext.currentProxy())).add(currencyRateReq);
                }
                paramImportResultDTO.addResult(AnyTxnHttpResponse.success());
                successNum++;
            } catch (Exception e) {
                paramImportResultDTO.addResult(buildExceptionInfo(e, lineNum));
            }
        }
        paramImportResultDTO.setTotalNum(String.valueOf(readLines.size() - 1));
        paramImportResultDTO.setSuccessNum(String.valueOf(successNum));
        return paramImportResultDTO.buildSuccessInfo();
    }

}
