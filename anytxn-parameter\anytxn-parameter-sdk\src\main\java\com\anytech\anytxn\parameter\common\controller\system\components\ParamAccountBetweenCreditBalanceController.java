package com.anytech.anytxn.parameter.common.controller.system.components;

import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import io.swagger.v3.oas.annotations.Operation;
import com.anytech.anytxn.common.core.base.PageResultDTO;
import com.anytech.anytxn.common.core.base.AnyTxnHttpResponse;
import com.anytech.anytxn.common.core.base.BizBaseController;
import com.anytech.anytxn.parameter.base.common.enums.ParameterRepDetailEnum;
import com.anytech.anytxn.parameter.base.common.domain.dto.AccountBetweenCreditBalanceDTO;
import com.anytech.anytxn.parameter.base.common.service.IAccountBetweenCreditBalanceService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import jakarta.validation.Valid;

/**
 * @Auther: lt
 * @Date: 2021/05/10/15:18
 * @Description:
 */
@Tag(name = "账户间贷方余额使用顺序参数接口")
@RestController
public class ParamAccountBetweenCreditBalanceController extends BizBaseController {

    @Autowired
    private IAccountBetweenCreditBalanceService accountBetweenCreditBalanceService;


    /**
     * @description 分页查询账户间贷方余额使用顺序参数
     * <AUTHOR>
     * @date 2020/06/22
     * @param page, rows
     * @return jrx.anytxn.biz.common.web.AnyTxnHttpResponse<jrx.anytxn.biz.common.dto.PageResultDTO<jrx.anytxn.auth.dto.VelocitySetDefinitionDTO>>
     */
    @Operation(summary = "分页查询账户间贷方余额使用顺序参数信息")
    @GetMapping("/param/accountBetweenCreditBalance/{page}/{rows}")
    public AnyTxnHttpResponse<PageResultDTO<AccountBetweenCreditBalanceDTO>> getVelocitySetDefinitionList(@Parameter(name = "page", description = "当前页", example = "1")@PathVariable("page") int page ,
                                                                                                          @Parameter(name = "rows", description = "每页大小", example = "8") @PathVariable("rows") int rows,
                                                                                                          AccountBetweenCreditBalanceDTO velocitySetDTO) {
        PageResultDTO<AccountBetweenCreditBalanceDTO> result = accountBetweenCreditBalanceService.findByPage(page, rows,velocitySetDTO);
        return AnyTxnHttpResponse.success(result);
    }

    /**
     * 根据id查询账户间贷方余额使用顺序参数
     * @param
     * @return HttpApiResponse<VelocityControlDTO>
     */
    @Operation(summary="根据卡片编号查询账户间贷方余额使用顺序参数", description = "需传入id")
    @GetMapping(value = "/param/accountBetweenCreditBalance/id/{id}")
    public AnyTxnHttpResponse<AccountBetweenCreditBalanceDTO> getByCardProductCode(@PathVariable String id){
        AccountBetweenCreditBalanceDTO velocityControlDTO = accountBetweenCreditBalanceService.findById(id);
        return AnyTxnHttpResponse.success(velocityControlDTO);
    }

    /**
     * 新建账户间贷方余额使用顺序参数
     * @param record
     * @return HttpApiResponse
     */
    @PostMapping("/param/accountBetweenCreditBalance")
    @Operation(summary = "新增账户间贷方余额使用顺序参数")
    public AnyTxnHttpResponse<Object> create(@Valid @RequestBody AccountBetweenCreditBalanceDTO record) {
        return AnyTxnHttpResponse.success(accountBetweenCreditBalanceService.add(record),ParameterRepDetailEnum.CREATE.message());
    }

    /**
     * 更新账户间贷方余额使用顺序参数
     * @param record
     * @return HttpApiResponse
     */
    @PutMapping(value = "/param/accountBetweenCreditBalance")
    @Operation(summary="根据id更新账户间贷方余额使用顺序参数")
    public AnyTxnHttpResponse<Object> modify(@Valid @RequestBody AccountBetweenCreditBalanceDTO record) {
        return AnyTxnHttpResponse.success(accountBetweenCreditBalanceService.modify(record),ParameterRepDetailEnum.UPDATE.message());
    }

    /**
     * 通过id删除账户间贷方余额使用顺序参数
     * @param id 技术id
     * @return HttpApiResponse
     */
    @DeleteMapping(value = "/param/accountBetweenCreditBalance/id/{id}")
    @Operation(summary="根据id删除账户间贷方余额使用顺序参数", description = "需传入id")
    public AnyTxnHttpResponse<Object> remove(@PathVariable String id) {
        return AnyTxnHttpResponse.success(accountBetweenCreditBalanceService.remove(id),ParameterRepDetailEnum.DEL.message());
    }


}
