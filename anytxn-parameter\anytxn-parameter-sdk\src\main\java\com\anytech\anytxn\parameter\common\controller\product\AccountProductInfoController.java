package com.anytech.anytxn.parameter.common.controller.product;

import io.swagger.v3.oas.annotations.tags.Tag;
import io.swagger.v3.oas.annotations.Operation;
import com.anytech.anytxn.common.core.base.PageResultDTO;
import com.anytech.anytxn.common.core.base.AnyTxnHttpResponse;
import com.anytech.anytxn.common.core.base.BizBaseController;
import com.anytech.anytxn.parameter.base.common.enums.ParameterRepDetailEnum;
import com.anytech.anytxn.parameter.base.common.domain.dto.product.AccountProductInfoReqDTO;
import com.anytech.anytxn.parameter.base.common.domain.dto.product.AccountProductInfoResDTO;
import com.anytech.anytxn.parameter.base.common.domain.dto.system.SysCodeSimpleResDTO;
import com.anytech.anytxn.parameter.base.common.service.product.IAccountProductInfoService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import jakarta.validation.Valid;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;

/**
 * 账户产品信息接口
 *
 * <AUTHOR>
 * @date 2018-12-04 17:44
 **/
@Tag(name = "账户产品信息")
@RestController
public class AccountProductInfoController extends BizBaseController {

    @Autowired
    private IAccountProductInfoService accountProductInfoService;


    @Operation(summary = "查询账户产品信息",description = "查询账户产品信息")
    @GetMapping(value = "/param/accountProduct/organizationNumber/{organizationNumber}/accountProNum/{accountProNum}")
    public AnyTxnHttpResponse<AccountProductInfoResDTO> getByIndex(@PathVariable(value = "organizationNumber") String orgNum,
                                                                   @PathVariable(value = "accountProNum") String accountProNum) {
        AccountProductInfoResDTO accountProductInfoResDTO = accountProductInfoService.findByIndex(orgNum, accountProNum);
        return AnyTxnHttpResponse.success(accountProductInfoResDTO);
    }


    @Operation(summary = "新增账户产品信息",description = "新增账户产品信息")
    @PostMapping(value = "/param/accountProduct")
    public AnyTxnHttpResponse<Object> create(@Valid @RequestBody AccountProductInfoReqDTO accountProductInfoReqDTO) {
        return AnyTxnHttpResponse.success(accountProductInfoService.add(accountProductInfoReqDTO), ParameterRepDetailEnum.CREATE.message());
    }

    @Operation(summary = "删除账户产品信息",description = "删除账户产品信息")
    @DeleteMapping(value = "/param/accountProduct/id/{id}")
    public AnyTxnHttpResponse<Object> remove(@PathVariable String id) {
        return AnyTxnHttpResponse.success(accountProductInfoService.remove(id),ParameterRepDetailEnum.DEL.message());
    }

    @Operation(summary = "修改账户产品信息",description = "修改账户产品信息")
    @PutMapping(value = "/param/accountProduct")
    public AnyTxnHttpResponse<Object> modify(@Valid @RequestBody AccountProductInfoReqDTO accountProductInfoReqDTO) {
        return AnyTxnHttpResponse.success(accountProductInfoService.modify(accountProductInfoReqDTO),ParameterRepDetailEnum.UPDATE.message());
    }

    @Operation(summary = "分页查询账户产品信息",description = "分页查询账户产品信息")
    @GetMapping(value = "/param/accountProduct/pageNum/{pageNum}/pageSize/{pageSize}")
    public AnyTxnHttpResponse<PageResultDTO<AccountProductInfoResDTO>> getPage(@PathVariable(value = "pageNum")Integer pageNum,
                                                                               @PathVariable(value = "pageSize")Integer pageSize,
                                                                               @RequestParam(required = false) String organizationNumber) {
        PageResultDTO<AccountProductInfoResDTO> txnPage = accountProductInfoService.findPage(pageNum, pageSize, organizationNumber);
        return AnyTxnHttpResponse.success(txnPage);
    }

    @Operation(summary = "根据id查询账户产品信息",description = "根据id查询账户产品信息")
    @GetMapping(value = "/param/accountProduct/id/{id}")
    public AnyTxnHttpResponse<AccountProductInfoResDTO> getById(@PathVariable String id) {
        AccountProductInfoResDTO accountProductInfoResDTO = accountProductInfoService.findOne(id);
        return AnyTxnHttpResponse.success(accountProductInfoResDTO);
    }

    @Operation(summary = "查询账户产品编号",description = "查询账户产品编号")
    @GetMapping(value = "/param/accountProduct/acctProductTypeList")
    public AnyTxnHttpResponse<HashMap<String, ArrayList<SysCodeSimpleResDTO>>> acctProductTypeList(@RequestParam String organizationNumber){
        HashMap<String, ArrayList<SysCodeSimpleResDTO>> acctProductTypeMap = accountProductInfoService.findAcctProductTypeList(organizationNumber);
        return AnyTxnHttpResponse.success(acctProductTypeMap);
    }

    @Operation(summary = "查询账户产品编号",description = "查询账户产品编号")
    @GetMapping(value = "/param/accountProduct/acctProductTypeList/limitTypeNum/{limitTypeNum}/tableId/{tableId}/customerId/{customerId}")
    public AnyTxnHttpResponse<HashMap<String, List<SysCodeSimpleResDTO>>> acctProductTypeList
            (@PathVariable(value = "limitTypeNum") String limitTypeNum
                    ,@PathVariable(value= "tableId") String tableId
                    ,@PathVariable(value= "customerId") String customerId) {
        HashMap<String, List<SysCodeSimpleResDTO>> acctProductTypeMap = accountProductInfoService
                .findAcctProductTypeListByLimitTypeNumAnd(limitTypeNum,tableId,customerId);
        return AnyTxnHttpResponse.success(acctProductTypeMap);
    }
}
