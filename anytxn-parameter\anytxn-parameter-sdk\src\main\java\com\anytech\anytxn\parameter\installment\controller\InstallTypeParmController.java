package com.anytech.anytxn.parameter.installment.controller;

import com.anytech.anytxn.common.core.base.PageResultDTO;
import com.anytech.anytxn.common.core.base.BizBaseController;
import com.anytech.anytxn.common.core.base.AnyTxnHttpResponse;
import io.swagger.v3.oas.annotations.tags.Tag;
import io.swagger.v3.oas.annotations.Operation;
import com.anytech.anytxn.parameter.base.common.enums.ParameterRepDetailEnum;
import com.anytech.anytxn.parameter.base.common.domain.dto.ParmTransactionCodeResDTO;
import com.anytech.anytxn.parameter.base.installment.domain.dto.InstallTypeParmReqDTO;
import com.anytech.anytxn.parameter.base.installment.domain.dto.InstallTypeParmResDTO;
import com.anytech.anytxn.parameter.base.installment.domain.dto.InstallTypeSupportTxnResDTO;
import com.anytech.anytxn.parameter.base.installment.service.IInstallTypeParmService;
import com.anytech.anytxn.parameter.base.installment.service.IInstallTypeSupportTxnService;
import com.anytech.anytxn.parameter.base.common.utils.AnyTxnHttpResponseHelper;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import jakarta.validation.Valid;
import java.util.ArrayList;
import java.util.List;

/***
 * 分期类型参数
 *
 * @author: huaxianchao
 * @create: 2019-05-14 13:08
 **/
@Tag(name = "分期类型参数")
@RestController
public class InstallTypeParmController extends BizBaseController {

    @Autowired
    private IInstallTypeParmService installTypeParmService;

    @Autowired
    private IInstallTypeSupportTxnService installTypeSupportTxnService;

    /**
     * @Author: huaxianchao
     * @Date: 2019/5/14 13:32
     * @Param: installTypeParmReqDTO
     * @Return: 分期类型响应参数
     */
    @Operation(summary = "添加分期类型参数", description = "添加分期类型参数")
    @PostMapping("/param/installtype")
    public AnyTxnHttpResponse<Object> create(@Valid @RequestBody InstallTypeParmReqDTO installTypeParmReqDTO) {
        if(installTypeParmReqDTO.getInstallTypeSupportTxnResList() == null){
            installTypeParmReqDTO.setInstallTypeSupportTxnResList(new ArrayList<>());
        }
        return AnyTxnHttpResponse.success(installTypeParmService.addInstallTypeParm(installTypeParmReqDTO),ParameterRepDetailEnum.CREATE.message());
    }

    @Operation(summary = "根据id删除分期类型参数", description = "根据id删除分期类型参数")
    @DeleteMapping("/param/installtype/id/{id}")
    public AnyTxnHttpResponse<Object> remove(@PathVariable String id) {
        return AnyTxnHttpResponse.success(installTypeParmService.removeInstallTypeParm(id),ParameterRepDetailEnum.DEL.message());

    }

    @Operation(summary = "修改分期类型参数", description = "修改分期类型参数")
    @PutMapping("/param/installtype")
    public AnyTxnHttpResponse<Object> modify(@Valid @RequestBody InstallTypeParmReqDTO installTypeParmReqDTO) {
        if(installTypeParmReqDTO.getInstallTypeSupportTxnResList() == null){
            installTypeParmReqDTO.setInstallTypeSupportTxnResList(new ArrayList<>());
        }
        return AnyTxnHttpResponse.success(installTypeParmService.modifyInstallTypeParm(installTypeParmReqDTO),ParameterRepDetailEnum.UPDATE.message());
    }

    @Operation(summary = "根据id查询分期类型参数表", description = "根据id查询分期类型参数表")
    @GetMapping("/param/installtype/id/{id}")
    public AnyTxnHttpResponse<InstallTypeParmResDTO> getById(@PathVariable("id") String id) {
        InstallTypeParmResDTO installTypeParmResDTO = installTypeParmService.findById(id);

        return AnyTxnHttpResponse.success(installTypeParmResDTO);
    }

    @Operation(summary = "分页查询分期类型参数表", description = "分页查询分期类型参数表")
    @GetMapping("/param/installtype/pageNum/{pageNum}/pageSize/{pageSize}")
    public AnyTxnHttpResponse<PageResultDTO<InstallTypeParmResDTO>> getPage(
            @PathVariable("pageNum") Integer pageNum,
            @PathVariable("pageSize") Integer pageSize,
            InstallTypeParmReqDTO installTypeParmReqDTO
    ) {
        PageResultDTO<InstallTypeParmResDTO> pageResultDto = installTypeParmService.findAll(pageNum, pageSize,installTypeParmReqDTO);
        return AnyTxnHttpResponse.success(pageResultDto);
    }

    @Operation(summary = "根据机构号,类型查询分期类型参数表", description = "根据organizationNumber,type查询分期类型参数表")
    @GetMapping("/param/installtype/organizationNumber/{organizationNumber}/type/{type}")
    public AnyTxnHttpResponse<InstallTypeParmResDTO> getByOrgNumAndType(@PathVariable String organizationNumber, @PathVariable String type) {
        InstallTypeParmResDTO installTypeParmResDTO = installTypeParmService.findByOrgNumAndType(organizationNumber, type);
        return AnyTxnHttpResponse.success(installTypeParmResDTO);
    }

    @Operation(summary = "根据type删除分期类型参数", description = "根据type删除分期类型参数")
    @DeleteMapping("/param/installtype/type/{type}")
    public AnyTxnHttpResponse removeById(@PathVariable String type) {
        installTypeParmService.removeByType(type);
        return AnyTxnHttpResponseHelper.succ(ParameterRepDetailEnum.DEL.message());
    }

    @Operation(summary = "根据机构号,分期大类,细类查询分期类型参数表", description = "根据机构号,分期大类,细类查询分期类型参数表")
    @GetMapping("/param/installtype/organizationNumber/{organizationNumber}/authTransactionType/{authTransactionType}/authTransactionTypeDetail/{authTransactionTypeDetail}")
    public AnyTxnHttpResponse<InstallTypeParmResDTO> getByOrgNumAndTypeAndDetail(@PathVariable String organizationNumber,
                                                                              @PathVariable String authTransactionType,
                                                                              @PathVariable String authTransactionTypeDetail)
    {
        InstallTypeParmResDTO installTypeParmResDTO = installTypeParmService.findByOrgNumAndTypeAndDetail(organizationNumber, authTransactionType,authTransactionTypeDetail);
        return AnyTxnHttpResponse.success(installTypeParmResDTO);
    }

    @Operation(summary = "根据类型、机构号查询分期类型交易参数表", description = "根据类型、机构号查询分期类型交易参数表")
    @GetMapping("/param/installtypesupporttxn/type/{type}/organizationNumber/{organizationNumber}")
    public AnyTxnHttpResponse<List<InstallTypeSupportTxnResDTO>> getTypeSupportTxnByTypeAndOrgNum(@PathVariable String type,
                                                                                               @PathVariable String organizationNumber)
    {
        List<InstallTypeSupportTxnResDTO> installTypeSupportTxnResDtos = installTypeSupportTxnService
                .getByTypeAndOrgNum(type, organizationNumber);
        return AnyTxnHttpResponse.success(installTypeSupportTxnResDtos);
    }

    @Operation(summary = "根据消费、取现类交易属性查询交易码参数表", description = "根据消费、取现类交易属性查询交易码参数表")
    @GetMapping("/param/installtype/forConsumeCashCode")
    public AnyTxnHttpResponse<List<ParmTransactionCodeResDTO>> getTransCodeAndDesc(@RequestParam String organizationNumber)
    {
        List<ParmTransactionCodeResDTO> transactionCodeResDtos = installTypeParmService.findTransCodeAndDesc(organizationNumber);
        return AnyTxnHttpResponse.success(transactionCodeResDtos);
    }
}
