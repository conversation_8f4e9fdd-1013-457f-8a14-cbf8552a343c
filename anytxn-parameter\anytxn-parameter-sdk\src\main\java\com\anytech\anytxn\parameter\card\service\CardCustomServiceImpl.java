package com.anytech.anytxn.parameter.card.service;

import com.github.pagehelper.Page;
import com.github.pagehelper.PageHelper;
import com.anytech.anytxn.common.core.utils.OrgNumberUtils;
import com.anytech.anytxn.parameter.base.common.constants.Constants;
import com.anytech.anytxn.common.core.base.PageResultDTO;
import com.anytech.anytxn.common.core.utils.BeanMapping;
import com.anytech.anytxn.common.sequence.utils.Number16IdGen;
import com.anytech.anytxn.common.core.utils.TenantUtils;
import com.anytech.anytxn.parameter.base.card.domain.dto.CardCustomReqDTO;
import com.anytech.anytxn.parameter.base.card.domain.dto.CardCustomResDTO;
import com.anytech.anytxn.parameter.base.card.service.ICardCustomService;
import com.anytech.anytxn.parameter.base.common.enums.AnyTxnParameterRespCodeEnum;
import com.anytech.anytxn.parameter.base.common.exception.AnyTxnParameterException;
import com.anytech.anytxn.parameter.card.mapper.broadcast.ParmCardCustomMapper;
import com.anytech.anytxn.parameter.card.mapper.broadcast.ParmCardCustomSelfMapper;
import com.anytech.anytxn.parameter.base.card.domain.model.ParmCardCustom;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.util.List;

/**
 * 产品参数 业务实现
 *
 * <AUTHOR>
 * @date 2018-08-16 11:37
 **/
@Service
public class CardCustomServiceImpl implements ICardCustomService {

    private Logger logger = LoggerFactory.getLogger(CardCustomServiceImpl.class);

    @Autowired
    private ParmCardCustomSelfMapper parmCardCustomSelfMapper;
    @Autowired
    private ParmCardCustomMapper parmCardCustomMapper;

    @Autowired
    private Number16IdGen numberIdGenerator;

    /**
     * 新增记录
     *
     * @param cardCustomReqDTO
     * @return 产品信息详情
     * @throws AnyTxnParameterException
     */
    @Override
    public CardCustomResDTO add(CardCustomReqDTO cardCustomReqDTO) {
        if(StringUtils.isEmpty(cardCustomReqDTO.getOrganizationNumber())){
            cardCustomReqDTO.setOrganizationNumber(OrgNumberUtils.getOrg());
        }
        int count = parmCardCustomSelfMapper.isExists(cardCustomReqDTO.getOrganizationNumber(), cardCustomReqDTO.getCardBinTableId(),cardCustomReqDTO.getProductNumber());
        if (count>0) {
            logger.warn("记录已存在, Organization ={} CardBin={} ProductNumber={}",
                    cardCustomReqDTO.getOrganizationNumber(), cardCustomReqDTO.getCardBinTableId(),cardCustomReqDTO.getProductNumber());
            throw new AnyTxnParameterException(AnyTxnParameterRespCodeEnum.D_EXIST_PARM_CARD_CARD_CUSTOM_REQ_DTO_FAULT);
        }

        //构建产品详情
        ParmCardCustom parmCardCustom = BeanMapping.copy(cardCustomReqDTO, ParmCardCustom.class);
        parmCardCustom.setCreateTime(LocalDateTime.now());
        parmCardCustom.setVersionNumber(1L);
        parmCardCustom.setUpdateTime(LocalDateTime.now());
        parmCardCustom.setUpdateBy(Constants.DEFAULT_USER);
        parmCardCustom.setId(numberIdGenerator.generateId(TenantUtils.getTenantId()));
        parmCardCustomMapper.insertSelective(parmCardCustom);

        cardCustomReqDTO.setId(parmCardCustom.getId());

        return BeanMapping.copy(parmCardCustom, CardCustomResDTO.class);
    }

    /**
     * 通过id删除条目
     *
     * @param id 技术主键
     * @return true:删除成功|false:删除失败
     * @throws AnyTxnParameterException
     */
    @Override
    public Boolean remove(Long id) {
        ParmCardCustom parmCardCustom = parmCardCustomMapper.selectByPrimaryKey(id);

        if (parmCardCustom == null) {
            logger.error("删除卡号个性化信息, 通过主键id({})未找到数据", id);
            throw new AnyTxnParameterException(AnyTxnParameterRespCodeEnum.D_QUERY_PARM_CARD_CARD_CUSTOM_FAULT);
        }

        logger.warn("通过id删除卡号个性化信息, {}", parmCardCustom);

        int deleteRows = parmCardCustomMapper.deleteByPrimaryKey(id);
        return deleteRows > 0;
    }

    /**
     * 通过id获取详情
     *
     * @param id 主键id
     * @return 产品信息详情
     * @throws AnyTxnParameterException
     */
    @Override
    public CardCustomResDTO find(Long id){
        if (id == null) {
            throw new AnyTxnParameterException(AnyTxnParameterRespCodeEnum.P_PARAMETER_EMPTY_FAULT);
        }

        ParmCardCustom parmCardCustom = parmCardCustomMapper.selectByPrimaryKey(id);

        if (parmCardCustom == null) {
            logger.error("查询卡号个性化信息, 通过主键id({})未找到数据", id);
            throw new AnyTxnParameterException(AnyTxnParameterRespCodeEnum.D_QUERY_PARM_CARD_CARD_CUSTOM_FAULT);
        }

        return BeanMapping.copy(parmCardCustom, CardCustomResDTO.class);
    }

    /**
     * 修改产品
     *
     * @param cardCustomReqDTO 产品入参对象
     * @return 产品信息详情
     * @throws AnyTxnParameterException
     */
    @Override
    public CardCustomResDTO modify(CardCustomReqDTO cardCustomReqDTO){
        if (cardCustomReqDTO.getId() == null) {
            throw new AnyTxnParameterException(AnyTxnParameterRespCodeEnum.P_PARAMETER_ID_EMPTY_FAULT);
        }
        if(StringUtils.isEmpty(cardCustomReqDTO.getOrganizationNumber())){
            cardCustomReqDTO.setOrganizationNumber(OrgNumberUtils.getOrg());
        }
        ParmCardCustom parmCardCustom = parmCardCustomMapper.selectByPrimaryKey(cardCustomReqDTO.getId());

        if (parmCardCustom == null) {
            logger.error("修改卡号个性化信息, 通过主键id({})未找到数据", cardCustomReqDTO.getId());
            throw new AnyTxnParameterException(AnyTxnParameterRespCodeEnum.D_QUERY_PARM_CARD_CARD_CUSTOM_FAULT);
        }

        // 拷贝修改的数据并更新
        BeanMapping.copy(cardCustomReqDTO, parmCardCustom);
        parmCardCustom.setUpdateTime(LocalDateTime.now());
        parmCardCustom.setUpdateBy(Constants.DEFAULT_USER);
        parmCardCustomMapper.updateByPrimaryKeySelective(parmCardCustom);

        //历史表中添加记录

        return BeanMapping.copy(parmCardCustom, CardCustomResDTO.class);
    }

    /**
     * 分页查询产品信息 根据机构号
     *
     * @param pageNum  页号
     * @param pageSize 每页大小
     * @return 产品详情分页
     * @throws AnyTxnParameterException
     */
    @Override
    public PageResultDTO<CardCustomResDTO> findPageByOrgNumber(Integer pageNum, Integer pageSize, String organizationNumber){

        logger.debug("分页查询锁定卡号信息, pageNum={}, pageSize={}",pageNum, pageSize);
        Page<CardCustomResDTO> page = PageHelper.startPage(pageNum, pageSize);
        List<ParmCardCustom> dataList = parmCardCustomSelfMapper.selectByOrganizationNumber(organizationNumber, false);
        if (dataList.isEmpty()) {
            logger.error("未查询到信息");
            throw new AnyTxnParameterException(AnyTxnParameterRespCodeEnum.D_PAGE_QUERY_PARM_CARD_CARD_CUSTOM_FAULT);
        }
        List<CardCustomResDTO> cardCustomResList = BeanMapping.copyList(dataList, CardCustomResDTO.class);

        return new PageResultDTO<>(pageNum, pageSize, page.getTotal(), page.getPages(), cardCustomResList);
    }

}
